"""
系统相关模型 - 模块化结构

包含系统配置、日志、缓存等系统相关的数据模型
"""

# 暂时禁用系统模型导入以避免循环导入和表重复定义问题
# 这些模型可以直接从主models.py导入使用
try:
    import logging
    logger = logging.getLogger(__name__)
    logger.info("系统模型包已禁用，请直接从app.models导入")
    __all__ = []
    
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"系统模型包初始化失败: {e}")
    __all__ = []

from .database_config import DatabaseConfig, DatabaseMapping
from .stage_mapping import StageMappingConfig
# DifyConfig已合并到主models.py中，避免重复定义

__all__ = ['DatabaseConfig', 'DatabaseMapping', 'StageMappingConfig'] 