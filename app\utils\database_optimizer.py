"""
数据库查询优化器
"""
import time
import logging
from functools import wraps
from flask import current_app, Blueprint, jsonify, request
from sqlalchemy import event, text
from sqlalchemy.engine import Engine

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    def __init__(self, app=None):
        self.app = app
        self.slow_query_threshold = 1.0  # 1秒
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        # 监听SQL查询事件
        @event.listens_for(Engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @event.listens_for(Engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total = time.time() - context._query_start_time
            
            if total > self.slow_query_threshold:
                logger.warning(f"慢查询 ({total:.2f}s): {statement[:100]}...")
                
                # 记录到性能监控
                if hasattr(current_app, 'performance_stats'):
                    current_app.performance_stats['slow_queries'].append({
                        'query': statement[:200],
                        'duration': total,
                        'timestamp': time.time()
                    })
    
    def optimize_query(self, func):
        """查询优化装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 记录查询性能
                if duration > 0.5:  # 超过0.5秒的查询
                    logger.info(f"查询性能: {func.__name__} - {duration:.2f}s")
                
                return result
            except Exception as e:
                logger.error(f"查询错误: {func.__name__} - {e}")
                raise
        return wrapper

# 常用查询优化方法
class QueryOptimizations:
    @staticmethod
    def bulk_insert(model_class, data_list):
        """批量插入优化"""
        from app import db
        
        if not data_list:
            return
        
        try:
            db.session.bulk_insert_mappings(model_class, data_list)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
    
    @staticmethod
    def paginate_query(query, page=1, per_page=50):
        """分页查询优化"""
        return query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False,
            max_per_page=100
        )
    
    @staticmethod
    def optimize_joins(query):
        """连接查询优化"""
        # 使用joinedload避免N+1查询问题
        from sqlalchemy.orm import joinedload
        return query.options(joinedload('*'))

def create_database_optimization_routes(app):
    """创建数据库优化管理路由"""
    
    db_optimizer_bp = Blueprint('db_optimizer', __name__)
    
    @db_optimizer_bp.route('/admin/db-optimizer/status')
    def get_optimizer_status():
        """获取数据库优化器状态"""
        try:
            # 获取性能统计
            stats = getattr(current_app, 'performance_stats', {})
            slow_queries = stats.get('slow_queries', [])
            
            # 最近慢查询统计
            recent_queries = [q for q in slow_queries if time.time() - q['timestamp'] < 3600]  # 最近1小时
            
            return jsonify({
                'status': 'success',
                'data': {
                    'optimizer_enabled': True,
                    'slow_query_threshold': '1.0s',
                    'recent_slow_queries_count': len(recent_queries),
                    'total_slow_queries': len(slow_queries),
                    'recent_queries': recent_queries[-10:]  # 最近10条
                }
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'获取优化器状态失败: {str(e)}'
            }), 500
    
    @db_optimizer_bp.route('/admin/db-optimizer/clear-stats', methods=['POST'])
    def clear_optimizer_stats():
        """清空优化器统计"""
        try:
            if hasattr(current_app, 'performance_stats'):
                current_app.performance_stats['slow_queries'] = []
            
            return jsonify({
                'status': 'success',
                'message': '优化器统计已清空'
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'清空统计失败: {str(e)}'
            }), 500
    
    # 注册蓝图
    app.register_blueprint(db_optimizer_bp)
    logger.info('✅ 数据库优化器路由已注册')
