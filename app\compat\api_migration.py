#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API迁移兼容层
处理API v1到v2的URL映射和兼容性问题
"""

from flask import Blueprint, request, redirect, url_for
from flask_login import login_required
import logging
import time

logger = logging.getLogger(__name__)

# 创建API兼容蓝图
api_compat_bp = Blueprint('api_compat', __name__, url_prefix='/api')

@api_compat_bp.route('/user-filter-presets', methods=['GET', 'POST'])
@login_required
def user_filter_presets_compat():
    """用户过滤器预设API兼容层"""
    # 直接处理请求，避免重定向问题
    from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
    
    if request.method == 'GET':
        return get_user_filter_presets()
    elif request.method == 'POST':
        return save_user_filter_preset()
    
    # 如果没有匹配的方法，返回404
    from flask import abort
    abort(404)

@api_compat_bp.route('/user-filter-presets/<int:preset_id>', methods=['PUT', 'DELETE'])
@login_required
def user_filter_presets_with_id_compat(preset_id):
    """用户过滤器预设API兼容层（带ID）"""
    from app.api_v2.production.missing_apis import update_user_filter_preset, delete_user_filter_preset
    
    if request.method == 'PUT':
        return update_user_filter_preset(preset_id)
    elif request.method == 'DELETE':
        return delete_user_filter_preset(preset_id)
    
    from flask import abort
    abort(404)

@api_compat_bp.route('/production/auto-schedule', methods=['POST'])
@login_required
def auto_schedule_compat():
    """自动排产API兼容层"""
    from app.api_v2.production.missing_apis import start_auto_schedule
    return start_auto_schedule()

# 其他可能需要兼容的API端点
@api_compat_bp.route('/production/manual-schedule', methods=['POST'])
@login_required
def manual_schedule_compat():
    """手动排产API兼容层"""
    # 这里可以添加手动排产的逻辑，或者重定向到相应的API v2端点
    from flask import jsonify
    return jsonify({
        'success': True,
        'message': '手动排产功能正在开发中',
        'data': {
            'schedule_id': 'MANUAL_' + str(int(time.time())),
            'status': 'pending',
            'message': '手动排产请求已接收'
        }
    })

# 注意：资源管理相关的API已迁移到 API v2 (/api/v2/resources/)
# 旧的 /api/resources/* 端点不再提供兼容层支持 