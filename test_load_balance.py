#!/usr/bin/env python3
"""
测试负载均衡算法改进效果
"""
import sys
sys.path.append('.')
from app.services.real_scheduling_service import RealSchedulingService
import json

def test_load_balance():
    """测试会话内负载累积和新的负载评分"""
    print("=== 负载均衡算法测试 ===")
    
    service = RealSchedulingService()
    
    # 模拟设备
    equipment = {
        'HANDLER_ID': 'HCHC-C-015-6800',
        'EQP_TYPE': 'HANDLER',
        'STAGE': 'FT',
        'HANDLER_CONFIG': 'HC_CONFIG_A',
        'KIT_PN': 'KIT_001'
    }
    
    # 测试负载评分变化
    print("=== 负载评分测试 ===")
    for i in range(5):
        score = service.calculate_load_balance_score(equipment, 2.0, 15)
        current_load = service.equipment_workload.get('HCHC-C-015-6800', 0.0)
        print(f"第{i+1}次: 当前负载={current_load:.2f}h, 评分={score:.1f}")
        # 模拟累积负载
        service.equipment_workload['HCHC-C-015-6800'] = current_load + 2.25  # 2h处理 + 0.25h改机
    
    print("\n=== 配置参数测试 ===")
    from app.config.scheduling_config import SchedulingConfig
    policy = getattr(SchedulingConfig, 'ALGORITHM_POLICY', {})
    bw = policy.get('BANDWIDTH', {})
    div = policy.get('DIVERSIFICATION', {})
    th = policy.get('THRESHOLDS', {})
    
    print(f"带宽安全阀: L_MIN={bw.get('L_MIN', '未配置')}, L_MAX={bw.get('L_MAX', '未配置')}")
    print(f"去集中化强度: LAMBDA={div.get('LAMBDA', '未配置')}")
    print(f"穿插阈值: DELTA={th.get('DELTA', '未配置')}, DELTA_UP={th.get('DELTA_UP', '未配置')}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_load_balance()
