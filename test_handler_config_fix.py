#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 HANDLER_CONFIG 重复设定修复效果
验证修复后的配置获取逻辑是否正确
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('handler_config_fix_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('HandlerConfigFixTest')

def test_handler_config_fix():
    """测试HANDLER_CONFIG修复效果"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        from app.services.real_scheduling_service import RealSchedulingService
        
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 初始化排产服务
            scheduling_service = RealSchedulingService()
            
            # 创建测试批次数据
            test_lot = {
                'LOT_ID': 'TEST_LOT_001',
                'DEVICE': 'JWH7030QFNAZ',  # 使用一个常见的设备型号
                'STAGE': 'FT',
                'PKG_PN': 'PKG_TEST',
                'WAFER_QTY': 100
            }
            
            logger.info("🧪 开始测试 HANDLER_CONFIG 配置获取...")
            logger.info(f"📋 测试批次: {test_lot}")
            
            # 预加载数据
            preloaded_data = {
                'recipe_files': scheduling_service._get_recipe_files_data(),
                'test_specs': [],  # 简化测试，主要关注recipe_files
                'uph_data': {},
                'device_priority': {},
                'lot_priority': {},
                'stage_mappings': []
            }
            
            logger.info(f"📁 预加载recipe_files数量: {len(preloaded_data['recipe_files'])}")
            
            # 测试配置获取
            config = scheduling_service._get_lot_configuration_requirements_optimized(
                test_lot, preloaded_data
            )
            
            if config:
                logger.info("✅ 配置获取成功")
                logger.info(f"📋 配置详情:")
                for key, value in config.items():
                    if key == 'HANDLER_CONFIG':
                        logger.info(f"  🎯 {key}: '{value}' (关键字段)")
                    elif key == 'HANDLER_CONFIG_LOCKED':
                        logger.info(f"  🔒 {key}: {value} (保护标记)")
                    else:
                        logger.info(f"     {key}: {value}")
                
                # 验证HANDLER_CONFIG是否已正确设定
                handler_config = config.get('HANDLER_CONFIG', '')
                config_locked = config.get('HANDLER_CONFIG_LOCKED', False)
                
                if handler_config and config_locked:
                    logger.info("🎉 修复成功: HANDLER_CONFIG已正确设定并锁定保护")
                    return True
                elif handler_config and not config_locked:
                    logger.warning("⚠️ HANDLER_CONFIG已设定但未锁定，可能存在被覆盖风险")
                    return False
                else:
                    logger.warning("❌ HANDLER_CONFIG未正确设定")
                    return False
            else:
                logger.error("❌ 配置获取失败")
                return False
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_multiple_lots():
    """测试多个批次的配置获取，验证修复的普适性"""
    try:
        from app import create_app
        from app.services.real_scheduling_service import RealSchedulingService
        
        app, socketio = create_app()
        
        with app.app_context():
            scheduling_service = RealSchedulingService()
            
            # 准备测试批次
            test_lots = [
                {'LOT_ID': 'TEST_001', 'DEVICE': 'JWH7030QFNAZ', 'STAGE': 'FT', 'PKG_PN': 'PKG_001'},
                {'LOT_ID': 'TEST_002', 'DEVICE': 'JWH7030QFNAZ', 'STAGE': 'HOT', 'PKG_PN': 'PKG_002'},
                {'LOT_ID': 'TEST_003', 'DEVICE': 'UNKNOWN_DEVICE', 'STAGE': 'FT', 'PKG_PN': 'PKG_003'},
            ]
            
            preloaded_data = {
                'recipe_files': scheduling_service._get_recipe_files_data(),
                'test_specs': [],
                'uph_data': {},
                'device_priority': {},
                'lot_priority': {},
                'stage_mappings': []
            }
            
            success_count = 0
            protected_count = 0
            
            for lot in test_lots:
                logger.info(f"🧪 测试批次: {lot['LOT_ID']}")
                
                config = scheduling_service._get_lot_configuration_requirements_optimized(
                    lot, preloaded_data
                )
                
                if config:
                    success_count += 1
                    handler_config = config.get('HANDLER_CONFIG', '')
                    config_locked = config.get('HANDLER_CONFIG_LOCKED', False)
                    
                    if config_locked:
                        protected_count += 1
                        logger.info(f"  ✅ HANDLER_CONFIG: '{handler_config}' (已保护)")
                    else:
                        logger.info(f"  ⚠️ HANDLER_CONFIG: '{handler_config}' (未保护)")
                else:
                    logger.info(f"  ❌ 配置获取失败")
            
            logger.info(f"📊 测试结果: {success_count}/{len(test_lots)} 成功, {protected_count} 个受保护")
            
            return success_count > 0 and protected_count == success_count
            
    except Exception as e:
        logger.error(f"❌ 多批次测试失败: {e}")
        return False

def main():
    """入口函数"""
    logger.info("🚀 开始测试 HANDLER_CONFIG 重复设定修复效果...")
    
    # 单个批次测试
    logger.info("=" * 50)
    logger.info("📋 单个批次配置测试")
    logger.info("=" * 50)
    single_test_success = test_handler_config_fix()
    
    # 多个批次测试
    logger.info("\n" + "=" * 50)
    logger.info("📋 多个批次配置测试")
    logger.info("=" * 50)
    multiple_test_success = test_multiple_lots()
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("📊 测试结果总结")
    logger.info("=" * 50)
    
    if single_test_success and multiple_test_success:
        logger.info("🎉 修复验证成功!")
        logger.info("✅ HANDLER_CONFIG重复设定问题已解决")
        logger.info("🔒 配置保护机制工作正常")
        print("\n🎉 测试通过: HANDLER_CONFIG重复设定问题已修复")
    else:
        logger.error("❌ 修复验证失败!")
        if not single_test_success:
            logger.error("❌ 单个批次测试失败")
        if not multiple_test_success:
            logger.error("❌ 多个批次测试失败")
        print("\n❌ 测试失败: 需要进一步检查修复效果")

if __name__ == "__main__":
    main()