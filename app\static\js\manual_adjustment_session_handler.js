
/**
 * 手动调整会话处理模块
 * 处理会话过期、API错误等情况
 */

class ManualAdjustmentSessionHandler {
    constructor() {
        this.isProcessing = false;
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1秒
    }

    /**
     * 安全的API调用，包含会话检查和重试机制
     */
    async safeApiCall(url, data, options = {}) {
        const defaultOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            
            // 检查是否被重定向到登录页面
            if (response.url.includes('/auth/login') || response.status === 401) {
                throw new Error('SESSION_EXPIRED');
            }

            // 检查响应类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // 如果返回的不是JSON，可能是登录页面
                const text = await response.text();
                if (text.includes('login-container') || text.includes('AEC-FT ICP')) {
                    throw new Error('SESSION_EXPIRED');
                }
                throw new Error('INVALID_RESPONSE');
            }

            const result = await response.json();
            
            // 检查业务逻辑错误
            if (!result.success && result.error_type === 'session_expired') {
                throw new Error('SESSION_EXPIRED');
            }

            return result;

        } catch (error) {
            if (error.message === 'SESSION_EXPIRED') {
                this.handleSessionExpired();
                throw error;
            } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                throw new Error('NETWORK_ERROR');
            } else {
                throw error;
            }
        }
    }

    /**
     * 处理会话过期
     */
    handleSessionExpired() {
        console.warn('⚠️ 用户会话已过期');
        
        // 显示友好的错误提示
        Swal.fire({
            icon: 'warning',
            title: '会话已过期',
            text: '您的登录会话已过期，需要重新登录。页面将自动刷新。',
            confirmButtonText: '确定',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then(() => {
            // 刷新页面重新登录
            window.location.reload();
        });
    }

    /**
     * 批量更新的安全包装器
     */
    async batchUpdateWithRetry(updates, operationType = 'drag_adjustment') {
        if (this.isProcessing) {
            console.warn('⚠️ 已有批量更新正在进行中，跳过重复请求');
            return { success: false, message: '已有更新正在进行中' };
        }

        this.isProcessing = true;

        try {
            // 数据验证
            if (!updates || !Array.isArray(updates) || updates.length === 0) {
                throw new Error('更新数据为空或格式错误');
            }

            // 检查批量大小限制
            if (updates.length > 200) {
                throw new Error(`批量更新数量超限，最多支持200条，当前${updates.length}条`);
            }

            // 生成唯一的会话ID
            const sessionId = `manual_adj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            const requestData = {
                updates: updates,
                operation_type: operationType,
                session_id: sessionId
            };

            console.log(`🔄 开始批量更新 ${updates.length} 条记录`);

            // 尝试API调用，最多重试3次
            let lastError = null;
            for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
                try {
                    const result = await this.safeApiCall(
                        '/api/v2/production/manual-adjustment/batch-update',
                        requestData
                    );

                    console.log(`✅ 批量更新成功: ${result.updated_count}/${result.total_count}`);
                    return result;

                } catch (error) {
                    lastError = error;
                    
                    if (error.message === 'SESSION_EXPIRED') {
                        // 会话过期，不重试
                        throw error;
                    }

                    if (attempt < this.maxRetries) {
                        console.warn(`⚠️ 第${attempt}次尝试失败，${this.retryDelay}ms后重试: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                        this.retryDelay *= 2; // 指数退避
                    }
                }
            }

            // 所有重试都失败了
            throw lastError;

        } finally {
            this.isProcessing = false;
            this.retryDelay = 1000; // 重置延迟
        }
    }

    /**
     * 显示用户友好的错误信息
     */
    showErrorMessage(error, context = '') {
        let title = '操作失败';
        let message = '未知错误';
        let icon = 'error';

        if (error.message === 'SESSION_EXPIRED') {
            // 会话过期在handleSessionExpired中处理
            return;
        } else if (error.message === 'NETWORK_ERROR') {
            title = '网络错误';
            message = '网络连接失败，请检查网络状态后重试';
            icon = 'warning';
        } else if (error.message.includes('批量更新数量超限')) {
            title = '数据量过大';
            message = error.message;
            icon = 'warning';
        } else if (error.message.includes('数据验证失败')) {
            title = '数据验证失败';
            message = error.message;
            icon = 'warning';
        } else {
            message = error.message || '系统内部错误';
        }

        if (context) {
            message = `${context}: ${message}`;
        }

        Swal.fire({
            icon: icon,
            title: title,
            text: message,
            confirmButtonText: '确定'
        });
    }
}

// 全局实例
window.manualAdjustmentHandler = new ManualAdjustmentSessionHandler();

// 为兼容性导出
window.ManualAdjustmentSessionHandler = ManualAdjustmentSessionHandler;
