#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能连接路由器 - 阶段2优化
基于请求类型自动选择最佳连接池，实现负载均衡和资源隔离
"""

import logging
import threading
import time
import re
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class RequestType(Enum):
    """请求类型枚举"""
    SCHEDULING_CALCULATION = "scheduling_calculation"    # 排产计算
    WEB_PAGE_REQUEST = "web_page_request"               # 网页请求
    API_DATA_QUERY = "api_data_query"                   # API数据查询
    EXCEL_IMPORT_EXPORT = "excel_import_export"         # Excel导入导出
    BATCH_PROCESSING = "batch_processing"               # 批处理任务
    MONITORING_DIAGNOSTIC = "monitoring_diagnostic"    # 监控诊断
    REAL_TIME_QUERY = "real_time_query"                # 实时查询
    BACKGROUND_TASK = "background_task"                # 后台任务

@dataclass
class RequestContext:
    """请求上下文"""
    request_type: RequestType
    estimated_duration: float  # 预估持续时间(秒)
    priority_level: int        # 优先级(1-10)
    resource_intensive: bool   # 是否资源密集
    user_facing: bool         # 是否面向用户
    batch_size: int = 1       # 批量大小

class IntelligentConnectionRouter:
    """智能连接路由器"""
    
    def __init__(self):
        self._request_patterns = self._initialize_request_patterns()
        self._routing_rules = self._initialize_routing_rules()
        
        # 路由统计
        self._routing_stats = {
            'total_requests': 0,
            'routing_decisions': {},
            'performance_metrics': {},
            'load_balancing_stats': {}
        }
        
        self._stats_lock = threading.Lock()
        logger.info("🧠 智能连接路由器已初始化")
    
    def _initialize_request_patterns(self) -> Dict[str, RequestType]:
        """初始化请求模式识别"""
        return {
            # SQL模式识别
            r'.*智能排产.*|.*scheduling.*|.*algorithm.*': RequestType.SCHEDULING_CALCULATION,
            r'.*INSERT INTO.*batch.*|.*BULK INSERT.*': RequestType.BATCH_PROCESSING,
            r'.*SELECT.*FROM.*excel.*|.*LOAD DATA.*': RequestType.EXCEL_IMPORT_EXPORT,
            r'.*SELECT.*LIMIT [1-9][0-9]?$': RequestType.WEB_PAGE_REQUEST,  # 小量查询
            r'.*SELECT.*LIMIT [0-9]{3,}|.*COUNT\(\*\).*': RequestType.API_DATA_QUERY,  # 大量查询
            
            # 功能模式识别
            r'.*monitor.*|.*health.*|.*status.*': RequestType.MONITORING_DIAGNOSTIC,
            r'.*real.*time.*|.*live.*|.*current.*': RequestType.REAL_TIME_QUERY,
            r'.*background.*|.*task.*|.*job.*': RequestType.BACKGROUND_TASK,
        }
    
    def _initialize_routing_rules(self) -> Dict[RequestType, Dict]:
        """初始化路由规则"""
        from app.utils.unified_connection_manager import ConnectionPriority
        
        return {
            RequestType.SCHEDULING_CALCULATION: {
                'priority': ConnectionPriority.CRITICAL,
                'max_duration': 3600,
                'isolation_required': True,
                'resource_reservation': True
            },
            RequestType.WEB_PAGE_REQUEST: {
                'priority': ConnectionPriority.HIGH,
                'max_duration': 30,
                'isolation_required': False,
                'resource_reservation': False
            },
            RequestType.API_DATA_QUERY: {
                'priority': ConnectionPriority.HIGH,
                'max_duration': 120,
                'isolation_required': False,
                'resource_reservation': False
            },
            RequestType.EXCEL_IMPORT_EXPORT: {
                'priority': ConnectionPriority.NORMAL,
                'max_duration': 300,
                'isolation_required': True,
                'resource_reservation': True
            },
            RequestType.BATCH_PROCESSING: {
                'priority': ConnectionPriority.NORMAL,
                'max_duration': 600,
                'isolation_required': True,
                'resource_reservation': False
            },
            RequestType.MONITORING_DIAGNOSTIC: {
                'priority': ConnectionPriority.LOW,
                'max_duration': 60,
                'isolation_required': False,
                'resource_reservation': False
            },
            RequestType.REAL_TIME_QUERY: {
                'priority': ConnectionPriority.HIGH,
                'max_duration': 10,
                'isolation_required': False,
                'resource_reservation': False
            },
            RequestType.BACKGROUND_TASK: {
                'priority': ConnectionPriority.LOW,
                'max_duration': 1800,
                'isolation_required': False,
                'resource_reservation': False
            }
        }
    
    def analyze_request(self, context_hints: Dict[str, Any] = None) -> RequestContext:
        """分析请求类型和上下文"""
        if not context_hints:
            context_hints = {}
        
        # 从上下文提示中推断请求类型
        request_type = self._infer_request_type(context_hints)
        
        # 估算请求特征
        estimated_duration = self._estimate_duration(request_type, context_hints)
        priority_level = self._calculate_priority(request_type, context_hints)
        resource_intensive = self._is_resource_intensive(request_type, context_hints)
        user_facing = self._is_user_facing(request_type)
        
        return RequestContext(
            request_type=request_type,
            estimated_duration=estimated_duration,
            priority_level=priority_level,
            resource_intensive=resource_intensive,
            user_facing=user_facing,
            batch_size=context_hints.get('batch_size', 1)
        )
    
    def _infer_request_type(self, context_hints: Dict[str, Any]) -> RequestType:
        """推断请求类型"""
        # 检查显式类型提示
        if 'request_type' in context_hints:
            try:
                return RequestType(context_hints['request_type'])
            except ValueError:
                pass
        
        # 检查SQL查询模式
        sql_query = context_hints.get('sql_query', '').lower()
        if sql_query:
            for pattern, req_type in self._request_patterns.items():
                if re.search(pattern, sql_query, re.IGNORECASE):
                    return req_type
        
        # 检查函数名模式
        function_name = context_hints.get('function_name', '').lower()
        if function_name:
            if any(keyword in function_name for keyword in ['schedule', 'algorithm', 'calculate']):
                return RequestType.SCHEDULING_CALCULATION
            elif any(keyword in function_name for keyword in ['excel', 'import', 'export']):
                return RequestType.EXCEL_IMPORT_EXPORT
            elif any(keyword in function_name for keyword in ['monitor', 'health', 'diagnostic']):
                return RequestType.MONITORING_DIAGNOSTIC
            elif any(keyword in function_name for keyword in ['api', 'query', 'search']):
                return RequestType.API_DATA_QUERY
        
        # 检查端点路径
        endpoint = context_hints.get('endpoint', '').lower()
        if endpoint:
            if '/api/production/scheduling' in endpoint:
                return RequestType.SCHEDULING_CALCULATION
            elif '/api/v2/orders/excel' in endpoint:
                return RequestType.EXCEL_IMPORT_EXPORT
            elif '/api/' in endpoint:
                return RequestType.API_DATA_QUERY
            elif endpoint.startswith('/'):
                return RequestType.WEB_PAGE_REQUEST
        
        # 默认为API查询
        return RequestType.API_DATA_QUERY
    
    def _estimate_duration(self, request_type: RequestType, context_hints: Dict[str, Any]) -> float:
        """估算请求持续时间"""
        base_durations = {
            RequestType.SCHEDULING_CALCULATION: 300,  # 5分钟
            RequestType.WEB_PAGE_REQUEST: 5,          # 5秒
            RequestType.API_DATA_QUERY: 30,           # 30秒
            RequestType.EXCEL_IMPORT_EXPORT: 120,     # 2分钟
            RequestType.BATCH_PROCESSING: 300,        # 5分钟
            RequestType.MONITORING_DIAGNOSTIC: 10,   # 10秒
            RequestType.REAL_TIME_QUERY: 2,           # 2秒
            RequestType.BACKGROUND_TASK: 600          # 10分钟
        }
        
        base_duration = base_durations.get(request_type, 30)
        
        # 根据批量大小调整
        batch_size = context_hints.get('batch_size', 1)
        if batch_size > 1:
            duration_multiplier = min(batch_size / 10, 5)  # 最多5倍
            base_duration *= duration_multiplier
        
        # 根据数据量调整
        data_size = context_hints.get('estimated_rows', 0)
        if data_size > 1000:
            size_multiplier = min(data_size / 10000, 3)  # 最多3倍
            base_duration *= size_multiplier
        
        return base_duration
    
    def _calculate_priority(self, request_type: RequestType, context_hints: Dict[str, Any]) -> int:
        """计算优先级(1-10, 10最高)"""
        base_priorities = {
            RequestType.SCHEDULING_CALCULATION: 10,  # 最高优先级
            RequestType.WEB_PAGE_REQUEST: 8,         # 高优先级
            RequestType.REAL_TIME_QUERY: 8,          # 高优先级
            RequestType.API_DATA_QUERY: 6,           # 中优先级
            RequestType.EXCEL_IMPORT_EXPORT: 5,      # 中等优先级
            RequestType.BATCH_PROCESSING: 4,         # 较低优先级
            RequestType.BACKGROUND_TASK: 2,          # 低优先级
            RequestType.MONITORING_DIAGNOSTIC: 3     # 较低优先级
        }
        
        base_priority = base_priorities.get(request_type, 5)
        
        # 用户紧急度调整
        if context_hints.get('urgent', False):
            base_priority = min(base_priority + 2, 10)
        
        # 系统负载调整
        system_load = context_hints.get('system_load', 0.5)
        if system_load > 0.8:
            base_priority = max(base_priority - 1, 1)
        
        return base_priority
    
    def _is_resource_intensive(self, request_type: RequestType, context_hints: Dict[str, Any]) -> bool:
        """判断是否资源密集"""
        resource_intensive_types = {
            RequestType.SCHEDULING_CALCULATION,
            RequestType.EXCEL_IMPORT_EXPORT,
            RequestType.BATCH_PROCESSING
        }
        
        if request_type in resource_intensive_types:
            return True
        
        # 检查数据量
        data_size = context_hints.get('estimated_rows', 0)
        if data_size > 10000:
            return True
        
        # 检查预估持续时间
        estimated_duration = context_hints.get('estimated_duration', 0)
        if estimated_duration > 120:  # 超过2分钟
            return True
        
        return False
    
    def _is_user_facing(self, request_type: RequestType) -> bool:
        """判断是否面向用户"""
        user_facing_types = {
            RequestType.WEB_PAGE_REQUEST,
            RequestType.API_DATA_QUERY,
            RequestType.REAL_TIME_QUERY
        }
        
        return request_type in user_facing_types
    
    def route_connection(self, context: RequestContext) -> Tuple['ConnectionPriority', Dict[str, Any]]:
        """路由连接到最佳池"""
        from app.utils.unified_connection_manager import ConnectionPriority
        
        # 基础路由规则
        routing_rule = self._routing_rules[context.request_type]
        priority = routing_rule['priority']
        
        # 动态优化路由决策
        priority = self._optimize_routing_decision(priority, context)
        
        # 连接配置
        connection_config = {
            'timeout': routing_rule['max_duration'],
            'isolation_required': routing_rule['isolation_required'],
            'resource_reservation': routing_rule['resource_reservation'],
            'auto_commit': not context.resource_intensive  # 资源密集操作手动提交
        }
        
        # 更新统计
        with self._stats_lock:
            self._routing_stats['total_requests'] += 1
            route_key = f"{context.request_type.value}_{priority.name}"
            self._routing_stats['routing_decisions'][route_key] = \
                self._routing_stats['routing_decisions'].get(route_key, 0) + 1
        
        logger.debug(f"🧭 路由决策: {context.request_type.value} → {priority.name}")
        
        return priority, connection_config
    
    def _optimize_routing_decision(self, base_priority: 'ConnectionPriority', 
                                  context: RequestContext) -> 'ConnectionPriority':
        """优化路由决策"""
        from app.utils.unified_connection_manager import ConnectionPriority
        
        # 获取当前连接池负载
        try:
            from app.utils.unified_connection_manager import get_connection_status
            pool_status = get_connection_status()
            
            # 检查目标池的负载
            target_pool = pool_status['pools'].get(base_priority.name, {})
            if target_pool:
                pool_capacity = target_pool.get('pool_size', 0) + target_pool.get('max_overflow', 0)
                pool_used = target_pool.get('checked_out', 0) + target_pool.get('overflow', 0)
                usage_rate = (pool_used / pool_capacity) if pool_capacity > 0 else 0
                
                # 高负载时的降级策略
                if usage_rate > 0.8 and context.priority_level < 8:
                    # 非关键请求降级到低优先级池
                    if base_priority == ConnectionPriority.HIGH:
                        logger.info(f"🔄 HIGH池负载过高({usage_rate:.1%})，降级到NORMAL池")
                        return ConnectionPriority.NORMAL
                    elif base_priority == ConnectionPriority.NORMAL:
                        logger.info(f"🔄 NORMAL池负载过高({usage_rate:.1%})，降级到LOW池")
                        return ConnectionPriority.LOW
                
                # 低负载时的升级策略
                elif usage_rate < 0.3 and context.user_facing and context.priority_level >= 7:
                    if base_priority == ConnectionPriority.NORMAL:
                        logger.debug(f"⬆️ NORMAL池负载低({usage_rate:.1%})，升级到HIGH池")
                        return ConnectionPriority.HIGH
                    
        except Exception as e:
            logger.debug(f"路由优化失败: {e}")
        
        return base_priority
    
    @contextmanager
    def get_routed_connection(self, context_hints: Dict[str, Any] = None):
        """获取智能路由的连接"""
        from app.utils.unified_connection_manager import get_db_connection
        
        # 分析请求
        context = self.analyze_request(context_hints)
        
        # 路由决策
        priority, config = self.route_connection(context)
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            with get_db_connection(priority, config.get('auto_commit', True)) as session:
                yield session
            
            # 记录成功统计
            duration = time.time() - start_time
            self._record_performance_metric(context.request_type, priority, duration, True)
            
        except Exception as e:
            # 记录失败统计
            duration = time.time() - start_time
            self._record_performance_metric(context.request_type, priority, duration, False)
            logger.error(f"路由连接异常 ({context.request_type.value}): {e}")
            raise
    
    def _record_performance_metric(self, request_type: RequestType, priority: 'ConnectionPriority',
                                  duration: float, success: bool):
        """记录性能指标"""
        with self._stats_lock:
            metric_key = f"{request_type.value}_{priority.name}"
            if metric_key not in self._routing_stats['performance_metrics']:
                self._routing_stats['performance_metrics'][metric_key] = {
                    'count': 0,
                    'success_count': 0,
                    'total_duration': 0,
                    'max_duration': 0,
                    'min_duration': float('inf')
                }
            
            metrics = self._routing_stats['performance_metrics'][metric_key]
            metrics['count'] += 1
            if success:
                metrics['success_count'] += 1
            
            metrics['total_duration'] += duration
            metrics['max_duration'] = max(metrics['max_duration'], duration)
            metrics['min_duration'] = min(metrics['min_duration'], duration)
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计"""
        with self._stats_lock:
            stats = self._routing_stats.copy()
            
            # 计算性能指标
            for metric_key, metrics in stats['performance_metrics'].items():
                if metrics['count'] > 0:
                    metrics['average_duration'] = metrics['total_duration'] / metrics['count']
                    metrics['success_rate'] = metrics['success_count'] / metrics['count']
            
            return stats
    
    def print_routing_report(self):
        """打印路由报告"""
        stats = self.get_routing_statistics()
        
        print("\n🧭 智能连接路由统计报告")
        print("=" * 50)
        print(f"总路由请求: {stats['total_requests']}")
        
        if stats['routing_decisions']:
            print(f"\n📊 路由决策分布:")
            for route, count in stats['routing_decisions'].items():
                percentage = (count / stats['total_requests']) * 100
                print(f"   {route}: {count}次 ({percentage:.1f}%)")
        
        if stats['performance_metrics']:
            print(f"\n⚡ 性能指标:")
            for metric_key, metrics in stats['performance_metrics'].items():
                print(f"   {metric_key}:")
                print(f"      平均耗时: {metrics.get('average_duration', 0):.2f}s")
                print(f"      成功率: {metrics.get('success_rate', 0):.1%}")
                print(f"      请求次数: {metrics['count']}")

# 全局智能路由器实例
intelligent_router = IntelligentConnectionRouter()

# 便利函数
def get_smart_connection(context_hints: Dict[str, Any] = None):
    """获取智能路由连接的便利函数"""
    return intelligent_router.get_routed_connection(context_hints)

def analyze_request_type(context_hints: Dict[str, Any] = None) -> RequestContext:
    """分析请求类型的便利函数"""
    return intelligent_router.analyze_request(context_hints)

# 使用示例
"""
# 1. 基于上下文的智能路由
with get_smart_connection({'function_name': 'calculate_schedule'}) as session:
    result = session.execute(text("SELECT * FROM scheduling_tasks"))

# 2. 显式指定请求类型
with get_smart_connection({'request_type': 'excel_import_export', 'batch_size': 1000}) as session:
    # Excel处理操作
    pass

# 3. 基于SQL查询的自动识别
with get_smart_connection({'sql_query': 'INSERT INTO batch_data VALUES ...'}) as session:
    # 自动路由到NORMAL池进行批处理
    pass
"""