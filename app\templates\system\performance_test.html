{% extends "base.html" %}

{% block title %}前端性能测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tachometer-alt"></i>
                        前端性能测试和监控
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 性能指标显示 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 id="page-load-time">-</h4>
                                    <small>页面加载时间 (ms)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 id="memory-usage">-</h4>
                                    <small>内存使用 (MB)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 id="active-timers">-</h4>
                                    <small>活跃定时器</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 id="refresh-interval">-</h4>
                                    <small>刷新间隔 (秒)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能测试控制 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>智能刷新控制</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label>当前状态：</label>
                                        <span id="refresh-status" class="badge badge-success">活跃</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="testUserActivity()">
                                            模拟用户活动
                                        </button>
                                        <button type="button" class="btn btn-sm btn-secondary" onclick="testInactivity()">
                                            模拟用户不活跃
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" onclick="testVisibilityChange()">
                                            模拟页面隐藏
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>内存管理控制</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label>内存管理器状态：</label>
                                        <span id="memory-status" class="badge badge-success">正常</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-success" onclick="showMemoryStats()">
                                            查看内存统计
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" onclick="triggerCleanup()">
                                            触发清理
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="createMemoryLeak()">
                                            创建内存泄漏测试
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能日志 -->
                    <div class="card">
                        <div class="card-header">
                            <h6>性能日志</h6>
                            <button type="button" class="btn btn-sm btn-outline-secondary float-right" onclick="clearLog()">
                                清空日志
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="performance-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                                <!-- 日志内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 性能测试脚本
class PerformanceTest {
    constructor() {
        this.logContainer = document.getElementById('performance-log');
        this.startTime = performance.now();
        this.init();
    }
    
    init() {
        this.log('🚀 性能测试初始化...');
        this.updateMetrics();
        
        // 定期更新指标
        setInterval(() => {
            this.updateMetrics();
        }, 1000);
        
        // 监听性能事件
        this.setupPerformanceListeners();
    }
    
    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
        this.logContainer.appendChild(logEntry);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
    }
    
    updateMetrics() {
        // 页面加载时间
        const loadTime = performance.now() - this.startTime;
        document.getElementById('page-load-time').textContent = Math.round(loadTime);
        
        // 内存使用
        if (performance.memory) {
            const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            document.getElementById('memory-usage').textContent = memoryMB;
        }
        
        // 活跃定时器
        if (window.memoryManager) {
            const stats = window.memoryManager.getStats();
            document.getElementById('active-timers').textContent = stats.timers + stats.intervals;
        }
        
        // 刷新间隔
        if (window.smartRefresh) {
            const interval = window.smartRefresh.getOptimalInterval() / 1000;
            document.getElementById('refresh-interval').textContent = interval;
        }
    }
    
    setupPerformanceListeners() {
        // 监听智能刷新状态变化
        if (window.smartRefresh) {
            const originalUpdate = window.smartRefresh.updateRefreshInterval;
            window.smartRefresh.updateRefreshInterval = () => {
                originalUpdate.call(window.smartRefresh);
                this.log(`🔄 刷新间隔已更新: ${window.smartRefresh.getOptimalInterval()/1000}秒`);
                this.updateRefreshStatus();
            };
        }
        
        // 监听内存管理器事件
        if (window.memoryManager) {
            const originalCleanup = window.memoryManager.suggestCleanup;
            window.memoryManager.suggestCleanup = () => {
                originalCleanup.call(window.memoryManager);
                this.log('🧹 内存管理器建议清理');
            };
        }
    }
    
    updateRefreshStatus() {
        const statusElement = document.getElementById('refresh-status');
        if (window.smartRefresh) {
            const state = window.smartRefresh.state;
            if (!state.isOnline) {
                statusElement.textContent = '离线';
                statusElement.className = 'badge badge-danger';
            } else if (!state.isVisible) {
                statusElement.textContent = '后台';
                statusElement.className = 'badge badge-secondary';
            } else if (state.isActive) {
                statusElement.textContent = '活跃';
                statusElement.className = 'badge badge-success';
            } else {
                statusElement.textContent = '不活跃';
                statusElement.className = 'badge badge-warning';
            }
        }
    }
}

// 测试函数
function testUserActivity() {
    if (window.smartRefresh) {
        window.smartRefresh.recordActivity();
        performanceTest.log('👆 模拟用户活动');
    }
}

function testInactivity() {
    if (window.smartRefresh) {
        window.smartRefresh.state.lastActivity = Date.now() - 120000; // 2分钟前
        window.smartRefresh.state.isActive = false;
        window.smartRefresh.updateRefreshInterval();
        performanceTest.log('😴 模拟用户不活跃');
    }
}

function testVisibilityChange() {
    if (window.smartRefresh) {
        window.smartRefresh.state.isVisible = !window.smartRefresh.state.isVisible;
        window.smartRefresh.updateRefreshInterval();
        performanceTest.log(`👁️ 模拟页面${window.smartRefresh.state.isVisible ? '显示' : '隐藏'}`);
    }
}

function showMemoryStats() {
    if (window.memoryManager) {
        const stats = window.memoryManager.getStats();
        performanceTest.log(`📊 内存统计: 定时器${stats.timers}个, 间隔器${stats.intervals}个, 监听器${stats.listeners}个`);
        if (stats.memory) {
            performanceTest.log(`💾 内存使用: ${stats.memory.used}MB / ${stats.memory.total}MB (限制: ${stats.memory.limit}MB)`);
        }
    }
}

function triggerCleanup() {
    if (window.memoryManager) {
        window.memoryManager.suggestCleanup();
        performanceTest.log('🧹 手动触发内存清理');
    }
}

function createMemoryLeak() {
    // 创建一些测试用的内存泄漏
    const leakArray = [];
    for (let i = 0; i < 1000; i++) {
        leakArray.push(new Array(1000).fill('memory leak test'));
    }
    
    // 创建一些未清理的定时器
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            console.log('Leaked timer', i);
        }, 60000);
    }
    
    performanceTest.log('⚠️ 创建了内存泄漏测试 (1000个数组 + 10个定时器)');
}

function clearLog() {
    document.getElementById('performance-log').innerHTML = '';
}

// 初始化性能测试
let performanceTest;
document.addEventListener('DOMContentLoaded', function() {
    performanceTest = new PerformanceTest();
});
</script>
{% endblock %}
