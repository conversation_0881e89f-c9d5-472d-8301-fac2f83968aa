#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Task 2.3 并行排产计算引擎 - Phase 2 智能排产系统优化

核心功能：
1. 多核CPU并行计算设备匹配评分
2. 动态线程池管理和资源优化
3. 任务分片和负载均衡
4. 并行性能监控和调优
5. 故障恢复和容错处理

技术架构：
- ThreadPoolExecutor：线程级并行，适用于I/O密集型计算
- ProcessPoolExecutor：进程级并行，适用于CPU密集型计算  
- 混合模式：根据计算类型智能选择并行策略
- 缓存集成：与Task 2.2多级缓存系统深度集成
"""

import os
import time
import logging
import multiprocessing as mp
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed, Future
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict
from functools import partial
import threading
import queue

logger = logging.getLogger(__name__)

@dataclass
class ParallelTask:
    """并行任务定义"""
    task_id: str
    task_type: str  # 'equipment_scoring', 'lot_optimization', 'constraint_checking'
    data: Dict[str, Any]
    priority: int = 1  # 1最高，5最低
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ParallelStats:
    """并行计算统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    total_execution_time: float = 0.0
    average_task_time: float = 0.0
    cpu_utilization: float = 0.0
    memory_usage: float = 0.0
    thread_pool_utilization: float = 0.0
    process_pool_utilization: float = 0.0
    cache_hit_rate: float = 0.0
    
    def update_completion(self, execution_time: float, success: bool = True):
        """更新完成统计"""
        self.total_execution_time += execution_time
        if success:
            self.completed_tasks += 1
        else:
            self.failed_tasks += 1
        
        if self.completed_tasks > 0:
            self.average_task_time = self.total_execution_time / self.completed_tasks

class ParallelExecutionMode:
    """并行执行模式"""
    THREAD_ONLY = "thread_only"      # 仅线程并行
    PROCESS_ONLY = "process_only"    # 仅进程并行  
    HYBRID = "hybrid"                # 混合模式
    AUTO = "auto"                    # 自动选择

class ParallelSchedulingEngine:
    """并行排产计算引擎"""
    
    def __init__(self, 
                 max_thread_workers: Optional[int] = None,
                 max_process_workers: Optional[int] = None,
                 execution_mode: str = ParallelExecutionMode.AUTO,
                 multilevel_cache=None):
        """
        初始化并行排产引擎
        
        Args:
            max_thread_workers: 最大线程工作者数量
            max_process_workers: 最大进程工作者数量
            execution_mode: 执行模式
            multilevel_cache: 多级缓存管理器(Task 2.2集成)
        """
        # 计算最优工作者数量
        self.cpu_count = mp.cpu_count()
        self.max_thread_workers = max_thread_workers or min(self.cpu_count * 2, 16)
        self.max_process_workers = max_process_workers or min(self.cpu_count, 8)
        self.execution_mode = execution_mode
        
        # 集成Task 2.2多级缓存
        self.multilevel_cache = multilevel_cache
        
        # 线程池和进程池
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        self.process_pool: Optional[ProcessPoolExecutor] = None
        
        # 性能统计
        self.stats = ParallelStats()
        self._stats_lock = threading.Lock()
        
        # 任务队列和管理
        self._task_queue = queue.PriorityQueue()
        self._active_tasks: Dict[str, Future] = {}
        self._running = False
        self._worker_thread: Optional[threading.Thread] = None
        
        logger.info(f"🚀 并行排产引擎初始化完成")
        logger.info(f"   • CPU核心数: {self.cpu_count}")
        logger.info(f"   • 最大线程工作者: {self.max_thread_workers}")
        logger.info(f"   • 最大进程工作者: {self.max_process_workers}")
        logger.info(f"   • 执行模式: {self.execution_mode}")
        logger.info(f"   • 多级缓存集成: {'✅' if self.multilevel_cache else '❌'}")
    
    def start(self):
        """启动并行引擎"""
        if self._running:
            logger.warning("并行引擎已在运行中")
            return
        
        self._running = True
        
        # 创建线程池
        if self.execution_mode in [ParallelExecutionMode.THREAD_ONLY, 
                                  ParallelExecutionMode.HYBRID, 
                                  ParallelExecutionMode.AUTO]:
            self.thread_pool = ThreadPoolExecutor(
                max_workers=self.max_thread_workers,
                thread_name_prefix="ParallelScheduling"
            )
            logger.info(f"✅ 线程池启动: {self.max_thread_workers} workers")
        
        # 创建进程池
        if self.execution_mode in [ParallelExecutionMode.PROCESS_ONLY,
                                  ParallelExecutionMode.HYBRID,
                                  ParallelExecutionMode.AUTO]:
            self.process_pool = ProcessPoolExecutor(
                max_workers=self.max_process_workers
            )
            logger.info(f"✅ 进程池启动: {self.max_process_workers} workers")
        
        # 启动任务调度线程
        self._worker_thread = threading.Thread(target=self._task_worker, daemon=True)
        self._worker_thread.start()
        logger.info("✅ 任务调度线程启动")
    
    def stop(self):
        """停止并行引擎"""
        if not self._running:
            return
        
        self._running = False
        logger.info("🛑 正在停止并行引擎...")
        
        # 等待任务队列清空
        while not self._task_queue.empty():
            time.sleep(0.1)
        
        # 关闭线程池
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
            self.thread_pool = None
            logger.info("✅ 线程池已关闭")
        
        # 关闭进程池
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
            self.process_pool = None
            logger.info("✅ 进程池已关闭")
        
        # 等待工作线程结束
        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=5.0)
        
        logger.info("✅ 并行引擎已停止")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
    
    def parallel_equipment_scoring(self, lots: List[Dict], equipment: List[Dict], 
                                 algorithm_type: str = 'heuristic') -> List[Dict]:
        """
        并行计算设备匹配评分 - Task 2.3核心功能
        
        Args:
            lots: 待排产批次列表
            equipment: 可用设备列表
            algorithm_type: 算法类型，影响并行策略选择
            
        Returns:
            List[Dict]: 设备匹配评分结果
        """
        start_time = time.time()
        logger.info(f"🚀 开始并行设备匹配评分")
        logger.info(f"   • 批次数量: {len(lots)}")
        logger.info(f"   • 设备数量: {len(equipment)}")
        logger.info(f"   • 算法类型: {algorithm_type}")
        
        # 检查并启动引擎
        if not self._running:
            self.start()
        
        try:
            # 根据算法类型选择并行策略
            execution_strategy = self._select_execution_strategy(
                task_type='equipment_scoring',
                data_size=len(lots) * len(equipment),
                algorithm_type=algorithm_type
            )
            
            logger.info(f"   • 并行策略: {execution_strategy}")
            
            # 任务分片策略
            chunk_size = self._calculate_optimal_chunk_size(len(lots), algorithm_type)
            lot_chunks = [lots[i:i + chunk_size] for i in range(0, len(lots), chunk_size)]
            
            logger.info(f"   • 分片数量: {len(lot_chunks)}")
            logger.info(f"   • 分片大小: {chunk_size}")
            
            # 选择执行器
            executor = self._get_executor(execution_strategy)
            if not executor:
                logger.error("❌ 无可用执行器，回退到串行计算")
                return self._serial_equipment_scoring(lots, equipment, algorithm_type)
            
            # 并行执行设备评分
            future_to_chunk = {}
            
            for i, lot_chunk in enumerate(lot_chunks):
                # 集成Task 2.2缓存优化 - 预载设备数据
                cached_equipment = self._get_cached_equipment_data(equipment)
                
                # 提交并行任务
                task_func = partial(
                    self._score_lot_chunk_equipment,
                    lot_chunk=lot_chunk,
                    equipment=cached_equipment,
                    algorithm_type=algorithm_type,
                    chunk_id=i
                )
                
                future = executor.submit(task_func)
                future_to_chunk[future] = i
            
            # 收集结果
            all_results = []
            completed_chunks = 0
            
            for future in as_completed(future_to_chunk):
                chunk_id = future_to_chunk[future]
                
                try:
                    chunk_results = future.result()
                    all_results.extend(chunk_results)
                    completed_chunks += 1
                    
                    # 更新统计
                    with self._stats_lock:
                        self.stats.update_completion(
                            execution_time=(time.time() - start_time) / len(lot_chunks),
                            success=True
                        )
                    
                    logger.debug(f"   • 分片{chunk_id}完成: {len(chunk_results)}个评分结果")
                    
                except Exception as e:
                    logger.error(f"   • 分片{chunk_id}失败: {e}")
                    with self._stats_lock:
                        self.stats.update_completion(0, success=False)
            
            total_time = time.time() - start_time
            
            logger.info(f"✅ 并行设备匹配评分完成")
            logger.info(f"   • 总耗时: {total_time:.3f}s")
            logger.info(f"   • 完成分片: {completed_chunks}/{len(lot_chunks)}")
            logger.info(f"   • 评分结果: {len(all_results)}个")
            logger.info(f"   • 平均性能: {len(all_results)/total_time:.1f} scores/sec")
            
            return all_results
            
        except Exception as e:
            logger.error(f"❌ 并行设备评分失败: {e}")
            # 回退到串行计算
            return self._serial_equipment_scoring(lots, equipment, algorithm_type)
    
    def parallel_lot_optimization(self, lots: List[Dict], constraints: Dict[str, Any],
                                algorithm_type: str = 'heuristic') -> List[Dict]:
        """
        并行批次优化计算
        
        Args:
            lots: 批次列表
            constraints: 约束条件
            algorithm_type: 算法类型
            
        Returns:
            List[Dict]: 优化后的批次列表
        """
        start_time = time.time()
        logger.info(f"🚀 开始并行批次优化计算")
        logger.info(f"   • 批次数量: {len(lots)}")
        logger.info(f"   • 约束数量: {len(constraints)}")
        
        if not self._running:
            self.start()
        
        try:
            # 选择执行策略
            execution_strategy = self._select_execution_strategy(
                task_type='lot_optimization',
                data_size=len(lots),
                algorithm_type=algorithm_type
            )
            
            # 获取执行器
            executor = self._get_executor(execution_strategy)
            if not executor:
                return self._serial_lot_optimization(lots, constraints, algorithm_type)
            
            # 任务分片
            chunk_size = max(1, len(lots) // self.max_thread_workers)
            lot_chunks = [lots[i:i + chunk_size] for i in range(0, len(lots), chunk_size)]
            
            # 并行执行
            future_to_chunk = {}
            for i, lot_chunk in enumerate(lot_chunks):
                task_func = partial(
                    self._optimize_lot_chunk,
                    lot_chunk=lot_chunk,
                    constraints=constraints,
                    algorithm_type=algorithm_type,
                    chunk_id=i
                )
                
                future = executor.submit(task_func)
                future_to_chunk[future] = i
            
            # 收集结果
            optimized_lots = []
            for future in as_completed(future_to_chunk):
                try:
                    chunk_results = future.result()
                    optimized_lots.extend(chunk_results)
                except Exception as e:
                    logger.error(f"批次优化分片失败: {e}")
            
            total_time = time.time() - start_time
            logger.info(f"✅ 并行批次优化完成: {total_time:.3f}s, {len(optimized_lots)}个批次")
            
            return optimized_lots
            
        except Exception as e:
            logger.error(f"❌ 并行批次优化失败: {e}")
            return self._serial_lot_optimization(lots, constraints, algorithm_type)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._stats_lock:
            stats_dict = asdict(self.stats)
        
        # 添加实时系统信息
        stats_dict.update({
            'cpu_count': self.cpu_count,
            'max_thread_workers': self.max_thread_workers,
            'max_process_workers': self.max_process_workers,
            'execution_mode': self.execution_mode,
            'engine_running': self._running,
            'active_tasks': len(self._active_tasks),
            'queued_tasks': self._task_queue.qsize(),
            'thread_pool_active': self.thread_pool is not None,
            'process_pool_active': self.process_pool is not None,
            'cache_integration': self.multilevel_cache is not None
        })
        
        return stats_dict
    
    def _select_execution_strategy(self, task_type: str, data_size: int, 
                                 algorithm_type: str) -> str:
        """
        智能选择执行策略
        
        Args:
            task_type: 任务类型
            data_size: 数据规模
            algorithm_type: 算法类型
            
        Returns:
            str: 执行策略
        """
        if self.execution_mode != ParallelExecutionMode.AUTO:
            return self.execution_mode
        
        # 基于任务特性的智能选择
        if task_type == 'equipment_scoring':
            # 设备评分主要是计算密集型
            if data_size > 10000:  # 大规模计算用进程
                return ParallelExecutionMode.PROCESS_ONLY
            elif data_size > 1000:  # 中等规模用混合
                return ParallelExecutionMode.HYBRID
            else:  # 小规模用线程
                return ParallelExecutionMode.THREAD_ONLY
        
        elif task_type == 'lot_optimization':
            # 批次优化主要是I/O和内存操作
            return ParallelExecutionMode.THREAD_ONLY
        
        # 默认线程模式
        return ParallelExecutionMode.THREAD_ONLY
    
    def _get_executor(self, strategy: str) -> Optional[Union[ThreadPoolExecutor, ProcessPoolExecutor]]:
        """获取对应策略的执行器"""
        if strategy == ParallelExecutionMode.THREAD_ONLY:
            return self.thread_pool
        elif strategy == ParallelExecutionMode.PROCESS_ONLY:
            return self.process_pool
        elif strategy == ParallelExecutionMode.HYBRID:
            # 混合模式优先使用线程池
            return self.thread_pool or self.process_pool
        return self.thread_pool
    
    def _calculate_optimal_chunk_size(self, total_items: int, algorithm_type: str) -> int:
        """计算最优分片大小"""
        if algorithm_type == 'heuristic':
            # 启发式算法计算简单，可以大分片
            base_chunk_size = max(1, total_items // (self.max_thread_workers * 2))
        elif algorithm_type in ['ortools', 'full_ortools']:
            # OR-Tools算法计算复杂，需要小分片
            base_chunk_size = max(1, total_items // (self.max_thread_workers * 4))
        else:
            # 默认中等分片
            base_chunk_size = max(1, total_items // self.max_thread_workers)
        
        # 限制分片大小范围
        return max(1, min(base_chunk_size, 100))
    
    def _get_cached_equipment_data(self, equipment: List[Dict]) -> List[Dict]:
        """获取缓存的设备数据 - Task 2.2集成"""
        if not self.multilevel_cache:
            return equipment
        
        try:
            from app.services.multilevel_cache_manager import DataType
            cache_key = f"equipment_data_{len(equipment)}"
            
            # 尝试从缓存获取
            cached_data = self.multilevel_cache.get(cache_key, DataType.BUSINESS_DATA)
            if cached_data is not None:
                logger.debug(f"   • 从缓存获取设备数据: {len(cached_data)}台设备")
                return cached_data
            
            # 缓存未命中，存储到缓存
            self.multilevel_cache.set(cache_key, equipment, DataType.BUSINESS_DATA, ttl=300)
            logger.debug(f"   • 设备数据已缓存: {len(equipment)}台设备")
            
            return equipment
            
        except Exception as e:
            logger.warning(f"缓存操作失败: {e}")
            return equipment
    
    def _score_lot_chunk_equipment(self, lot_chunk: List[Dict], equipment: List[Dict],
                                 algorithm_type: str, chunk_id: int) -> List[Dict]:
        """
        为批次分片计算设备匹配评分
        
        Args:
            lot_chunk: 批次分片
            equipment: 设备列表
            algorithm_type: 算法类型
            chunk_id: 分片ID
            
        Returns:
            List[Dict]: 评分结果
        """
        try:
            logger.debug(f"   • 分片{chunk_id}开始: {len(lot_chunk)}个批次")
            
            chunk_results = []
            
            for lot in lot_chunk:
                for eqp in equipment:
                    # 计算设备匹配评分
                    score_result = self._calculate_equipment_score(lot, eqp, algorithm_type)
                    if score_result:
                        chunk_results.append(score_result)
            
            logger.debug(f"   • 分片{chunk_id}完成: {len(chunk_results)}个评分")
            return chunk_results
            
        except Exception as e:
            logger.error(f"分片{chunk_id}处理失败: {e}")
            return []
    
    def _calculate_equipment_score(self, lot: Dict, equipment: Dict, 
                                 algorithm_type: str) -> Optional[Dict]:
        """
        计算单个批次-设备匹配评分
        
        Args:
            lot: 批次信息
            equipment: 设备信息
            algorithm_type: 算法类型
            
        Returns:
            Optional[Dict]: 评分结果
        """
        try:
            # 基础匹配检查
            if not self._is_compatible(lot, equipment):
                return None
            
            # 根据算法类型选择评分策略
            if algorithm_type == 'heuristic':
                score = self._heuristic_scoring(lot, equipment)
            elif algorithm_type in ['ortools', 'full_ortools']:
                score = self._ortools_scoring(lot, equipment)
            else:
                score = self._default_scoring(lot, equipment)
            
            return {
                'lot_id': lot.get('LOT_ID', ''),
                'equipment_id': equipment.get('EQP_ID', ''),
                'equipment_name': equipment.get('EQP_NAME', ''),
                'compatibility_score': score.get('compatibility', 0),
                'load_balance_score': score.get('load_balance', 0),
                'priority_score': score.get('priority', 0),
                'total_score': score.get('total', 0),
                'algorithm_type': algorithm_type,
                'calculated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算评分失败 - 批次{lot.get('LOT_ID')}, 设备{equipment.get('EQP_ID')}: {e}")
            return None
    
    def _is_compatible(self, lot: Dict, equipment: Dict) -> bool:
        """检查批次和设备兼容性"""
        # 简化的兼容性检查
        lot_device = str(lot.get('DEVICE', '')).strip()
        eqp_device = str(equipment.get('EQP_NAME', '')).strip()
        
        if not lot_device or not eqp_device:
            return False
        
        # 设备名称匹配或包含关系
        return (lot_device.lower() in eqp_device.lower() or 
                eqp_device.lower() in lot_device.lower())
    
    def _heuristic_scoring(self, lot: Dict, equipment: Dict) -> Dict[str, float]:
        """启发式评分算法"""
        compatibility = 85.0 if self._is_compatible(lot, equipment) else 0.0
        load_balance = 75.0  # 简化的负载均衡评分
        priority = float(lot.get('PRIORITY', 50))
        
        total = (compatibility * 0.5 + load_balance * 0.3 + priority * 0.2)
        
        return {
            'compatibility': compatibility,
            'load_balance': load_balance,
            'priority': priority,
            'total': total
        }
    
    def _ortools_scoring(self, lot: Dict, equipment: Dict) -> Dict[str, float]:
        """OR-Tools评分算法"""
        compatibility = 90.0 if self._is_compatible(lot, equipment) else 0.0
        load_balance = 80.0  # 更精确的负载均衡
        priority = float(lot.get('PRIORITY', 50))
        
        # OR-Tools通常有更复杂的评分逻辑
        total = (compatibility * 0.6 + load_balance * 0.25 + priority * 0.15)
        
        return {
            'compatibility': compatibility,
            'load_balance': load_balance,
            'priority': priority,
            'total': total
        }
    
    def _default_scoring(self, lot: Dict, equipment: Dict) -> Dict[str, float]:
        """默认评分算法"""
        compatibility = 80.0 if self._is_compatible(lot, equipment) else 0.0
        load_balance = 70.0
        priority = float(lot.get('PRIORITY', 50))
        
        total = (compatibility + load_balance + priority) / 3
        
        return {
            'compatibility': compatibility,
            'load_balance': load_balance,
            'priority': priority,
            'total': total
        }
    
    def _optimize_lot_chunk(self, lot_chunk: List[Dict], constraints: Dict[str, Any],
                          algorithm_type: str, chunk_id: int) -> List[Dict]:
        """优化批次分片"""
        try:
            logger.debug(f"优化分片{chunk_id}: {len(lot_chunk)}个批次")
            
            # 简化的批次优化逻辑
            optimized_chunk = []
            for lot in lot_chunk:
                optimized_lot = lot.copy()
                # 添加优化标记
                optimized_lot['optimized'] = True
                optimized_lot['optimization_algorithm'] = algorithm_type
                optimized_lot['optimization_time'] = datetime.now().isoformat()
                optimized_chunk.append(optimized_lot)
            
            return optimized_chunk
            
        except Exception as e:
            logger.error(f"优化分片{chunk_id}失败: {e}")
            return lot_chunk
    
    def _serial_equipment_scoring(self, lots: List[Dict], equipment: List[Dict],
                                algorithm_type: str) -> List[Dict]:
        """串行设备评分 - 回退方案"""
        logger.info("🔄 回退到串行设备评分")
        
        results = []
        for lot in lots:
            for eqp in equipment:
                score_result = self._calculate_equipment_score(lot, eqp, algorithm_type)
                if score_result:
                    results.append(score_result)
        
        logger.info(f"✅ 串行评分完成: {len(results)}个结果")
        return results
    
    def _serial_lot_optimization(self, lots: List[Dict], constraints: Dict[str, Any],
                               algorithm_type: str) -> List[Dict]:
        """串行批次优化 - 回退方案"""
        logger.info("🔄 回退到串行批次优化")
        return self._optimize_lot_chunk(lots, constraints, algorithm_type, 0)
    
    def _task_worker(self):
        """任务调度工作线程"""
        logger.info("🚀 任务调度线程启动")
        
        while self._running:
            try:
                # 从队列获取任务（阻塞1秒）
                priority, task = self._task_queue.get(timeout=1.0)
                
                if task is None:  # 停止信号
                    break
                
                # 执行任务
                self._execute_task(task)
                
                # 标记任务完成
                self._task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"任务调度异常: {e}")
        
        logger.info("✅ 任务调度线程结束")
    
    def _execute_task(self, task: ParallelTask):
        """执行单个任务"""
        try:
            task.started_at = datetime.now()
            logger.debug(f"执行任务: {task.task_id} ({task.task_type})")
            
            # 根据任务类型执行相应逻辑
            if task.task_type == 'equipment_scoring':
                # 设备评分任务
                pass
            elif task.task_type == 'lot_optimization':
                # 批次优化任务
                pass
            
            task.completed_at = datetime.now()
            logger.debug(f"任务完成: {task.task_id}")
            
        except Exception as e:
            logger.error(f"任务执行失败 {task.task_id}: {e}")
    
    def __del__(self):
        """析构函数 - 确保资源清理"""
        try:
            self.stop()
        except:
            pass