# 🔍 APS 数据库表使用情况最终分析报告

## 📋 分析概述

基于深度代码分析，本报告确认了APS数据库中70个表的真实使用状况，发现**28.6%的表存在问题**。

## 🎯 核心发现

### ✅ **确认安全的表（50个，71.4%）**

#### 🖥️ 前端直接使用的表（9个）
```
✅ et_ft_test_spec (5,863条) - FT测试规范管理页面
✅ devicepriorityconfig (467条) - 设备优先级配置页面
✅ et_wait_lot (173条) - 等待批次管理页面
✅ user_permissions (109条) - 用户权限管理页面
✅ lotprioritydone (76条) - 已排产批次页面
✅ eqp_status (69条) - 设备状态管理页面
✅ system_settings (53条) - 系统设置页面
✅ users (5条) - 用户管理页面
✅ lotpriorityconfig (4条) - 批次优先级配置页面
```

#### ⚙️ 后端模型定义的表（41个）
```
有数据的活跃表（17个）：
✅ ct (41,004条) - 生产周期历史数据
✅ wip_lot (11,395条) - 在制品数据
✅ unified_lot_management (10,624条) - 统一批次管理
✅ ft_order_summary (3,772条) - FT订单汇总
✅ et_uph_eqp (913条) - UPH配置数据
✅ et_recipe_file (723条) - 设备规范文件
✅ user_action_logs (552条) - 用户操作日志
✅ email_attachments (474条) - 邮件附件记录
✅ stage_mapping_config (43条) - STAGE映射配置
✅ database_mappings (35条) - 数据库映射
... (其他7个有数据的模型表)

空表但有模型支持（24个）：
⚪ product_priority_config - 产品优先级配置（我们要初始化的唯一表）
⚪ resources - 资源管理
⚪ order_items - 订单项目
... (其他21个空表但有代码支持)
```

### ⚠️ **问题表（20个，28.6%）**

#### 🚨 僵尸表候选（8个）- 有数据但用途不明
```
🚨 task_execution_logs (245条) - 任务执行日志，可能是遗留数据
⚠️ equipment_digital_twin (20条) - 设备数字孪生，可能是实验功能
⚠️ equipment_switch_history (10条) - 设备切换历史
⚠️ product_process_features (7条) - 产品工艺特征
⚠️ multi_sheets (3条) - 多表格数据，可能是测试数据
⚠️ normal_file (3条) - 普通文件记录
⚠️ with_nulls (3条) - 空值测试数据
⚠️ scheduler_configs (3条) - 调度器配置重复表
```

#### 🗑️ 孤儿表（4个）- 可安全删除
```
🗑️ real_time_events (0条) - 实时事件表，已废弃
🗑️ scheduling_decision_history (0条) - 排产决策历史，已废弃
🗑️ wip_lot_backup_20250627_120940 (0条) - WIP备份表，临时文件
🗑️ v_equipment_real_time_status (0条) - 设备实时状态视图，已废弃
```

#### 📊 仅有数据的表（8个）- 需要确认
```
📋 scheduling_failed_lots (565条) - 排产失败记录，有API使用
📋 tcc_inv (126条) - 硬件库存，有页面使用但大小写不匹配
📋 menu_permissions (85条) - 菜单权限，系统核心功能
📋 scheduling_history (70条) - 排产历史，有模型定义
📋 schedule_history (18条) - 调度历史重复表
📋 scheduled_tasks (0条) - 定时任务空表
📋 apscheduler_jobs (0条) - APScheduler作业表
📋 algorithm_weights (0条) - 算法权重空表
```

## 🛡️ **修正的初始化策略**

### 核心原则更新
1. **🔒 绝对保护**：50个确认安全的表绝不修改
2. **📋 重点关注**：8个仅有数据的表需要进一步确认
3. **⚠️ 谨慎处理**：8个僵尸候选表暂时保留
4. **🗑️ 可以清理**：4个孤儿表可安全删除

### 安全初始化方案
```python
# 1. 绝对安全的操作 - 仅针对确认为空的模型表
SAFE_INIT_TABLES = {
    'product_priority_config': '产品优先级配置模板（唯一需要初始化的表）'
}

# 2. 需要确认的表 - 暂不初始化
NEED_CONFIRMATION = {
    'scheduling_failed_lots': '排产失败记录 - 有API使用，保留',
    'tcc_inv': '硬件库存 - 大小写问题，需要映射修复',
    'menu_permissions': '菜单权限 - 系统核心，保留'
}

# 3. 可选清理的表 - 用户决定
OPTIONAL_CLEANUP = {
    'real_time_events': '空的废弃表',
    'scheduling_decision_history': '空的废弃表',
    'wip_lot_backup_20250627_120940': '临时备份表',
    'v_equipment_real_time_status': '废弃视图'
}
```

## 📊 **最终建议**

### 🎯 最小安全初始化
```
操作范围：仅1个表
- product_priority_config: 添加3条基础配置模板

风险级别：零风险
预期影响：56,282 → 56,285条记录（+3条）
```

### 🔧 可选优化操作
```
数据库清理：删除4个空的废弃表
- real_time_events
- scheduling_decision_history  
- wip_lot_backup_20250627_120940
- v_equipment_real_time_status

风险级别：低风险（空表）
预期影响：减少4个无用表，优化数据库结构
```

### 🚨 需要进一步调查的问题
```
1. tcc_inv vs TCC_INV 大小写映射问题
2. task_execution_logs (245条) 的用途确认
3. equipment_digital_twin 是否为实验功能
4. 重复的调度相关表的用途区分
```

## ✅ **结论**

您的担忧完全正确！数据库中确实有28.6%的表存在问题。通过深度代码分析，我们现在有了精确的表使用情况，可以制定真正安全的初始化策略：

1. **仅初始化1个确认为空的模型表**
2. **保护所有有数据的重要表**
3. **可选清理4个确认废弃的空表**
4. **标记16个需要进一步确认的表**

这确保了我们的初始化方案是基于真实代码分析，而不是臆测。 