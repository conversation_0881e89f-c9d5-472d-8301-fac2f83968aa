#!/usr/bin/env python3
"""
检查DEVICE匹配情况的脚本
"""
import mysql.connector

def check_device_matching():
    """检查DEVICE匹配情况"""
    try:
        # 直接连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("🔍 检查批次与测试规范的DEVICE匹配情况...")
        
        # 获取批次中的DEVICE列表
        cursor.execute("""
            SELECT DISTINCT DEVICE, COUNT(*) as lot_count
            FROM et_wait_lot 
            GROUP BY DEVICE 
            ORDER BY lot_count DESC
        """)
        lot_devices = cursor.fetchall()
        print(f"\n📋 批次中的DEVICE列表 ({len(lot_devices)} 种):")
        for device, count in lot_devices[:10]:
            print(f"  - {device}: {count} 个批次")
        
        # 获取测试规范中的DEVICE列表
        cursor.execute("""
            SELECT DISTINCT DEVICE, COUNT(*) as spec_count
            FROM et_ft_test_spec 
            WHERE APPROVAL_STATE = 'Released'
            GROUP BY DEVICE 
            ORDER BY spec_count DESC
        """)
        spec_devices = cursor.fetchall()
        print(f"\n📋 测试规范中的DEVICE列表 ({len(spec_devices)} 种):")
        for device, count in spec_devices[:10]:
            print(f"  - {device}: {count} 条规范")
        
        # 检查匹配情况
        print(f"\n🔍 检查DEVICE匹配情况...")
        
        # 创建集合用于快速查找
        lot_device_set = {device for device, _ in lot_devices}
        spec_device_set = {device for device, _ in spec_devices}
        
        # 找到匹配的DEVICE
        matched_devices = lot_device_set & spec_device_set
        print(f"✅ 匹配的DEVICE数量: {len(matched_devices)}")
        if matched_devices:
            print("📋 匹配的DEVICE:")
            for device in list(matched_devices)[:10]:
                print(f"  - {device}")
        
        # 找到批次中有但测试规范中没有的DEVICE
        lot_only_devices = lot_device_set - spec_device_set
        print(f"\n❌ 批次中有但测试规范中没有的DEVICE数量: {len(lot_only_devices)}")
        if lot_only_devices:
            print("📋 批次独有的DEVICE:")
            for device in list(lot_only_devices)[:10]:
                print(f"  - {device}")
        
        # 找到测试规范中有但批次中没有的DEVICE
        spec_only_devices = spec_device_set - lot_device_set
        print(f"\n📊 测试规范中有但批次中没有的DEVICE数量: {len(spec_only_devices)}")
        if spec_only_devices:
            print("📋 测试规范独有的DEVICE:")
            for device in list(spec_only_devices)[:10]:
                print(f"  - {device}")
        
        # 检查具体的匹配统计
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT l.DEVICE) as lot_devices,
                COUNT(DISTINCT s.DEVICE) as spec_devices,
                COUNT(DISTINCT CASE WHEN s.DEVICE IS NOT NULL THEN l.DEVICE END) as matched_devices,
                COUNT(DISTINCT l.LOT_ID) as total_lots,
                COUNT(DISTINCT CASE WHEN s.DEVICE IS NOT NULL THEN l.LOT_ID END) as matched_lots
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE AND s.APPROVAL_STATE = 'Released'
        """)
        stats = cursor.fetchone()
        
        print(f"\n📊 匹配统计:")
        print(f"  - 批次DEVICE总数: {stats[0]}")
        print(f"  - 测试规范DEVICE总数: {stats[1]}")
        print(f"  - 匹配的DEVICE数: {stats[2]}")
        print(f"  - 批次总数: {stats[3]}")
        print(f"  - 有匹配测试规范的批次数: {stats[4]}")
        print(f"  - 批次匹配率: {stats[4]/stats[3]*100:.1f}%")
        
        # 检查具体的不匹配批次
        cursor.execute("""
            SELECT l.DEVICE, l.STAGE, COUNT(*) as lot_count
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE AND s.APPROVAL_STATE = 'Released'
            WHERE s.DEVICE IS NULL
            GROUP BY l.DEVICE, l.STAGE
            ORDER BY lot_count DESC
            LIMIT 10
        """)
        unmatched_lots = cursor.fetchall()
        
        if unmatched_lots:
            print(f"\n❌ 无匹配测试规范的批次（前10）:")
            for device, stage, count in unmatched_lots:
                print(f"  - {device}/{stage}: {count} 个批次")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_device_matching()
