# APS 车规芯片终测智能调度平台 - 环境变量配置模板
# 复制此文件为 .env 并填写实际配置值

# =================================================================
# 数据库配置 (MySQL)
# =================================================================
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password_here
DB_NAME=aps
DB_CHARSET=utf8mb4

# =================================================================
# Redis配置
# =================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# =================================================================
# Flask应用配置
# =================================================================
FLASK_HOST=127.0.0.1
FLASK_PORT=5000
FLASK_ENV=development
SECRET_KEY=your_secret_key_here_please_generate_random_32_chars

# =================================================================
# 系统配置
# =================================================================
TIMEZONE=Asia/Shanghai
DEFAULT_PAGE_SIZE=1000
DEFAULT_UPH=1000
MAX_WORKERS=10

# =================================================================
# 文件路径配置
# =================================================================
EXCEL_BASE_PATH=Excellist2025.06.05
SQLITE_DB_PATH=instance/aps.db
LOG_DIR=logs
UPLOAD_DIR=uploads
DOWNLOAD_DIR=downloads

# =================================================================
# 管理员配置
# =================================================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password_here

# =================================================================
# 业务常量配置
# =================================================================
LARGE_BATCH_THRESHOLD=10000
MEDIUM_BATCH_THRESHOLD=1000
BASE_VALUE_RATE=10000.0
MAX_PRIORITY_SCORE=100.0
MAX_PROCESSING_TIME_HOURS=24

# =================================================================
# 调试和开发配置
# =================================================================
DEBUG=True
TESTING=False
LOG_LEVEL=INFO
QUIET_STARTUP=0

# =================================================================
# 安全配置
# =================================================================
CORS_ENABLED=True
CSRF_ENABLED=True
SESSION_TIMEOUT=3600

# =================================================================
# 性能配置
# =================================================================
CACHE_ENABLED=True
CACHE_TIMEOUT=300
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=30

# =================================================================
# 配置说明:
# 1. 请将 your_mysql_password_here 替换为实际的MySQL密码
# 2. 请将 your_secret_key_here_please_generate_random_32_chars 替换为32位随机字符串
# 3. 请将 your_admin_password_here 替换为管理员密码
# 4. 生产环境请设置 FLASK_ENV=production 和 DEBUG=False
# 5. 所有路径配置支持相对路径和绝对路径
# =================================================================