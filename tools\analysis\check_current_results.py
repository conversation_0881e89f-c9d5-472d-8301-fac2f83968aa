#!/usr/bin/env python3
"""
检查当前排产结果的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.db_connection_pool import get_connection

def check_current_results():
    """检查当前排产结果"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # 检查成功排产的批次
        cursor.execute("""
            SELECT COUNT(*) as total_success, SESSION_ID
            FROM lotprioritydone 
            WHERE SESSION_ID LIKE 'session_20250115_%'
            GROUP BY SESSION_ID
            ORDER BY SESSION_ID DESC
            LIMIT 1
        """)
        success_result = cursor.fetchone()

        if success_result:
            print(f"✅ 成功排产批次: {success_result[0]} 个")
            print(f"📋 会话ID: {success_result[1]}")
            session_id = success_result[1]
        else:
            print("❌ 未找到成功排产的批次")
            return
        
        # 检查失败批次
        cursor.execute("""
            SELECT COUNT(*) as total_failed
            FROM scheduling_failed_lots 
            WHERE session_id = %s
        """, (session_id,))
        failed_result = cursor.fetchone()

        if failed_result:
            print(f"❌ 失败批次: {failed_result[0]} 个")
        
        # 检查A类问题的具体批次
        cursor.execute("""
            SELECT l.LOT_ID, l.DEVICE, l.STAGE, 
                   lpd.HANDLER_ID, lpd.CHANGEOVER_TIME, lpd.MATCH_TYPE
            FROM lotprioritydone lpd
            JOIN et_wait_lot l ON l.LOT_ID = lpd.LOT_ID
            WHERE lpd.SESSION_ID = %s
            AND EXISTS (
                SELECT 1 FROM et_recipe_file r 
                WHERE r.DEVICE = l.DEVICE AND UPPER(r.STAGE) = UPPER(l.STAGE)
                AND EXISTS (
                    SELECT 1 FROM eqp_status e 
                    WHERE e.HANDLER_ID = lpd.HANDLER_ID
                    AND r.HANDLER_CONFIG = e.HANDLER_CONFIG 
                    AND r.KIT_PN = e.KIT_PN
                    AND EXISTS (
                        SELECT 1 FROM et_ft_test_spec s 
                        WHERE s.DEVICE = l.DEVICE AND UPPER(s.STAGE) = UPPER(l.STAGE)
                        AND s.TESTER = e.TESTER
                        AND UPPER(e.STAGE) = UPPER(l.STAGE)
                    )
                )
            )
            LIMIT 5
        """, (session_id,))
        
        a_problems = cursor.fetchall()
        if a_problems:
            print(f"\n🔍 A类问题样本 ({len(a_problems)} 个):")
            for problem in a_problems:
                print(f"  - {problem[0]}: {problem[1]}/{problem[2]} -> {problem[3]} ({problem[5]}, {problem[4]}分钟)")
        
        # 检查J类问题的具体批次
        cursor.execute("""
            SELECT sfl.lot_id, sfl.device, sfl.stage, sfl.failure_reason
            FROM scheduling_failed_lots sfl
            WHERE sfl.session_id = %s
            AND EXISTS (
                SELECT 1 FROM eqp_status e 
                WHERE e.DEVICE = sfl.device AND UPPER(e.STAGE) = UPPER(sfl.stage)
            )
            LIMIT 5
        """, (session_id,))
        
        j_problems = cursor.fetchall()
        if j_problems:
            print(f"\n🔍 J类问题样本 ({len(j_problems)} 个):")
            for problem in j_problems:
                print(f"  - {problem[0]}: {problem[1]}/{problem[2]} - {problem[3]}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_current_results()
