/**
 * 智能刷新管理器 - 解决频繁轮询导致的性能问题
 */
class SmartRefreshManager {
    constructor() {
        this.intervals = {
            active: 30000,      // 用户活跃时30秒
            inactive: 120000,   // 用户不活跃时2分钟  
            background: 300000, // 页面在后台时5分钟
            offline: 600000     // 离线时10分钟
        };
        
        this.state = {
            isActive: true,
            isVisible: true,
            isOnline: navigator.onLine,
            lastActivity: Date.now()
        };
        
        this.refreshCallbacks = new Map();
        this.currentTimer = null;
        
        this.init();
    }
    
    init() {
        this.setupActivityListeners();
        this.setupVisibilityListener();
        this.setupOnlineListener();
        this.startRefreshCycle();
        
        console.log('🚀 智能刷新管理器已启动');
    }
    
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.recordActivity();
            }, { passive: true });
        });
        
        // 检查用户不活跃状态
        setInterval(() => {
            const inactiveTime = Date.now() - this.state.lastActivity;
            this.state.isActive = inactiveTime < 60000; // 1分钟内有活动
        }, 10000);
    }
    
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            this.state.isVisible = !document.hidden;
            this.updateRefreshInterval();
            
            if (this.state.isVisible) {
                // 页面重新可见时立即刷新一次
                this.executeRefresh();
            }
        });
    }
    
    setupOnlineListener() {
        window.addEventListener('online', () => {
            this.state.isOnline = true;
            this.updateRefreshInterval();
            this.executeRefresh(); // 重新上线时立即刷新
        });
        
        window.addEventListener('offline', () => {
            this.state.isOnline = false;
            this.updateRefreshInterval();
        });
    }
    
    recordActivity() {
        this.state.lastActivity = Date.now();
        if (!this.state.isActive) {
            this.state.isActive = true;
            this.updateRefreshInterval();
        }
    }
    
    updateRefreshInterval() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
        }
        
        let interval = this.getOptimalInterval();
        
        this.currentTimer = setInterval(() => {
            this.executeRefresh();
        }, interval);
        
        console.log(`🔄 刷新间隔已更新: ${interval/1000}秒`);
    }
    
    getOptimalInterval() {
        if (!this.state.isOnline) {
            return this.intervals.offline;
        }
        
        if (!this.state.isVisible) {
            return this.intervals.background;
        }
        
        return this.state.isActive ? this.intervals.active : this.intervals.inactive;
    }
    
    registerRefreshCallback(name, callback) {
        this.refreshCallbacks.set(name, callback);
        console.log(`📝 注册刷新回调: ${name}`);
    }
    
    unregisterRefreshCallback(name) {
        this.refreshCallbacks.delete(name);
        console.log(`🗑️ 移除刷新回调: ${name}`);
    }
    
    async executeRefresh() {
        if (!this.state.isOnline) {
            console.log('📡 离线状态，跳过刷新');
            return;
        }
        
        console.log('🔄 执行智能刷新...');
        
        for (const [name, callback] of this.refreshCallbacks) {
            try {
                await callback();
            } catch (error) {
                console.error(`❌ 刷新回调失败 ${name}:`, error);
            }
        }
    }
    
    startRefreshCycle() {
        this.updateRefreshInterval();
    }
    
    destroy() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
        }
        this.refreshCallbacks.clear();
        console.log('🛑 智能刷新管理器已销毁');
    }
}

// 全局智能刷新管理器
window.smartRefresh = new SmartRefreshManager();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.smartRefresh) {
        window.smartRefresh.destroy();
    }
});

// 替换原有的定时刷新
if (typeof loadStats === 'function') {
    window.smartRefresh.registerRefreshCallback('dashboard_stats', loadStats);
}

if (typeof updateCharts === 'function') {
    window.smartRefresh.registerRefreshCallback('dashboard_charts', updateCharts);
}
