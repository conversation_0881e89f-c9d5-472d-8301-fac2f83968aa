/* APS生产系统统一表格样式 */
/* 版本: 1.0 */
/* 适用于: semi_auto.html, done_lots.html, failed_lots.html */

:root {
    /* 表格字体规范 */
    --aps-table-header-font-size: 0.75rem;
    --aps-table-cell-font-size: 0.8rem;
    --aps-table-line-height: 1.3;
    
    /* 表格间距规范 */
    --aps-table-cell-padding: 0.4rem 0.5rem;
    --aps-table-compact-padding: 0.3rem 0.4rem;
    --aps-table-header-padding: 0.5rem 0.5rem;
    
    /* 状态颜色系统 */
    --aps-priority-high: #dc3545;
    --aps-priority-medium: #fd7e14;
    --aps-priority-low: #6c757d;
    
    --aps-status-success: #28a745;
    --aps-status-warning: #ffc107;
    --aps-status-danger: #dc3545;
    --aps-status-info: #17a2b8;
    
    --aps-score-excellent: #28a745;
    --aps-score-good: #20c997;
    --aps-score-fair: #fd7e14;
    --aps-score-poor: #dc3545;
    
    /* 表格背景色 */
    --aps-table-header-bg: #f8f9fa;
    --aps-table-hover-bg: rgba(183, 36, 36, 0.05);
    --aps-table-stripe-bg: rgba(0, 0, 0, 0.025);
}

/* 统一表格基础类 */
.aps-table {
    font-size: var(--aps-table-cell-font-size);
    line-height: var(--aps-table-line-height);
    border-collapse: collapse;
    width: 100%;
}

.aps-table th {
    background-color: var(--aps-table-header-bg);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: var(--aps-table-header-font-size);
    padding: var(--aps-table-header-padding);
    text-align: left;
    vertical-align: middle;
    position: sticky;
    top: 0;
    z-index: 10;
}

.aps-table td {
    padding: var(--aps-table-cell-padding);
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
    font-size: var(--aps-table-cell-font-size);
    line-height: var(--aps-table-line-height);
}

/* 紧凑模式 */
.aps-table.compact th,
.aps-table.compact td {
    padding: var(--aps-table-compact-padding);
    font-size: calc(var(--aps-table-cell-font-size) - 0.05rem);
}

/* 悬停效果 */
.aps-table.table-hover tbody tr:hover {
    background-color: var(--aps-table-hover-bg);
}

/* 条纹效果 */
.aps-table.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--aps-table-stripe-bg);
}

/* 优先级样式系统 */
.aps-priority-high {
    color: var(--aps-priority-high);
    font-weight: bold;
}

.aps-priority-medium {
    color: var(--aps-priority-medium);
    font-weight: bold;
}

.aps-priority-low {
    color: var(--aps-priority-low);
    font-weight: normal;
}

/* 状态标签系统 */
.aps-status-success {
    background-color: var(--aps-status-success);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aps-status-warning {
    background-color: var(--aps-status-warning);
    color: #212529;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aps-status-danger {
    background-color: var(--aps-status-danger);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aps-status-info {
    background-color: var(--aps-status-info);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 评分系统 */
.aps-score-excellent {
    background-color: var(--aps-score-excellent);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.65rem;
    font-weight: bold;
    display: inline-block;
    min-width: 2.5rem;
    text-align: center;
}

.aps-score-good {
    background-color: var(--aps-score-good);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.65rem;
    font-weight: bold;
    display: inline-block;
    min-width: 2.5rem;
    text-align: center;
}

.aps-score-fair {
    background-color: var(--aps-score-fair);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.65rem;
    font-weight: bold;
    display: inline-block;
    min-width: 2.5rem;
    text-align: center;
}

.aps-score-poor {
    background-color: var(--aps-score-poor);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.65rem;
    font-weight: bold;
    display: inline-block;
    min-width: 2.5rem;
    text-align: center;
}

/* 特殊列样式 */
.aps-lot-id-column {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-weight: bold;
    color: var(--aps-primary);
    font-size: 0.75rem;
}

.aps-quantity-column {
    text-align: right;
    font-weight: bold;
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
}

.aps-time-column {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-size: 0.7rem;
    color: #495057;
}

.aps-datetime-column {
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
}

/* 设备信息样式 */
.aps-handler-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.25rem;
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    color: #1565c0;
    font-weight: 500;
    display: inline-block;
}

/* 响应式表格容器 */
.aps-table-responsive {
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.aps-table-responsive::-webkit-scrollbar {
    height: 6px;
    width: 6px;
}

.aps-table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.aps-table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.aps-table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 排序功能样式 */
.aps-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.aps-table th.sortable:hover {
    background-color: #e9ecef;
}

.aps-table th.sortable .sort-icon {
    margin-left: 0.25rem;
    font-size: 0.6rem;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.aps-table th.sortable.sorted-asc .sort-icon,
.aps-table th.sortable.sorted-desc .sort-icon {
    opacity: 1;
    color: var(--aps-primary);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .aps-table {
        font-size: 0.7rem;
    }
    
    .aps-table th {
        font-size: 0.65rem;
        padding: 0.3rem 0.4rem;
    }
    
    .aps-table td {
        padding: 0.3rem 0.4rem;
    }
    
    .aps-status-success,
    .aps-status-warning,
    .aps-status-danger,
    .aps-status-info {
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }
    
    .aps-score-excellent,
    .aps-score-good,
    .aps-score-fair,
    .aps-score-poor {
        font-size: 0.55rem;
        padding: 0.1rem 0.25rem;
        min-width: 2rem;
    }
}

/* 加载状态 */
.aps-table-loading {
    position: relative;
    overflow: hidden;
}

.aps-table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(183, 36, 36, 0.1),
        transparent
    );
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 打印样式 */
@media print {
    .aps-table {
        font-size: 8pt;
        line-height: 1.2;
    }
    
    .aps-table th,
    .aps-table td {
        padding: 2pt 4pt;
        border: 1pt solid #000;
    }
    
    .aps-status-success,
    .aps-status-warning,
    .aps-status-danger,
    .aps-status-info,
    .aps-score-excellent,
    .aps-score-good,
    .aps-score-fair,
    .aps-score-poor {
        background: transparent !important;
        color: #000 !important;
        border: 1pt solid #000;
    }
} 