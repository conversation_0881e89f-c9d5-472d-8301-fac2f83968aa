/* APS系统主题色彩定义 */
:root {
    --aps-primary: #b72424;
    --aps-primary-dark: #a01e1e;
    --aps-primary-light: #d73027;
    --aps-primary-lighter: #f8e6e6;
    --aps-secondary: #6c757d;
    --aps-success: #28a745;
    --aps-info: #17a2b8;
    --aps-warning: #ffc107;
    --aps-danger: #dc3545;
    
    /* 表格主题化扩展 */
    --aps-table-hover: rgba(183, 36, 36, 0.05);
    --aps-table-border: #dee2e6;
    --aps-table-header-bg: #f8f9fa;
    --aps-table-cell-padding: 0.4rem 0.5rem;
}

/* 分页组件主题化 */
.pagination .page-link {
    color: var(--aps-primary);
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: var(--aps-primary-dark);
    background-color: var(--aps-primary-lighter);
    border-color: var(--aps-primary-light);
}

.pagination .page-link:focus {
    color: var(--aps-primary-dark);
    background-color: var(--aps-primary-lighter);
    border-color: var(--aps-primary);
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
}

.pagination .page-item.active .page-link {
    color: #fff;
    background-color: var(--aps-primary);
    border-color: var(--aps-primary);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* 按钮主题化 - 统一所有按钮为主题红色，使用!important确保优先级 */
.btn-primary {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

.btn-primary:hover {
    background-color: var(--aps-primary-dark) !important;
    border-color: var(--aps-primary-dark) !important;
    color: white !important;
}

.btn-primary:focus, .btn-primary.focus {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.5) !important;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active {
    background-color: var(--aps-primary-dark) !important;
    border-color: var(--aps-primary-dark) !important;
    color: white !important;
}

/* 信息按钮主题化 - 改为主题红色，使用!important确保优先级 */
.btn-info {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

.btn-info:hover {
    background-color: var(--aps-primary-dark) !important;
    border-color: var(--aps-primary-dark) !important;
    color: white !important;
}

.btn-info:focus, .btn-info.focus {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.5) !important;
}

.btn-info:not(:disabled):not(.disabled):active,
.btn-info:not(:disabled):not(.disabled).active {
    background-color: var(--aps-primary-dark) !important;
    border-color: var(--aps-primary-dark) !important;
    color: white !important;
}

/* 次要按钮保持灰色，但增强样式一致性 */
.btn-secondary {
    background-color: var(--aps-secondary) !important;
    border-color: var(--aps-secondary) !important;
    color: white !important;
}

.btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    color: white !important;
}

.btn-secondary:focus, .btn-secondary.focus {
    background-color: var(--aps-secondary) !important;
    border-color: var(--aps-secondary) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;
}

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    color: white !important;
}

/* 轮廓按钮主题化 - 统一为主题红色 */
.btn-outline-primary {
    color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

.btn-outline-primary:focus, .btn-outline-primary.focus {
    background-color: transparent !important;
    border-color: var(--aps-primary) !important;
    color: var(--aps-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.5) !important;
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

/* 轮廓信息按钮主题化 - 改为主题红色 */
.btn-outline-info {
    color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    background-color: transparent !important;
}

.btn-outline-info:hover {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

.btn-outline-info:focus, .btn-outline-info.focus {
    background-color: transparent !important;
    border-color: var(--aps-primary) !important;
    color: var(--aps-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.5) !important;
}

.btn-outline-info:not(:disabled):not(.disabled):active,
.btn-outline-info:not(:disabled):not(.disabled).active {
    background-color: var(--aps-primary) !important;
    border-color: var(--aps-primary) !important;
    color: white !important;
}

/* 轮廓次要按钮主题化 - 保持灰色但边框使用主题色 */
.btn-outline-secondary {
    color: var(--aps-secondary) !important;
    border-color: var(--aps-secondary) !important;
    background-color: transparent !important;
}

.btn-outline-secondary:hover {
    background-color: var(--aps-secondary) !important;
    border-color: var(--aps-secondary) !important;
    color: white !important;
}

.btn-outline-secondary:focus, .btn-outline-secondary.focus {
    background-color: transparent !important;
    border-color: var(--aps-secondary) !important;
    color: var(--aps-secondary) !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active {
    background-color: var(--aps-secondary) !important;
    border-color: var(--aps-secondary) !important;
    color: white !important;
}

/* 链接主题化 */
a {
    color: var(--aps-primary);
}

a:hover {
    color: var(--aps-primary-dark);
}

/* 表格边框主题化 */
.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* 导航栏主题化 */
.navbar-brand {
    color: var(--aps-primary) !important;
}

.nav-link.active {
    color: var(--aps-primary) !important;
}

/* 成功按钮保持绿色但增强一致性 */
.btn-success {
    background-color: var(--aps-success) !important;
    border-color: var(--aps-success) !important;
    color: white !important;
}

/* 文本颜色主题化 - 确保所有text-primary都使用主题色 */
.text-primary {
    color: var(--aps-primary) !important;
}

.text-info {
    color: var(--aps-info) !important;
}

/* 背景颜色主题化 */
.bg-primary {
    background-color: var(--aps-primary) !important;
}

.bg-info {
    background-color: var(--aps-info) !important;
}

/* 边框颜色主题化 */
.border-primary {
    border-color: var(--aps-primary) !important;
}

.border-info {
    border-color: var(--aps-info) !important;
}

/* 警告框主题化 */
.alert-primary {
    background-color: rgba(183, 36, 36, 0.1) !important;
    border-color: var(--aps-primary) !important;
    color: #721c24 !important;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: var(--aps-info) !important;
    color: #0c5460 !important;
}

/* 徽章主题化 */
.badge.bg-primary {
    background-color: var(--aps-primary) !important;
}

.badge.bg-info {
    background-color: var(--aps-info) !important;
}

/* 进度条主题化 */
.progress-bar {
    background-color: var(--aps-primary) !important;
}

.progress-bar-primary {
    background-color: var(--aps-primary) !important;
}

.progress-bar-info {
    background-color: var(--aps-info) !important;
}

.btn-success:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: white !important;
}

.btn-success:focus, .btn-success.focus {
    background-color: var(--aps-success) !important;
    border-color: var(--aps-success) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5) !important;
}

.btn-success:not(:disabled):not(.disabled):active,
.btn-success:not(:disabled):not(.disabled).active {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: white !important;
}

/* 危险按钮保持红色但增强一致性 */
.btn-danger {
    background-color: var(--aps-danger) !important;
    border-color: var(--aps-danger) !important;
    color: white !important;
}

.btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

.btn-danger:focus, .btn-danger.focus {
    background-color: var(--aps-danger) !important;
    border-color: var(--aps-danger) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5) !important;
}

.btn-danger:not(:disabled):not(.disabled):active,
.btn-danger:not(:disabled):not(.disabled).active {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

/* 警告按钮保持黄色但增强一致性 */
.btn-warning {
    background-color: var(--aps-warning) !important;
    border-color: var(--aps-warning) !important;
    color: #212529 !important;
}

.btn-warning:hover {
    background-color: #e0a800 !important;
    border-color: #d39e00 !important;
    color: #212529 !important;
}

.btn-warning:focus, .btn-warning.focus {
    background-color: var(--aps-warning) !important;
    border-color: var(--aps-warning) !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5) !important;
}

.btn-warning:not(:disabled):not(.disabled):active,
.btn-warning:not(:disabled):not(.disabled).active {
    background-color: #e0a800 !important;
    border-color: #d39e00 !important;
    color: #212529 !important;
}

/* 统一表格样式系统兼容性 */
.aps-table {
    --table-accent-bg: var(--aps-table-hover);
    --table-border-color: var(--aps-table-border);
}

.aps-table-responsive {
    --scrollbar-color: var(--aps-primary);
}