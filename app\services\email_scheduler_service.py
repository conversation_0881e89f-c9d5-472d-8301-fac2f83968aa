#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱定时任务管理服务
专门管理邮箱配置的定时任务，独立于主调度器
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.jobstores.memory import MemoryJobStore
from flask import current_app

from app.models import EmailConfig
from app.utils.task_executors import email_attachment_task
from app import db

logger = logging.getLogger(__name__)

class EmailSchedulerService:
    """邮箱定时任务管理服务"""
    
    def __init__(self):
        self.scheduler = None
        self._running = False
        self.app = None  # 保存应用实例
        self._init_scheduler()
    
    def _init_scheduler(self):
        """初始化独立的邮箱任务调度器"""
        try:
            # 使用内存存储，避免与主调度器冲突
            jobstores = {
                'default': MemoryJobStore()
            }
            
            executors = {
                'default': ThreadPoolExecutor(max_workers=3)
            }
            
            job_defaults = {
                'coalesce': True,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5分钟容错时间
            }
            
            self.scheduler = BackgroundScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )
            
            logger.info("✅ 邮箱定时任务调度器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 邮箱定时任务调度器初始化失败: {e}")
            raise
    
    def start(self):
        """启动调度器"""
        if not self.scheduler:
            self._init_scheduler()
        
        if not self._running:
            try:
                # 保存当前应用实例
                from flask import current_app
                if current_app:
                    self.app = current_app._get_current_object()
                
                self.scheduler.start()
                self._running = True
                logger.info("✅ 邮箱定时任务调度器启动成功")
                
                # 加载现有的启用配置
                self._load_enabled_configs()
                
            except Exception as e:
                logger.error(f"❌ 邮箱定时任务调度器启动失败: {e}")
                raise
    
    def stop(self):
        """停止调度器"""
        if self.scheduler and self._running:
            try:
                self.scheduler.shutdown()
                self._running = False
                logger.info("🔄 邮箱定时任务调度器已停止")
            except Exception as e:
                logger.error(f"❌ 停止邮箱定时任务调度器失败: {e}")
    
    def add_email_config_task(self, config: EmailConfig) -> Dict[str, Any]:
        """为邮箱配置添加定时任务"""
        try:
            if not self._running:
                self.start()
            
            job_id = f"email_config_{config.id}"
            
            # 移除已存在的任务
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # 根据检查间隔创建触发器
            check_interval = config.check_interval or 60  # 默认60分钟
            
            if check_interval >= 60:
                # 大于等于60分钟，使用小时间隔
                hours = check_interval // 60
                trigger = IntervalTrigger(hours=hours)
            else:
                # 小于60分钟，使用分钟间隔
                trigger = IntervalTrigger(minutes=check_interval)
            
            # 添加任务
            job = self.scheduler.add_job(
                func=self._execute_email_task,
                trigger=trigger,
                args=[config.id],
                id=job_id,
                name=f"邮箱配置任务-{config.name}",
                replace_existing=True
            )
            
            logger.info(f"✅ 邮箱配置任务添加成功: {config.name} (间隔: {check_interval}分钟)")
            
            return {
                'success': True,
                'job_id': job_id,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'message': f'定时任务已启动，间隔: {check_interval}分钟'
            }
            
        except Exception as e:
            logger.error(f"❌ 添加邮箱配置任务失败: {config.name}, 错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '添加定时任务失败'
            }
    
    def remove_email_config_task(self, config_id: int) -> Dict[str, Any]:
        """移除邮箱配置的定时任务"""
        try:
            job_id = f"email_config_{config_id}"
            
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"✅ 邮箱配置任务移除成功: {config_id}")
                return {
                    'success': True,
                    'message': '定时任务已移除'
                }
            else:
                return {
                    'success': True,
                    'message': '任务不存在或已移除'
                }
                
        except Exception as e:
            logger.error(f"❌ 移除邮箱配置任务失败: {config_id}, 错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '移除定时任务失败'
            }
    
    def get_task_status(self, config_id: int) -> Dict[str, Any]:
        """获取邮箱配置任务状态"""
        try:
            job_id = f"email_config_{config_id}"
            job = self.scheduler.get_job(job_id)
            
            if job:
                return {
                    'exists': True,
                    'job_id': job_id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                }
            else:
                return {
                    'exists': False,
                    'message': '任务不存在'
                }
                
        except Exception as e:
            logger.error(f"❌ 获取任务状态失败: {config_id}, 错误: {e}")
            return {
                'exists': False,
                'error': str(e)
            }
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有邮箱任务状态"""
        try:
            tasks = []
            for job in self.scheduler.get_jobs():
                if job.id.startswith('email_config_'):
                    config_id = int(job.id.replace('email_config_', ''))
                    tasks.append({
                        'config_id': config_id,
                        'job_id': job.id,
                        'name': job.name,
                        'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                        'trigger': str(job.trigger)
                    })
            return tasks
            
        except Exception as e:
            logger.error(f"❌ 获取所有任务失败: {e}")
            return []
    
    def _load_enabled_configs(self):
        """加载所有启用的邮箱配置"""
        try:
            # 使用保存的应用实例
            if not self.app:
                logger.error("❌ 应用实例未初始化，无法加载邮箱配置")
                return
            
            with self.app.app_context():
                enabled_configs = EmailConfig.query.filter_by(enabled=True).all()
                
                for config in enabled_configs:
                    self.add_email_config_task(config)
                
                logger.info(f"✅ 加载了 {len(enabled_configs)} 个启用的邮箱配置")
                
        except Exception as e:
            logger.error(f"❌ 加载启用配置失败: {e}")
    
    def _execute_email_task(self, config_id: int):
        """执行邮箱任务"""
        try:
            logger.info(f"🚀 开始执行邮箱任务: config_id={config_id}")
            
            # 使用保存的应用实例创建应用上下文
            if not self.app:
                logger.error(f"❌ 应用实例未初始化，无法执行邮箱任务: config_id={config_id}")
                return
            
            with self.app.app_context():
                # 检查配置是否仍然启用
                config = EmailConfig.query.get(config_id)
                if not config or not config.enabled:
                    logger.info(f"⚠️ 邮箱配置已禁用或不存在: {config_id}")
                    # 注意：在应用上下文内调用移除任务
                    self.remove_email_config_task(config_id)
                    return
                
                # 执行邮件附件获取任务
                result = email_attachment_task(
                    check_interval_hours=1,
                    config_id=config_id
                )
                
                if result.get('status') == 'success':
                    logger.info(f"✅ 邮箱任务执行成功: {config.name}, 处理附件: {result.get('processed_attachments', 0)}")
                else:
                    logger.error(f"❌ 邮箱任务执行失败: {config.name}, 错误: {result.get('error', 'Unknown')}")
                
        except Exception as e:
            logger.error(f"❌ 执行邮箱任务异常: config_id={config_id}, 错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self._running and self.scheduler and self.scheduler.running

# 全局实例
_email_scheduler_service = None

def get_email_scheduler_service() -> EmailSchedulerService:
    """获取邮箱调度器服务实例"""
    global _email_scheduler_service
    if _email_scheduler_service is None:
        _email_scheduler_service = EmailSchedulerService()
    return _email_scheduler_service

def init_email_scheduler_service():
    """初始化邮箱调度器服务"""
    try:
        from flask import current_app
        service = get_email_scheduler_service()
        
        # 确保应用实例被正确设置
        if current_app and not service.app:
            service.app = current_app._get_current_object()
        
        service.start()
        logger.info("✅ 邮箱调度器服务初始化成功")
        return service
    except Exception as e:
        logger.error(f"❌ 邮箱调度器服务初始化失败: {e}")
        raise 