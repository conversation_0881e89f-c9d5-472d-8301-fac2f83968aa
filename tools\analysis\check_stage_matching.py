#!/usr/bin/env python3
"""
检查STAGE匹配情况的脚本
"""
import mysql.connector

def check_stage_matching():
    """检查STAGE匹配情况"""
    try:
        # 直接连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("🔍 检查批次与测试规范的DEVICE+STAGE匹配情况...")
        
        # 从排产日志中提取的具体批次信息
        test_cases = [
            ('JWQ5103CSFQFNAT_TR0', 'ROOM-TTR'),
            ('JWQ7101SOTB-J115_TR1', 'ROOM-TTR'),
            ('JWQ5276QFNA-J127_TR1', 'Hot'),
            ('JWQ5276QFNA-J096_TR1', 'Trim'),
            ('JW3655E', 'ROOM-TTR'),
            ('JWH5087AQFNAG-M001_TR1', 'UIS'),
            ('JWQ7806-3.3SOTA', 'Hot')
        ]
        
        for device, stage in test_cases:
            print(f"\n🔍 检查 {device}/{stage}:")
            
            # 检查批次是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM et_wait_lot 
                WHERE DEVICE = %s AND STAGE = %s
            """, (device, stage))
            lot_count = cursor.fetchone()[0]
            print(f"  📦 批次数量: {lot_count}")
            
            # 检查精确匹配的测试规范
            cursor.execute("""
                SELECT COUNT(*) FROM et_ft_test_spec 
                WHERE DEVICE = %s AND STAGE = %s AND APPROVAL_STATE = 'Released'
            """, (device, stage))
            exact_spec_count = cursor.fetchone()[0]
            print(f"  📋 精确匹配测试规范: {exact_spec_count}")
            
            # 检查DEVICE匹配但STAGE不同的测试规范
            cursor.execute("""
                SELECT STAGE, COUNT(*) as cnt FROM et_ft_test_spec 
                WHERE DEVICE = %s AND APPROVAL_STATE = 'Released'
                GROUP BY STAGE
                ORDER BY cnt DESC
            """, (device,))
            device_specs = cursor.fetchall()
            if device_specs:
                print(f"  📋 该DEVICE的所有STAGE:")
                for spec_stage, count in device_specs:
                    marker = "✅" if spec_stage == stage else "❌"
                    print(f"    {marker} {spec_stage}: {count} 条规范")
            else:
                print(f"  ❌ 该DEVICE没有任何测试规范")
        
        # 检查整体的DEVICE+STAGE匹配统计
        print(f"\n📊 整体DEVICE+STAGE匹配统计:")
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT CONCAT(l.DEVICE, '|', l.STAGE)) as lot_combinations,
                COUNT(DISTINCT CONCAT(s.DEVICE, '|', s.STAGE)) as spec_combinations,
                COUNT(DISTINCT CASE WHEN s.DEVICE IS NOT NULL THEN CONCAT(l.DEVICE, '|', l.STAGE) END) as matched_combinations
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE 
                AND UPPER(l.STAGE) = UPPER(s.STAGE) 
                AND s.APPROVAL_STATE = 'Released'
        """)
        stats = cursor.fetchone()
        print(f"  - 批次DEVICE+STAGE组合数: {stats[0]}")
        print(f"  - 测试规范DEVICE+STAGE组合数: {stats[1]}")
        print(f"  - 匹配的组合数: {stats[2]}")
        print(f"  - 组合匹配率: {stats[2]/stats[0]*100:.1f}%")
        
        # 检查不匹配的DEVICE+STAGE组合
        cursor.execute("""
            SELECT l.DEVICE, l.STAGE, COUNT(*) as lot_count
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE 
                AND UPPER(l.STAGE) = UPPER(s.STAGE) 
                AND s.APPROVAL_STATE = 'Released'
            WHERE s.DEVICE IS NULL
            GROUP BY l.DEVICE, l.STAGE
            ORDER BY lot_count DESC
            LIMIT 15
        """)
        unmatched = cursor.fetchall()
        
        if unmatched:
            print(f"\n❌ 无匹配测试规范的DEVICE+STAGE组合（前15）:")
            for device, stage, count in unmatched:
                print(f"  - {device}/{stage}: {count} 个批次")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_stage_matching()
