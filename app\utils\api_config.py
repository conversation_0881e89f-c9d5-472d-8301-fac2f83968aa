#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置工具类
提供统一的API配置管理和路径生成功能
"""

from config.aps_config import config
from typing import Optional, Dict, Any

class APIConfig:
    """API配置管理类"""
    
    def __init__(self):
        self._config = config
    
    # ==============================================
    # 基础配置属性
    # ==============================================
    
    @property
    def prefix(self) -> str:
        """API前缀路径"""
        return self._config.API_PREFIX
    
    @property
    def version(self) -> str:
        """API版本"""
        return self._config.API_VERSION
    
    @property
    def host(self) -> str:
        """API主机地址"""
        return self._config.API_HOST
    
    @property
    def port(self) -> int:
        """API端口"""
        return self._config.API_PORT
    
    @property
    def base_url(self) -> str:
        """API基础URL"""
        return self._config.API_BASE_URL
    
    @property
    def v2_prefix(self) -> str:
        """API v2前缀路径"""
        return self._config.API_V2_PREFIX
    
    @property
    def v1_prefix(self) -> str:
        """API v1前缀路径（向后兼容）"""
        return self._config.API_V1_PREFIX
    
    # ==============================================
    # 路径生成方法
    # ==============================================
    
    def get_route(self, endpoint: str, version: Optional[str] = None) -> str:
        """
        生成API路由路径
        
        Args:
            endpoint: 端点路径 (如 'orders', 'auth/login')
            version: API版本 (如 'v1', 'v2')，默认使用当前版本
            
        Returns:
            完整的API路由路径
        """
        if version is None:
            version = self.version
        
        # 确保endpoint不以/开头
        if endpoint.startswith('/'):
            endpoint = endpoint[1:]
        
        return f"{self.prefix}/{version}/{endpoint}"
    
    def get_blueprint_url_prefix(self, version: Optional[str] = None) -> str:
        """
        获取蓝图URL前缀
        
        Args:
            version: API版本，默认使用当前版本
            
        Returns:
            蓝图URL前缀
        """
        if version is None:
            version = self.version
        
        return f"{self.prefix}/{version}"
    
    def get_full_url(self, endpoint: str, version: Optional[str] = None) -> str:
        """
        生成完整的API URL
        
        Args:
            endpoint: 端点路径
            version: API版本，默认使用当前版本
            
        Returns:
            完整的API URL
        """
        route = self.get_route(endpoint, version)
        protocol = "https" if self._config.FLASK_ENV == "production" else "http"
        return f"{protocol}://{self.host}:{self.port}{route}"
    
    # ==============================================
    # 配置检查方法
    # ==============================================
    
    def is_cors_enabled(self) -> bool:
        """检查是否启用CORS"""
        return self._config.API_CORS_ENABLED
    
    def is_auth_enabled(self) -> bool:
        """检查是否启用API认证"""
        return self._config.API_AUTH_ENABLED
    
    def is_rate_limit_enabled(self) -> bool:
        """检查是否启用API限流"""
        return self._config.API_RATE_LIMIT_ENABLED
    
    def is_docs_enabled(self) -> bool:
        """检查是否启用API文档"""
        return self._config.API_DOCS_ENABLED
    
    # ==============================================
    # 配置获取方法
    # ==============================================
    
    def get_cors_config(self) -> Dict[str, Any]:
        """获取CORS配置"""
        return {
            'enabled': self._config.API_CORS_ENABLED,
            'origins': self._config.API_CORS_ORIGINS,
            'methods': self._config.API_CORS_METHODS
        }
    
    def get_auth_config(self) -> Dict[str, Any]:
        """获取认证配置"""
        return {
            'enabled': self._config.API_AUTH_ENABLED,
            'token_expire_hours': self._config.API_TOKEN_EXPIRE_HOURS,
            'refresh_token_expire_days': self._config.API_REFRESH_TOKEN_EXPIRE_DAYS
        }
    
    def get_rate_limit_config(self) -> Dict[str, Any]:
        """获取限流配置"""
        return {
            'enabled': self._config.API_RATE_LIMIT_ENABLED,
            'default': self._config.API_RATE_LIMIT_DEFAULT,
            'storage_url': self._config.API_RATE_LIMIT_STORAGE_URL
        }
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有API配置"""
        return {
            'basic': {
                'prefix': self.prefix,
                'version': self.version,
                'host': self.host,
                'port': self.port,
                'base_url': self.base_url,
                'v2_prefix': self.v2_prefix,
                'v1_prefix': self.v1_prefix,
                'timeout': self._config.API_TIMEOUT,
                'max_content_length': self._config.API_MAX_CONTENT_LENGTH,
                'docs_enabled': self._config.API_DOCS_ENABLED
            },
            'cors': self.get_cors_config(),
            'auth': self.get_auth_config(),
            'rate_limit': self.get_rate_limit_config()
        }
    
    # ==============================================
    # 便捷方法
    # ==============================================
    
    @staticmethod
    def create_api_response(data: Any = None, message: str = "success", 
                          status_code: int = 200) -> Dict[str, Any]:
        """
        创建标准API响应格式
        
        Args:
            data: 响应数据
            message: 响应消息
            status_code: HTTP状态码
            
        Returns:
            标准格式的API响应
        """
        response = {
            'status': 'success' if status_code < 400 else 'error',
            'message': message,
            'code': status_code,
            'data': data,
            'timestamp': __import__('datetime').datetime.utcnow().isoformat()
        }
        
        # 如果没有数据，移除data字段
        if data is None:
            response.pop('data', None)
            
        return response


# ==============================================
# 全局API配置实例
# ==============================================

# 创建全局API配置实例，供整个应用使用
api_config = APIConfig()

# 导出便捷函数
def get_api_route(endpoint: str, version: Optional[str] = None) -> str:
    """便捷函数：获取API路由路径"""
    return api_config.get_route(endpoint, version)

def get_api_prefix(version: Optional[str] = None) -> str:
    """便捷函数：获取API前缀"""
    return api_config.get_blueprint_url_prefix(version)

def get_full_api_url(endpoint: str, version: Optional[str] = None) -> str:
    """便捷函数：获取完整API URL"""
    return api_config.get_full_url(endpoint, version)

def create_standard_response(data: Any = None, message: str = "success", 
                           status_code: int = 200) -> Dict[str, Any]:
    """便捷函数：创建标准API响应"""
    return APIConfig.create_api_response(data, message, status_code) 