#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存优化配置 - 提升静态资源加载速度
"""

from flask import request, make_response
from datetime import datetime, timedelta
import os

class CacheOptimizer:
    """缓存优化器"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化缓存优化"""
        app.after_request(self.add_cache_headers)
        app.before_request(self.handle_conditional_requests)
    
    def add_cache_headers(self, response):
        """添加缓存头部"""
        try:
            # 静态资源缓存策略
            if request.endpoint and 'static' in request.endpoint:
                # 清除可能存在的no-cache设置
                response.cache_control.no_cache = None
                response.cache_control.no_store = None

                # 静态资源缓存1年
                response.cache_control.max_age = 31536000  # 1年
                response.cache_control.public = True
                response.cache_control.immutable = True

                # 移除Pragma no-cache
                if 'Pragma' in response.headers:
                    del response.headers['Pragma']

                # 添加ETag
                if hasattr(response, 'data') and response.data:
                    import hashlib
                    etag = hashlib.md5(response.data).hexdigest()[:16]
                    response.set_etag(etag)
                
            # CSS/JS文件缓存
            elif request.path.endswith(('.css', '.js')):
                response.cache_control.max_age = 86400  # 1天
                response.cache_control.public = True
                
            # 图片文件缓存
            elif request.path.endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico')):
                response.cache_control.max_age = 604800  # 1周
                response.cache_control.public = True
                
            # HTML页面缓存
            elif response.content_type and 'text/html' in response.content_type:
                response.cache_control.max_age = 300  # 5分钟
                response.cache_control.must_revalidate = True
                
            # API响应缓存
            elif request.path.startswith('/api/'):
                # 对关键配置接口禁用缓存，避免数据不一致
                if request.path.startswith('/api/production/algorithm-weights'):
                    response.cache_control.no_cache = True
                    response.cache_control.no_store = True
                    response.cache_control.max_age = 0
                    response.headers['Pragma'] = 'no-cache'
                    response.headers['Expires'] = '0'
                elif request.method == 'GET':
                    response.cache_control.max_age = 60  # 1分钟
                else:
                    response.cache_control.no_cache = True

        except Exception as e:
            # 静默处理错误，不影响正常响应
            pass

        return response
    
    def handle_conditional_requests(self):
        """处理条件请求"""
        try:
            # 处理If-Modified-Since
            if request.if_modified_since:
                # 检查文件修改时间
                if request.endpoint and 'static' in request.endpoint:
                    file_path = request.path.replace('/static/', 'app/static/')
                    if os.path.exists(file_path):
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_mtime <= request.if_modified_since:
                            return make_response('', 304)
                            
        except Exception as e:
            # 静默处理错误
            pass
            
        return None

# 压缩配置
COMPRESS_CONFIG = {
    'COMPRESS_MIMETYPES': [
        'text/html',
        'text/css',
        'text/xml',
        'text/plain',
        'application/json',
        'application/javascript',
        'application/xml+rss',
        'application/atom+xml',
        'image/svg+xml'
    ],
    'COMPRESS_LEVEL': 6,
    'COMPRESS_MIN_SIZE': 500
}

def init_compression(app):
    """初始化压缩"""
    try:
        from flask_compress import Compress
        
        # 配置压缩
        for key, value in COMPRESS_CONFIG.items():
            app.config[key] = value
            
        # 启用压缩
        Compress(app)
        print("✅ Gzip压缩已启用")
        
    except ImportError:
        print("⚠️ flask-compress未安装，跳过压缩配置")
        print("   安装命令: pip install flask-compress")

def optimize_static_files():
    """优化静态文件"""
    static_dir = "app/static"
    
    # 检查静态文件大小
    large_files = []
    for root, dirs, files in os.walk(static_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                if size > 100 * 1024:  # 大于100KB
                    large_files.append({
                        'path': file_path,
                        'size_kb': round(size / 1024, 1)
                    })
            except:
                continue
    
    # 输出优化建议
    if large_files:
        print("📊 大文件优化建议:")
        for file_info in sorted(large_files, key=lambda x: x['size_kb'], reverse=True)[:10]:
            print(f"   {file_info['path']}: {file_info['size_kb']}KB")
    
    return large_files
