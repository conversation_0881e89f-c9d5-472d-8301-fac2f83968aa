# SQL预选 + Python分配 混合调度落地方案（实施说明）

更新时间：2025-09-04  作者：AEC-FT 团队

---

## 1. 摘要
本方案在保持现有“效果与功能”前提下，优化智能排产的性能与可维护性。核心思路：
- 阶段A（数据库侧）：使用 MySQL 8.0 的 CTE 与窗口函数，在 SQL 层完成“重过滤 + 重计算”，为每个批次生成 Top-K 设备候选及维度分解与综合分。
- 阶段B（服务侧）：在 Python/Flask 服务中对候选进行“冲突消解 + 序列插入（含SDST换型时间）+ 滚动优化 + 入库”，保持现有接口与失败追踪。
- 配置化 Feature Flag，支持一键回退原逻辑；增强可观测性，输出维度分解与决策理由。

收益：降低整体调度时延、提升可复现性与可解释性、简化维护与调参，且与现有架构完全兼容。

---

## 2. 背景与目标
- 背景：现有 RealSchedulingService 采用“统一启发式 + 多维度权重 + 绝对优先权”，在大规模数据时，设备遍历评分成为主要开销。
- 目标：
  1) 将可表达为SQL的过滤/打分下推到DB，提高吞吐；
  2) 保持现有调度结果一致性与可回退性；
  3) 提升可观测性（候选维度分解、总分、选择理由）；
  4) 不改变外部API与入库行为。

---

## 3. 总体方案概述
- SQL预选负责：技术匹配、改机等级/时长、处理时长估算、负载/交期/价值/业务/温度等维度分与综合分、Top-K 提取、绝对优先权加成。
- Python分配负责：候选冲突消解、同设备序列插入（涵盖SDST换型时间）、TCC资源/温度/能力最终校验、失败记录与入库。
- 全程配置化：开关、Top-K、权重、超时、日志Top-N 等均在配置中管理。

---

## 4. 架构流程
```mermaid
flowchart LR
  A[待排产批次 wait_lots] --> B[SQL 预选(CTE+窗口函数)]
  B -->|每批次Top-K候选: 维度分/综合分| C[Python分配(冲突消解+序列插入)]
  C --> D{设备资源校验\n(TCC/温度/能力)}
  D -->|通过| E[构造排产记录 _build_scheduled_lot_record]
  D -->|失败| F[失败跟踪器 failure_tracker]
  E --> G[落库 _save_to_database]
  F --> H[失败落库 + 清理]
```

---

## 5. 配置项与 Feature Flag（config.ini > 环境变量 > 默认）
- SCHED_USE_SQL_PRESELECT: bool，是否启用SQL预选（默认 false，灰度开）
- SCHED_PRESELECT_TOPK: int，每批次候选设备Top-K（默认 3）
- SCHED_SQL_TIMEOUT_MS: int，SQL查询超时毫秒（默认 2000）
- SCHED_ABSOLUTE_PRIORITY_BOOST: int，绝对优先权加成（默认 10000）
- SCHED_PRESELECT_LOG_TOPN: int，日志输出候选Top-N（默认 3）
- SCHED_WEIGHTS:（可选）权重来源，或使用分散键：
  - W_MATCH(默认0.25), W_LOAD(0.20), W_DEADLINE(0.25), W_VALUE(0.20), W_BUSINESS(0.10), W_TEMP(可配)

说明：权重遵循项目“技术匹配25% + 负载20% + 交期25% + 产值20% + 业务10%”基准，可按策略动态调整。

---

## 6. 数据与索引建议
- lots_wait：(LOT_ID, DEVICE, KIT_PN, TB_PN, HB_PN, STAGE, GOOD_QTY, CREATE_TIME, DELIVERY_DATE/REQ_DATE/DUE_DATE, LOT_TYPE, ...)
- equipment：(HANDLER_ID, TESTER_ID, HANDLER_CONFIG, KIT_PN, TB_PN, HB_PN, STAGE, UPH, current_workload, temp_range, ...)
- priority_device / priority_lot：产品/批次优先级配置表
- 索引：STAGE、DEVICE、KIT_PN/TB_PN/HB_PN/HANDLER_CONFIG、CREATE_TIME、DELIVERY_DATE、TESTER_ID/HANDLER_ID

---

## 7. SQL 预选设计（CTE + 窗口函数）
> 以下为示例骨架，实际字段请对齐现有库表。使用 SQLAlchemy text + 命名参数，严格参数化，避免注入。

```sql
/* + MAX_EXECUTION_TIME(:timeout_ms) */
WITH cand AS (
  SELECT l.LOT_ID, l.DEVICE, l.STAGE, l.GOOD_QTY, l.CREATE_TIME, l.DELIVERY_DATE,
         e.TESTER_ID, e.HANDLER_ID, e.UPH,
         CASE WHEN e.KIT_PN=l.KIT_PN AND e.TB_PN=l.TB_PN AND e.HB_PN=l.HB_PN THEN 'same'
              WHEN e.KIT_PN=l.KIT_PN THEN 'small' ELSE 'big' END AS chg,
         CASE WHEN e.KIT_PN=l.KIT_PN AND e.TB_PN=l.TB_PN AND e.HB_PN=l.HB_PN THEN 0
              WHEN e.KIT_PN=l.KIT_PN THEN 45 ELSE 120 END AS changeover_min
  FROM aps.lots_wait l
  JOIN aps.equipment e ON e.STAGE = l.STAGE
  WHERE l.LOT_ID IN :lot_ids
),
Dims AS (
  SELECT c.*,
         /* 技术匹配分 */
         CASE c.chg WHEN 'same' THEN 100 WHEN 'small' THEN 80 ELSE 60 END AS match_score,
         /* 负载均衡分：示意，实际可联表设备负载 */
         GREATEST(0, 100 - COALESCE(e_current_load, 0)) AS load_score,
         /* 交期紧迫分：示意，建议参考现有 _calculate_delivery_urgency 逻辑 */
         50 AS deadline_score,
         /* 产值效率分：示意 */
         50 AS value_score,
         /* 业务优先级分：示意，结合 priority_device / priority_lot / FIFO */
         50 AS business_score,
         /* 温度权重分：示意 */
         50 AS temp_score,
         (CASE WHEN c.UPH>0 THEN c.GOOD_QTY/c.UPH ELSE 0 END) + c.changeover_min AS processing_time_min
  FROM cand c
),
Scored AS (
  SELECT d.*,
         (:w_match * d.match_score + :w_load * d.load_score + :w_dead * d.deadline_score +
          :w_val * d.value_score + :w_biz * d.business_score + :w_tmp * d.temp_score)
          +
         (CASE WHEN EXISTS(
                SELECT 1 FROM aps.priority_device pd WHERE pd.DEVICE=d.DEVICE AND pd.priority=0
              ) THEN :priority_boost ELSE 0 END) AS total_score
  FROM Dims d
),
Ranked AS (
  SELECT s.*, ROW_NUMBER() OVER (PARTITION BY s.LOT_ID ORDER BY s.total_score DESC) AS rk
  FROM Scored s
)
SELECT * FROM Ranked WHERE rk <= :topk;
```

输出字段（建议）：
- LOT_ID, TESTER_ID, HANDLER_ID, chg, changeover_min, UPH, processing_time_min
- 各维度分：match_score, load_score, deadline_score, value_score, business_score, temp_score
- total_score, rk

---

## 8. Python 服务集成步骤（RealSchedulingService）
1) 读取配置与权重（遵守“config.ini > 环境变量 > 默认”）：
   - USE_SQL_PRESELECT、TOPK、TIMEOUT_MS、BOOST、LOG_TOPN、WEIGHTS
2) 在启发式执行前尝试调用 SQLPreselector：
   - 获取 {lot_id: [候选...]} 映射
   - 若为空或异常，则回退至原有“设备遍历评分”逻辑
3) 分配与序列插入：
   - 对每个批次，按 total_score 优先尝试候选；
   - 同设备内采用“最小增量成本”插入位置（考虑 changeover_min + processing_time_min + 交期罚金）；
   - 校验 TCC资源、温度范围、设备能力，失败则尝试下一个候选；
4) 记录：构造 _build_scheduled_lot_record，统一调用 _save_to_database；失败批次进入 failure_tracker 并落库。

---

## 9. 绝对优先权与特殊阶段
- 绝对优先权（priority=0）在 SQL 层以 :priority_boost 加分（默认 +10000），保证“排序硬优先”。
- 特殊阶段（如 BTT/BAKING/LSTR）可在 SQL 的维度分规则中单独加成，或在 Python 分配阶段前置排序提升级别。

---

## 10. 可观测性与日志
- 对每个批次输出 Top-N 候选：设备标识、total_score、维度分解；
- 记录 SQL 耗时、候选覆盖率（有无候选）、最终选择与排除原因（不兼容/资源冲突/温度超限等）；
- 统计：平均候选生成时间、调度总时间、成功/失败批次数、cache 命中。

---

## 11. 测试与灰度
- 单元测试：
  - SQLPreselector 正常路径（Top-K 排序正确、绝对优先权生效、维度分字段齐全）；
  - 超时/异常触发回退；
- 集成测试：
  - 与原逻辑结果对比（小样本一致性/可解释差异）；
  - 性能对比（中等规模数据，预期整体调度耗时下降 30%+，视数据而定）；
- 灰度：
  - 开发/测试环境先行，分车间/产品线逐步开启 SCHED_USE_SQL_PRESELECT；
  - 监控KPI，稳定后全量开启。

---

## 12. 风险与回滚
- SQL 逻辑与 Python 逻辑存在细微差异：
  - 对策：对齐规则；日志输出维度分，便于审计；灰度观测。
- SQL 超时：
  - 对策：加索引、控制 WHERE 范围；设置 MAX_EXECUTION_TIME；异常自动回退。
- 回滚：
  - SCHED_USE_SQL_PRESELECT=false 立即回到原有逻辑；不影响对外接口。

---

## 13. 实施计划与任务清单
- T1 配置与Feature Flag（0.5天）
- T2 SQL预选模块与DAO封装（2天）
- T3 服务集成与回退（1.5天）
- T4 可观测性与日志（0.5天）
- T5 单元/集成测试与灰度（2天）
- 合计：~6工作日（不含数据规模特殊情况与额外调优）

---

## 14. 验收标准与KPI
- 功能：保持现有调度“效果与功能”不回退；
- 性能：中等规模数据调度总耗时下降 ≥30%；
- 可观测性：每批次Top-N候选与维度分可追溯；
- 稳定性：无新增错误；异常回退路径可靠；
- 配置化：所有参数均可通过 config.ini 或环境变量调整。

---

## 15. 代码骨架示例

### 15.1 services/sql_preselector.py（骨架）
```python
from sqlalchemy import text
import logging
logger = logging.getLogger(__name__)

class SQLPreselector:
    def __init__(self, db_engine):
        self.engine = db_engine

    def get_candidates(self, lot_ids, weights, topk, priority_boost, timeout_ms):
        sql = text("""
        /*+ MAX_EXECUTION_TIME(:timeout_ms) */
        WITH cand AS (
          SELECT l.LOT_ID, l.DEVICE, l.STAGE, l.GOOD_QTY, l.CREATE_TIME, l.DELIVERY_DATE,
                 e.TESTER_ID, e.HANDLER_ID, e.UPH,
                 CASE WHEN e.KIT_PN=l.KIT_PN AND e.TB_PN=l.TB_PN AND e.HB_PN=l.HB_PN THEN 'same'
                      WHEN e.KIT_PN=l.KIT_PN THEN 'small' ELSE 'big' END AS chg,
                 CASE WHEN e.KIT_PN=l.KIT_PN AND e.TB_PN=l.TB_PN AND e.HB_PN=l.HB_PN THEN 0
                      WHEN e.KIT_PN=l.KIT_PN THEN 45 ELSE 120 END AS changeover_min
          FROM aps.lots_wait l
          JOIN aps.equipment e ON e.STAGE = l.STAGE
          WHERE l.LOT_ID IN :lot_ids
        ),
        Dims AS (
          SELECT c.*,
                 CASE c.chg WHEN 'same' THEN 100 WHEN 'small' THEN 80 ELSE 60 END AS match_score,
                 GREATEST(0, 100 - COALESCE(e_current_load, 0)) AS load_score,
                 50 AS deadline_score, 50 AS value_score, 50 AS business_score, 50 AS temp_score,
                 (CASE WHEN c.UPH>0 THEN c.GOOD_QTY/c.UPH ELSE 0 END) + c.changeover_min AS processing_time_min
          FROM cand c
        ),
        Scored AS (
          SELECT d.*,
                 (:w_match * d.match_score + :w_load * d.load_score + :w_dead * d.deadline_score +
                  :w_val * d.value_score + :w_biz * d.business_score + :w_tmp * d.temp_score)
                  + (CASE WHEN EXISTS(
                        SELECT 1 FROM aps.priority_device pd WHERE pd.DEVICE=d.DEVICE AND pd.priority=0
                     ) THEN :priority_boost ELSE 0 END) AS total_score
          FROM Dims d
        ),
        Ranked AS (
          SELECT s.*, ROW_NUMBER() OVER (PARTITION BY s.LOT_ID ORDER BY s.total_score DESC) AS rk
          FROM Scored s
        )
        SELECT * FROM Ranked WHERE rk <= :topk
        """)
        params = {
            'timeout_ms': timeout_ms,
            'lot_ids': tuple(lot_ids) if lot_ids else tuple(['__EMPTY__']),
            'w_match': weights['match'], 'w_load': weights['load'],
            'w_dead': weights['deadline'], 'w_val': weights['value'],
            'w_biz': weights['business'], 'w_tmp': weights.get('temp', 0),
            'priority_boost': priority_boost, 'topk': topk,
        }
        try:
            with self.engine.connect() as conn:
                rows = conn.execute(sql, params).mappings().all()
            result = {}
            for r in rows:
                lot_id = r['LOT_ID']
                result.setdefault(lot_id, []).append(dict(r))
            return result
        except Exception as e:
            logger.error(f"SQL预选失败，将回退到Python逻辑: {e}")
            return None
```

### 15.2 RealSchedulingService 集成（关键调用点示例）
```python
use_sql = self._cfg('SCHED_USE_SQL_PRESELECT', False)
if use_sql:
    candidates = self.sql_preselector.get_candidates(
        lot_ids=[lot['LOT_ID'] for lot in wait_lots],
        weights=self._get_weights_from_config(preloaded_data),
        topk=self._cfg('SCHED_PRESELECT_TOPK', 3),
        priority_boost=self._cfg('SCHED_ABSOLUTE_PRIORITY_BOOST', 10000),
        timeout_ms=self._cfg('SCHED_SQL_TIMEOUT_MS', 2000)
    )
else:
    candidates = None  # 走原逻辑

# 在 _execute_heuristic_scheduling_optimized 中：
# 若 candidates 存在，用 candidates[lot_id] 作为设备候选集合；
# 否则回退原设备遍历评分逻辑。
```

---

## 16. 结语
本方案保持现有架构与外部行为不变，通过“SQL预选 + Python分配”实现性能与可维护性全面提升。建议按“可回退、可观测、可灰度”的方式小步推进，以数据驱动调优并最终稳定切换。

