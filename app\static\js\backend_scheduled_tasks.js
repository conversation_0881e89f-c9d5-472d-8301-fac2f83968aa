#!/usr/bin/env javascript
/**
 * 后端定时任务JavaScript - 替代前端localStorage操作
 * 
 * 功能说明：
 * 1. 保持所有原有函数名称不变，确保UI兼容性
 * 2. 将localStorage操作替换为后端API调用
 * 3. 提供完整的任务管理功能：创建、暂停、恢复、删除
 * 4. 支持从localStorage自动迁移到后端
 * 5. 实时状态更新和错误处理
 * 
 * 替换方案：
 * - loadScheduledTasks() → 调用 GET /api/v2/system/scheduled-tasks
 * - saveScheduledTask() → 调用 POST /api/v2/system/scheduled-tasks
 * - updateTaskStatus() → 调用 GET /api/v2/system/scheduled-tasks/status
 * - viewScheduledTasks() → 本地渲染后端数据
 * - 新增：任务暂停、恢复、删除功能
 * - 新增：localStorage迁移功能
 */

// ==================== 🔥 后端定时任务功能实现 ====================

// 全局变量存储定时任务（从后端获取）
let scheduledTasks = [];

// 页面加载时初始化定时任务
document.addEventListener('DOMContentLoaded', function() {
    loadScheduledTasks();
    updateTaskStatus();
    
    // 每分钟检查一次任务状态
    setInterval(updateTaskStatus, 60000);
    
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    const taskDateInput = document.getElementById('taskDate');
    if (taskDateInput) {
        taskDateInput.value = today;
        taskDateInput.min = today; // 不允许选择过去的日期
    }
    
    // 监听表单变化以更新预览
    const form = document.getElementById('scheduledTaskForm');
    if (form) {
        form.addEventListener('input', updateTaskPreview);
        form.addEventListener('change', updateTaskPreview);
    }
    
    // 检查是否需要从localStorage迁移
    checkAndMigrateFromLocalStorage();
});

// 检查并从localStorage迁移任务
async function checkAndMigrateFromLocalStorage() {
    try {
        const localTasks = localStorage.getItem('scheduledTasks');
        if (localTasks) {
            const tasks = JSON.parse(localTasks);
            if (tasks.length > 0) {
                // 询问用户是否迁移
                const shouldMigrate = confirm(
                    `检测到 ${tasks.length} 个前端定时任务，是否迁移到后端服务？\n` +
                    `迁移后将享受更稳定的7×24小时运行能力。`
                );
                
                if (shouldMigrate) {
                    await migrateFromLocalStorage(tasks);
                }
            }
        }
    } catch (error) {
        console.error('检查localStorage迁移失败:', error);
    }
}

// 从localStorage迁移任务到后端
async function migrateFromLocalStorage(tasks) {
    try {
        showNotification('迁移中', '正在迁移前端任务到后端...', 'info');
        
        const response = await fetch('/api/v2/system/scheduled-tasks/migrate-from-frontend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ tasks: tasks })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(
                '迁移完成', 
                `成功迁移 ${result.migrated} 个任务，失败 ${result.failed} 个`, 
                'success'
            );
            
            // 清理localStorage
            localStorage.removeItem('scheduledTasks');
            
            // 重新加载任务列表
            await loadScheduledTasks();
        } else {
            showNotification('迁移失败', result.message || '迁移过程中出现错误', 'error');
        }
    } catch (error) {
        console.error('迁移任务失败:', error);
        showNotification('迁移失败', '迁移过程中出现网络错误', 'error');
    }
}

// 打开定时任务设置模态框
function openScheduledTaskModal() {
    // 重置表单
    document.getElementById('scheduledTaskForm').reset();
    
    // 设置默认日期为明天
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateStr = tomorrow.toISOString().split('T')[0];
    document.getElementById('taskDate').value = dateStr;
    
    // 设置所有时间输入的默认值为9:00
    document.getElementById('taskHour').value = 9;
    document.getElementById('taskMinute').value = 0;
    if (document.getElementById('dailyHour')) document.getElementById('dailyHour').value = 9;
    if (document.getElementById('dailyMinute')) document.getElementById('dailyMinute').value = 0;
    if (document.getElementById('weeklyHour')) document.getElementById('weeklyHour').value = 9;
    if (document.getElementById('weeklyMinute')) document.getElementById('weeklyMinute').value = 0;
    
    // 设置间隔任务默认值
    if (document.getElementById('intervalValue')) document.getElementById('intervalValue').value = 1;
    if (document.getElementById('intervalUnit')) document.getElementById('intervalUnit').value = 'hours';
    
    // 设置默认任务类型为一次性任务
    document.getElementById('taskType').value = 'once';
    
    // 更新任务类型选项
    updateTaskTypeOptions();
    
    // 更新预览
    updateTaskPreview();
    
    // 添加输入事件监听器，实时更新预览
    const inputElements = [
        'taskName', 'taskType', 'taskDate', 'taskHour', 'taskMinute',
        'dailyHour', 'dailyMinute', 'weeklyHour', 'weeklyMinute',
        'intervalValue', 'intervalUnit', 'intervalEndTime',
        'taskStrategy', 'autoImport', 'emailNotification'
    ];
    
    inputElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            // 移除旧的监听器（避免重复绑定）
            element.removeEventListener('input', updateTaskPreview);
            element.removeEventListener('change', updateTaskPreview);
            // 添加新的监听器
            element.addEventListener('input', updateTaskPreview);
            element.addEventListener('change', updateTaskPreview);
        }
    });
    
    // 为星期复选框添加事件监听器
    ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
        const element = document.getElementById(day);
        if (element) {
            element.removeEventListener('change', updateTaskPreview);
            element.addEventListener('change', updateTaskPreview);
        }
    });
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduledTaskModal'));
    modal.show();
}

// 更新任务类型选项（保持原有逻辑）
function updateTaskTypeOptions() {
    const taskType = document.getElementById('taskType').value;
    const onceOptions = document.getElementById('onceTaskOptions');
    const dailyOptions = document.getElementById('dailyTaskOptions');
    const weeklyOptions = document.getElementById('weeklyTaskOptions');
    const intervalOptions = document.getElementById('intervalTaskOptions');
    
    // 隐藏所有选项
    [onceOptions, dailyOptions, weeklyOptions, intervalOptions].forEach(el => {
        if (el) el.style.display = 'none';
    });
    
    // 显示对应选项
    switch(taskType) {
        case 'once':
            if (onceOptions) onceOptions.style.display = 'block';
            break;
        case 'daily':
            if (dailyOptions) dailyOptions.style.display = 'block';
            break;
        case 'weekly':
            if (weeklyOptions) weeklyOptions.style.display = 'block';
            break;
        case 'interval':
            if (intervalOptions) intervalOptions.style.display = 'block';
            break;
    }
    
    updateTaskPreview();
}

// 更新任务预览（修复不同任务类型的时间输入）
function updateTaskPreview() {
    const previewContent = document.getElementById('taskPreviewContent');
    if (!previewContent) return;
    
    const taskName = document.getElementById('taskName').value || '未命名任务';
    const taskType = document.getElementById('taskType').value;
    const taskStrategy = document.getElementById('taskStrategy').value;
    const autoImport = document.getElementById('autoImport').checked;
    
    let preview = `📋 任务名称：${taskName}<br>`;
    
    // 任务类型和时间
    const typeNames = {
        'once': '一次性任务',
        'daily': '每日重复',
        'weekly': '每周重复',
        'interval': '间隔重复'
    };
    preview += `🔄 任务类型：${typeNames[taskType]}<br>`;
    
    // 根据任务类型获取对应的时间输入
    let taskHour, taskMinute;
    
    switch(taskType) {
        case 'once':
            taskHour = document.getElementById('taskHour').value || '9';
            taskMinute = document.getElementById('taskMinute').value || '0';
            const taskDate = document.getElementById('taskDate').value || '请选择日期';
            const timeStr = `${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}`;
            preview += `⏰ 执行时间：${taskDate} ${timeStr}<br>`;
            break;
            
        case 'daily':
            taskHour = document.getElementById('dailyHour').value || '9';
            taskMinute = document.getElementById('dailyMinute').value || '0';
            const dailyTimeStr = `${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}`;
            preview += `⏰ 执行时间：每天 ${dailyTimeStr}<br>`;
            break;
            
        case 'weekly':
            taskHour = document.getElementById('weeklyHour').value || '9';
            taskMinute = document.getElementById('weeklyMinute').value || '0';
            const weeklyTimeStr = `${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}`;
            
            const weekdays = [];
            ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
                if (document.getElementById(day)?.checked) {
                    const dayNames = {
                        'sunday': '周日', 'monday': '周一', 'tuesday': '周二', 
                        'wednesday': '周三', 'thursday': '周四', 'friday': '周五', 'saturday': '周六'
                    };
                    weekdays.push(dayNames[day]);
                }
            });
            
            const weekdaysText = weekdays.length > 0 ? weekdays.join('、') : '请选择星期';
            preview += `⏰ 执行时间：${weekdaysText} ${weeklyTimeStr}<br>`;
            break;
            
        case 'interval':
            const intervalValue = document.getElementById('intervalValue').value || '1';
            const intervalUnit = document.getElementById('intervalUnit').value || 'hours';
            const unitNames = { 'minutes': '分钟', 'hours': '小时', 'days': '天' };
            preview += `⏰ 执行间隔：每 ${intervalValue} ${unitNames[intervalUnit]}<br>`;
            
            const endTime = document.getElementById('intervalEndTime').value;
            if (endTime) {
                preview += `⏰ 结束时间：${new Date(endTime).toLocaleString()}<br>`;
            } else {
                preview += `⏰ 结束时间：无限重复<br>`;
            }
            break;
    }
    
    // 排产策略
    const strategyNames = {
        'intelligent': '🧠 智能综合策略',
        'deadline': '📅 交期优先策略',
        'product': '📦 产品优先策略',
        'value': '💰 产值优先策略'
    };
    preview += `🎯 排产策略：${strategyNames[taskStrategy]}<br>`;
    
    // 高级选项
    if (autoImport) {
        preview += `📥 自动导入最新数据<br>`;
    }
    
    // Excel自动保存选项
    const autoSaveExcel = document.getElementById('autoSaveExcel')?.checked;
    if (autoSaveExcel) {
        const savePath = document.getElementById('excelSavePath')?.value;
        const previewFilename = document.getElementById('excelFilenamePreview')?.textContent;
        
        preview += `📊 Excel自动保存：已启用<br>`;
        if (savePath) {
            preview += `📁 保存路径：${savePath}<br>`;
        }
        if (previewFilename) {
            preview += `📄 文件名示例：${previewFilename}<br>`;
        }
    }
    
    previewContent.innerHTML = preview;
}

// 保存定时任务（修复不同任务类型的数据收集）
async function saveScheduledTask() {
    const form = document.getElementById('scheduledTaskForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const taskType = document.getElementById('taskType').value;
    
    // 基础任务数据
    const taskData = {
        name: document.getElementById('taskName').value,
        type: taskType,
        strategy: document.getElementById('taskStrategy').value,
        target: document.getElementById('taskTarget').value,
        autoImport: document.getElementById('autoImport').checked,
        emailNotification: document.getElementById('emailNotification').checked,
        // Excel自动保存配置
        excelAutoSave: {
            enabled: document.getElementById('autoSaveExcel').checked,
            savePath: document.getElementById('excelSavePath').value,
            filenamePattern: document.getElementById('excelFilenamePattern').value === '自定义' 
                ? document.getElementById('customFilenamePattern').value 
                : document.getElementById('excelFilenamePattern').value,
            overwriteMode: document.getElementById('excelOverwriteMode').value,
            maxBackupFiles: parseInt(document.getElementById('maxBackupFiles').value) || 10
        }
    };
    
    // 根据任务类型收集对应的时间数据
    switch(taskType) {
        case 'once':
            taskData.date = document.getElementById('taskDate').value;
            taskData.hour = parseInt(document.getElementById('taskHour').value);
            taskData.minute = parseInt(document.getElementById('taskMinute').value);
            
            if (!taskData.date) {
                alert('请选择执行日期');
                return;
            }
            break;
            
        case 'daily':
            taskData.hour = parseInt(document.getElementById('dailyHour').value);
            taskData.minute = parseInt(document.getElementById('dailyMinute').value);
            break;
            
        case 'weekly':
            taskData.hour = parseInt(document.getElementById('weeklyHour').value);
            taskData.minute = parseInt(document.getElementById('weeklyMinute').value);
            taskData.weekdays = [];
            
            ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
                if (document.getElementById(day)?.checked) {
                    taskData.weekdays.push(day);
                }
            });
            
            if (taskData.weekdays.length === 0) {
                alert('请至少选择一个星期');
                return;
            }
            break;
            
        case 'interval':
            taskData.intervalValue = parseInt(document.getElementById('intervalValue').value);
            taskData.intervalUnit = document.getElementById('intervalUnit').value;
            taskData.endTime = document.getElementById('intervalEndTime').value;
            
            if (!taskData.intervalValue || taskData.intervalValue < 1) {
                alert('请设置有效的间隔时间');
                return;
            }
            break;
    }
    
    try {
        // 🔧 添加策略验证日志
        console.log('🚀 创建定时任务，策略:', taskData.strategy);
        console.log('📤 发送到后端的任务数据:', taskData);
        
        // 如果启用了Excel自动保存，先保存全局配置
        if (taskData.excelAutoSave.enabled) {
            if (!taskData.excelAutoSave.savePath || !taskData.excelAutoSave.savePath.trim()) {
                alert('启用Excel自动保存时，必须指定保存路径');
                return;
            }
            
            console.log('💾 保存Excel自动保存全局配置...');
            const configSaved = await saveExcelAutoSaveGlobalConfig();
            if (!configSaved) {
                console.error('❌ Excel配置保存失败，停止创建任务');
                return;
            }
        }
        
        // 调用后端API创建任务
        const response = await fetch('/api/v2/system/scheduled-tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('scheduledTaskModal'));
            modal.hide();
            
            // 重新加载任务列表
            await loadScheduledTasks();
            updateTaskStatus();
            
            showNotification('任务创建成功', `定时任务"${taskData.name}"已创建`, 'success');
        } else {
            showNotification('创建失败', result.message || '任务创建失败', 'error');
        }
    } catch (error) {
        console.error('创建任务失败:', error);
        showNotification('创建失败', '网络错误，请稍后重试', 'error');
    }
}

// 加载已保存的定时任务（替换为后端API调用）
async function loadScheduledTasks() {
    try {
        // 🔧 修复：添加缓存破坏机制，确保获取最新任务数据
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/v2/system/scheduled-tasks?_t=${timestamp}`, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        const result = await response.json();
        
        if (result.success) {
            scheduledTasks = result.tasks || [];
        } else {
            console.error('加载任务失败:', result.message);
            scheduledTasks = [];
        }
    } catch (error) {
        console.error('加载任务失败:', error);
        scheduledTasks = [];
    }
}

// 更新任务状态显示（替换为后端API调用）
async function updateTaskStatus() {
    try {
        // 🔧 修复：添加缓存破坏机制，确保获取最新状态
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/v2/system/scheduled-tasks/status?_t=${timestamp}`, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        const result = await response.json();
        
        const statusText = document.getElementById('taskStatusText');
        if (!statusText) return;
        
        if (result.success) {
            const status = result.status;
            
            if (status.activeTasks === 0) {
                statusText.textContent = '当前无定时任务';
                statusText.className = 'text-muted';
            } else {
                statusText.innerHTML = status.statusText;
                statusText.className = 'task-status-active';
                
                if (status.nextTask) {
                    const nextTime = new Date(status.nextTask.nextExecution);
                    statusText.innerHTML += `<br><small>${nextTime.toLocaleString()}</small>`;
                }
            }
        } else {
            statusText.textContent = result.status?.statusText || '状态获取失败';
            statusText.className = 'text-muted';
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        const statusText = document.getElementById('taskStatusText');
        if (statusText) {
            statusText.textContent = '状态获取失败';
            statusText.className = 'text-muted';
        }
    }
}

// 查看定时任务列表（保持原有UI逻辑，数据来源改为后端）
function viewScheduledTasks() {
    const tbody = document.getElementById('scheduledTaskTableBody');
    
    if (scheduledTasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无定时任务</td></tr>';
    } else {
        let html = '';
        scheduledTasks.forEach(task => {
            const typeNames = {
                'once': '一次性',
                'daily': '每日',
                'weekly': '每周',
                'interval': '间隔'
            };
            
            const strategyNames = {
                'intelligent': '智能综合',
                'deadline': '交期优先',
                'product': '产品优先',
                'value': '产值优先'
            };
            
            // 🔧 修复：改进状态显示，支持错误详情
            function getStatusBadge(task) {
                let badgeClass = 'bg-secondary';
                let statusText = '未知';
                let tooltip = '';
                
                if (task.status === 'active') {
                    badgeClass = 'bg-success';
                    statusText = '活跃';
                    tooltip = '任务正在正常运行';
                } else if (task.status === 'paused') {
                    badgeClass = 'bg-warning';
                    statusText = '暂停';
                    tooltip = '任务已暂停';
                } else if (task.status === 'completed') {
                    badgeClass = 'bg-info';
                    statusText = '已完成';
                    tooltip = '任务已完成';
                } else if (task.status === 'error') {
                    badgeClass = 'bg-danger';
                    statusText = '错误';
                    if (task.trigger === 'orphaned') {
                        tooltip = '孤立任务：' + (task.error_message || '任务在调度器中不存在');
                    } else {
                        tooltip = '任务执行出错：' + (task.error_message || '未知错误');
                    }
                } else if (task.status === 'expired') {
                    badgeClass = 'bg-secondary';
                    statusText = '已过期';
                    tooltip = '任务已过期';
                }
                
                return `<span class="badge ${badgeClass}" title="${tooltip}">${statusText}</span>`;
            }
            
            let timeInfo = '';
            if (task.type === 'once') {
                timeInfo = `${task.date} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'daily') {
                timeInfo = `每天 ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'weekly') {
                const weekdayNames = {
                    'sunday': '周日', 'monday': '周一', 'tuesday': '周二', 
                    'wednesday': '周三', 'thursday': '周四', 'friday': '周五', 'saturday': '周六'
                };
                const days = (task.weekdays || []).map(day => weekdayNames[day]).join('、');
                timeInfo = `${days} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'interval') {
                const unitNames = { 'minutes': '分钟', 'hours': '小时', 'days': '天' };
                timeInfo = `每 ${task.intervalValue} ${unitNames[task.intervalUnit]}`;
            }
            
            const nextExecution = task.nextExecution ? 
                new Date(task.nextExecution).toLocaleString() : '-';
            
            const lastExecuted = task.lastExecuted ? 
                new Date(task.lastExecuted).toLocaleString() : '从未执行';
            
            // 操作按钮
            let actionButtons = '';
            if (task.status === 'active') {
                actionButtons = `
                    <button class="btn btn-sm btn-warning me-1" onclick="pauseTask('${task.id}')">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                `;
            } else if (task.status === 'paused') {
                actionButtons = `
                    <button class="btn btn-sm btn-success me-1" onclick="resumeTask('${task.id}')">
                        <i class="fas fa-play"></i> 恢复
                    </button>
                `;
            }
            
            // 🔧 修复：孤立任务显示特殊的删除按钮
            if (task.status === 'error' && task.trigger === 'orphaned') {
                actionButtons += `
                    <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')" title="删除孤立任务">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                `;
            } else {
                actionButtons += `
                    <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                `;
            }
            
            html += `
                <tr>
                    <td>${task.name}</td>
                    <td>${typeNames[task.type] || task.type}</td>
                    <td>${timeInfo}</td>
                    <td>${strategyNames[task.strategy] || task.strategy}</td>
                    <td>${getStatusBadge(task)}</td>
                    <td><small>${nextExecution}</small></td>
                    <td><small>${lastExecuted}</small></td>
                    <td>${actionButtons}</td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
    
    // 显示任务列表模态框
    const modalElement = document.getElementById('scheduledTaskListModal');
    const modal = new bootstrap.Modal(modalElement);
    
    // 添加模态框关闭事件监听器，确保清理背景遮罩
    modalElement.addEventListener('hidden.bs.modal', function () {
        // 清理可能残留的模态框背景遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });
        
        // 确保body没有modal-open类
        document.body.classList.remove('modal-open');
        
        // 恢复body的样式
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }, { once: true }); // 使用once确保监听器只执行一次
    
    modal.show();
}

// 🔧 修复：简化的模态框刷新函数，使用统一的状态管理
async function refreshCurrentTaskList() {
    console.log('⚠️ refreshCurrentTaskList 已废弃，使用 refreshAllTaskStates 替代');
    await refreshAllTaskStates();
}

// 暂停任务
async function pauseTask(taskId) {
    try {
        // 🔧 修复：添加防重复点击保护
        const button = event.target.closest('button');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 暂停中...';
        }
        
        const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}/pause`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('任务已暂停', result.message, 'success');
            
            // 🔧 修复：统一状态更新，避免竞态条件
            await refreshAllTaskStates();
        } else {
            showNotification('暂停失败', result.message, 'error');
            // 🔧 修复：失败时恢复按钮状态
            const button = event.target.closest('button');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-pause"></i> 暂停';
            }
        }
    } catch (error) {
        console.error('暂停任务失败:', error);
        showNotification('暂停失败', '网络错误，请稍后重试', 'error');
        // 🔧 修复：异常时恢复按钮状态
        const button = event.target.closest('button');
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-pause"></i> 暂停';
        }
    }
}

// 恢复任务
async function resumeTask(taskId) {
    try {
        console.log(`🔧 DEBUG: 开始恢复任务 ${taskId}`);
        
        // 🔧 修复：添加防重复点击保护
        const button = event.target.closest('button');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 恢复中...';
        }
        
        const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}/resume`, {
            method: 'POST'
        });
        
        const result = await response.json();
        console.log(`🔧 DEBUG: 恢复任务 ${taskId} 响应:`, result);
        
        if (result.success) {
            showNotification('任务已恢复', result.message, 'success');
            
            // 🔧 修复：统一状态更新，避免竞态条件
            await refreshAllTaskStates();
        } else {
            showNotification('恢复失败', result.message, 'error');
            // 🔧 修复：失败时恢复按钮状态
            const button = event.target.closest('button');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-play"></i> 恢复';
            }
        }
    } catch (error) {
        console.error('恢复任务失败:', error);
        showNotification('恢复失败', '网络错误，请稍后重试', 'error');
        // 🔧 修复：异常时恢复按钮状态
        const button = event.target.closest('button');
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-play"></i> 恢复';
        }
    }
}

// 删除任务
async function deleteTask(taskId) {
    if (!confirm('确定要删除这个定时任务吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        // 🔧 修复：添加防重复点击保护
        const button = event.target.closest('button');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
        }
        
        const response = await fetch(`/api/v2/system/scheduled-tasks/${taskId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('任务已删除', result.message, 'success');
            
            // 🔧 修复：统一状态更新，避免竞态条件
            await refreshAllTaskStates();
        } else {
            showNotification('删除失败', result.message, 'error');
            // 🔧 修复：失败时恢复按钮状态
            const button = event.target.closest('button');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-trash"></i> 删除';
            }
        }
    } catch (error) {
        console.error('删除任务失败:', error);
        showNotification('删除失败', '网络错误，请稍后重试', 'error');
        // 🔧 修复：异常时恢复按钮状态
        const button = event.target.closest('button');
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-trash"></i> 删除';
        }
    }
}

// 清理孤立任务
async function cleanupOrphanedTasks() {
    if (!confirm('确定要清理所有孤立任务吗？这将删除数据库中存在但调度器中不存在的任务。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/v2/system/scheduled-tasks/cleanup-orphaned', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('清理完成', result.message, 'success');
            
            // 刷新任务列表
            await refreshAllTaskStates();
        } else {
            showNotification('清理失败', result.message, 'error');
        }
    } catch (error) {
        console.error('清理孤立任务失败:', error);
        showNotification('清理失败', '网络错误，请稍后重试', 'error');
    }
}

// 🔧 新增：统一的状态刷新函数，避免竞态条件
async function refreshAllTaskStates() {
    try {
        console.log('🔄 统一刷新所有任务状态...');
        
        // 🔧 修复：添加短暂延迟确保后端状态完全更新
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 1. 获取最新任务数据 - 🔧 修复：添加缓存破坏机制
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/v2/system/scheduled-tasks?_t=${timestamp}`, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        const result = await response.json();
        
        if (result.success) {
            const latestTasks = result.tasks || [];
            console.log(`📋 获取到 ${latestTasks.length} 个最新任务`);
            
            // 2. 更新全局缓存
            window.scheduledTasks = latestTasks;
            
            // 3. 更新主页面的任务状态显示
            updateTaskStatus();
            
            // 4. 如果模态框正在显示，更新模态框内容
            const modalElement = document.getElementById('scheduledTaskListModal');
            if (modalElement && modalElement.classList.contains('show')) {
                console.log('🔄 更新模态框任务列表');
                renderTaskListInModal(latestTasks);
            }
            
            console.log('✅ 所有任务状态刷新完成');
        } else {
            console.error('获取任务列表失败:', result.message);
        }
    } catch (error) {
        console.error('刷新任务状态失败:', error);
    }
}

// 🔧 新增：在模态框中渲染任务列表（独立函数，避免重复代码）
function renderTaskListInModal(tasks) {
    const tbody = document.querySelector('#scheduledTaskListModal tbody');
    if (!tbody) return;
    
    const typeNames = {
        'once': '一次性',
        'daily': '每日重复', 
        'weekly': '每周重复',
        'interval': '间隔重复'
    };
    
    const strategyNames = {
        'intelligent': '智能综合',
        'deadline': '交期优先',
        'product': '产品优先',
        'value': '产值优先'
    };
    
    const statusBadges = {
        'active': '<span class="badge bg-success">运行中</span>',
        'paused': '<span class="badge bg-warning">已暂停</span>',
        'completed': '<span class="badge bg-info">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    
    let html = '';
    if (tasks.length === 0) {
        html = '<tr><td colspan="8" class="text-center text-muted">暂无定时任务</td></tr>';
    } else {
        tasks.forEach(task => {
            let timeInfo = '';
            if (task.type === 'once') {
                timeInfo = `${task.date} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'daily') {
                timeInfo = `每日 ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'weekly') {
                const dayNames = {
                    'monday': '周一', 'tuesday': '周二', 'wednesday': '周三',
                    'thursday': '周四', 'friday': '周五', 'saturday': '周六', 'sunday': '周日'
                };
                const days = (task.weekdays || []).map(day => dayNames[day]).join(', ');
                timeInfo = `${days} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'interval') {
                const unitNames = { 'minutes': '分钟', 'hours': '小时', 'days': '天' };
                timeInfo = `每 ${task.intervalValue} ${unitNames[task.intervalUnit]}`;
            }
            
            const nextExecution = task.nextExecution ? 
                new Date(task.nextExecution).toLocaleString() : '-';
            
            const lastExecuted = task.lastExecuted ? 
                new Date(task.lastExecuted).toLocaleString() : '从未执行';
            
            // 🔧 修复：确保每个按钮都有唯一的taskId绑定
            let actionButtons = '';
            if (task.status === 'active') {
                actionButtons = `
                    <button class="btn btn-sm btn-warning me-1" 
                            onclick="pauseTask('${task.id}')" 
                            data-task-id="${task.id}">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                `;
            } else if (task.status === 'paused') {
                actionButtons = `
                    <button class="btn btn-sm btn-success me-1" 
                            onclick="resumeTask('${task.id}')" 
                            data-task-id="${task.id}">
                        <i class="fas fa-play"></i> 恢复
                    </button>
                `;
            }
            
            actionButtons += `
                <button class="btn btn-sm btn-danger" 
                        onclick="deleteTask('${task.id}')" 
                        data-task-id="${task.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `;
            
            html += `
                <tr data-task-id="${task.id}">
                    <td>${task.name}</td>
                    <td>${typeNames[task.type] || task.type}</td>
                    <td>${timeInfo}</td>
                    <td>${strategyNames[task.strategy] || task.strategy}</td>
                    <td>${statusBadges[task.status] || task.status}</td>
                    <td><small>${nextExecution}</small></td>
                    <td><small>${lastExecuted}</small></td>
                    <td>${actionButtons}</td>
                </tr>
            `;
        });
    }
    
    tbody.innerHTML = html;
    console.log(`✅ 模态框任务列表已更新，显示 ${tasks.length} 个任务`);
}

// 显示通知函数（如果不存在则创建简单版本）
function showNotification(title, message, type = 'info') {
    // 检查是否有现有的通知函数
    if (typeof window.showNotification === 'function' && window.showNotification !== showNotification) {
        window.showNotification(title, message, type);
        return;
    }
    
    // 简单的通知实现
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <strong>${title}</strong><br>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// ==================== 兼容性函数 ====================

// 这些函数保持原有名称，确保UI兼容性
// 但实际上不再需要，因为任务执行已移到后端

function calculateNextExecution(taskData) {
    // 兼容性函数，后端会自动计算
    console.log('calculateNextExecution: 已由后端处理');
    return null;
}

function scheduleTask(taskData) {
    // 兼容性函数，后端会自动调度
    console.log('scheduleTask: 已由后端处理');
}

function executeScheduledTask(taskData) {
    // 兼容性函数，后端会自动执行
    console.log('executeScheduledTask: 已由后端处理');
}

// ==================== Excel自动保存相关函数 ====================

// 切换Excel自动保存选项显示
function toggleExcelAutoSaveOptions() {
    const autoSaveCheckbox = document.getElementById('autoSaveExcel');
    const optionsDiv = document.getElementById('excelAutoSaveOptions');
    
    if (autoSaveCheckbox.checked) {
        optionsDiv.style.display = 'block';
        // 加载现有配置
        loadExcelAutoSaveConfig();
    } else {
        optionsDiv.style.display = 'none';
    }
    
    // 更新任务预览
    updateTaskPreview();
}

// 加载Excel自动保存配置
async function loadExcelAutoSaveConfig() {
    try {
        const response = await fetch('/api/v2/system/excel-auto-save/config');
        const result = await response.json();
        
        if (result.success && result.data) {
            const config = result.data;
            
            // 填充表单
            if (config.save_path) {
                document.getElementById('excelSavePath').value = config.save_path;
            }
            
            if (config.filename_pattern) {
                const pattern = config.filename_pattern;
                const selectElement = document.getElementById('excelFilenamePattern');
                
                // 检查是否是预定义模式
                let found = false;
                for (let option of selectElement.options) {
                    if (option.value === pattern) {
                        selectElement.value = pattern;
                        found = true;
                        break;
                    }
                }
                
                // 如果不是预定义模式，设为自定义
                if (!found) {
                    selectElement.value = '自定义';
                    document.getElementById('customFilenamePattern').value = pattern;
                    document.getElementById('customFilenameRow').style.display = 'block';
                }
            }
            
            if (config.overwrite_mode) {
                document.getElementById('excelOverwriteMode').value = config.overwrite_mode;
            }
            
            if (config.max_backup_files) {
                document.getElementById('maxBackupFiles').value = config.max_backup_files;
            }
            
            // 更新文件名预览
            updateExcelFilenamePreview();
        }
    } catch (error) {
        console.error('加载Excel自动保存配置失败:', error);
    }
}

// 选择Excel保存路径
function selectExcelSavePath() {
    // 由于浏览器安全限制，无法直接打开文件夹选择对话框
    // 提供用户友好的提示
    const currentPath = document.getElementById('excelSavePath').value;
    const userPath = prompt(
        '请输入Excel文件保存路径：\n\n' +
        '示例：\n' +
        'Windows: C:\\用户\\用户名\\Documents\\排产结果\n' +
        '或: D:\\排产数据\\Excel导出\n\n' +
        '注意：如果路径不存在，系统会自动创建',
        currentPath || 'C:\\排产结果'
    );
    
    if (userPath && userPath.trim()) {
        document.getElementById('excelSavePath').value = userPath.trim();
        updateExcelFilenamePreview();
    }
}

// 测试Excel保存路径
async function testExcelSavePath() {
    const savePath = document.getElementById('excelSavePath').value;
    
    if (!savePath || !savePath.trim()) {
        showNotification('warning', '请先输入保存路径');
        return;
    }
    
    try {
        const response = await fetch('/api/v2/system/excel-auto-save/test-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                save_path: savePath.trim()
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('success', result.message);
        } else {
            showNotification('error', result.message);
        }
    } catch (error) {
        console.error('测试保存路径失败:', error);
        showNotification('error', '测试路径失败: ' + error.message);
    }
}

// 更新Excel文件名预览
function updateExcelFilenamePreview() {
    const patternSelect = document.getElementById('excelFilenamePattern');
    const customPatternInput = document.getElementById('customFilenamePattern');
    const customRow = document.getElementById('customFilenameRow');
    const previewDiv = document.getElementById('excelFilenamePreview');
    
    // 处理自定义选项显示
    if (patternSelect.value === '自定义') {
        customRow.style.display = 'block';
    } else {
        customRow.style.display = 'none';
    }
    
    // 生成预览文件名
    let pattern = patternSelect.value;
    if (pattern === '自定义') {
        pattern = customPatternInput.value || '排产结果_{timestamp}.xlsx';
    }
    
    // 替换变量生成预览
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
    const date = now.toISOString().slice(0, 10).replace(/-/g, '');
    
    const previewFilename = pattern
        .replace('{timestamp}', timestamp)
        .replace('{date}', date)
        .replace('{source}', '手动'); // 默认显示手动源
    
    previewDiv.textContent = previewFilename;
    
    // 更新任务预览
    updateTaskPreview();
}

// 保存Excel自动保存全局配置
async function saveExcelAutoSaveGlobalConfig() {
    const config = {
        auto_save_enabled: document.getElementById('autoSaveExcel').checked,
        save_path: document.getElementById('excelSavePath').value,
        filename_pattern: document.getElementById('excelFilenamePattern').value === '自定义' 
            ? document.getElementById('customFilenamePattern').value 
            : document.getElementById('excelFilenamePattern').value,
        overwrite_mode: document.getElementById('excelOverwriteMode').value,
        max_backup_files: parseInt(document.getElementById('maxBackupFiles').value) || 10
    };
    
    try {
        const response = await fetch('/api/v2/system/excel-auto-save/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('success', '配置保存成功');
            return true;
        } else {
            showNotification('error', result.message);
            return false;
        }
    } catch (error) {
        console.error('保存Excel配置失败:', error);
        showNotification('error', '保存配置失败: ' + error.message);
        return false;
    }
}

// ==================== 导出函数 ====================

// 确保函数在全局作用域中可用
window.openScheduledTaskModal = openScheduledTaskModal;
window.updateTaskTypeOptions = updateTaskTypeOptions;
window.updateTaskPreview = updateTaskPreview;
window.saveScheduledTask = saveScheduledTask;
window.loadScheduledTasks = loadScheduledTasks;
window.updateTaskStatus = updateTaskStatus;
window.viewScheduledTasks = viewScheduledTasks;
// Excel自动保存相关函数
window.toggleExcelAutoSaveOptions = toggleExcelAutoSaveOptions;
window.selectExcelSavePath = selectExcelSavePath;
window.testExcelSavePath = testExcelSavePath;
window.updateExcelFilenamePreview = updateExcelFilenamePreview;
window.saveExcelAutoSaveGlobalConfig = saveExcelAutoSaveGlobalConfig;
window.refreshCurrentTaskList = refreshCurrentTaskList;
window.pauseTask = pauseTask;
window.resumeTask = resumeTask;
window.deleteTask = deleteTask;
window.showNotification = showNotification;
// 🔧 新增函数导出
window.refreshAllTaskStates = refreshAllTaskStates;
window.renderTaskListInModal = renderTaskListInModal;

console.log('✅ 后端定时任务JavaScript已加载 - 前端定时任务已替换为后端服务');