/**
 * AI助手管理类
 */
class AIAssistant {
    constructor() {
        this.currentMode = 'training_ai'; // 默认模式
        this.chatHistory = this.loadChatHistory();
        this.apiClient = new APIClient();
        this.initializeUI();
        this.loadAIStatus();
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        // 绑定发送按钮
        const sendBtn = document.getElementById('send-btn');
        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        // 绑定输入框回车事件
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // 绑定模式切换按钮
        const trainingBtn = document.getElementById('training-ai-btn');
        const progressBtn = document.getElementById('progress-ai-btn');
        
        if (trainingBtn) {
            trainingBtn.addEventListener('click', () => this.switchMode('training_ai'));
        }
        
        if (progressBtn) {
            progressBtn.addEventListener('click', () => this.switchMode('progress_ai'));
        }

        // 绑定新对话按钮
        const newChatBtn = document.getElementById('new-chat-btn');
        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.startNewChat());
        }

        // 绑定清除历史按钮
        const clearHistoryBtn = document.getElementById('clear-history-btn');
        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        }

        // 显示当前模式的聊天历史
        this.displayChatHistory();
    }

    /**
     * 加载AI状态
     */
    async loadAIStatus() {
        try {
            const response = await this.apiClient.get('/api/ai/status');
            if (response.success) {
                this.updateModeButtons(response.data);
            }
        } catch (error) {
            console.error('加载AI状态失败:', error);
        }
    }

    /**
     * 更新模式按钮状态
     */
    updateModeButtons(statusData) {
        const trainingBtn = document.getElementById('training-ai-btn');
        const progressBtn = document.getElementById('progress-ai-btn');

        if (trainingBtn) {
            const enabled = statusData.training_ai?.enabled;
            trainingBtn.classList.toggle('btn-success', enabled);
            trainingBtn.classList.toggle('btn-outline-secondary', !enabled);
            trainingBtn.disabled = !enabled;
        }

        if (progressBtn) {
            const enabled = statusData.progress_ai?.enabled;
            progressBtn.classList.toggle('btn-success', enabled);
            progressBtn.classList.toggle('btn-outline-secondary', !enabled);
            progressBtn.disabled = !enabled;
        }
    }

    /**
     * 切换AI模式
     */
    switchMode(mode) {
        this.currentMode = mode;
        
        // 更新按钮状态
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.getElementById(`${mode.replace('_', '-')}-btn`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // 显示对应模式的聊天历史
        this.displayChatHistory();
        
        // 更新模式显示
        const modeDisplay = document.getElementById('current-mode');
        if (modeDisplay) {
            const modeText = mode === 'training_ai' ? '新员工培训AI助教' : '产品进度查询AI助手';
            modeDisplay.textContent = `当前模式: ${modeText}`;
        }
    }

    /**
     * 发送消息
     */
    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput?.value?.trim();

        if (!message) {
            this.showToast('请输入消息内容', 'warning');
            return;
        }

        // 清空输入框
        chatInput.value = '';

        // 添加用户消息到界面
        this.addMessage('user', message);

        // 显示加载状态
        const loadingId = this.addMessage('assistant', '正在思考中...', true);

        try {
            // 发送请求到后端
            const response = await this.apiClient.post('/api/ai/chat', {
                message: message,
                mode: this.currentMode
            });

            // 移除加载消息
            this.removeMessage(loadingId);

            if (response.success) {
                // 添加AI回复
                this.addMessage('assistant', response.response);
                
                // 保存到聊天历史
                this.saveChatMessage('user', message);
                this.saveChatMessage('assistant', response.response);
            } else {
                this.addMessage('assistant', `发送消息失败: ${response.error}`);
            }

        } catch (error) {
            // 移除加载消息
            this.removeMessage(loadingId);
            
            console.error('发送消息失败:', error);
            this.addMessage('assistant', '发送消息失败，请稍后重试。');
            this.showToast('发送消息失败', 'error');
        }
    }

    /**
     * 添加消息到聊天界面
     */
    addMessage(role, content, isLoading = false) {
        const chatContainer = document.getElementById('chat-messages');
        if (!chatContainer) return null;

        const messageId = `msg-${Date.now()}-${Math.random()}`;
        const messageDiv = document.createElement('div');
        messageDiv.id = messageId;
        messageDiv.className = `chat-message ${role}-message`;

        const avatar = role === 'user' ? 
            '<i class="fas fa-user"></i>' : 
            '<i class="fas fa-robot"></i>';

        const loadingClass = isLoading ? ' loading' : '';
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${avatar}
            </div>
            <div class="message-content${loadingClass}">
                ${content}
                ${isLoading ? '<div class="loading-dots"><span></span><span></span><span></span></div>' : ''}
            </div>
            <div class="message-time">
                ${new Date().toLocaleTimeString()}
            </div>
        `;

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;

        return messageId;
    }

    /**
     * 移除消息
     */
    removeMessage(messageId) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            messageElement.remove();
        }
    }

    /**
     * 保存聊天消息到历史记录
     */
    saveChatMessage(role, content) {
        if (!this.chatHistory[this.currentMode]) {
            this.chatHistory[this.currentMode] = [];
        }

        this.chatHistory[this.currentMode].push({
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        });

        // 限制历史记录长度
        if (this.chatHistory[this.currentMode].length > 100) {
            this.chatHistory[this.currentMode] = this.chatHistory[this.currentMode].slice(-100);
        }

        this.saveChatHistory();
    }

    /**
     * 显示聊天历史
     */
    displayChatHistory() {
        const chatContainer = document.getElementById('chat-messages');
        if (!chatContainer) return;

        chatContainer.innerHTML = '';

        const history = this.chatHistory[this.currentMode] || [];
        history.forEach(msg => {
            this.addMessage(msg.role, msg.content);
        });
    }

    /**
     * 开始新对话
     */
    startNewChat() {
        if (confirm('确定要开始新对话吗？当前对话记录将被清除。')) {
            this.chatHistory[this.currentMode] = [];
            this.saveChatHistory();
            this.displayChatHistory();
            this.showToast('已开始新对话', 'success');
        }
    }

    /**
     * 清除聊天历史
     */
    clearHistory() {
        if (confirm('确定要清除所有聊天历史吗？此操作不可恢复。')) {
            this.chatHistory = {};
            this.saveChatHistory();
            this.displayChatHistory();
            this.showToast('聊天历史已清除', 'success');
        }
    }

    /**
     * 加载聊天历史
     */
    loadChatHistory() {
        try {
            const saved = localStorage.getItem('ai_chat_history');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.error('加载聊天历史失败:', error);
            return {};
        }
    }

    /**
     * 保存聊天历史
     */
    saveChatHistory() {
        try {
            localStorage.setItem('ai_chat_history', JSON.stringify(this.chatHistory));
        } catch (error) {
            console.error('保存聊天历史失败:', error);
        }
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// API客户端类
class APIClient {
    constructor() {
        this.baseURL = '';
    }

    async request(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    async get(url) {
        return this.request(url, { method: 'GET' });
    }

    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.aiAssistant = new AIAssistant();
}); 