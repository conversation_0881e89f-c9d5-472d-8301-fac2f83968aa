#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控API端点
提供实时性能指标查询接口
"""

from flask import Blueprint, jsonify, current_app
from app.utils.performance_monitor import get_performance_metrics
import psutil
from datetime import datetime

monitoring_bp = Blueprint('monitoring', __name__)

@monitoring_bp.route('/api/monitoring/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'APS Intelligent Commander Platform'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/monitoring/metrics', methods=['GET'])
def get_metrics():
    """获取性能指标"""
    try:
        # 获取实时系统指标
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 获取监控系统的性能摘要
        performance_summary = get_performance_metrics()
        
        return jsonify({
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_mb': memory.used / 1024 / 1024,
                'disk_percent': disk.percent
            },
            'monitoring_summary': performance_summary
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/monitoring/alerts', methods=['GET'])
def get_alerts():
    """获取告警信息"""
    try:
        performance_summary = get_performance_metrics()
        
        return jsonify({
            'timestamp': datetime.now().isoformat(),
            'recent_alerts': performance_summary.get('recent_alerts', []),
            'total_alerts': performance_summary.get('total_alerts', 0)
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
