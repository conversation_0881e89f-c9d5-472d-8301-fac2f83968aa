#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按会话与设备，解释机台内每个批次的综合评分构成（用于排查机台内排序原因）

用法：
  python tools/analysis/explain_device_queue.py --session-id SESSION --handler-id HCHC-C-015-6800 [--lot-id YX0125HB0428]

说明：
- 复用项目内 RealSchedulingService 的计算方法，尽量与实际排产一致
- 仅读取数据库（lotprioritydone/eqp_status 等），不会修改任何数据
"""

import argparse
from typing import Dict, Any, List

from app.services.real_scheduling_service import RealSchedulingService
import pymysql

def db_query(sql: str, params=None):
    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
    try:
        with conn.cursor() as cur:
            cur.execute(sql, params or ())
            cols = [c[0] for c in cur.description]
            return [dict(zip(cols, row)) for row in cur.fetchall()]
    finally:
        conn.close()


def fetch_device_queue(session_id: str, handler_id: str) -> List[Dict[str, Any]]:
    base = (
        "SELECT PRIORITY, HANDLER_ID, LOT_ID, DEVICE, STAGE, PKG_PN, LOT_TYPE, GOOD_QTY, "
        "       match_type, comprehensive_score, changeover_time, priority_score, CREATE_TIME "
        "FROM lotprioritydone WHERE HANDLER_ID=%s "
    )
    if session_id is None or session_id.strip().lower() in ('none', 'null', ''):
        sql = base + "AND SESSION_ID IS NULL ORDER BY PRIORITY ASC"
        rows = db_query(sql, (handler_id,))
    else:
        sql = base + "AND SESSION_ID=%s ORDER BY PRIORITY ASC"
        rows = db_query(sql, (handler_id, session_id))
    return rows


def fetch_equipment(handler_id: str) -> Dict[str, Any]:
    rows = db_query("SELECT * FROM eqp_status WHERE HANDLER_ID=%s LIMIT 1", (handler_id,))
    return rows[0] if rows else {}


def build_preloaded(service: RealSchedulingService) -> Dict[str, Any]:
    """直接通过pymysql查询，避免Flask应用上下文依赖"""
    preloaded: Dict[str, Any] = {}
    preloaded['test_specs'] = db_query("SELECT DEVICE, STAGE, TESTER, HB_PN, TB_PN, UPH, TESTER_CONFIG FROM ET_FT_TEST_SPEC")
    preloaded['recipe_files'] = db_query("SELECT DEVICE, STAGE, HANDLER_CONFIG, KIT_PN, SOCKET_PN FROM ET_RECIPE_FILE")
    try:
        preloaded['stage_mappings'] = db_query("SELECT mapping_type, source_stage, target_stage, alias FROM stage_mapping_config WHERE active=1")
        preloaded['stage_mapping_config'] = preloaded['stage_mappings']
    except Exception:
        preloaded['stage_mappings'] = []
        preloaded['stage_mapping_config'] = []
    try:
        preloaded['device_priority'] = db_query("SELECT * FROM devicepriorityconfig")
    except Exception:
        preloaded['device_priority'] = []
    try:
        preloaded['lot_priority'] = db_query("SELECT * FROM lotpriorityconfig")
    except Exception:
        preloaded['lot_priority'] = []
    preloaded['equipment_status'] = db_query("SELECT * FROM eqp_status")
    return preloaded


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--session-id', required=True)
    parser.add_argument('--handler-id', required=True)
    parser.add_argument('--lot-id', required=False)
    args = parser.parse_args()

    svc = RealSchedulingService()
    pre = build_preloaded(svc)

    eqp = fetch_equipment(args.handler_id)
    if not eqp:
        print(f"[ERROR] 未找到设备: {args.handler_id}")
        return

    rows = fetch_device_queue(args.session_id, args.handler_id)
    if not rows:
        print(f"[WARN] 该会话下设备无排产记录: session={args.session_id}, handler={args.handler_id}")
        return

    print(f"设备 {args.handler_id} - 会话 {args.session_id} - 排队批次数: {len(rows)}")
    print("PRIORITY, LOT_ID, match_type, changeover_time, tech, load, deadline, value, business, int_cont, chg_cost, global_imp, proc_time, comprehensive")

    target_lot = args.lot_id.upper() if args.lot_id else None

    for r in rows:
        # 构造 lot 字典供评分函数使用（尽量覆盖常用字段）
        lot = {
            'LOT_ID': r.get('LOT_ID', ''),
            'DEVICE': r.get('DEVICE', ''),
            'STAGE': r.get('STAGE', ''),
            'PKG_PN': r.get('PKG_PN', ''),
            'LOT_TYPE': r.get('LOT_TYPE', ''),
            'GOOD_QTY': r.get('GOOD_QTY', 0),
        }

        res = svc.calculate_intelligent_equipment_score(lot, eqp, pre)
        if not res:
            print(f"{r.get('PRIORITY')}, {r.get('LOT_ID')}, [不匹配], -, -, -, -, -, -, -, -, -, -, -")
            continue

        # 分项评分（与综合评分中相同方法，以便展示构成）
        processing_time = res.get('processing_time', 0.0)
        changeover_time = res.get('changeover_time', 0)
        tech_score = res.get('tech_score', 0.0)
        int_cont = res.get('intelligent_continuity', 0.0)
        chg_cost = res.get('changeover_cost', 0.0)
        global_imp = res.get('global_impact', 0.0)
        load_score = svc.calculate_load_balance_score(eqp, processing_time, changeover_time)
        deadline_score = svc.calculate_deadline_urgency_score(lot, processing_time)
        value_score = svc.calculate_value_efficiency_score(lot, processing_time)
        business_score = svc.calculate_business_priority_score_optimized(lot, pre)

        line = (
            f"{r.get('PRIORITY')}, {r.get('LOT_ID')}, {res.get('match_type','')}, {changeover_time}, "
            f"{tech_score:.1f}, {load_score:.1f}, {deadline_score:.1f}, {value_score:.1f}, {business_score:.1f}, "
            f"{int_cont:.1f}, {chg_cost:.1f}, {global_imp:.1f}, {processing_time:.1f}, {res.get('comprehensive_score',0):.1f}"
        )

        # 高亮目标批次
        if target_lot and r.get('LOT_ID','').upper() == target_lot:
            print("* " + line)
        else:
            print("  " + line)


if __name__ == '__main__':
    main()

