#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 简化缓存系统 - 适配中小型APS系统
替换过度工程化的确定性缓存，提供简单高效的缓存解决方案

核心特性：
1. 两层缓存架构：内存LRU + 智能TTL
2. 数据分类缓存：按变化频率分层管理
3. 简单缓存键设计：易于理解和维护
4. Flask应用上下文安全
"""

import hashlib
import json
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Union
from functools import lru_cache, wraps
from collections import OrderedDict
import logging

logger = logging.getLogger(__name__)

class TTLCacheEntry:
    """TTL缓存条目"""
    def __init__(self, value: Any, ttl: int):
        self.value = value
        self.expires_at = time.time() + ttl
        self.created_at = time.time()
    
    def is_expired(self) -> bool:
        return time.time() > self.expires_at
    
    def age(self) -> float:
        return time.time() - self.created_at

class SimpleCache:
    """
    🎯 简化缓存管理器
    
    设计理念：简单高效 > 过度工程化
    适用场景：中小型单机应用
    """
    
    def __init__(self, 
                 max_memory_entries: int = 500,
                 cleanup_interval: int = 300):  # 5分钟清理一次
        """
        初始化简化缓存系统
        
        Args:
            max_memory_entries: 内存缓存最大条目数
            cleanup_interval: 清理过期缓存的间隔(秒)
        """
        # 🔧 核心数据结构 - 保持简单
        self._memory_cache: OrderedDict[str, TTLCacheEntry] = OrderedDict()
        self._max_entries = max_memory_entries
        
        # 🔒 线程安全
        self._lock = threading.RLock()
        
        # ⏰ 清理机制
        self._cleanup_interval = cleanup_interval
        self._last_cleanup = time.time()
        
        # 📊 简单统计
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0,
            'cleanups': 0,
            'errors': 0
        }
        
        # 🎯 数据分类TTL配置 - 基于业务特点优化
        self.ttl_config = {
            # 🏃 高频变化数据 - 短TTL确保调度数据实时性
            'wait_lots': 60,              # 🔥 CRITICAL FIX: 降低至1分钟 (等待排产批次)
            'wip_lots': 60,               # 🔥 CRITICAL FIX: 降低至1分钟 (在制品批次)
            'equipment_status': 120,      # 🔥 CRITICAL FIX: 降低至2分钟 (设备状态)
            
            # 📊 中频变化数据 - 适度TTL  
            'priority_config': 300,       # 🔥 CRITICAL FIX: 降低至5分钟 (优先级配置)
            'device_priority_config': 300, # 🔥 NEW: 5分钟 (设备优先级配置)
            'lot_priority_config': 300,   # 🔥 NEW: 5分钟 (批次优先级配置)
            'stage_mapping_config': 600,  # 🔥 NEW: 10分钟 (工序映射配置)
            
            # 📋 低频变化数据 - 长TTL
            'test_specs': 1800,      # 30分钟 (测试规范) 
            'recipe_data': 1800,     # 30分钟 (配方数据)
            'uph_data': 1800,        # 30分钟 (UPH产能数据)
            
            # 🔧 配置数据 - 很长TTL
            'system_config': 3600,   # 1小时 (系统配置)
            'business_rules': 3600,  # 1小时 (业务规则)
            
            # 🔧 默认TTL
            'default': 600           # 10分钟默认
        }
        
        logger.info("🎯 简化缓存系统初始化完成 - 适配中小型APS系统")
    
    def _generate_cache_key(self, key: str, category: str = 'default') -> str:
        """
        生成简单有效的缓存键
        
        格式: category:key_hash[:8]
        """
        if len(key) > 100:  # 长key进行hash
            key_hash = hashlib.md5(key.encode()).hexdigest()[:8]
            return f"{category}:{key_hash}"
        else:
            return f"{category}:{key}"
    
    def _cleanup_expired(self):
        """清理过期缓存条目"""
        if time.time() - self._last_cleanup < self._cleanup_interval:
            return
            
        try:
            expired_keys = []
            current_time = time.time()
            
            for key, entry in self._memory_cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._memory_cache[key]
            
            self._stats['cleanups'] += 1
            self._last_cleanup = current_time
            
            if expired_keys:
                logger.debug(f"🧹 清理了 {len(expired_keys)} 个过期缓存条目")
                
        except Exception as e:
            logger.warning(f"缓存清理出错: {e}")
            self._stats['errors'] += 1
    
    def _evict_oldest(self):
        """LRU淘汰最旧的缓存条目"""
        if len(self._memory_cache) >= self._max_entries:
            # 移除最旧的条目
            oldest_key = next(iter(self._memory_cache))
            del self._memory_cache[oldest_key]
            self._stats['evictions'] += 1
    
    def get(self, key: str, category: str = 'default') -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            category: 数据分类，用于确定TTL
            
        Returns:
            缓存值或None
        """
        cache_key = self._generate_cache_key(key, category)
        
        with self._lock:
            # 定期清理过期条目
            self._cleanup_expired()
            
            entry = self._memory_cache.get(cache_key)
            
            if entry is None:
                self._stats['misses'] += 1
                return None
            
            if entry.is_expired():
                del self._memory_cache[cache_key]
                self._stats['misses'] += 1
                return None
            
            # LRU: 移动到末尾
            self._memory_cache.move_to_end(cache_key)
            self._stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, category: str = 'default', ttl: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            category: 数据分类
            ttl: 自定义TTL，None则使用category默认TTL
            
        Returns:
            是否设置成功
        """
        try:
            cache_key = self._generate_cache_key(key, category)
            
            # 确定TTL
            if ttl is None:
                ttl = self.ttl_config.get(category, self.ttl_config['default'])
            
            with self._lock:
                # 检查容量并淘汰
                self._evict_oldest()
                
                # 设置新条目
                self._memory_cache[cache_key] = TTLCacheEntry(value, ttl)
                self._stats['sets'] += 1
                
                return True
                
        except Exception as e:
            logger.warning(f"设置缓存失败 {key}: {e}")
            self._stats['errors'] += 1
            return False
    
    def delete(self, key: str, category: str = 'default') -> bool:
        """删除缓存条目"""
        cache_key = self._generate_cache_key(key, category)
        
        with self._lock:
            if cache_key in self._memory_cache:
                del self._memory_cache[cache_key]
                return True
            return False
    
    def clear_category(self, category: str):
        """清空指定分类的所有缓存"""
        with self._lock:
            keys_to_remove = [k for k in self._memory_cache.keys() if k.startswith(f"{category}:")]
            for key in keys_to_remove:
                del self._memory_cache[key]
            
            logger.info(f"🧹 清空了分类 '{category}' 的 {len(keys_to_remove)} 个缓存条目")
    
    def clear_all(self):
        """清空所有缓存"""
        with self._lock:
            count = len(self._memory_cache)
            self._memory_cache.clear()
            logger.info(f"🧹 清空了所有 {count} 个缓存条目")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            hit_rate = 0.0
            total_requests = self._stats['hits'] + self._stats['misses']
            if total_requests > 0:
                hit_rate = self._stats['hits'] / total_requests * 100
            
            return {
                **self._stats,
                'total_entries': len(self._memory_cache),
                'hit_rate_percent': hit_rate,
                'memory_usage_percent': len(self._memory_cache) / self._max_entries * 100
            }
    
    def get_category_info(self) -> Dict[str, Dict[str, Any]]:
        """获取各分类的缓存信息"""
        category_info = {}
        
        with self._lock:
            for cache_key, entry in self._memory_cache.items():
                category = cache_key.split(':', 1)[0]
                
                if category not in category_info:
                    category_info[category] = {
                        'count': 0,
                        'oldest_age': 0,
                        'newest_age': float('inf'),
                        'expired_count': 0
                    }
                
                info = category_info[category]
                info['count'] += 1
                
                age = entry.age()
                info['oldest_age'] = max(info['oldest_age'], age)
                info['newest_age'] = min(info['newest_age'], age)
                
                if entry.is_expired():
                    info['expired_count'] += 1
        
        return category_info

# 全局缓存实例
_simple_cache = None

def get_simple_cache() -> SimpleCache:
    """获取全局简化缓存实例"""
    global _simple_cache
    if _simple_cache is None:
        _simple_cache = SimpleCache()
    return _simple_cache

def cache_result(category: str = 'default', ttl: Optional[int] = None):
    """
    简化的缓存装饰器
    
    用法:
        @cache_result('equipment_status', 600)
        def get_equipment_status():
            return expensive_computation()
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = func.__name__
            args_str = str(args) + str(sorted(kwargs.items()))
            cache_key = f"{func_name}:{hashlib.md5(args_str.encode()).hexdigest()[:8]}"
            
            cache = get_simple_cache()
            
            # 尝试从缓存获取
            result = cache.get(cache_key, category)
            if result is not None:
                return result
            
            # 计算并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, category, ttl)
            
            return result
        return wrapper
    return decorator

# 便利函数
def cache_get(key: str, category: str = 'default') -> Optional[Any]:
    """便利函数：获取缓存"""
    return get_simple_cache().get(key, category)

def cache_set(key: str, value: Any, category: str = 'default', ttl: Optional[int] = None) -> bool:
    """便利函数：设置缓存"""  
    return get_simple_cache().set(key, value, category, ttl)

def cache_delete(key: str, category: str = 'default') -> bool:
    """便利函数：删除缓存"""
    return get_simple_cache().delete(key, category)

def cache_clear_category(category: str):
    """便利函数：清空分类缓存"""
    return get_simple_cache().clear_category(category)