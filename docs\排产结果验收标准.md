# 排产结果验收标准 v1.0

更新时间：2025-09-10  作者：研发部

本标准用于对“终测智能调度平台”的排产结果进行全方位验收与长期回归审计，确保匹配逻辑、候选选择与设备内优先级符合业务口径。

---

## 1. 数据口径与来源

- 需求侧画像（以 LOT.DEVICE + LOT.STAGE 为键）：
  - et_ft_test_spec → TESTER、HB_PN、TB_PN（注：同一 DEVICE+STAGE 可能存在多条记录，构成“允许集合”）
  - et_recipe_file → HANDLER_CONFIG、KIT_PN（注：同一 DEVICE+STAGE 可能存在多条记录，构成“允许集合”）
  - 判定语义：采用“任一满足（EXISTS）”口径，即设备只要满足任意一条需求组合（配方r × 测试规范s）即可视为满足需求
- 设备侧画像（eqp_status）：HANDLER_CONFIG、KIT_PN、TESTER、HB_PN、TB_PN、DEVICE、STAGE
- 兼容规则：设备端 HB_PN/TB_PN 为空视为“可配置兼容”（不阻断匹配）

## 2. 六类匹配分类与判定（互斥，EXISTS 语义）

记 r∈et_recipe_file(DEVICE=lot.DEVICE, STAGE=lot.STAGE)、s∈et_ft_test_spec(DEVICE=lot.DEVICE, STAGE=lot.STAGE)。
当存在至少一组 (r,s) 与设备 e 满足以下条件时，即判定命中相应类别：

1) 同配置（改机 0 分钟）
   - e.HANDLER_CONFIG = r.HANDLER_CONFIG；e.KIT_PN = r.KIT_PN；
   - e.TESTER = s.TESTER；
   - STAGE 一致：e.STAGE = lot.STAGE；
   - HB/TB：基础PN一致或 e.HB_PN/e.TB_PN 为空（不阻断）。
2) 换测试机小改机（55 分钟）
   - e.HANDLER_CONFIG = r.HANDLER_CONFIG；e.KIT_PN = r.KIT_PN；
   - e.TESTER ≠ s.TESTER；（STAGE 可不一致）
3) 小改机（45 分钟）
   - e.HANDLER_CONFIG = r.HANDLER_CONFIG；e.KIT_PN = r.KIT_PN；
   - e.TESTER = s.TESTER；（STAGE 可不一致）
4) 大改机（120 分钟）
   - e.HANDLER_CONFIG = r.HANDLER_CONFIG；
   - e.KIT_PN ≠ r.KIT_PN 或 r.KIT_PN 为空（其他任意）。
5) 同产品续排（0 分钟，特例）
   - e.DEVICE = lot.DEVICE 且 e.STAGE = lot.STAGE（不依赖需求解析成功）。
6) 无法上机（9999）
   - 以上均不满足。

说明：跨工序允许时（忽略 STAGE），跨工序入池最低门槛为 HANDLER_CONFIG 一致；若同时 KIT_PN 也一致 → 进入 45/55 类别；若仅 HC 一致、KIT 不一致 → 仅可能 120。

## 3. 候选设备选择策略（三层候选池）

- 严格池：eqp.DEVICE == LOT.DEVICE 且 eqp.STAGE == LOT.STAGE
- 空闲池：eqp.DEVICE 为空 且 eqp.STAGE 为空，且 eqp.HC == 需求HC 且 eqp.KIT == 需求KIT
- 跨工序池（开关允许，忽略 STAGE）：
  - A 类：HC+KIT 一致（→ 45 或 55）
  - B 类：仅 HC 一致（KIT 不一致或缺失，→ 120）
- 更优不丢失（按改机时间优先级）：0 ≻ 45 ≻ 55 ≻ 120；跨工序 A 必须优先于跨工序 B。

## 4. 设备内排产优先级（三段式）

1) 配置优先级分层：lotpriorityconfig 与 devicepriorityconfig 的 priority 升序（0 > 1 > 2；两者并存取更小值）
2) 同层内综合评分（comprehensive_score）降序（若系统未持久化该分数，则仅能校验第 1/3 条）
3) 同分 FIFO：按 et_wait_lot.CREATE_TIME 升序（先到先服务）

验收失败情形：
- 高优层批次排在低优层之后
- 同层内出现低分在高分之前（若系统未持久化分数，则跳过此条）
- 同分时出现后到在先到之前（违反 FIFO）

## 5. 解析失败处理

- 当从 et_ft_test_spec / et_recipe_file 解析需求字段失败（空值/异常）时：
  - 先判“同产品续排特例前置”：若存在任一设备满足 DEVICE 与 STAGE 与批次完全一致 → 判为“同产品续排（0）”成功；
  - 若不存在 → 判定为“排产失败”，并将失败原因与细目写入 scheduling_failed_lots.failure_reason / failure_details。

## 6. 巡检 SQL 使用指南

- 所有巡检 SQL 已整理至：tools/analysis/sql_checks.sql（A〜K）。
- 使用方式（示例）：
  - CLI：`mysql -uroot -pWWWwww123! aps < tools/analysis/sql_checks.sql`
  - 或在客户端按段执行，关注各段注释与期望结果。

## 7. 验收清单（每次排产后执行）

- A/B/C/D/E：六类分类充分性与合理性无冲突；不得出现“应判 0/45/55 却落 120”的情况
- F/G/H：跨工序入池门槛、A 类优先性正确；不得出现“跨工序 B 被选而 A 存在”的情况
- I：设备内优先级三段式不被违反（若未持久化分数，则仅检第 1/3 条）
- J：解析失败但可“同产品续排”的批次不得落入失败表
- K：候选池计数与期望吻合（用于发现“更优候选未入池”的潜在问题）

## 8. 变更记录

- 2025-09-10：首版发布；纳入“同产品续排特例前置”、跨工序 A/B 分类、三段式优先级与 SQL 巡检清单。

