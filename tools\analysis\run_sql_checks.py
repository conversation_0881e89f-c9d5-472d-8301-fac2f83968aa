#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产结果验收标准 - 一键化SQL巡检工具
执行 A~K 全量SQL检查，输出问题项汇总表（CSV）

使用方式：
  python tools/analysis/run_sql_checks.py
  python tools/analysis/run_sql_checks.py --session-id SESSION_123
  python tools/analysis/run_sql_checks.py --date 2025-09-10
"""

import os
import csv
import json
import argparse
from datetime import datetime
from typing import List, Dict, Optional

# 复用项目内的高性能连接池
from app.utils.db_connection_pool import get_db_cursor, register_process_type

register_process_type('sql_checks')

OUTPUT_DIR = os.path.join('reports')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# SQL检查清单（A~K）
SQL_CHECKS = {
    'A_同配置应命中': """
        SELECT 'A_同配置应命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '应为同配置(0分钟)但可能未正确分类' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE) AND UPPER(e.STAGE)=UPPER(l.STAGE)
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
            WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
              AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
          )
          AND EXISTS (
            SELECT 1 FROM et_ft_test_spec s
            WHERE s.DEVICE=l.DEVICE AND UPPER(s.STAGE)=UPPER(l.STAGE)
              AND s.TESTER=e.TESTER
          )
          AND (l.match_type NOT IN ('同配置匹配', '同产品续排') OR l.match_type IS NULL)
          {session_filter}
    """,
    
    'B_小改机应命中': """
        SELECT 'B_小改机应命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '应为小改机(45分钟)但可能未正确分类' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE) AND UPPER(e.STAGE)=UPPER(l.STAGE)
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
            WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
              AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
          )
          AND EXISTS (
            SELECT 1 FROM et_ft_test_spec s
            WHERE s.DEVICE=l.DEVICE AND UPPER(s.STAGE)=UPPER(l.STAGE)
              AND s.TESTER=e.TESTER
          )
          AND l.match_type NOT IN ('小改机匹配', '同配置匹配', '同产品续排')
          {session_filter}
    """,
    
    'C_换测试机小改机应命中': """
        SELECT 'C_换测试机小改机应命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '应为换测试机小改机(55分钟)但可能未正确分类' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE) AND UPPER(e.STAGE)=UPPER(l.STAGE)
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
            WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
              AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
          )
          AND EXISTS (
            SELECT 1 FROM et_ft_test_spec s
            WHERE s.DEVICE=l.DEVICE AND UPPER(s.STAGE)=UPPER(l.STAGE)
              AND s.TESTER<>e.TESTER
          )
          AND l.match_type NOT IN ('换测试机小改机匹配', '小改机匹配', '同配置匹配', '同产品续排')
          {session_filter}
    """,
    
    'D_大改机合理性': """
        SELECT 'D_大改机合理性' as check_type, l.LOT_ID, l.HANDLER_ID,
               '大改机(120分钟)分类合理性检查' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE) AND UPPER(e.STAGE)=UPPER(l.STAGE)
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
             WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
               AND r.HANDLER_CONFIG=e.HANDLER_CONFIG
          )
          AND NOT EXISTS (
            SELECT 1 FROM et_recipe_file r2
             WHERE r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
               AND r2.HANDLER_CONFIG=e.HANDLER_CONFIG AND r2.KIT_PN=e.KIT_PN
          )
          AND COALESCE(l.match_type,'') <> '大改机匹配'
          {session_filter}
    """,
    
    'E_同产品续排命中': """
        SELECT 'E_同产品续排命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '同产品续排(0分钟特例)命中检查' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE)
          AND UPPER(e.STAGE)=UPPER(l.STAGE)
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
             WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
               AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
          )
          AND COALESCE(l.match_type,'') <> '同产品续排'
          {session_filter}
    """,
    
    'F_跨工序合法性': """
        SELECT 'F_跨工序合法性' as check_type, l.LOT_ID, l.HANDLER_ID,
               '跨工序违规：STAGE不等且HC不等' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE UPPER(e.STAGE)<>UPPER(l.STAGE)
          AND NOT EXISTS (
            SELECT 1 FROM et_recipe_file r2
            WHERE r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
              AND r2.HANDLER_CONFIG=e.HANDLER_CONFIG
          )
          {session_filter}
    """,
    
    'G_更优未选': """
        SELECT 'G_更优未选' as check_type, l.LOT_ID, l.HANDLER_ID,
               '存在HC+KIT一致候选却分配到120分钟设备' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        JOIN et_recipe_file r ON r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
        WHERE e.HANDLER_CONFIG=r.HANDLER_CONFIG AND e.KIT_PN<>r.KIT_PN
          AND EXISTS (SELECT 1 FROM eqp_status a
                      WHERE a.HANDLER_CONFIG=r.HANDLER_CONFIG AND a.KIT_PN=r.KIT_PN)
          {session_filter}
    """,
    
    'H_跨工序A优先性': """
        SELECT 'H_跨工序A优先性' as check_type, l.LOT_ID, l.HANDLER_ID,
               '存在跨工序A类(HC+KIT一致)候选但未被优先选择' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE EXISTS (
          SELECT 1 FROM et_recipe_file r2
          WHERE r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
            AND EXISTS (
              SELECT 1 FROM eqp_status a
              WHERE a.HANDLER_CONFIG=r2.HANDLER_CONFIG AND a.KIT_PN=r2.KIT_PN
            )
        )
        AND NOT EXISTS (
          SELECT 1 FROM et_recipe_file r3
          WHERE r3.DEVICE=l.DEVICE AND UPPER(r3.STAGE)=UPPER(l.STAGE)
            AND r3.HANDLER_CONFIG=e.HANDLER_CONFIG AND r3.KIT_PN=e.KIT_PN
        )
          {session_filter}
    """,
    
    'J_解析失败但可续排': """
        SELECT 'J_解析失败但可续排' as check_type, f.lot_id as LOT_ID, '' as HANDLER_ID,
               '解析失败但存在同产品同工序设备，应为续排成功' as issue_desc
        FROM scheduling_failed_lots f
        WHERE EXISTS (
          SELECT 1 FROM eqp_status e
          WHERE UPPER(e.DEVICE) COLLATE utf8mb4_unicode_ci = UPPER(f.device) COLLATE utf8mb4_unicode_ci
            AND UPPER(e.STAGE) COLLATE utf8mb4_unicode_ci = UPPER(f.stage) COLLATE utf8mb4_unicode_ci
            AND EXISTS (
              SELECT 1 FROM et_recipe_file r
               WHERE UPPER(r.DEVICE) COLLATE utf8mb4_unicode_ci = UPPER(f.device) COLLATE utf8mb4_unicode_ci
                 AND UPPER(r.STAGE) COLLATE utf8mb4_unicode_ci = UPPER(f.stage) COLLATE utf8mb4_unicode_ci
                 AND r.HANDLER_CONFIG = e.HANDLER_CONFIG AND r.KIT_PN = e.KIT_PN
            )
        )
        {session_filter_failed}
    """,

    'K_候选池计数': """
        SELECT 'K_候选池计数' as check_type, sub.LOT_ID, sub.HANDLER_ID,
               sub.strict_cnt, sub.idle_cnt, sub.cross_A_cnt, sub.cross_B_cnt
        FROM (
            SELECT l.LOT_ID, l.HANDLER_ID,
              (SELECT COUNT(*) FROM eqp_status e
                WHERE UPPER(e.DEVICE)=UPPER(l.DEVICE) AND UPPER(e.STAGE)=UPPER(l.STAGE)) AS strict_cnt,
              (SELECT COUNT(*) FROM eqp_status e
                 JOIN et_recipe_file r2 ON r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
                WHERE (e.DEVICE='' OR e.DEVICE IS NULL) AND (e.STAGE='' OR e.STAGE IS NULL)
                  AND e.HANDLER_CONFIG=r2.HANDLER_CONFIG AND e.KIT_PN=r2.KIT_PN) AS idle_cnt,
              (SELECT COUNT(*) FROM eqp_status e
                 JOIN et_recipe_file r2 ON r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
                WHERE (UPPER(e.DEVICE)<>UPPER(l.DEVICE) OR UPPER(e.STAGE)<>UPPER(l.STAGE))
                  AND e.HANDLER_CONFIG=r2.HANDLER_CONFIG AND e.KIT_PN=r2.KIT_PN) AS cross_A_cnt,
              (SELECT COUNT(*) FROM eqp_status e
                 JOIN et_recipe_file r2 ON r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
                WHERE (UPPER(e.DEVICE)<>UPPER(l.DEVICE) OR UPPER(e.STAGE)<>UPPER(l.STAGE))
                  AND e.HANDLER_CONFIG=r2.HANDLER_CONFIG AND (e.KIT_PN<>r2.KIT_PN OR e.KIT_PN='' OR e.KIT_PN IS NULL)) AS cross_B_cnt
            FROM lotprioritydone l
            WHERE 1=1 {session_filter}
        ) sub
    """,

    'L_跨工序A类应命中': """
        SELECT 'L_跨工序A类应命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '应为跨工序A类匹配(60分钟)但可能未正确分类' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE (UPPER(e.DEVICE)<>UPPER(l.DEVICE) OR UPPER(e.STAGE)<>UPPER(l.STAGE))
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
             WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
               AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
          )
          AND l.match_type NOT IN ('跨工序A类匹配')
          {session_filter}
    """,

    'M_跨工序B类应命中': """
        SELECT 'M_跨工序B类应命中' as check_type, l.LOT_ID, l.HANDLER_ID,
               '应为跨工序B类匹配(90分钟)但可能未正确分类' as issue_desc
        FROM lotprioritydone l
        JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
        WHERE (UPPER(e.DEVICE)<>UPPER(l.DEVICE) OR UPPER(e.STAGE)<>UPPER(l.STAGE))
          AND EXISTS (
            SELECT 1 FROM et_recipe_file r
             WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
               AND r.HANDLER_CONFIG=e.HANDLER_CONFIG
          )
          AND NOT EXISTS (
            SELECT 1 FROM et_recipe_file r2
             WHERE r2.DEVICE=l.DEVICE AND UPPER(r2.STAGE)=UPPER(l.STAGE)
               AND r2.HANDLER_CONFIG=e.HANDLER_CONFIG AND r2.KIT_PN=e.KIT_PN
          )
          AND l.match_type NOT IN ('跨工序B类匹配')
          {session_filter}
    """
}

# 设备内优先级检查（I）- 单独处理，因为涉及窗口函数
SQL_PRIORITY_CHECK = """
    WITH p AS (
      SELECT l.*, COALESCE(lp.priority,dp.priority,2) eff_p,
             ROW_NUMBER() OVER(PARTITION BY l.HANDLER_ID
               ORDER BY COALESCE(lp.priority,dp.priority,2), l.CREATE_TIME) AS rn
      FROM lotprioritydone l
      LEFT JOIN lotpriorityconfig lp ON lp.LOT_ID=l.LOT_ID
      LEFT JOIN devicepriorityconfig dp ON dp.DEVICE=l.DEVICE
      {session_where}
    )
    SELECT 'I_设备内优先级' as check_type, a.LOT_ID, a.HANDLER_ID,
           CONCAT('优先级违规：', a.LOT_ID, '应排在', b.LOT_ID, '之前') as issue_desc
    FROM p a JOIN p b ON a.HANDLER_ID=b.HANDLER_ID AND a.rn=b.rn+1
    WHERE a.eff_p>b.eff_p OR (a.eff_p=b.eff_p AND a.CREATE_TIME>b.CREATE_TIME)
"""

def build_session_filter(session_id: Optional[str] = None, date_filter: Optional[str] = None) -> tuple:
    """构建会话/日期过滤条件"""
    if session_id:
        return f"AND l.SESSION_ID = '{session_id}'", f"AND f.SESSION_ID = '{session_id}'", f"WHERE l.SESSION_ID = '{session_id}'"
    elif date_filter:
        return (f"AND DATE(l.CREATE_TIME) = '{date_filter}'", 
                f"AND DATE(f.timestamp) = '{date_filter}'",
                f"WHERE DATE(l.CREATE_TIME) = '{date_filter}'")
    else:
        return "", "", ""

def execute_sql_checks(session_id: Optional[str] = None, date_filter: Optional[str] = None) -> List[Dict]:
    """执行所有SQL检查"""
    session_filter, session_filter_failed, session_where = build_session_filter(session_id, date_filter)
    
    all_issues = []
    
    with get_db_cursor('aps', autocommit=True) as cur:
        # 执行A~H, J检查
        for check_name, sql_template in SQL_CHECKS.items():
            try:
                sql = sql_template.format(
                    session_filter=session_filter,
                    session_filter_failed=session_filter_failed
                )
                cur.execute(sql)
                results = cur.fetchall() or []

                if check_name == 'K_候选池计数':
                    # 将候选池审计结果输出到独立CSV，不计入问题项统计
                    audit_path = os.path.join(OUTPUT_DIR, 'sql_candidate_pool_audit.csv')
                    audit_headers = ['LOT_ID', 'HANDLER_ID', 'strict_cnt', 'idle_cnt', 'cross_A_cnt', 'cross_B_cnt']
                    with open(audit_path, 'w', newline='', encoding='utf-8-sig') as af:
                        aw = csv.DictWriter(af, fieldnames=audit_headers)
                        aw.writeheader()
                        for row in results:
                            aw.writerow({
                                'LOT_ID': row.get('LOT_ID', ''),
                                'HANDLER_ID': row.get('HANDLER_ID', ''),
                                'strict_cnt': row.get('strict_cnt', 0),
                                'idle_cnt': row.get('idle_cnt', 0),
                                'cross_A_cnt': row.get('cross_A_cnt', 0),
                                'cross_B_cnt': row.get('cross_B_cnt', 0),
                            })
                    print(f"[K_候选池计数] 已输出审计CSV: {audit_path}（不计入问题项）")
                    continue

                for row in results:
                    all_issues.append({
                        'check_type': row.get('check_type', check_name),
                        'LOT_ID': row.get('LOT_ID', ''),
                        'HANDLER_ID': row.get('HANDLER_ID', ''),
                        'issue_desc': row.get('issue_desc', ''),
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

                print(f"[{check_name}] 发现 {len(results)} 个问题项")

            except Exception as e:
                print(f"[ERROR] {check_name} 执行失败: {e}")
                all_issues.append({
                    'check_type': check_name,
                    'LOT_ID': 'ERROR',
                    'HANDLER_ID': '',
                    'issue_desc': f'SQL执行失败: {str(e)}',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 执行I检查（设备内优先级）
        try:
            sql_i = SQL_PRIORITY_CHECK.format(session_where=session_where)
            cur.execute(sql_i)
            results = cur.fetchall() or []
            
            for row in results:
                all_issues.append({
                    'check_type': row.get('check_type', 'I_设备内优先级'),
                    'LOT_ID': row.get('LOT_ID', ''),
                    'HANDLER_ID': row.get('HANDLER_ID', ''),
                    'issue_desc': row.get('issue_desc', ''),
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
            print(f"[I_设备内优先级] 发现 {len(results)} 个问题项")
            
        except Exception as e:
            print(f"[ERROR] I_设备内优先级 执行失败: {e}")
            all_issues.append({
                'check_type': 'I_设备内优先级',
                'LOT_ID': 'ERROR',
                'HANDLER_ID': '',
                'issue_desc': f'SQL执行失败: {str(e)}',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    return all_issues

def main():
    parser = argparse.ArgumentParser(description='排产结果验收标准 - SQL巡检工具')
    parser.add_argument('--session-id', help='指定会话ID进行过滤')
    parser.add_argument('--date', help='指定日期进行过滤 (格式: YYYY-MM-DD)')
    parser.add_argument('--output', default='sql_checks_issues.csv', help='输出文件名')
    
    args = parser.parse_args()
    
    print("🔍 开始执行排产结果验收标准SQL巡检...")
    print(f"过滤条件: 会话ID={args.session_id}, 日期={args.date}")
    
    # 执行检查
    issues = execute_sql_checks(args.session_id, args.date)
    
    # 输出CSV
    output_path = os.path.join(OUTPUT_DIR, args.output)
    headers = ['check_type', 'LOT_ID', 'HANDLER_ID', 'issue_desc', 'timestamp']
    
    with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(issues)
    
    # 统计汇总
    total_issues = len(issues)
    error_count = len([i for i in issues if i['LOT_ID'] == 'ERROR'])
    valid_issues = total_issues - error_count
    
    print(f"\n📊 检查完成统计:")
    print(f"总问题项: {total_issues}")
    print(f"有效问题: {valid_issues}")
    print(f"执行错误: {error_count}")
    print(f"输出文件: {output_path}")
    
    if valid_issues > 0:
        print(f"\n⚠️  发现 {valid_issues} 个潜在问题，请查看CSV详情进行核实")
    else:
        print(f"\n✅ 未发现问题，排产逻辑符合验收标准")

if __name__ == '__main__':
    main()
