#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据匹配测试脚本
检查实际数据库中的匹配情况，验证六类匹配规则的实际效果
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.services.real_scheduling_service import RealSchedulingService
from app.utils.db_connection_pool import get_db_connection

def test_real_data_availability():
    """测试真实数据的可用性"""
    print("=== 测试真实数据可用性 ===")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 1. 检查et_wait_lot数据
        cursor.execute("SELECT COUNT(*) as count FROM et_wait_lot")
        wait_lot_count = cursor.fetchone()['count']
        print(f"   et_wait_lot 批次数量: {wait_lot_count}")
        
        if wait_lot_count > 0:
            cursor.execute("SELECT DEVICE, STAGE, LOT_ID FROM et_wait_lot LIMIT 5")
            sample_lots = cursor.fetchall()
            print("   样例批次:")
            for lot in sample_lots:
                print(f"     - {lot['LOT_ID']}: {lot['DEVICE']}-{lot['STAGE']}")
        
        # 2. 检查et_ft_test_spec数据
        cursor.execute("SELECT COUNT(*) as count FROM et_ft_test_spec")
        test_spec_count = cursor.fetchone()['count']
        print(f"   et_ft_test_spec 规格数量: {test_spec_count}")
        
        if test_spec_count > 0:
            cursor.execute("SELECT DEVICE, STAGE, TESTER, HB_PN, TB_PN FROM et_ft_test_spec LIMIT 5")
            sample_specs = cursor.fetchall()
            print("   样例规格:")
            for spec in sample_specs:
                print(f"     - {spec['DEVICE']}-{spec['STAGE']}: TESTER={spec.get('TESTER', 'N/A')}, HB={spec.get('HB_PN', 'N/A')}, TB={spec.get('TB_PN', 'N/A')}")
        
        # 3. 检查et_recipe_file数据
        cursor.execute("SELECT COUNT(*) as count FROM et_recipe_file")
        recipe_count = cursor.fetchone()['count']
        print(f"   et_recipe_file 配方数量: {recipe_count}")
        
        if recipe_count > 0:
            cursor.execute("SELECT DEVICE, STAGE, HANDLER_CONFIG, KIT_PN FROM et_recipe_file LIMIT 5")
            sample_recipes = cursor.fetchall()
            print("   样例配方:")
            for recipe in sample_recipes:
                print(f"     - {recipe['DEVICE']}-{recipe['STAGE']}: CONFIG={recipe.get('HANDLER_CONFIG', 'N/A')}, KIT={recipe.get('KIT_PN', 'N/A')}")
        
        # 4. 检查eqp_status数据
        cursor.execute("SELECT COUNT(*) as count FROM eqp_status")
        eqp_count = cursor.fetchone()['count']
        print(f"   eqp_status 设备数量: {eqp_count}")
        
        if eqp_count > 0:
            cursor.execute("SELECT HANDLER_ID, DEVICE, STAGE, HANDLER_CONFIG, KIT_PN, TESTER, HB_PN, TB_PN FROM eqp_status LIMIT 5")
            sample_eqps = cursor.fetchall()
            print("   样例设备:")
            for eqp in sample_eqps:
                print(f"     - {eqp['HANDLER_ID']}: {eqp['DEVICE']}-{eqp['STAGE']}")
                print(f"       CONFIG={eqp.get('HANDLER_CONFIG', 'N/A')}, KIT={eqp.get('KIT_PN', 'N/A')}, TESTER={eqp.get('TESTER', 'N/A')}")
        
        cursor.close()
        conn.close()
        
        return wait_lot_count, test_spec_count, recipe_count, eqp_count
        
    except Exception as e:
        print(f"✗ 数据可用性检查失败: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0, 0

def test_real_matching_logic():
    """测试真实数据的匹配逻辑"""
    print("\n=== 测试真实数据匹配逻辑 ===")
    
    try:
        # 初始化服务
        service = RealSchedulingService()
        
        # 获取预加载数据
        print("   正在预加载数据...")
        preloaded_data = service._smart_preload_strategy()
        
        print(f"   预加载数据统计:")
        print(f"     - wait_lots: {len(preloaded_data.get('wait_lots', []))}")
        print(f"     - equipment_status: {len(preloaded_data.get('equipment_status', []))}")
        print(f"     - test_specs: {len(preloaded_data.get('test_specs', []))}")
        print(f"     - recipe_files: {len(preloaded_data.get('recipe_files', []))}")
        
        # 取前5个待排产批次进行测试
        wait_lots = preloaded_data.get('wait_lots', [])[:5]
        equipment_status = preloaded_data.get('equipment_status', [])
        
        if not wait_lots:
            print("   ⚠️ 没有待排产批次数据")
            return
        
        if not equipment_status:
            print("   ⚠️ 没有设备状态数据")
            return
        
        print(f"\n   测试前{len(wait_lots)}个批次的匹配情况:")
        
        for lot in wait_lots:
            lot_id = lot.get('LOT_ID', 'Unknown')
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            
            print(f"\n   📦 批次 {lot_id} ({device}-{stage}):")
            
            # 获取批次需求
            lot_requirements = service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
            if not lot_requirements:
                print(f"     ❌ 无法获取配置需求")
                continue
            
            # 查找合适设备
            equipment_candidates = service.find_suitable_equipment_optimized(lot, lot_requirements, preloaded_data)
            
            if not equipment_candidates:
                print(f"     ❌ 无合适设备")
                
                # 生成失败详情
                failure_details = service._generate_failure_details(lot, lot_requirements, preloaded_data)
                details_obj = json.loads(failure_details)
                print(f"     失败原因: {json.dumps(details_obj, ensure_ascii=False)}")
            else:
                print(f"     ✅ 找到 {len(equipment_candidates)} 个候选设备:")
                for i, candidate in enumerate(equipment_candidates[:3]):  # 只显示前3个
                    eqp = candidate['equipment']
                    print(f"       {i+1}. {eqp['HANDLER_ID']}: {candidate['match_type']}({candidate['comprehensive_score']:.1f}分, {candidate['changeover_time']}分钟)")
        
        print("\n✅ 真实数据匹配测试完成")
        
    except Exception as e:
        print(f"✗ 真实数据匹配测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_specific_matching_case():
    """测试特定的匹配案例"""
    print("\n=== 测试特定匹配案例 ===")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 找一个有完整配置的DEVICE+STAGE组合
        cursor.execute("""
            SELECT w.DEVICE, w.STAGE, COUNT(*) as lot_count
            FROM et_wait_lot w
            WHERE EXISTS (
                SELECT 1 FROM et_ft_test_spec t 
                WHERE t.DEVICE = w.DEVICE AND t.STAGE = w.STAGE
            )
            AND EXISTS (
                SELECT 1 FROM et_recipe_file r 
                WHERE r.DEVICE = w.DEVICE AND r.STAGE = w.STAGE
            )
            AND EXISTS (
                SELECT 1 FROM eqp_status e 
                WHERE e.DEVICE = w.DEVICE AND e.STAGE = w.STAGE
            )
            GROUP BY w.DEVICE, w.STAGE
            ORDER BY lot_count DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("   ⚠️ 没有找到有完整配置的DEVICE+STAGE组合")
            cursor.close()
            conn.close()
            return
        
        test_device = result['DEVICE']
        test_stage = result['STAGE']
        lot_count = result['lot_count']
        
        print(f"   测试组合: {test_device}-{test_stage} (有{lot_count}个批次)")
        
        # 获取该组合的详细信息
        cursor.execute("SELECT * FROM et_ft_test_spec WHERE DEVICE = %s AND STAGE = %s LIMIT 1", (test_device, test_stage))
        test_spec = cursor.fetchone()
        
        cursor.execute("SELECT * FROM et_recipe_file WHERE DEVICE = %s AND STAGE = %s LIMIT 1", (test_device, test_stage))
        recipe = cursor.fetchone()
        
        cursor.execute("SELECT * FROM eqp_status WHERE DEVICE = %s AND STAGE = %s LIMIT 3", (test_device, test_stage))
        equipments = cursor.fetchall()
        
        print(f"   规格配置: TESTER={test_spec.get('TESTER', 'N/A')}, HB_PN={test_spec.get('HB_PN', 'N/A')}, TB_PN={test_spec.get('TB_PN', 'N/A')}")
        print(f"   配方配置: HANDLER_CONFIG={recipe.get('HANDLER_CONFIG', 'N/A')}, KIT_PN={recipe.get('KIT_PN', 'N/A')}")
        print(f"   可用设备: {len(equipments)}个")
        
        for eqp in equipments:
            print(f"     - {eqp['HANDLER_ID']}: CONFIG={eqp.get('HANDLER_CONFIG', 'N/A')}, KIT={eqp.get('KIT_PN', 'N/A')}, TESTER={eqp.get('TESTER', 'N/A')}")
        
        cursor.close()
        conn.close()
        
        print("✅ 特定匹配案例分析完成")
        
    except Exception as e:
        print(f"✗ 特定匹配案例测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 真实数据匹配测试开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 检查数据可用性
    wait_count, spec_count, recipe_count, eqp_count = test_real_data_availability()
    
    if wait_count == 0:
        print("\n❌ 没有待排产批次，无法进行匹配测试")
    elif spec_count == 0:
        print("\n❌ 没有测试规格数据，无法进行匹配测试")
    elif recipe_count == 0:
        print("\n❌ 没有配方数据，无法进行匹配测试")
    elif eqp_count == 0:
        print("\n❌ 没有设备状态数据，无法进行匹配测试")
    else:
        # 2. 测试真实匹配逻辑
        test_real_matching_logic()
        
        # 3. 测试特定案例
        test_specific_matching_case()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")
