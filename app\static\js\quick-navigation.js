/**
 * 真正有效的快速导航系统
 * 解决服务器慢导致的菜单切换问题
 * 通过激进预加载和用户体验优化，让2秒变成瞬间响应
 */
class QuickNavigation {
    constructor() {
        this.preloadCache = new Map();
        this.preloadQueue = [];
        this.isPreloading = false;
        this.hoverTimer = null;
        this.clickStartTime = null;
        this.progressBar = null;
        this.preloadIframes = new Set();

        this.init();
    }
    
    init() {
        this.setupProgressBar();
        this.setupAggressivePreload();
        this.setupInstantFeedback();
        this.setupClickOptimization();
        this.setupCacheOptimization();
        this.preloadCommonPages();

        console.log('🚀 真正有效的快速导航系统已启动');
        console.log('💡 策略: 激进预加载 + 即时反馈 + 用户体验优化');
    }
    
    /**
     * 激进预加载策略 - 解决2秒服务器响应问题
     */
    setupAggressivePreload() {
        // 1. 鼠标悬停立即预加载（不等待）
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('.nav-link[href]:not([href="#"])');
            if (link && !this.preloadCache.has(link.href)) {
                // 立即开始预加载，不延迟
                this.preloadPage(link.href, 'hover');
                console.log(`🖱️ 悬停预加载: ${link.href}`);
            }
        });

        // 2. 鼠标进入菜单区域时预加载所有可见链接
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.addEventListener('mouseenter', () => {
                this.preloadVisibleLinks();
            });
        }

        // 3. 页面空闲时预加载常用页面
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.preloadCommonPages();
            });
        } else {
            setTimeout(() => this.preloadCommonPages(), 2000);
        }
    }
    
    /**
     * 即时反馈系统 - 让用户感觉系统很快
     */
    setupInstantFeedback() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('.nav-link[href]:not([href="#"])');
            if (link) {
                // 立即显示反馈，不等待服务器响应
                this.showInstantFeedback(link);
                this.showProgressBar();

                // 如果已预加载，显示特殊提示
                if (this.preloadCache.has(link.href)) {
                    this.showPreloadedHint();
                    console.log(`⚡ 使用预加载页面: ${link.href}`);
                } else {
                    console.log(`🔄 正在加载: ${link.href}`);
                }
            }
        });
    }

    /**
     * 显示即时反馈
     */
    showInstantFeedback(link) {
        // 1. 立即改变链接样式
        link.style.cssText += `
            background-color: rgba(183, 36, 36, 0.1) !important;
            transform: scale(0.98);
            transition: all 0.1s ease;
        `;

        // 2. 添加加载图标
        const originalHTML = link.innerHTML;
        link.innerHTML = originalHTML + ' <i class="fas fa-spinner fa-spin" style="margin-left: 5px;"></i>';

        // 3. 设置活动状态
        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
    }

    /**
     * 显示预加载提示
     */
    showPreloadedHint() {
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        hint.innerHTML = '⚡ 预加载加速中...';
        document.body.appendChild(hint);

        setTimeout(() => {
            hint.remove();
        }, 1500);
    }
    
    /**
     * 高效预加载页面 - 针对2秒服务器响应优化
     */
    async preloadPage(url, priority = 'normal') {
        if (this.preloadCache.has(url)) return;

        try {
            // 标记为预加载中
            this.preloadCache.set(url, 'loading');
            const startTime = performance.now();

            // 使用多种策略并行预加载
            const strategies = [];

            // 策略1: 隐藏iframe预加载（最有效）
            strategies.push(this.preloadWithIframe(url));

            // 策略2: fetch预加载（备用）
            if (priority === 'high') {
                strategies.push(this.preloadWithFetch(url));
            }

            // 等待任一策略成功
            const result = await Promise.race(strategies);

            const loadTime = performance.now() - startTime;
            this.preloadCache.set(url, {
                preloaded: true,
                timestamp: Date.now(),
                loadTime: loadTime,
                strategy: result.strategy
            });

            console.log(`🔥 预加载完成: ${url} (${loadTime.toFixed(1)}ms, ${result.strategy})`);

        } catch (error) {
            console.warn(`⚠️ 预加载失败: ${url}`, error);
            this.preloadCache.delete(url);
        }
    }

    /**
     * iframe预加载策略
     */
    async preloadWithIframe(url) {
        return new Promise((resolve, reject) => {
            const iframe = document.createElement('iframe');
            iframe.style.cssText = `
                position: absolute;
                left: -10000px;
                top: -10000px;
                width: 1px;
                height: 1px;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
            `;

            const timeout = setTimeout(() => {
                this.cleanupIframe(iframe);
                reject(new Error('iframe预加载超时'));
            }, 8000); // 8秒超时，给服务器足够时间

            iframe.onload = () => {
                clearTimeout(timeout);
                this.preloadIframes.add(iframe);

                // 延迟清理iframe，保持预加载效果
                setTimeout(() => {
                    this.cleanupIframe(iframe);
                }, 3000);

                resolve({ strategy: 'iframe' });
            };

            iframe.onerror = () => {
                clearTimeout(timeout);
                this.cleanupIframe(iframe);
                reject(new Error('iframe预加载失败'));
            };

            document.body.appendChild(iframe);
            iframe.src = url;
        });
    }

    /**
     * fetch预加载策略
     */
    async preloadWithFetch(url) {
        const response = await fetch(url, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Preload': 'true'
            }
        });

        if (!response.ok) {
            throw new Error(`fetch预加载失败: ${response.status}`);
        }

        return { strategy: 'fetch' };
    }

    /**
     * 清理iframe
     */
    cleanupIframe(iframe) {
        if (iframe && iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
            this.preloadIframes.delete(iframe);
        }
    }
    
    /**
     * 预加载可见链接
     */
    preloadVisibleLinks() {
        const links = document.querySelectorAll('.nav-link[href]:not([href="#"])');
        const visibleLinks = Array.from(links).filter(link => {
            const rect = link.getBoundingClientRect();
            return rect.top >= 0 && rect.bottom <= window.innerHeight;
        });

        console.log(`📋 预加载${visibleLinks.length}个可见链接`);

        visibleLinks.forEach((link, index) => {
            // 错开预加载，避免同时发起太多请求
            setTimeout(() => {
                if (!this.preloadCache.has(link.href)) {
                    this.preloadPage(link.href, 'batch');
                }
            }, index * 500); // 每500ms预加载一个
        });
    }

    /**
     * 预加载常用页面
     */
    preloadCommonPages() {
        const commonPages = [
            '/',
            '/production/semi-auto',
            '/orders/semi-auto',
            '/api/v3/universal/et_wait_lot'
        ];

        console.log('🎯 预加载常用页面...');

        commonPages.forEach((url, index) => {
            setTimeout(() => {
                if (!this.preloadCache.has(url)) {
                    this.preloadPage(url, 'common');
                }
            }, index * 1000); // 每1秒预加载一个
        });
    }

    /**
     * 缓存优化
     */
    setupCacheOptimization() {
        // 清理过期缓存
        setInterval(() => {
            this.cleanupCache();
        }, 60000); // 每分钟清理一次

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    /**
     * 设置进度条
     */
    setupProgressBar() {
        // 创建更明显的进度条
        this.progressBar = document.createElement('div');
        this.progressBar.id = 'quick-nav-progress';
        this.progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #b72424, #d73027, #ff6b6b);
            z-index: 10000;
            transition: width 0.3s ease;
            opacity: 0;
            box-shadow: 0 0 10px rgba(183, 36, 36, 0.5);
        `;
        document.body.appendChild(this.progressBar);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .nav-link.loading {
                position: relative;
                overflow: hidden;
            }

            .nav-link.loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                animation: shimmer 1.5s infinite;
            }

            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 显示进度条
     */
    showProgressBar() {
        if (this.progressBar) {
            this.progressBar.style.opacity = '1';
            this.progressBar.style.width = '20%';

            // 模拟进度
            setTimeout(() => {
                this.progressBar.style.width = '50%';
            }, 200);

            setTimeout(() => {
                this.progressBar.style.width = '80%';
            }, 800);

            // 2秒后完成（匹配服务器响应时间）
            setTimeout(() => {
                this.progressBar.style.width = '100%';
                setTimeout(() => {
                    this.progressBar.style.opacity = '0';
                    this.progressBar.style.width = '0%';
                }, 300);
            }, 2000);
        }
    }

    /**
     * 点击优化
     */
    setupClickOptimization() {
        // 鼠标按下时立即开始高优先级预加载
        document.addEventListener('mousedown', (e) => {
            const link = e.target.closest('.nav-link[href]:not([href="#"])');
            if (link && e.button === 0) { // 左键
                this.clickStartTime = performance.now();

                // 立即开始高优先级预加载
                if (!this.preloadCache.has(link.href)) {
                    this.preloadPage(link.href, 'high');
                }

                // 添加视觉反馈
                link.classList.add('loading');
            }
        });
    }
    
    showProgress() {
        const progressBar = document.getElementById('quick-nav-progress');
        if (progressBar) {
            progressBar.style.opacity = '1';
            progressBar.style.width = '30%';
            
            // 模拟进度
            setTimeout(() => {
                progressBar.style.width = '60%';
            }, 100);
            
            setTimeout(() => {
                progressBar.style.width = '90%';
            }, 300);
        }
    }
    
    hideProgress() {
        const progressBar = document.getElementById('quick-nav-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            setTimeout(() => {
                progressBar.style.opacity = '0';
                progressBar.style.width = '0%';
            }, 200);
        }
    }
    
    /**
     * 清理过期缓存
     */
    cleanupCache() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5分钟
        
        for (const [url, data] of this.preloadCache.entries()) {
            if (typeof data === 'object' && data.timestamp && (now - data.timestamp) > maxAge) {
                this.preloadCache.delete(url);
                console.log(`🧹 清理过期缓存: ${url}`);
            }
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            cacheSize: this.preloadCache.size,
            preloadedPages: 0,
            preheatedPages: 0
        };
        
        for (const [url, data] of this.preloadCache.entries()) {
            if (typeof data === 'object') {
                if (data.html) {
                    stats.preloadedPages++;
                } else if (data.preheated) {
                    stats.preheatedPages++;
                }
            }
        }
        
        return stats;
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
        }
        this.preloadCache.clear();
        console.log('🧹 快速导航系统已清理');
    }
}

/**
 * 资源缓存优化器
 */
class ResourceCacheOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.optimizeStaticResources();
        this.setupServiceWorker();
        console.log('📦 资源缓存优化器已启动');
    }
    
    /**
     * 优化静态资源缓存
     */
    optimizeStaticResources() {
        // 预加载关键CSS
        const criticalCSS = [
            '/static/vendor/bootstrap/bootstrap.min.css',
            '/static/vendor/fontawesome/all.min.css'
        ];
        
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            link.onload = () => {
                link.rel = 'stylesheet';
            };
            document.head.appendChild(link);
        });
        
        // 预加载关键JavaScript
        const criticalJS = [
            '/static/vendor/bootstrap/bootstrap.bundle.min.js',
            '/static/js/unified_api_client.js'
        ];
        
        criticalJS.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'script';
            link.href = src;
            document.head.appendChild(link);
        });
    }
    
    /**
     * 设置Service Worker（如果支持）
     */
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/js/sw.js')
                .then(registration => {
                    console.log('📡 Service Worker注册成功');
                })
                .catch(error => {
                    console.log('📡 Service Worker注册失败:', error);
                });
        }
    }
}

/**
 * 页面性能监控器
 */
class PagePerformanceMonitor {
    constructor() {
        this.metrics = {
            navigationStart: 0,
            loadComplete: 0,
            domReady: 0,
            firstPaint: 0
        };
        
        this.init();
    }
    
    init() {
        this.collectMetrics();
        this.setupPerformanceObserver();
        console.log('📊 页面性能监控器已启动');
    }
    
    collectMetrics() {
        // 收集导航时间
        if (performance.timing) {
            const timing = performance.timing;
            this.metrics.navigationStart = timing.navigationStart;
            this.metrics.domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
            this.metrics.loadComplete = timing.loadEventEnd - timing.navigationStart;
        }
        
        // 收集Paint时间
        if (performance.getEntriesByType) {
            const paintEntries = performance.getEntriesByType('paint');
            paintEntries.forEach(entry => {
                if (entry.name === 'first-paint') {
                    this.metrics.firstPaint = entry.startTime;
                }
            });
        }
    }
    
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'navigation') {
                        console.log(`📊 页面加载性能:`, {
                            domReady: `${entry.domContentLoadedEventEnd.toFixed(1)}ms`,
                            loadComplete: `${entry.loadEventEnd.toFixed(1)}ms`,
                            transferSize: `${(entry.transferSize / 1024).toFixed(1)}KB`
                        });
                    }
                }
            });
            
            observer.observe({entryTypes: ['navigation']});
        }
    }
    
    getMetrics() {
        return this.metrics;
    }
}

// 全局初始化
let quickNavigation, resourceOptimizer, performanceMonitor;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化快速导航系统
    quickNavigation = new QuickNavigation();
    
    // 初始化资源缓存优化器
    resourceOptimizer = new ResourceCacheOptimizer();
    
    // 初始化性能监控器
    performanceMonitor = new PagePerformanceMonitor();
    
    console.log('🎉 快速导航优化系统已全部启动');
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (quickNavigation) {
        quickNavigation.cleanup();
    }
});

// 全局访问接口
window.quickNavigation = {
    getStats: () => quickNavigation ? quickNavigation.getStats() : null,
    getMetrics: () => performanceMonitor ? performanceMonitor.getMetrics() : null,
    preloadPage: (url) => quickNavigation ? quickNavigation.preloadPage(url, true) : null
};
