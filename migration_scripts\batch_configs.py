#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 批次配置文件
定义所有迁移批次的配置和策略
"""

from datetime import datetime

# 迁移批次配置
MIGRATION_BATCHES = {
    1: {
        'name': '低风险配置表',
        'description': '迁移空表和低风险配置表',
        'priority': 'high',
        'risk_level': 'low',
        'tables': [
            'settings',
            'database_info', 
            'scheduling_tasks',
            'user_filter_presets'
        ],
        'models': [
            'Settings',
            'DatabaseInfo',
            'SchedulingTasks',
            'UserFilterPresets'
        ],
        'validation_tests': [
            'test_basic_connectivity',
            'test_table_structure'
        ]
    },
    
    2: {
        'name': '系统配置表',
        'description': '迁移系统设置和AI配置表',
        'priority': 'high',
        'risk_level': 'medium',
        'tables': [
            'system_settings',
            'ai_settings',
            'algorithm_weights',
            'scheduling_config'
        ],
        'models': [
            'SystemSetting',
            'AISettings',
            'AlgorithmWeights',
            'SchedulingConfig'
        ],
        'validation_tests': [
            'test_system_settings_api',
            'test_ai_settings_functionality',
            'test_scheduling_config'
        ]
    },
    
    3: {
        'name': '历史和日志表',
        'description': '迁移历史记录和日志表',
        'priority': 'medium',
        'risk_level': 'low',
        'tables': [
            'migration_log',
            'scheduling_history'
        ],
        'models': [
            'MigrationLog',
            'SchedulingHistory'
        ],
        'validation_tests': [
            'test_log_functionality',
            'test_history_queries'
        ]
    },
    
    4: {
        'name': '权限和用户管理表',
        'description': '迁移用户权限和菜单权限表',
        'priority': 'high',
        'risk_level': 'high',
        'tables': [
            'user_permissions',
            'menu_permissions'
        ],
        'models': [
            'UserPermission',
            'MenuPermission'
        ],
        'validation_tests': [
            'test_user_authentication',
            'test_permission_system',
            'test_menu_access',
            'test_role_based_access'
        ]
    },
    
    5: {
        'name': '邮件和文件管理表',
        'description': '迁移邮件配置和附件表',
        'priority': 'medium',
        'risk_level': 'medium',
        'tables': [
            'email_configs',
            'email_attachments'
        ],
        'models': [
            'EmailConfig',
            'EmailAttachment'
        ],
        'validation_tests': [
            'test_email_functionality',
            'test_attachment_handling'
        ]
    },
    
    6: {
        'name': '数据库配置表',
        'description': '迁移数据库配置和映射表',
        'priority': 'low',
        'risk_level': 'high',
        'tables': [
            'database_configs',
            'database_mappings'
        ],
        'models': [
            'DatabaseConfig',
            'DatabaseMapping'
        ],
        'validation_tests': [
            'test_database_config_functionality',
            'test_mapping_system'
        ]
    },
    
    7: {
        'name': '设备优先级数据补全',
        'description': '补全设备优先级配置数据',
        'priority': 'high',
        'risk_level': 'medium',
        'tables': [
            'devicepriorityconfig'
        ],
        'models': [
            'DevicePriorityConfig'
        ],
        'validation_tests': [
            'test_device_priority_functionality',
            'test_priority_algorithm'
        ],
        'special_operation': 'data_completion'  # 特殊标记：数据补全而非迁移
    }
}

# 模型绑定配置更新批次
MODEL_BINDING_BATCHES = {
    1: {
        'name': '基础模型绑定更新',
        'models': ['Settings', 'DatabaseInfo', 'SchedulingTasks', 'UserFilterPresets']
    },
    2: {
        'name': '系统配置模型绑定更新', 
        'models': ['SystemSetting', 'AISettings', 'AlgorithmWeights', 'SchedulingConfig']
    },
    3: {
        'name': '日志模型绑定更新',
        'models': ['MigrationLog', 'SchedulingHistory']
    },
    4: {
        'name': '权限模型绑定更新',
        'models': ['User', 'UserPermission', 'UserActionLog', 'MenuSetting']
    },
    5: {
        'name': '邮件模型绑定更新',
        'models': ['EmailConfig', 'ExcelMapping', 'EmailAttachment']
    },
    6: {
        'name': '其他模型绑定更新',
        'models': ['OrderData', 'LotTypeClassificationRule', 'SchedulerJob', 'SchedulerJobLog', 'SchedulerConfig']
    }
}

# 验证测试配置
VALIDATION_CONFIG = {
    'data_integrity_checks': [
        'record_count_match',
        'field_completeness',
        'foreign_key_integrity',
        'data_type_consistency'
    ],
    'functionality_tests': [
        'api_endpoints',
        'page_rendering',
        'user_operations',
        'system_operations'
    ],
    'performance_benchmarks': {
        'max_response_time': 2000,  # ms
        'max_memory_increase': 20,  # %
        'max_connection_count': 50
    }
}

# 回滚配置
ROLLBACK_CONFIG = {
    'auto_rollback_conditions': [
        'data_integrity_failure',
        'critical_functionality_failure',
        'performance_degradation_threshold'
    ],
    'backup_retention_days': 30,
    'rollback_timeout_minutes': 10
}

# 数据库连接配置
DATABASE_CONFIG = {
    'source_db': 'aps_system',
    'target_db': 'aps',
    'connection_params': {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'WWWwww123!',
        'charset': 'utf8mb4'
    }
}

def get_batch_config(batch_number):
    """获取指定批次的配置"""
    return MIGRATION_BATCHES.get(batch_number)

def get_all_batches():
    """获取所有批次配置"""
    return MIGRATION_BATCHES

def get_model_binding_batch(batch_number):
    """获取模型绑定更新批次配置"""
    return MODEL_BINDING_BATCHES.get(batch_number)

def get_validation_config():
    """获取验证配置"""
    return VALIDATION_CONFIG

def get_rollback_config():
    """获取回滚配置"""
    return ROLLBACK_CONFIG

def get_database_config():
    """获取数据库配置"""
    return DATABASE_CONFIG 