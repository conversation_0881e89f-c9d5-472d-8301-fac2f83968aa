# 测试脚本上下文管理规则

## 目标
彻底解决测试脚本中反复出现的Flask应用上下文错误、导入问题和认证错误，建立标准化的测试脚本模板。

## 根本问题分析

### 1. Flask应用上下文问题
- **问题**: 测试脚本无法正确创建Flask应用上下文
- **原因**: 错误的导入方式和应用实例化方法
- **解决方案**: 使用标准化的应用创建模式

### 2. 导入错误问题
- **问题**: 模块路径错误，无法正确导入应用组件
- **原因**: 相对导入路径问题和环境配置不当
- **解决方案**: 统一路径管理和导入策略

### 3. 数据库连接问题
- **问题**: 测试中数据库连接失败或超时
- **原因**: 连接池配置和上下文管理不当
- **解决方案**: 标准化的数据库访问模式

## 标准化测试脚本模板

### 基础模板结构

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[测试名称] - [测试描述]
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
# 确保在项目根目录执行
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置 (标准化格式)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'{测试名称}_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestName')

def test_main_function():
    """主测试函数"""
    
    print("🧪 [测试标题]")
    print("=" * 60)
    
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 6. 测试逻辑开始
            # 在这里编写具体的测试代码
            
            # 7. 测试结果返回
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    print(f"🚀 [测试名称]")
    print(f"测试时间: {datetime.now()}")
    print("=" * 60)
    
    success = test_main_function()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 [测试名称]: 通过")
    else:
        print("❌ [测试名称]: 失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
```

## 关键规则和最佳实践

### Rule 1: 路径和导入管理
```python
# ✅ 正确的路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# ✅ 正确的Flask应用导入
from app import create_app
app, socketio = create_app()

# ❌ 错误的导入方式
from run import app  # 会导致循环导入
import run; app = run.create_app()  # 不稳定
```

### Rule 2: 应用上下文管理
```python
# ✅ 正确的上下文使用
with app.app_context():
    # 所有数据库操作和业务逻辑都在这里
    from app.services.real_scheduling_service import RealSchedulingService
    service = RealSchedulingService()

# ❌ 错误的上下文使用
service = RealSchedulingService()  # 在上下文外创建
with app.app_context():
    pass  # 空上下文
```

### Rule 3: 错误处理和日志
```python
# ✅ 标准化的错误处理
try:
    result = some_operation()
    logger.info(f"✅ 操作成功: {result}")
except Exception as e:
    logger.error(f"❌ 操作失败: {e}")
    import traceback
    logger.error(traceback.format_exc())
    return False
```

### Rule 4: 编码问题解决
```python
# ✅ 必须在文件开头设置
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
```

### Rule 5: 数据库操作规范
```python
# ✅ 在应用上下文中进行数据库操作
with app.app_context():
    from app.models import SomeModel
    from app import db
    
    # 数据库查询
    records = SomeModel.query.all()
    
    # 事务处理（如果需要）
    try:
        db.session.add(new_record)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        raise
```

## 特殊场景处理

### 测试服务类时
```python
# ✅ 正确的服务测试模式
with app.app_context():
    from app.services.real_scheduling_service import RealSchedulingService
    
    # 创建服务实例
    service = RealSchedulingService()
    
    # 测试方法调用
    result = service.some_method(param1, param2)
```

### 测试API接口时
```python
# ✅ 使用test_client
with app.test_client() as client:
    response = client.post('/api/endpoint', 
                          json={'param': 'value'},
                          headers={'Content-Type': 'application/json'})
    
    assert response.status_code == 200
    data = response.get_json()
```

### 测试数据库操作时
```python
# ✅ 事务回滚模式（不影响实际数据）
with app.app_context():
    from app import db
    
    # 开始事务
    db.session.begin()
    
    try:
        # 测试操作
        # ...
        
        # 测试完成后回滚（不保存测试数据）
        db.session.rollback()
        
    except Exception as e:
        db.session.rollback()
        raise
```

## 常见错误模式及修复

### 错误1: 'tuple' object has no attribute 'app_context'
```python
# ❌ 错误代码
from run import app  # app是tuple

# ✅ 正确修复
from app import create_app
app, socketio = create_app()
```

### 错误2: attempted relative import with no known parent package
```python
# ❌ 错误代码
from .app import create_app

# ✅ 正确修复
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from app import create_app
```

### 错误3: No application found
```python
# ❌ 错误代码
from app.models import SomeModel
service = SomeService()  # 在应用上下文外

# ✅ 正确修复
with app.app_context():
    from app.models import SomeModel
    service = SomeService()
```

## 强制执行规则

### 检查清单
在创建任何测试脚本时，必须确认：

1. ✅ 编码修复代码在文件最开头
2. ✅ 路径设置在所有导入之前
3. ✅ 使用 `from app import create_app` 而不是其他导入方式
4. ✅ 所有业务逻辑都在 `with app.app_context():` 内部
5. ✅ 异常处理包含完整的traceback
6. ✅ 日志格式统一，包含成功和失败信息
7. ✅ 测试结果有明确的返回值（True/False）

### 自动化检查
```python
def validate_test_script_structure():
    """验证测试脚本结构是否符合规范"""
    required_patterns = [
        r'sys\.stdout = io\.TextIOWrapper',  # 编码修复
        r'from app import create_app',        # 正确导入
        r'with app\.app_context\(\):',        # 上下文使用
        r'logging\.basicConfig',              # 日志配置
        r'except Exception as e:',            # 异常处理
    ]
    # 检查逻辑...
```

## 总结

通过严格遵循这些规则，可以彻底避免测试脚本中的上下文错误、导入问题和认证错误。每次创建测试脚本时，都应该使用标准化模板，并通过检查清单确保符合规范。

**关键原则：先建立上下文，再进行业务操作**