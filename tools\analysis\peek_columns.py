#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pymysql

def show(table):
    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
    try:
        with conn.cursor() as cur:
            cur.execute(f"SHOW COLUMNS FROM {table}")
            cols = [row[0] for row in cur.fetchall()]
            print(table, ':', ','.join(cols))
    finally:
        conn.close()

if __name__ == '__main__':
    for t in ['ET_FT_TEST_SPEC','ET_RECIPE_FILE','EQP_STATUS','devicepriorityconfig','lotpriorityconfig','lotprioritydone']:
        try:
            show(t)
        except Exception as e:
            print('ERR', t, e)

