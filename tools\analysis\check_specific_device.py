#!/usr/bin/env python3
"""
检查特定DEVICE的测试规范数据
"""
import pymysql

def check_specific_device():
    """检查特定DEVICE的测试规范"""
    print("🔍 检查特定DEVICE的测试规范数据...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询JWQ7101SOTB-J115_TR1的测试规范
            device = 'JWQ7101SOTB-J115_TR1'
            cursor.execute("""
                SELECT id, DEVICE, STAGE, TESTER, HB_PN, TB_PN, APPROVAL_STATE
                FROM et_ft_test_spec 
                WHERE DEVICE = %s AND APPROVAL_STATE = 'Released'
                ORDER BY id DESC
            """, (device,))
            
            results = cursor.fetchall()
            print(f"📊 {device} 的测试规范记录数: {len(results)}")
            
            if results:
                print(f"\n📋 {device} 的测试规范详情:")
                for i, row in enumerate(results, 1):
                    print(f"  {i}. ID={row['id']} STAGE='{row['STAGE']}' TESTER='{row['TESTER']}' HB_PN='{row['HB_PN']}' TB_PN='{row['TB_PN']}'")
            
            # 查询前10条记录的DEVICE分布（按ID降序）
            print(f"\n🔍 查询前10条记录的DEVICE分布（按ID降序）:")
            cursor.execute("""
                SELECT id, DEVICE, STAGE, APPROVAL_STATE
                FROM et_ft_test_spec 
                WHERE APPROVAL_STATE = 'Released'
                ORDER BY id DESC
                LIMIT 10
            """)
            
            top_results = cursor.fetchall()
            for i, row in enumerate(top_results, 1):
                print(f"  {i}. ID={row['id']} DEVICE='{row['DEVICE']}' STAGE='{row['STAGE']}'")
            
            # 检查JWQ5279EQFNAG_TR1是否存在
            print(f"\n🔍 检查JWQ5279EQFNAG_TR1是否存在:")
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM et_ft_test_spec 
                WHERE DEVICE = 'JWQ5279EQFNAG_TR1' AND APPROVAL_STATE = 'Released'
            """)
            
            count_result = cursor.fetchone()
            print(f"📊 JWQ5279EQFNAG_TR1 的记录数: {count_result['count']}")
            
            if count_result['count'] > 0:
                cursor.execute("""
                    SELECT id, DEVICE, STAGE, TESTER, APPROVAL_STATE
                    FROM et_ft_test_spec 
                    WHERE DEVICE = 'JWQ5279EQFNAG_TR1' AND APPROVAL_STATE = 'Released'
                    ORDER BY id DESC
                    LIMIT 5
                """)
                
                jwq_results = cursor.fetchall()
                print(f"📋 JWQ5279EQFNAG_TR1 的前5条记录:")
                for i, row in enumerate(jwq_results, 1):
                    print(f"  {i}. ID={row['id']} STAGE='{row['STAGE']}' TESTER='{row['TESTER']}'")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_specific_device()
