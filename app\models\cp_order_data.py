#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CP订单数据模型
专门用于存储CP模板格式的订单数据
"""

from datetime import datetime
from app import db

class CpOrderData(db.Model):
    """CP订单数据表 - 专门用于CP模板格式的订单"""
    __tablename__ = 'cp_order_data'
    __table_args__ = {'extend_existing': True}
    
    # 主键和基础信息
    id = db.Column(db.Integer, primary_key=True)
    
    # === 横向信息字段 (来自CP模板前10行) ===
    document_number = db.Column(db.String(50), comment='编号 (如: JW25062304)')
    processing_type = db.Column(db.String(100), comment='加工属性 (如: CP1)')
    order_date = db.Column(db.Date, comment='日期')
    contractor_name = db.Column(db.String(200), comment='加工承揽商 (如: 无锡市宜欣科技有限公司)')
    contractor_contact = db.Column(db.String(100), comment='承揽商联系人')
    contractor_address = db.Column(db.String(500), comment='承揽商地址')
    contractor_phone = db.Column(db.String(50), comment='承揽商电话')
    client_name = db.Column(db.String(200), comment='加工委托方 (如: 杰华特微电子股份有限公司)')
    client_contact = db.Column(db.String(100), comment='委托方联系人')
    client_address = db.Column(db.String(500), comment='委托方地址')
    client_phone = db.Column(db.String(50), comment='委托方电话')
    
    # === 纵向表格数据字段 (来自CP表格行) ===
    order_number = db.Column(db.String(64), nullable=False, index=True, comment='订单号 (如: JHT2506230015)')
    product_name = db.Column(db.String(200), comment='产品名称')
    chip_name = db.Column(db.String(100), comment='芯片名称 (如: JP16701C)')
    chip_batch = db.Column(db.String(100), comment='芯片批号 (如: FA55-7133)')
    processing_pieces = db.Column(db.Integer, comment='加工片数')
    finished_model = db.Column(db.String(100), comment='成品型号 (如: JP16701C_P00R)')
    wafer_numbers = db.Column(db.Text, comment='片号 (多个片号，逗号分隔)')
    cp_mapping = db.Column(db.String(100), comment='CP Mapping')
    package_method = db.Column(db.String(100), comment='包装方式 (如: 圆片)')
    process_step = db.Column(db.String(50), comment='工序 (如: CP1, CP2)')
    shipping_address = db.Column(db.String(500), comment='发货地址')
    
    # === 分类和状态字段 ===
    classification = db.Column(db.Enum('cp1', 'cp2', 'cp3', 'unknown'), default='unknown', comment='CP工序分类')
    wafer_count = db.Column(db.Integer, comment='晶圆片数 (从片号字段计算)')
    
    # === 扩展的业务字段 ===
    customer = db.Column(db.String(128), comment='客户名称 (从委托方提取)')
    product_code = db.Column(db.String(64), comment='产品编码 (从芯片名称提取)')
    quantity = db.Column(db.Integer, comment='数量 (加工片数)')
    unit_price = db.Column(db.Numeric(10, 2), comment='单价')
    total_price = db.Column(db.Numeric(10, 2), comment='总价')
    status = db.Column(db.String(20), default='new', comment='订单状态')
    urgent = db.Column(db.Boolean, default=False, comment='是否紧急')
    owner = db.Column(db.String(64), comment='负责人')
    note = db.Column(db.Text, comment='备注')
    
    # === 数据来源和追溯字段 ===
    source_file = db.Column(db.String(512), comment='数据来源文件 (Excel文件名)')
    raw_data = db.Column(db.Text, comment='原始数据JSON (包含所有扫描信息)')
    horizontal_data = db.Column(db.Text, comment='横向信息JSON')
    vertical_data = db.Column(db.Text, comment='纵向表格数据JSON')
    data_row_number = db.Column(db.Integer, comment='在Excel中的数据行号')
    extraction_method = db.Column(db.String(50), default='cp_parser', comment='提取方法')
    extraction_info = db.Column(db.Text, comment='提取过程信息')
    
    # === 时间戳字段 ===
    created_by = db.Column(db.String(64), comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    imported_at = db.Column(db.DateTime, comment='导入时间')
    processed_at = db.Column(db.DateTime, comment='处理时间')
    
    def __repr__(self):
        return f'<CpOrderData {self.order_number}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'product_name': self.product_name,
            'chip_name': self.chip_name,
            'chip_batch': self.chip_batch,
            'processing_pieces': self.processing_pieces,
            'finished_model': self.finished_model,
            'process_step': self.process_step,
            'classification': self.classification,
            'customer': self.customer,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'source_file': self.source_file
        }
    
    @classmethod
    def create_from_parsed_data(cls, data: dict, source_file: str = None, created_by: str = None):
        """从解析数据创建CP订单记录"""
        cp_order = cls()
        
        # 基础字段映射
        field_mapping = {
            '订单号': 'order_number',
            '产品名称': 'product_name', 
            '芯片名称': 'chip_name',
            '芯片批号': 'chip_batch',
            '加工片数': 'processing_pieces',
            '成品型号': 'finished_model',
            '片号': 'wafer_numbers',
            'CP Mapping': 'cp_mapping',
            '包装方式': 'package_method',
            '工序': 'process_step',
            '发货地址': 'shipping_address'
        }
        
        # 设置字段值
        for chinese_name, english_name in field_mapping.items():
            if chinese_name in data:
                value = data[chinese_name]
                
                # 特殊处理数值字段
                if english_name == 'processing_pieces' and value:
                    try:
                        value = int(str(value).strip())
                    except (ValueError, TypeError):
                        value = None
                
                setattr(cp_order, english_name, value)
        
        # 设置横向信息
        if '编号' in data:
            cp_order.document_number = data['编号']
        if '加工属性' in data:
            cp_order.processing_type = data['加工属性']
        if '日期' in data:
            cp_order.order_date = data['日期']
        if '加工承揽商' in data:
            cp_order.contractor_name = data['加工承揽商']
        if '加工委托方' in data:
            cp_order.client_name = data['加工委托方']
        
        # 自动分类
        process_step = cp_order.process_step or ''
        if 'CP1' in process_step.upper():
            cp_order.classification = 'cp1'
        elif 'CP2' in process_step.upper():
            cp_order.classification = 'cp2' 
        elif 'CP3' in process_step.upper():
            cp_order.classification = 'cp3'
        else:
            cp_order.classification = 'unknown'
        
        # 计算晶圆片数
        if cp_order.wafer_numbers:
            try:
                wafer_list = [w.strip() for w in str(cp_order.wafer_numbers).split(',') if w.strip()]
                cp_order.wafer_count = len(wafer_list)
            except:
                cp_order.wafer_count = 0
        
        # 设置业务字段
        cp_order.customer = cp_order.client_name
        cp_order.product_code = cp_order.chip_name
        cp_order.quantity = cp_order.processing_pieces
        
        # 设置元数据
        cp_order.source_file = source_file
        cp_order.created_by = created_by
        cp_order.raw_data = data.get('raw_data', '')
        cp_order.extraction_method = 'cp_parser'
        cp_order.imported_at = datetime.utcnow()
        
        return cp_order 