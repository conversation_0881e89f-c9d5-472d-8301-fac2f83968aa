# APS Platform - Login Fix Build Script with Socket.IO Progress Bar Patch
# Version: 4.1 - Minimal fix for progress bar display issue
# Based on original build_login_fix.ps1 structure

Write-Host "========================================================================" -ForegroundColor Cyan
Write-Host "APS Platform - Login Fix Build v4.1 with Socket.IO Progress Patch" -ForegroundColor Cyan
Write-Host "Minimal fix for PyInstaller progress bar issue" -ForegroundColor Cyan
Write-Host "========================================================================" -ForegroundColor Cyan

# Read app version
Write-Host "Reading app version..." -ForegroundColor Yellow
$configPath = "config\__init__.py"
if (Test-Path $configPath) {
    $configContent = Get-Content $configPath -Raw
    if ($configContent -match "APP_VERSION\s*=\s*['`"]([^'`"]+)['`"]") {
        $appVersion = $matches[1]
        Write-Host "Current app version: $appVersion" -ForegroundColor Green
    } else {
        Write-Host "Warning: Cannot read version info, using default version 2.3.9" -ForegroundColor Yellow
        $appVersion = "2.3.9"
    }
} else {
    Write-Host "Warning: Config file not found, using default version 2.3.9" -ForegroundColor Yellow
    $appVersion = "2.3.9"
}

# Generate exe name  
$exeName = "AEC-FT-Intelligent-Commander-Platform-$appVersion-Production"
Write-Host "Target exe file: $exeName.exe (with progress bar fix)" -ForegroundColor Cyan

# Check environment
Write-Host "Checking environment..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found" -ForegroundColor Red
    exit 1
}

# Clean old files
Write-Host "Cleaning old build files..." -ForegroundColor Yellow
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
Get-ChildItem -Path "." -Filter "*.spec" | Remove-Item -Force

# Verify model architecture works
Write-Host "Verifying model architecture..." -ForegroundColor Cyan
$modelTest = 'import os; os.environ["FLASK_QUIET_STARTUP"] = "1"; from app.models import User, ET_WAIT_LOT; print("Model architecture verification successful!")'
echo $modelTest | python
if ($LASTEXITCODE -ne 0) {
    Write-Host "Model architecture verification failed" -ForegroundColor Red
    exit 1
}

# Verify critical files exist
Write-Host "Verifying critical files..." -ForegroundColor Yellow
$criticalFiles = @(
    "app/models.py",
    "app/templates/auth/login.html",
    "app/templates/base.html"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "$file" -ForegroundColor Green
    } else {
        Write-Host "Missing: $file" -ForegroundColor Red
        exit 1
    }
}

# PATCH: Download Socket.IO client for offline use (minimal fix)
Write-Host "PATCH: Downloading Socket.IO client for offline use..." -ForegroundColor Cyan
$socketioUrl = "https://cdn.socket.io/4.7.2/socket.io.min.js"
$socketioPath = "app/static/js/socket.io.min.js"

if (-not (Test-Path $socketioPath)) {
    try {
        Write-Host "Downloading Socket.IO client library..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $socketioUrl -OutFile $socketioPath
        Write-Host "Downloaded Socket.IO client: $socketioPath" -ForegroundColor Green
    } catch {
        Write-Host "Warning: Failed to download Socket.IO, will use CDN fallback" -ForegroundColor Yellow
    }
} else {
    Write-Host "Socket.IO client already exists: $socketioPath" -ForegroundColor Green
}

# Execute PyInstaller with Socket.IO support (based on original structure)
Write-Host "Executing PyInstaller build with Socket.IO support..." -ForegroundColor Yellow
Write-Host "This build includes key fixes and enhancements:" -ForegroundColor Cyan

# Check MySQL library path
Write-Host "Checking MySQL library..." -ForegroundColor Yellow
$mysqlLibPath = "C:\Program Files\MySQL\MySQL Server 8.0\lib\libmysql.dll"
$mysqlBinaryArgs = @()
if (Test-Path $mysqlLibPath) {
    Write-Host "Found MySQL library: $mysqlLibPath" -ForegroundColor Green
    $mysqlBinaryArgs = @("--add-binary", "$mysqlLibPath;.")
} else {
    Write-Host "MySQL library not found, will use PyMySQL driver" -ForegroundColor Yellow
}

# PyInstaller arguments (based on original with Socket.IO additions)
$pyinstallerArgs = @(
    "run.py",
    "--name", "$exeName", 
    "--icon", "icon/icon.ico",
    "--clean",
    "--noconfirm",
    "--onefile", 
    "--console",
    # Core modules (from original)
    "--hidden-import", "pymysql",
    "--hidden-import", "sqlalchemy.dialects.mysql", 
    "--hidden-import", "flask",
    "--hidden-import", "flask_sqlalchemy",
    "--hidden-import", "flask_login",
    # PATCH: Socket.IO modules for progress bar
    "--hidden-import", "flask_socketio",
    "--hidden-import", "socketio",
    "--hidden-import", "socketio.server", 
    "--hidden-import", "engineio",
    "--hidden-import", "engineio.server",
    "--hidden-import", "eventlet",
    "--hidden-import", "gevent",
    # Progress bar related modules
    "--hidden-import", "app.api.websocket", 
    "--hidden-import", "app.services.progress_tracker",
    "--hidden-import", "app.services.task_manager",
    "--hidden-import", "app.services.event_bus",
    # App modules (from original)
    "--hidden-import", "app.models",
    "--hidden-import", "app.auth",
    "--hidden-import", "app.api_v2",
    "--hidden-import", "app.services", 
    "--hidden-import", "waitress",
    "--hidden-import", "scheduling_failure_fix",
    "--hidden-import", "tools.monitoring.scheduling_failure_fix",
    # Exclude modules to reduce size (from original)
    "--exclude-module", "torch",
    "--exclude-module", "torchvision",
    "--exclude-module", "torchaudio", 
    "--exclude-module", "scipy",
    "--exclude-module", "matplotlib",
    "--exclude-module", "cv2",
    "--exclude-module", "ultralytics", 
    "--exclude-module", "IPython",
    "--exclude-module", "jupyter",
    "--exclude-module", "notebook",
    "--exclude-module", "psycopg2",
    "--exclude-module", "oracledb",
    "--exclude-module", "cx_Oracle",
    "--exclude-module", "tensorflow", 
    "--exclude-module", "keras",
    "--exclude-module", "sklearn",
    "--exclude-module", "seaborn",
    "--exclude-module", "plotly",
    "--exclude-module", "bokeh",
    "--exclude-module", "dash",
    "--exclude-module", "streamlit",
    # Collect submodules (from original)
    "--collect-submodules", "app",
    # PATCH: Socket.IO submodules
    "--collect-submodules", "socketio",
    "--collect-submodules", "engineio", 
    # Include data files (from original)
    "--add-data", "app/templates;app/templates",
    "--add-data", "app/static;app/static",
    "--add-data", "app/models.py;app", 
    "--add-data", "config;config"
)

# Add MySQL binary files if exists (from original)
if ($mysqlBinaryArgs.Count -gt 0) {
    $pyinstallerArgs += $mysqlBinaryArgs
}

Write-Host "Key optimizations (from original):" -ForegroundColor Green
Write-Host "- Exclude deep learning modules (torch, tensorflow etc)" -ForegroundColor Green
Write-Host "- Exclude visualization libraries (scipy, matplotlib etc)" -ForegroundColor Green 
Write-Host "- Include Waitress production server" -ForegroundColor Green
Write-Host "- Exclude development tools (IPython, jupyter etc)" -ForegroundColor Green
Write-Host "- Exclude other database drivers" -ForegroundColor Green
Write-Host "- Complete templates and static resource packaging" -ForegroundColor Green
Write-Host ""
Write-Host "PATCH: Socket.IO progress bar support:" -ForegroundColor Cyan
Write-Host "- Socket.IO server modules" -ForegroundColor Cyan
Write-Host "- WebSocket event handling" -ForegroundColor Cyan  
Write-Host "- Progress tracking services" -ForegroundColor Cyan
Write-Host "- Offline Socket.IO client library" -ForegroundColor Cyan

python -m PyInstaller @pyinstallerArgs

# Check result (based on original)
if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "============================================================" -ForegroundColor Green
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "============================================================" -ForegroundColor Green
    
    $finalExePath = "dist/$exeName.exe"
    if (Test-Path $finalExePath) {
        $size = (Get-Item $finalExePath).Length
        $sizeMB = [math]::Round($size / 1MB, 1)
        
        Write-Host ""
        Write-Host "Target executable file: $finalExePath" -ForegroundColor Yellow
        Write-Host "File size: $sizeMB MB" -ForegroundColor Cyan
        Write-Host "Current version: $appVersion" -ForegroundColor Magenta
        Write-Host ""
        Write-Host "Build features:" -ForegroundColor Green
        Write-Host "- Include all template files (app/templates)" -ForegroundColor Green
        Write-Host "- Include static resource files (app/static)" -ForegroundColor Green
        Write-Host "- Include core model files (models.py)" -ForegroundColor Green
        Write-Host "- Include configuration files (config)" -ForegroundColor Green
        Write-Host "- Complete dependency verification and checking" -ForegroundColor Green
        Write-Host "- Socket.IO progress bar support patched" -ForegroundColor Green
        Write-Host ""
        Write-Host "Usage instructions:" -ForegroundColor Cyan
        Write-Host "1. Copy $exeName.exe to target directory" -ForegroundColor White
        Write-Host "2. Copy config/ folder to same directory as exe" -ForegroundColor White
        Write-Host "3. Configure database connection in config/environment.ini" -ForegroundColor White
        Write-Host "4. Run the exe file" -ForegroundColor White

        # Create config template if not exists (based on original structure)
        if (-not (Test-Path "dist/config.ini")) {
            $configTemplate = @"
# AEC-FT Database Configuration File
# Please modify the following configuration according to your environment

[DATABASE]
# MySQL database connection configuration
host = localhost
port = 3306
database = aps
username = root
password = WWWwww123!
charset = utf8mb4

[APPLICATION]
# Application configuration
debug = false
host = 0.0.0.0
port = 5000
secret_key = your-secret-key-here

[LOGGING]
# Logging configuration
level = INFO
file = logs/app.log
max_size = 10MB
backup_count = 5

[SOCKETIO]
# Socket.IO configuration for progress bars - PROGRESS BAR PATCH
enabled = true
cors_allowed_origins = *
logger = false
engineio_logger = false
"@
            [System.IO.File]::WriteAllText("dist/config.ini", $configTemplate, [System.Text.Encoding]::ASCII)
            Write-Host "Generated config template file: dist/config.ini (ASCII encoding)" -ForegroundColor Green
        } else {
            Write-Host "Config file already exists: dist/config.ini" -ForegroundColor Yellow
        }

        # Create database config wizard (minimal version from original)
        $dbInitScript = @"
@echo off
chcp 65001 >nul
title AEC-FT Socket.IO Fixed Database Configuration Wizard

echo.
echo ================================================================
echo     AEC-FT Socket.IO Fixed Database Configuration Wizard  
echo ================================================================
echo.
echo This wizard will help you configure database connection and Socket.IO progress bar
echo.

REM Check if config.ini exists
if not exist "config.ini" (
    echo ERROR: Cannot find config.ini file
    echo Please make sure this script is in the same directory as the exe file
    echo.
    pause
    exit /b 1
)

echo Please make sure MySQL service is running
echo.

REM Get database configuration
set /p db_host="Enter MySQL server address [default: localhost]: "
if "%db_host%"=="" set db_host=localhost

set /p db_port="Enter MySQL port [default: 3306]: "
if "%db_port%"=="" set db_port=3306

set /p db_user="Enter MySQL username [default: root]: "
if "%db_user%"=="" set db_user=root

set /p db_password="Enter MySQL password: "
if "%db_password%"=="" (
    echo.
    echo ERROR: MySQL password cannot be empty
    echo.
    pause
    exit /b 1
)

set /p db_name="Enter database name [default: aps]: "
if "%db_name%"=="" set db_name=aps

echo.
echo ================================================================
echo Updating configuration file...
echo ================================================================

powershell -ExecutionPolicy Bypass -Command "try { \$content = Get-Content 'config.ini' -Encoding UTF8; \$content = \$content -replace '^host = .*', 'host = %db_host%'; \$content = \$content -replace '^port = .*', 'port = %db_port%'; \$content = \$content -replace '^username = .*', 'username = %db_user%'; \$content = \$content -replace '^password = .*', 'password = %db_password%'; \$content = \$content -replace '^database = .*', 'database = %db_name%'; \$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Configuration updated successfully' } catch { Write-Host 'Failed to update configuration' }"

echo.
echo ================================================================
echo Configuration completed! Socket.IO Progress Bar Support Enabled!
echo ================================================================
echo.
echo Configuration summary:
echo     Database server: %db_host%:%db_port%
echo     Database name: %db_name%
echo     Username: %db_user%
echo     Socket.IO: Enabled for progress bars
echo.
echo Next steps:
echo 1. Double-click $exeName.exe to start the application
echo 2. Open browser and visit: http://localhost:5000
echo 3. Login with default account: admin / admin  
echo 4. Progress bars should now display detailed scheduling stages
echo.
echo Press any key to exit...
pause >nul
"@

        $dbInitScript | Out-File -FilePath "dist/database_config_wizard.bat" -Encoding Default
        Write-Host "Generated database config wizard: dist/database_config_wizard.bat" -ForegroundColor Green

        # Create deployment info (based on original)
        $deployInfo = @"
# AEC-FT Socket.IO Progress Bar Patch - Deployment Guide

## Build Information
- **Version**: $appVersion
- **Build Time**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **File Size**: $sizeMB MB
- **File Name**: $exeName.exe
- **Patch**: Socket.IO progress bar functionality fixed

## Key Fixes Applied

### Socket.IO Progress Bar Issue Fixed
The main issue was missing Socket.IO dependencies in PyInstaller packaging.
This patched version includes Flask-SocketIO, socketio, and engineio modules.

### Database Configuration  
1. Run database_config_wizard.bat
2. Follow the prompts to enter database connection info
3. The wizard will automatically update the config file

### Start Using
1. Double-click $exeName.exe
2. Visit: http://localhost:5000
3. Login: admin / admin
4. Progress bars should now show detailed scheduling stages

## Deployment Files
- $exeName.exe - Main executable file
- config.ini - Database configuration file  
- database_config_wizard.bat - Database configuration wizard
- README-$appVersion.txt - This readme file

## Features
1. **Socket.IO progress bar support fixed**
2. **Real-time WebSocket communication** 
3. **Detailed scheduling stage display**
4. **Complete database auto-initialization**

---
**Note**: This is a minimal patch version based on the original build_login_fix.ps1 structure.
"@
        $deployInfo | Out-File -FilePath "dist/README-$appVersion.txt" -Encoding UTF8
        Write-Host "Generated deployment guide: dist/README-$appVersion.txt" -ForegroundColor Green
        
    } else {
        Write-Host "Error: Executable file not found" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "Build failed" -ForegroundColor Red
    Write-Host "Please check the output above for error information" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "==================================================================" -ForegroundColor Cyan
Write-Host "Build completed - Socket.IO Progress Bar Patch Version" -ForegroundColor Cyan
Write-Host "==================================================================" -ForegroundColor Cyan