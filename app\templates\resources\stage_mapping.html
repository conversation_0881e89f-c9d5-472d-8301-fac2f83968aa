{% extends "base.html" %}

{% set page_title = "STAGE字段映射配置" %}

{% block extra_css %}
<style>
/* STAGE映射配置专用样式 */
.mapping-card {
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.mapping-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.mapping-type-exact { border-left-color: #28a745; }
.mapping-type-fuzzy { border-left-color: #ffc107; }
.mapping-type-alias { border-left-color: #17a2b8; }

.priority-high { background-color: #fff3cd; }
.priority-medium { background-color: #d1ecf1; }
.priority-low { background-color: #d4edda; }

.stage-tag {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: monospace;
    font-size: 0.9em;
}

.stage-arrow {
    color: #6c757d;
    font-size: 1.2em;
    margin: 0 10px;
}

.batch-actions {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-number {
    font-size: 2em;
    font-weight: bold;
}

.btn-create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    border-radius: 6px;
}

.btn-create:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-exchange-alt me-2"></i>STAGE字段映射配置</h2>
                    <p class="text-muted">管理工序字段的映射规则，支持精确匹配、模糊匹配和别名映射</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-create" data-bs-toggle="modal" data-bs-target="#createMappingModal">
                        <i class="fas fa-plus"></i> 新建映射
                    </button>
                    <button type="button" class="btn btn-info" onclick="refreshMappings()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="stats-card">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="stats-number" id="totalMappings">-</div>
                        <div>总映射数</div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stats-number" id="activeMappings">-</div>
                        <div>激活映射</div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="stats-number" id="exactMappings">-</div>
                        <div>精确匹配</div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="stats-number" id="fuzzyMappings">-</div>
                        <div>模糊匹配</div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="stats-number" id="aliasMappings">-</div>
                        <div>别名映射</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作区域 -->
    <div class="batch-actions">
        <h5><i class="fas fa-layer-group me-2"></i>批量操作</h5>
        <div class="row">
            <div class="col-md-6">
                <button type="button" class="btn btn-success" onclick="createDefaultMappings()">
                    <i class="fas fa-magic"></i> 创建默认映射配置
                </button>
                <button type="button" class="btn btn-warning ms-2" onclick="testMapping()">
                    <i class="fas fa-search"></i> 测试映射查找
                </button>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">过滤:</label>
                    <select class="form-select" id="filterType" onchange="filterMappings()" style="width: auto;" aria-label="映射过滤类型">
                        <option value="all">全部映射</option>
                        <option value="active">仅激活的</option>
                        <option value="exact">精确匹配</option>
                        <option value="fuzzy">模糊匹配</option>
                        <option value="alias">别名映射</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 映射配置列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">映射配置列表</h5>
                </div>
                <div class="card-body">
                    <div id="mappingsList">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载映射配置...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建映射配置模态框 -->
<div class="modal fade" id="createMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建STAGE映射配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createMappingForm">
                    <div class="mb-3">
                        <label for="sourceStage" class="form-label">源工序名称 *</label>
                        <input type="text" class="form-control" id="sourceStage" placeholder="例如: FT" required>
                    </div>
                    <div class="mb-3">
                        <label for="targetStage" class="form-label">目标工序名称 *</label>
                        <input type="text" class="form-control" id="targetStage" placeholder="例如: Final Test" required>
                    </div>
                    <div class="mb-3">
                        <label for="mappingType" class="form-label">映射类型</label>
                        <select class="form-select" id="mappingType" aria-label="映射类型选择">
                            <option value="exact">精确匹配</option>
                            <option value="fuzzy">模糊匹配</option>
                            <option value="alias">别名映射</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="priority" class="form-label">优先级</label>
                        <input type="number" class="form-control" id="priority" value="0" min="0" max="100">
                        <div class="form-text">数值越大，优先级越高</div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">映射说明</label>
                        <textarea class="form-control" id="description" rows="3" placeholder="请输入映射配置的说明..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createMapping()">创建映射</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑映射配置模态框 -->
<div class="modal fade" id="editMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑STAGE映射配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editMappingForm">
                    <input type="hidden" id="editMappingId">
                    <div class="mb-3">
                        <label for="editSourceStage" class="form-label">源工序名称 *</label>
                        <input type="text" class="form-control" id="editSourceStage" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTargetStage" class="form-label">目标工序名称 *</label>
                        <input type="text" class="form-control" id="editTargetStage" required>
                    </div>
                    <div class="mb-3">
                        <label for="editMappingType" class="form-label">映射类型</label>
                        <select class="form-select" id="editMappingType" aria-label="编辑映射类型">
                            <option value="exact">精确匹配</option>
                            <option value="fuzzy">模糊匹配</option>
                            <option value="alias">别名映射</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editPriority" class="form-label">优先级</label>
                        <input type="number" class="form-control" id="editPriority" min="0" max="100">
                    </div>
                    <div class="mb-3">
                        <label for="editIsActive" class="form-label">状态</label>
                        <select class="form-select" id="editIsActive" aria-label="激活状态选择">
                            <option value="true">激活</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">映射说明</label>
                        <textarea class="form-control" id="editDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateMapping()">更新映射</button>
            </div>
        </div>
    </div>
</div>

<!-- 测试映射查找模态框 -->
<div class="modal fade" id="testMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试映射查找</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="testSourceStage" class="form-label">输入源工序名称</label>
                    <input type="text" class="form-control" id="testSourceStage" placeholder="例如: FT">
                </div>
                <button type="button" class="btn btn-primary" onclick="performMappingTest()">查找目标工序</button>
                <div id="testResult" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <h6>映射结果:</h6>
                        <div id="testResultContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// STAGE映射配置管理JavaScript
let allMappings = [];
let filteredMappings = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadMappings();
    loadStatistics();
});

// 加载映射配置列表
function loadMappings(activeOnly = false) {
    const url = `/api/v2/resources/stage-mapping?active_only=${activeOnly}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allMappings = data.data;
                filteredMappings = [...allMappings];
                renderMappings();
            } else {
                showAlert('加载映射配置失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('加载映射配置失败:', error);
            showAlert('加载映射配置失败: ' + error.message, 'danger');
        });
}

// 渲染映射配置列表
function renderMappings() {
    const container = document.getElementById('mappingsList');
    
    if (filteredMappings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无映射配置</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createMappingModal">
                    创建第一个映射配置
                </button>
            </div>
        `;
        return;
    }
    
    const html = filteredMappings.map(mapping => `
        <div class="mapping-card card mb-3 mapping-type-${mapping.mapping_type}">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <span class="stage-tag">${mapping.source_stage}</span>
                            <span class="stage-arrow"><i class="fas fa-arrow-right"></i></span>
                            <span class="stage-tag">${mapping.target_stage}</span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-${getMappingTypeBadgeColor(mapping.mapping_type)}">
                            ${getMappingTypeText(mapping.mapping_type)}
                        </span>
                    </div>
                    <div class="col-md-1 text-center">
                        <span class="badge bg-secondary">${mapping.priority}</span>
                    </div>
                    <div class="col-md-2 text-center">
                        <span class="badge bg-${mapping.is_active ? 'success' : 'secondary'}">
                            ${mapping.is_active ? '激活' : '禁用'}
                        </span>
                    </div>
                    <div class="col-md-3 text-end">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editMapping(${mapping.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger ms-1" onclick="deleteMapping(${mapping.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
                ${mapping.description ? `
                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted"><i class="fas fa-info-circle me-1"></i>${mapping.description}</small>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// 获取映射类型徽章颜色
function getMappingTypeBadgeColor(type) {
    switch(type) {
        case 'exact': return 'success';
        case 'fuzzy': return 'warning';
        case 'alias': return 'info';
        default: return 'secondary';
    }
}

// 获取映射类型文本
function getMappingTypeText(type) {
    switch(type) {
        case 'exact': return '精确匹配';
        case 'fuzzy': return '模糊匹配';
        case 'alias': return '别名映射';
        default: return type;
    }
}

// 加载统计信息
function loadStatistics() {
    fetch('/api/v2/resources/stage-mapping/statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.statistics;
                document.getElementById('totalMappings').textContent = stats.total_mappings || 0;
                document.getElementById('activeMappings').textContent = stats.active_mappings || 0;
                document.getElementById('exactMappings').textContent = stats.exact_mappings || 0;
                document.getElementById('fuzzyMappings').textContent = stats.fuzzy_mappings || 0;
                document.getElementById('aliasMappings').textContent = stats.alias_mappings || 0;
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}

// 创建映射配置
function createMapping() {
    const formData = {
        source_stage: document.getElementById('sourceStage').value.trim(),
        target_stage: document.getElementById('targetStage').value.trim(),
        mapping_type: document.getElementById('mappingType').value,
        priority: parseInt(document.getElementById('priority').value) || 0,
        description: document.getElementById('description').value.trim()
    };
    
    if (!formData.source_stage || !formData.target_stage) {
        showAlert('请填写源工序和目标工序名称', 'warning');
        return;
    }
    
    fetch('/api/v2/resources/stage-mapping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('映射配置创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createMappingModal')).hide();
            document.getElementById('createMappingForm').reset();
            loadMappings();
            loadStatistics();
        } else {
            showAlert('创建失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('创建映射配置失败:', error);
        showAlert('创建失败: ' + error.message, 'danger');
    });
}

// 编辑映射配置
function editMapping(id) {
    const mapping = allMappings.find(m => m.id === id);
    if (!mapping) return;
    
    document.getElementById('editMappingId').value = mapping.id;
    document.getElementById('editSourceStage').value = mapping.source_stage;
    document.getElementById('editTargetStage').value = mapping.target_stage;
    document.getElementById('editMappingType').value = mapping.mapping_type;
    document.getElementById('editPriority').value = mapping.priority;
    document.getElementById('editIsActive').value = mapping.is_active.toString();
    document.getElementById('editDescription').value = mapping.description || '';
    
    new bootstrap.Modal(document.getElementById('editMappingModal')).show();
}

// 更新映射配置
function updateMapping() {
    const id = document.getElementById('editMappingId').value;
    const formData = {
        source_stage: document.getElementById('editSourceStage').value.trim(),
        target_stage: document.getElementById('editTargetStage').value.trim(),
        mapping_type: document.getElementById('editMappingType').value,
        priority: parseInt(document.getElementById('editPriority').value) || 0,
        is_active: document.getElementById('editIsActive').value === 'true',
        description: document.getElementById('editDescription').value.trim()
    };
    
    fetch(`/api/v2/resources/stage-mapping/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('映射配置更新成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editMappingModal')).hide();
            loadMappings();
            loadStatistics();
        } else {
            showAlert('更新失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('更新映射配置失败:', error);
        showAlert('更新失败: ' + error.message, 'danger');
    });
}

// 删除映射配置
function deleteMapping(id) {
    const mapping = allMappings.find(m => m.id === id);
    if (!mapping) return;
    
    if (confirm(`确定要删除映射配置 "${mapping.source_stage} → ${mapping.target_stage}" 吗？`)) {
        fetch(`/api/v2/resources/stage-mapping/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('映射配置删除成功', 'success');
                loadMappings();
                loadStatistics();
            } else {
                showAlert('删除失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('删除映射配置失败:', error);
            showAlert('删除失败: ' + error.message, 'danger');
        });
    }
}

// 创建默认映射配置
function createDefaultMappings() {
    if (confirm('这将创建一系列默认的映射配置，是否继续？')) {
        fetch('/api/v2/resources/stage-mapping/batch-create-defaults', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`成功创建 ${data.created_count} 条默认映射配置`, 'success');
                loadMappings();
                loadStatistics();
            } else {
                showAlert('创建默认映射失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('创建默认映射失败:', error);
            showAlert('创建默认映射失败: ' + error.message, 'danger');
        });
    }
}

// 过滤映射配置
function filterMappings() {
    const filterType = document.getElementById('filterType').value;
    
    switch(filterType) {
        case 'all':
            filteredMappings = [...allMappings];
            break;
        case 'active':
            filteredMappings = allMappings.filter(m => m.is_active);
            break;
        case 'exact':
            filteredMappings = allMappings.filter(m => m.mapping_type === 'exact');
            break;
        case 'fuzzy':
            filteredMappings = allMappings.filter(m => m.mapping_type === 'fuzzy');
            break;
        case 'alias':
            filteredMappings = allMappings.filter(m => m.mapping_type === 'alias');
            break;
    }
    
    renderMappings();
}

// 刷新映射配置
function refreshMappings() {
    loadMappings();
    loadStatistics();
    showAlert('映射配置已刷新', 'info');
}

// 测试映射查找
function testMapping() {
    new bootstrap.Modal(document.getElementById('testMappingModal')).show();
}

// 执行映射测试
function performMappingTest() {
    const sourceStage = document.getElementById('testSourceStage').value.trim();
    if (!sourceStage) {
        showAlert('请输入源工序名称', 'warning');
        return;
    }
    
    fetch(`/api/v2/resources/stage-mapping/find-target?source_stage=${encodeURIComponent(sourceStage)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const resultDiv = document.getElementById('testResult');
                const contentDiv = document.getElementById('testResultContent');
                
                contentDiv.innerHTML = `
                    <p><strong>源工序:</strong> <span class="stage-tag">${data.source_stage}</span></p>
                    <p><strong>目标工序:</strong> <span class="stage-tag">${data.target_stage}</span></p>
                    <p><strong>是否映射:</strong> 
                        <span class="badge bg-${data.mapped ? 'success' : 'secondary'}">
                            ${data.mapped ? '是' : '否'}
                        </span>
                    </p>
                `;
                
                resultDiv.style.display = 'block';
            } else {
                showAlert('测试失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('测试映射失败:', error);
            showAlert('测试失败: ' + error.message, 'danger');
        });
}

// 显示提示信息
function showAlert(message, type = 'info') {
    // 创建toast提示
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 添加到页面并显示
    const toastContainer = document.querySelector('.toast-container') || (() => {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    })();
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // 显示后自动移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}
</script>
{% endblock %} 