#!/usr/bin/env python3
"""
Excel数据导入到MySQL数据库工具 - 增强版
集成完整的字段卡控和安全保护机制

Author: AI Assistant  
Date: 2025-01-16
Version: 3.0 - 生产级安全版本
"""

import os
import sys
import pandas as pd
import pymysql
from app.utils.db_connection_pool import get_db_connection_context, get_db_connection
import json
import time
from datetime import datetime
import logging
from pathlib import Path
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_mysql_enhanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ================================
# 业务表字段映射配置 - 基于实际数据库结构
# ================================

BUSINESS_TABLE_SCHEMAS = {
    'et_ft_test_spec': {
        'required_fields': ['TEST_SPEC_ID', 'TEST_SPEC_NAME', 'STAGE', 'DEVICE'],
        'field_mapping': {
            # Excel表头 -> 数据库字段 (基于实际数据库结构)
            'TEST_SPEC_ID': 'TEST_SPEC_ID',
            'TEST_SPEC_NAME': 'TEST_SPEC_NAME', 
            'TEST_SPEC_VER': 'TEST_SPEC_VER',
            'STAGE': 'STAGE',
            'TESTER': 'TESTER',
            'INV_ID': 'INV_ID',
            'TEST_SPEC_TYPE': 'TEST_SPEC_TYPE',
            'APPROVAL_STATE': 'APPROVAL_STATE',
            'ACTV_YN': 'ACTV_YN',
            'PROD_ID': 'PROD_ID',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'COMPANY_ID': 'COMPANY_ID',
            'DISABLE_USER': 'DISABLE_USER',
            'DISABLE_REASON': 'DISABLE_REASON',
            'DISABLE_TIME': 'DISABLE_TIME',
            'NOTE': 'NOTE',
            'APPROVE_USER': 'APPROVE_USER',
            'APPROVE_TIME': 'APPROVE_TIME',
            'ORT_QTY': 'ORT_QTY',
            'REMAIN_QTY': 'REMAIN_QTY',
            'STANDARD_YIELD': 'STANDARD_YIELD',
            'LOW_YIELD': 'LOW_YIELD',
            'DOWN_YIELD': 'DOWN_YIELD',
            'TEST_AREA': 'TEST_AREA',
            'HANDLER': 'HANDLER',
            'TEMPERATURE': 'TEMPERATURE',
            'FT_PROGRAM': 'FT_PROGRAM',
            'QA_PROGRAM': 'QA_PROGRAM',
            'GU_PROGRAM': 'GU_PROGRAM',
            'TB_PN': 'TB_PN',
            'HB_PN': 'HB_PN',
            'TIB': 'TIB',
            'TEST_TIME': 'TEST_TIME',
            'UPH': 'UPH',
            'SUFFIX_CODE': 'SUFFIX_CODE',
            'TESTER_CONFIG': 'TESTER_CONFIG',
            'GU_COMPARE_PARAM': 'GU_COMPARE_PARAM',
            'STA_COMPARE_PARAM': 'STA_COMPARE_PARAM',
            'DNR': 'DNR',
            'SITE': 'SITE',
            'DPAT': 'DPAT',
            'BS_NAME': 'BS_NAME',
            'GU_NAME': 'GU_NAME',
            'C_SPEC': 'C_SPEC',
            'TEST_ENG': 'TEST_ENG',
            'TEST_OPERATION': 'TEST_OPERATION',
            'ORDER_COMMENT': 'ORDER_COMMENT',
            'HIGH_YIELD': 'HIGH_YIELD',
            'VISION_LOSS_YIELD': 'VISION_LOSS_YIELD',
            'VISION_YIELD': 'VISION_YIELD',
            'LOSS_YIELD': 'LOSS_YIELD',
            'RETEST_YN': 'RETEST_YN',
            'FT_PROGRAM_PATH': 'FT_PROGRAM_PATH',
            'QA_PROGRAM_PATH': 'QA_PROGRAM_PATH',
            'GU_PROGRAM_PATH': 'GU_PROGRAM_PATH',
            'EFFECTIVE_TIME': 'EFFECTIVE_TIME',
            'TPL_RULE_TEMP': 'TPL_RULE_TEMP',
            'TPL_RULE_TEMP_PATH': 'TPL_RULE_TEMP_PATH',
            'ALARM_DATE': 'ALARM_DATE',
            'FAC_ID': 'FAC_ID',
            'EDIT_STATE': 'EDIT_STATE',
            'EDIT_TIME': 'EDIT_TIME',
            'EDIT_USER': 'EDIT_USER',
            'EVENT': 'EVENT',
            'EVENT_KEY': 'EVENT_KEY',
            'EVENT_TIME': 'EVENT_TIME',
            'EVENT_USER': 'EVENT_USER',
            'EVENT_MSG': 'EVENT_MSG',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER'
        },
        'field_types': {
            'APPROVAL_STATE': 'VARCHAR(20)',
            'ORT_QTY': 'INT',
            'REMAIN_QTY': 'INT',
            'STANDARD_YIELD': 'DECIMAL(5,2)',
            'LOW_YIELD': 'DECIMAL(5,2)',
            'DOWN_YIELD': 'DECIMAL(5,2)',
            'TEST_TIME': 'INT',
            'UPH': 'INT',
            'SITE': 'INT',
            'HIGH_YIELD': 'DECIMAL(5,2)',
            'VISION_LOSS_YIELD': 'DECIMAL(5,2)',
            'VISION_YIELD': 'DECIMAL(5,2)',
            'LOSS_YIELD': 'DECIMAL(5,2)'
        },
        'protect_mode': True  # 🔒 严格保护核心测试规范表
    },
    
    'et_wait_lot': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'LOT_ID': 'LOT_ID',
            'LOT_TYPE': 'LOT_TYPE',
            'GOOD_QTY': 'GOOD_QTY',
            'PROD_ID': 'PROD_ID',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'PO_ID': 'PO_ID',
            'STAGE': 'STAGE',
            'WIP_STATE': 'WIP_STATE',
            'PROC_STATE': 'PROC_STATE',
            'HOLD_STATE': 'HOLD_STATE',
            'FLOW_ID': 'FLOW_ID',
            'FLOW_VER': 'FLOW_VER',
            'RELEASE_TIME': 'RELEASE_TIME',
            'FAC_ID': 'FAC_ID',
            'CREATE_TIME': 'CREATE_TIME'
        },
        'field_types': {
            'GOOD_QTY': 'INT',
            'HOLD_STATE': 'INT'
        },
        'protect_mode': True  # 🔒 保护待测批次表
    },
    
    'et_uph_eqp': {
        'required_fields': ['DEVICE', 'STAGE', 'UPH'],
        'field_mapping': {
            'DEVICE': 'DEVICE',
            'PKG_PN': 'PKG_PN',
            'STAGE': 'STAGE',
            'UPH': 'UPH',
            'SORTER_MODEL': 'SORTER_MODEL',
            'FAC_ID': 'FAC_ID',
            'EDIT_STATE': 'EDIT_STATE',
            'EDIT_TIME': 'EDIT_TIME',
            'EDIT_USER': 'EDIT_USER',
            'EVENT': 'EVENT',
            'EVENT_KEY': 'EVENT_KEY',
            'EVENT_TIME': 'EVENT_TIME',
            'EVENT_USER': 'EVENT_USER',
            'EVENT_MSG': 'EVENT_MSG',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER'
        },
        'field_types': {
            'UPH': 'INT'
        },
        'protect_mode': True  # 🔒 保护设备产能表
    },
    
    'eqp_status': {
        'required_fields': ['HANDLER_ID', 'HANDLER_TYPE', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'HANDLER_ID': 'HANDLER_ID',
            'HANDLER_TYPE': 'HANDLER_TYPE',
            'TESTER_ID': 'TESTER_ID',
            'HANDLER_CONFIG': 'HANDLER_CONFIG',
            'SOCKET_PN': 'SOCKET_PN',
            'KIT_PN': 'KIT_PN',
            'EQP_CLASS': 'EQP_CLASS',
            'EQP_TYPE': 'EQP_TYPE',
            'TEMPERATURE_RANGE': 'TEMPERATURE_RANGE',
            'TEMPERATURE_CAPACITY': 'TEMPERATURE_CAPACITY',
            'LOT_ID': 'LOT_ID',
            'DEVICE': 'DEVICE',
            'STATUS': 'STATUS',
            'HB_PN': 'HB_PN',
            'TB_PN': 'TB_PN',
            'TESTER_CONFIG': 'TESTER_CONFIG',
            'STAGE': 'STAGE',
            'EVENT_TIME': 'EVENT_TIME'
        },
        'protect_mode': True  # 🔒 保护设备状态表
    },
    
    'tcc_inv': {
        'required_fields': ['硬件编码', '类别', '设备机型'],
        'field_mapping': {
            '硬件编码': '硬件编码',
            '关键硬件': '关键硬件',
            '图片': '图片',
            '寿命状态': '寿命状态',
            '仓库': '仓库',
            '初始库位': '初始库位',
            '当前储位1': '当前储位1',
            '当前储位2': '当前储位2',
            '责任人': '责任人',
            '周期消耗数': '周期消耗数',
            '当前库位': '当前库位',
            '封装形式': '封装形式',
            '状态': '状态',
            '类别': '类别',
            '设备机型': '设备机型',
            '寄放方': '寄放方',
            '备注(状态 shipout信息)': '备注_状态_shipout信息_',
            '类型': '类型',
            '操作': '操作'
        },
        'field_types': {
            '周期消耗数': 'INT'
        },
        'protect_mode': True  # 🔒 保护硬件库存表
    },
    
    'ct': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'LOT_ID': 'LOT_ID',
            'WORK_ORDER_ID': 'WORK_ORDER_ID',
            'PROD_ID': 'PROD_ID',
            'DEVICE': 'DEVICE',
            'PKG_PN': 'PKG_PN',
            'CHIP_ID': 'CHIP_ID',
            'FLOW_ID': 'FLOW_ID',
            'STAGE': 'STAGE',
            'LOT_QTY': 'LOT_QTY',
            'ACT_QTY': 'ACT_QTY',
            'GOOD_QTY': 'GOOD_QTY',
            'REJECT_QTY': 'REJECT_QTY',
            'LOSS_QTY': 'LOSS_QTY',
            'MAIN_EQP_ID': 'MAIN_EQP_ID',
            'AUXILIARY_EQP_ID': 'AUXILIARY_EQP_ID',
            'LOT_START_TIME': 'LOT_START_TIME',
            'LOT_END_TIME': 'LOT_END_TIME',
            'SETUP_TIME': 'SETUP_TIME',
            'FT_TEST_PROGRAM': 'FT_TEST_PROGRAM',
            'IS_HALF_LOT_DOWN': 'IS_HALF_LOT_DOWN',
            'FIRST_PASS_YIELD': 'FIRST_PASS_YIELD',
            'FINAL_YIELD': 'FINAL_YIELD',
            'VM_QTY': 'VM_QTY',
            'ALARM_BIN': 'ALARM_BIN',
            'EVENT': 'EVENT',
            'EVENT_KEY': 'EVENT_KEY',
            'EVENT_TIME': 'EVENT_TIME',
            'EVENT_USER': 'EVENT_USER',
            'EVENT_MSG': 'EVENT_MSG',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER',
            'FAC_ID': 'FAC_ID',
            'TRACK_CNT': 'TRACK_CNT',
            'COST_TIME': 'COST_TIME'
        },
        'field_types': {
            'LOT_QTY': 'INT',
            'ACT_QTY': 'INT',
            'GOOD_QTY': 'INT',
            'REJECT_QTY': 'INT',
            'LOSS_QTY': 'INT',
            'VM_QTY': 'INT',
            'TRACK_CNT': 'INT',
            'FIRST_PASS_YIELD': 'DECIMAL(5,2)',
            'FINAL_YIELD': 'DECIMAL(5,2)'
        },
        'protect_mode': True  # 🔒 保护生产周期表
    },
    
    'wip_lot': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'LOT_ID': 'LOT_ID',
            'LOT_TYPE': 'LOT_TYPE',
            'DET_LOT_TYPE': 'DET_LOT_TYPE',
            'LOT_QTY': 'LOT_QTY',
            'SUB_QTY': 'SUB_QTY',
            'UNIT': 'UNIT',
            'SUB_UNIT': 'SUB_UNIT',
            'WIP_STATE': 'WIP_STATE',
            'PROC_STATE': 'PROC_STATE',
            'HOLD_STATE': 'HOLD_STATE',
            'RW_STATE': 'RW_STATE',
            'REPAIR_STATE': 'REPAIR_STATE',
            'QC_STATE': 'QC_STATE',
            'PROD_ID': 'PROD_ID',
            'STAGE': 'STAGE',
            'GOOD_QTY': 'GOOD_QTY',
            'DEVICE': 'DEVICE',
            'PKG_PN': 'PKG_PN',
            'CHIP_ID': 'CHIP_ID',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER',
            'FAC_ID': 'FAC_ID'
            # 注意：这里只映射了核心字段，完整的wip_lot表有100+字段
        },
        'field_types': {
            'LOT_QTY': 'INT',
            'SUB_QTY': 'INT',
            'HOLD_STATE': 'INT',
            'RW_STATE': 'INT',
            'REPAIR_STATE': 'INT',
            'QC_STATE': 'INT',
            'GOOD_QTY': 'INT'
        },
        'protect_mode': True  # 🔒 严格保护在制品表
    },
    
    'et_recipe_file': {
        'required_fields': ['DEVICE', 'STAGE', 'RECIPE_FILE_NAME'],
        'field_mapping': {
            'PROD_ID': 'PROD_ID',
            'COMPANY_ID': 'COMPANY_ID',
            'STAGE': 'STAGE',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'RECIPE_FILE_NAME': 'RECIPE_FILE_NAME',
            'RECIPE_FILE_PATH': 'RECIPE_FILE_PATH',
            'APPROVAL_STATE': 'APPROVAL_STATE',
            'FAC_ID': 'FAC_ID',
            'EDIT_STATE': 'EDIT_STATE',
            'EDIT_TIME': 'EDIT_TIME',
            'EDIT_USER': 'EDIT_USER',
            'EVENT': 'EVENT',
            'EVENT_KEY': 'EVENT_KEY',
            'EVENT_TIME': 'EVENT_TIME',
            'EVENT_USER': 'EVENT_USER',
            'EVENT_MSG': 'EVENT_MSG',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER',
            'PROD_TYPE': 'PROD_TYPE',
            'EQP_TYPE': 'EQP_TYPE',
            'HANDLER_CONFIG': 'HANDLER_CONFIG',
            'SIMP_RECIPE_FILE_PATH': 'SIMP_RECIPE_FILE_PATH',
            'RECIPE_VER': 'RECIPE_VER',
            'SUB_FAC': 'SUB_FAC',
            'KIT_PN': 'KIT_PN',
            'SOCKET_PN': 'SOCKET_PN',
            'FAMILY': 'FAMILY',
            'COORDINATE_ONE': 'COORDINATE_ONE',
            'COORDINATE_TWO': 'COORDINATE_TWO',
            'COORDINATE_THREE': 'COORDINATE_THREE'
        },
        'field_types': {
            'COORDINATE_ONE': 'DECIMAL(9,6)',
            'COORDINATE_TWO': 'DECIMAL(9,6)',
            'COORDINATE_THREE': 'DECIMAL(9,6)'
        },
        'protect_mode': True  # 🔒 保护设备规范文件表
    }
}

# ================================
# 数据库连接和工具函数
# ================================

def get_mysql_connection():
    """建立MySQL数据库连接 - 使用统一配置管理器"""
    # 尝试使用统一配置管理器
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
        from app.utils.unified_db_config import get_pymysql_config
        mysql_config = get_pymysql_config()
        print(f"✅ 使用config.ini配置: {mysql_config['host']}:{mysql_config['port']}")
    except Exception as e:
        print(f"⚠️ 配置文件读取失败: {e}")
        if getattr(sys, 'frozen', False):
            raise Exception("❌ exe环境必须提供config.ini配置文件！")
        # 开发环境降级
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'charset': 'utf8mb4',
            'database': 'aps'
        }
        print("使用开发环境默认配置")
    
    try:
        connection = get_db_connection()  # 使用连接池
        logger.info("✅ MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"❌ 连接MySQL数据库失败: {e}")
        safe_config = {k: v for k, v in mysql_config.items() if k != 'password'}
        safe_config['password'] = '***'
        logger.error(f"连接配置: {safe_config}")
        raise

def is_valid_excel_file(file_path):
    """检查Excel文件是否有效，过滤临时文件和无效文件"""
    try:
        file_name = file_path.name

        # 过滤临时文件
        if file_name.startswith('~$'):
            logger.debug(f"跳过Excel临时文件: {file_name}")
            return False

        # 过滤其他临时文件
        temp_patterns = ['.tmp', '.temp', '.bak', '.backup', '.~lock']
        if any(pattern in file_name.lower() for pattern in temp_patterns):
            logger.debug(f"跳过临时文件: {file_name}")
            return False

        # 检查文件大小（空文件）
        if file_path.stat().st_size == 0:
            logger.warning(f"跳过空文件: {file_name}")
            return False

        # 检查文件是否被占用（尝试打开）
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)
                if len(header) < 8:
                    logger.warning(f"文件太小，可能损坏: {file_name}")
                    return False
        except (PermissionError, OSError) as e:
            logger.warning(f"文件被占用或无法访问: {file_name} - {e}")
            return False

        # 检查文件扩展名
        valid_extensions = ['.xlsx', '.xls']
        if not any(file_name.lower().endswith(ext) for ext in valid_extensions):
            logger.debug(f"跳过非Excel文件: {file_name}")
            return False

        return True

    except Exception as e:
        logger.error(f"检查文件有效性失败: {file_path} - {e}")
        return False

def clean_dataframe(df, file_name=""):
    """清理和验证DataFrame数据"""
    try:
        if df.empty:
            logger.warning(f"DataFrame为空: {file_name}")
            return df

        # 记录原始行数
        original_rows = len(df)

        # 1. 删除完全空白的行
        df = df.dropna(how='all')

        # 2. 清理列名
        cleaned_columns = []
        for col in df.columns:
            # 转换为字符串并清理
            clean_col = str(col).strip()
            # 替换特殊字符
            clean_col = clean_col.replace('\n', '_').replace('\r', '_')
            # 移除或替换其他特殊字符，只保留字母、数字、下划线、中文字符
            clean_col = re.sub(r'[^\w\u4e00-\u9fff]', '_', clean_col)
            # 确保列名不以数字开头
            if clean_col and clean_col[0].isdigit():
                clean_col = f"col_{clean_col}"
            # 限制长度
            if len(clean_col) > 64:
                clean_col = clean_col[:64]
            # 确保不为空
            if not clean_col:
                clean_col = f"column_{len(cleaned_columns)}"
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 3. 处理重复列名
        seen_columns = set()
        final_columns = []
        for col in df.columns:
            original_col = col
            counter = 1
            while col in seen_columns:
                col = f"{original_col}_{counter}"
                counter += 1
            seen_columns.add(col)
            final_columns.append(col)

        df.columns = final_columns

        # 4. 清理数据值
        for col in df.columns:
            try:
                # 先处理NaN值，然后转换为字符串
                df[col] = df[col].fillna('')
                df[col] = df[col].astype(str)
                # 替换 'nan', 'None', 'NaT' 等为空字符串
                df[col] = df[col].replace(['nan', 'None', 'NaT', '<NA>'], '')
                # 限制字符串长度（防止过长的文本）
                df[col] = df[col].apply(lambda x: x[:1000] if len(str(x)) > 1000 else x)
            except Exception as e:
                logger.warning(f"清理列 {col} 时出现警告: {e}")
                df[col] = df[col].apply(lambda x: str(x) if pd.notna(x) else '')

        cleaned_rows = len(df)
        if original_rows != cleaned_rows:
            logger.info(f"数据清理完成: {file_name} - 原始行数: {original_rows}, 清理后: {cleaned_rows}")

        return df

    except Exception as e:
        logger.error(f"清理DataFrame失败: {file_name} - {e}")
        return df

def get_existing_table_structure(conn, table_name):
    """获取现有表的字段结构"""
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"DESCRIBE {table_name}")
            fields = cursor.fetchall()
            
            existing_fields = {}
            for field in fields:
                existing_fields[field[0]] = {
                    'type': field[1],
                    'null': field[2] == 'YES',
                    'key': field[3],
                    'default': field[4],
                    'extra': field[5]
                }
            
            logger.info(f"获取表结构成功: {table_name}, 字段数: {len(existing_fields)}")
            return existing_fields
            
    except Exception as e:
        logger.error(f"获取表结构失败: {table_name} - {e}")
        raise

def validate_and_map_fields(df, table_name, existing_structure):
    """验证并映射Excel字段到数据库字段"""
    try:
        if table_name not in BUSINESS_TABLE_SCHEMAS:
            logger.warning(f"表 {table_name} 未在字段映射配置中，使用原始字段名")
            return df, list(df.columns)
        
        schema = BUSINESS_TABLE_SCHEMAS[table_name]
        field_mapping = schema['field_mapping']
        required_fields = schema['required_fields']
        
        # 记录映射过程
        logger.info(f"🔍 开始字段验证和映射: {table_name}")
        logger.info(f"📋 Excel原始字段: {list(df.columns)}")
        
        # 1. 检查必填字段
        missing_required = []
        for req_field in required_fields:
            # 检查是否有对应的Excel字段能映射到必填字段
            excel_field_found = False
            for excel_col in df.columns:
                if field_mapping.get(excel_col) == req_field or excel_col == req_field:
                    excel_field_found = True
                    break
            
            if not excel_field_found:
                missing_required.append(req_field)
        
        if missing_required:
            raise ValueError(f"❌ Excel缺少必填字段: {missing_required}")
        
        # 2. 执行字段映射
        mapped_df = pd.DataFrame()
        mapped_fields = []
        
        for excel_col in df.columns:
            # 检查是否有字段映射
            if excel_col in field_mapping:
                db_field = field_mapping[excel_col]
                
                # 验证目标字段在数据库中存在
                if db_field not in existing_structure:
                    logger.warning(f"⚠️ 映射的目标字段 {db_field} 在数据库表中不存在，跳过")
                    continue
                    
                mapped_df[db_field] = df[excel_col]
                mapped_fields.append(db_field)
                logger.info(f"✅ 字段映射: {excel_col} -> {db_field}")
                
            elif excel_col in existing_structure:
                # 直接匹配的字段（但避免与已映射的字段冲突）
                if excel_col not in mapped_fields:
                    mapped_df[excel_col] = df[excel_col]
                    mapped_fields.append(excel_col)
                    logger.info(f"✅ 字段直接匹配: {excel_col}")
                else:
                    logger.warning(f"⚠️ 字段 {excel_col} 已被映射，跳过直接匹配")
                
            else:
                logger.warning(f"⚠️ Excel字段 {excel_col} 无法映射到数据库，将被跳过")
        
        if mapped_df.empty:
            raise ValueError(f"❌ 没有任何字段能够成功映射到表 {table_name}")
        
        logger.info(f"🎯 字段映射完成: {len(mapped_fields)} 个字段")
        logger.info(f"📋 最终映射字段: {mapped_fields}")
        
        return mapped_df, mapped_fields
        
    except Exception as e:
        logger.error(f"❌ 字段验证和映射失败: {table_name} - {e}")
        raise

def convert_data_types(mapped_df, table_name, mapped_fields, existing_structure):
    """根据数据库字段类型转换数据"""
    try:
        if table_name not in BUSINESS_TABLE_SCHEMAS:
            return mapped_df
        
        schema = BUSINESS_TABLE_SCHEMAS[table_name]
        field_types = schema.get('field_types', {})
        
        for field in mapped_fields:
            if field in field_types:
                expected_type = field_types[field]
                
                try:
                    if 'INT' in expected_type.upper():
                        # 转换为整数
                        mapped_df[field] = pd.to_numeric(mapped_df[field], errors='coerce').fillna(0).astype(int)
                        logger.debug(f"转换字段 {field} 为整数类型")
                    elif 'DECIMAL' in expected_type.upper() or 'FLOAT' in expected_type.upper():
                        # 转换为浮点数
                        mapped_df[field] = pd.to_numeric(mapped_df[field], errors='coerce').fillna(0.0)
                        logger.debug(f"转换字段 {field} 为浮点数类型")
                    elif 'DATE' in expected_type.upper() or 'TIME' in expected_type.upper():
                        # 转换为日期时间
                        mapped_df[field] = pd.to_datetime(mapped_df[field], errors='coerce')
                        logger.debug(f"转换字段 {field} 为日期时间类型")
                    elif 'BOOL' in expected_type.upper():
                        # 转换为布尔值
                        mapped_df[field] = mapped_df[field].astype(str).str.lower().isin(['true', '1', 'yes', 'y'])
                        logger.debug(f"转换字段 {field} 为布尔类型")
                        
                except Exception as e:
                    logger.warning(f"字段 {field} 类型转换失败: {e}, 保持原始类型")
        
        logger.info(f"✅ 数据类型转换完成: {table_name}")
        return mapped_df
        
    except Exception as e:
        logger.error(f"数据类型转换失败: {table_name} - {e}")
        return mapped_df

def safe_import_to_table(conn, table_name, mapped_df, mapped_fields, import_mode='append'):
    """
    安全导入数据到表，支持保护模式和多种导入模式
    
    Args:
        conn: 数据库连接
        table_name: 目标表名
        mapped_df: 映射后的数据DataFrame
        mapped_fields: 映射后的字段列表
        import_mode: 导入模式 ('append'=追加, 'refresh'=刷新)
    
    Returns:
        int: 导入的记录数
    """
    try:
        # 检查是否为保护模式
        is_protected = (table_name in BUSINESS_TABLE_SCHEMAS and 
                       BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode', False))
        
        if is_protected:
            logger.info(f"🔒 表 {table_name} 处于保护模式，使用安全导入 (模式: {import_mode})")
            
            # 保护模式：只插入数据，不修改表结构
            with conn.cursor() as cursor:
                # 检查表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if not cursor.fetchone():
                    raise ValueError(f"保护模式下表 {table_name} 不存在，无法创建")
                
                # 🔄 新功能：根据导入模式决定是否清空现有数据或智能去重
                if import_mode == 'refresh':
                    # 获取清空前的记录数（用于日志）
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    old_count = cursor.fetchone()[0]
                    
                    # 清空现有数据
                    cursor.execute(f"TRUNCATE TABLE `{table_name}`")
                    logger.info(f"🧹 刷新模式：已清空表 {table_name} 的现有数据 ({old_count:,} 条记录)")
                    
                    # 准备插入语句
                    columns = ', '.join([f"`{field}`" for field in mapped_fields])
                    placeholders = ', '.join(['%s'] * len(mapped_fields))
                    insert_sql = f"INSERT INTO `{table_name}` ({columns}) VALUES ({placeholders})"
                    
                    # 批量插入数据
                    data_rows = []
                    for _, row in mapped_df.iterrows():
                        data_row = [row[field] if pd.notna(row[field]) else None for field in mapped_fields]
                        data_rows.append(data_row)
                    
                    cursor.executemany(insert_sql, data_rows)
                    inserted_count = len(data_rows)
                    
                elif import_mode == 'append':
                    logger.info(f"📝 智能追加模式：只添加不重复的数据到表 {table_name}")
                    
                    # 🎯 智能去重：使用INSERT IGNORE或ON DUPLICATE KEY UPDATE
                    inserted_count = insert_with_smart_deduplication(cursor, table_name, mapped_df, mapped_fields)
                    
                else:
                    raise ValueError(f"不支持的导入模式: {import_mode}，支持的模式: 'append', 'refresh'")
                
                conn.commit()
                
                mode_desc = "刷新导入" if import_mode == 'refresh' else "智能追加导入"
                logger.info(f"✅ 保护模式{mode_desc}完成: {table_name}, 插入 {inserted_count:,} 条记录")
                return inserted_count
        else:
            logger.info(f"🔓 表 {table_name} 非保护模式，使用常规导入 (模式: {import_mode})")
            # 非保护模式：可以创建/修改表结构（保持原有逻辑）
            return regular_import_to_table(conn, table_name, mapped_df, mapped_fields, import_mode)
            
    except Exception as e:
        logger.error(f"安全导入失败: {table_name} - {e}")
        raise

def regular_import_to_table(conn, table_name, df, columns, import_mode='append'):
    """
    常规模式导入，支持多种导入模式
    
    Args:
        conn: 数据库连接
        table_name: 目标表名
        df: 数据DataFrame
        columns: 字段列表
        import_mode: 导入模式 ('append'=追加, 'refresh'=刷新)
    
    Returns:
        int: 导入的记录数
    """
    try:
        with conn.cursor() as cursor:
            # 检查表是否存在
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                # 创建表
                create_table_sql = f"CREATE TABLE `{table_name}` ("
                create_table_sql += "id INT AUTO_INCREMENT PRIMARY KEY, "
                
                for col in columns:
                    create_table_sql += f"`{col}` TEXT, "
                
                create_table_sql += "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                create_table_sql += "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
                create_table_sql += ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
                
                cursor.execute(create_table_sql)
                logger.info(f"创建表: {table_name}")
            else:
                # 🔄 新功能：根据导入模式决定数据处理策略
                if import_mode == 'refresh':
                    # 获取清空前的记录数（用于日志）
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    old_count = cursor.fetchone()[0]
                    
                    # 清空现有数据
                    cursor.execute(f"TRUNCATE TABLE `{table_name}`")
                    logger.info(f"🧹 刷新模式：已清空表 {table_name} 的现有数据 ({old_count:,} 条记录)")
                    
                    # 插入数据
                    columns_str = ', '.join([f"`{col}`" for col in columns])
                    placeholders = ', '.join(['%s'] * len(columns))
                    insert_sql = f"INSERT INTO `{table_name}` ({columns_str}) VALUES ({placeholders})"
                    
                    data_rows = []
                    for _, row in df.iterrows():
                        data_row = [row[col] if pd.notna(row[col]) else None for col in columns]
                        data_rows.append(data_row)
                    
                    cursor.executemany(insert_sql, data_rows)
                    inserted_count = len(data_rows)
                    
                elif import_mode == 'append':
                    logger.info(f"📝 智能追加模式：只添加不重复的数据到表 {table_name}")
                    
                    # 🎯 智能去重：使用INSERT IGNORE或ON DUPLICATE KEY UPDATE
                    inserted_count = insert_with_smart_deduplication(cursor, table_name, df, columns)
            
            conn.commit()
            
            mode_desc = "刷新导入" if import_mode == 'refresh' else "智能追加导入"
            logger.info(f"常规模式{mode_desc}完成: {table_name}, 插入 {inserted_count:,} 条记录")
            return inserted_count
            
    except Exception as e:
        logger.error(f"常规导入失败: {table_name} - {e}")
        raise

def insert_with_smart_deduplication(cursor, table_name, df, columns):
    """
    智能去重插入数据
    
    Args:
        cursor: 数据库游标
        table_name: 目标表名
        df: 数据DataFrame
        columns: 字段列表
    
    Returns:
        int: 实际插入的记录数
    """
    try:
        # 准备数据
        data_rows = []
        for _, row in df.iterrows():
            data_row = [row[col] if pd.notna(row[col]) else None for col in columns]
            data_rows.append(tuple(data_row))
        
        if not data_rows:
            logger.info(f"没有数据需要插入到表 {table_name}")
            return 0
        
        # 🎯 策略1：使用INSERT IGNORE避免重复数据（适用于有主键或唯一索引的表）
        columns_str = ', '.join([f"`{col}`" for col in columns])
        placeholders = ', '.join(['%s'] * len(columns))
        
        # 先尝试使用INSERT IGNORE
        insert_ignore_sql = f"INSERT IGNORE INTO `{table_name}` ({columns_str}) VALUES ({placeholders})"
        
        # 获取插入前的记录数
        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
        before_count = cursor.fetchone()[0]
        
        # 执行插入
        cursor.executemany(insert_ignore_sql, data_rows)
        
        # 获取插入后的记录数
        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
        after_count = cursor.fetchone()[0]
        
        actual_inserted = after_count - before_count
        duplicates_skipped = len(data_rows) - actual_inserted
        
        logger.info(f"🎯 智能去重完成: {table_name}")
        logger.info(f"   📊 尝试插入: {len(data_rows):,} 条记录")
        logger.info(f"   ✅ 实际插入: {actual_inserted:,} 条记录")
        logger.info(f"   🔄 跳过重复: {duplicates_skipped:,} 条记录")
        
        return actual_inserted
        
    except Exception as e:
        # 如果INSERT IGNORE失败，回退到基于内容的去重策略
        logger.warning(f"INSERT IGNORE失败，使用内容比较去重: {e}")
        return insert_with_content_deduplication(cursor, table_name, df, columns)

def insert_with_content_deduplication(cursor, table_name, df, columns):
    """
    基于内容比较的去重插入（适用于没有主键或唯一索引的表）
    
    Args:
        cursor: 数据库游标
        table_name: 目标表名
        df: 数据DataFrame
        columns: 字段列表
    
    Returns:
        int: 实际插入的记录数
    """
    try:
        logger.info(f"🔍 使用内容比较进行去重: {table_name}")
        
        # 获取现有数据的哈希值（用于快速比较）
        cursor.execute(f"SELECT {', '.join([f'`{col}`' for col in columns])} FROM `{table_name}`")
        existing_data = cursor.fetchall()
        
        # 创建现有数据的哈希集合
        existing_hashes = set()
        for row in existing_data:
            # 将None值转换为空字符串进行哈希
            row_str = '|'.join([str(v) if v is not None else '' for v in row])
            existing_hashes.add(hash(row_str))
        
        logger.info(f"📊 现有数据记录数: {len(existing_data):,}")
        
        # 过滤出不重复的新数据
        new_data_rows = []
        duplicates_found = 0
        
        for _, row in df.iterrows():
            data_row = [row[col] if pd.notna(row[col]) else None for col in columns]
            
            # 创建新数据的哈希
            row_str = '|'.join([str(v) if v is not None else '' for v in data_row])
            row_hash = hash(row_str)
            
            if row_hash not in existing_hashes:
                new_data_rows.append(data_row)
                existing_hashes.add(row_hash)  # 避免新数据内部重复
            else:
                duplicates_found += 1
        
        # 插入不重复的数据
        if new_data_rows:
            columns_str = ', '.join([f"`{col}`" for col in columns])
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO `{table_name}` ({columns_str}) VALUES ({placeholders})"
            
            cursor.executemany(insert_sql, new_data_rows)
            
            logger.info(f"🎯 内容去重完成: {table_name}")
            logger.info(f"   📊 尝试插入: {len(df):,} 条记录")
            logger.info(f"   ✅ 实际插入: {len(new_data_rows):,} 条记录")
            logger.info(f"   🔄 跳过重复: {duplicates_found:,} 条记录")
            
            return len(new_data_rows)
        else:
            logger.info(f"📝 所有数据都是重复的，跳过插入: {table_name}")
            logger.info(f"   🔄 跳过重复: {len(df):,} 条记录")
            return 0
            
    except Exception as e:
        logger.error(f"内容去重插入失败: {table_name} - {e}")
        raise

def update_progress(percent, message, current_file=None, files_processed=0, total_files=0, error=False):
    """更新导入进度到文件"""
    try:
        # 修复路径计算：从当前文件位置计算到项目根目录的instance文件夹
        current_dir = os.path.dirname(__file__)  # tools/data_import/
        project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录
        instance_path = os.path.join(project_root, 'instance')
        
        if not os.path.exists(instance_path):
            os.makedirs(instance_path)
        
        progress_file = os.path.join(instance_path, 'import_progress.json')
        
        progress_data = {
            'percent': percent,
            'message': message,
            'files_processed': files_processed,
            'total_files': total_files,
            'timestamp': time.time()
        }
        
        if current_file:
            progress_data['current_file'] = current_file
            
        if error:
            progress_data['error'] = True
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"📊 进度更新: {percent}% - {message}")
        
    except Exception as e:
        logger.error(f"更新进度失败: {e}")
        # 打印详细错误信息用于调试
        import traceback
        logger.error(f"进度更新详细错误: {traceback.format_exc()}")

def import_excel_files_safely(directory_path, import_mode='append'):
    """
    安全导入Excel文件的主函数
    
    Args:
        directory_path: Excel文件目录路径
        import_mode: 导入模式 ('append'=追加模式, 'refresh'=刷新模式)
    
    Returns:
        tuple: (success, result)
    """
    try:
        start_time = time.time()
        logger.info(f"🚀 开始安全导入Excel文件: {directory_path}")
        
        # 验证目录
        if not os.path.exists(directory_path):
            raise ValueError(f"目录不存在: {directory_path}")
        
        directory_path = Path(directory_path)
        
        # 获取所有Excel文件
        excel_files = []
        for pattern in ['*.xlsx', '*.xls']:
            excel_files.extend(directory_path.glob(pattern))
        
        # 过滤有效文件
        valid_files = [f for f in excel_files if is_valid_excel_file(f)]
        
        if not valid_files:
            logger.warning("未找到有效的Excel文件")
            return False, {'error': '未找到有效的Excel文件', 'message': '目录中没有可导入的Excel文件'}
        
        logger.info(f"📁 找到 {len(valid_files)} 个有效Excel文件")
        
        # 建立数据库连接
        conn = get_db_connection()
        
        # 处理文件
        processed_files = []
        failed_files = []
        processed_records = 0
        
        for i, file_path in enumerate(valid_files):
            try:
                # 更新开始处理文件的进度
                current_percent = int((i / len(valid_files)) * 90)
                update_progress(current_percent, f"正在处理文件: {file_path.name}", 
                              current_file=file_path.name, files_processed=i, total_files=len(valid_files))
                
                logger.info(f"📄 处理文件 ({i+1}/{len(valid_files)}): {file_path.name}")
                
                # 读取Excel文件
                try:
                    df = pd.read_excel(file_path, engine='openpyxl')
                except Exception as e:
                    try:
                        df = pd.read_excel(file_path, engine='xlrd')
                    except Exception as e2:
                        raise Exception(f"无法读取Excel文件: {e}, {e2}")
                
                if df.empty:
                    logger.warning(f"文件为空: {file_path.name}")
                    continue
                
                # 清理数据
                df = clean_dataframe(df, file_path.name)
                
                # 确定表名（使用文件名，去除扩展名）
                table_name = file_path.stem.lower()
                
                # 获取现有表结构
                try:
                    existing_structure = get_existing_table_structure(conn, table_name)
                except:
                    # 如果表不存在且为保护模式，则跳过
                    if (table_name in BUSINESS_TABLE_SCHEMAS and 
                        BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode', False)):
                        logger.error(f"🔒 保护模式下表 {table_name} 不存在，跳过文件: {file_path.name}")
                        failed_files.append({
                            'file': file_path.name,
                            'error': f'保护模式下表 {table_name} 不存在'
                        })
                        continue
                    else:
                        # 非保护模式，表不存在时existing_structure为空
                        existing_structure = {}
                
                # 字段验证和映射
                if table_name in BUSINESS_TABLE_SCHEMAS:
                    mapped_df, mapped_fields = validate_and_map_fields(df, table_name, existing_structure)
                    mapped_df = convert_data_types(mapped_df, table_name, mapped_fields, existing_structure)
                else:
                    mapped_df = df
                    mapped_fields = list(df.columns)
                
                # 安全导入数据
                record_count = safe_import_to_table(conn, table_name, mapped_df, mapped_fields, import_mode)
                
                processed_files.append({
                    'file': file_path.name,
                    'table': table_name,
                    'records': record_count,
                    'fields': len(mapped_fields),
                    'protected': table_name in BUSINESS_TABLE_SCHEMAS and BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode', False)
                })
                processed_records += record_count
                
                # 更新文件处理完成的进度
                completed_percent = int(((i + 1) / len(valid_files)) * 90)
                update_progress(completed_percent, f"已完成文件: {file_path.name} ({record_count} 条记录)", 
                              current_file=file_path.name, files_processed=i+1, total_files=len(valid_files))
                
                logger.info(f"✅ 文件处理完成: {file_path.name} -> {table_name} ({record_count} 条记录)")
                
            except Exception as e:
                logger.error(f"❌ 文件处理失败: {file_path.name} - {e}")
                failed_files.append({
                    'file': file_path.name,
                    'error': str(e)
                })
                
                # 更新失败文件的进度
                failed_percent = int(((i + 1) / len(valid_files)) * 90)
                update_progress(failed_percent, f"文件处理失败: {file_path.name}", 
                              current_file=file_path.name, files_processed=i+1, total_files=len(valid_files))
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 最终进度更新
        update_progress(100, "安全导入完成！")
        
        # 准备返回结果
        mode_desc = "刷新模式" if import_mode == 'refresh' else "智能追加模式"
        result = {
            'message': f'{mode_desc}导入完成，成功处理 {len(processed_files)} 个文件，失败 {len(failed_files)} 个',
            'processed_files': processed_files,
            'failed_files': failed_files,
            'total_files': len(valid_files),
            'failed_count': len(failed_files),
            'total_records': processed_records,
            'processing_time': round(processing_time, 2),
            'protection_enabled': True,
            'import_mode': import_mode,
            'mode_description': "清空后导入" if import_mode == 'refresh' else "只添加不重复的数据"
        }
        
        if failed_files:
            result['details'] = f'部分文件处理失败，请检查日志'
            logger.warning(f"⚠️ 部分文件处理失败: {len(failed_files)} 个")
            return len(processed_files) > 0, result
        else:
            result['details'] = f'所有文件处理成功，共导入 {processed_records} 条记录'
            logger.info(f"🎉 所有文件处理成功！")
            return True, result
        
    except Exception as e:
        logger.error(f"❌ 安全导入过程失败: {e}")
        update_progress(0, f"导入失败: {str(e)}", error=True)
        return False, {'error': str(e), 'message': '安全导入过程失败'}

# ================================
# 兼容性接口
# ================================

def import_from_directory(directory_path, db_path=None, import_mode='append'):
    """
    兼容性函数，使用安全导入模式
    
    Args:
        directory_path: Excel文件目录路径
        db_path: 数据库路径（忽略，MySQL模式下不使用）
        import_mode: 导入模式 ('append'=追加模式, 'refresh'=刷新模式)
    
    Returns:
        tuple: (success, result)
    """
    return import_excel_files_safely(directory_path, import_mode)

# 注意：import_excel_files_safely 函数已在上面定义，这里移除重复定义

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("用法: python import_excel_to_mysql.py <Excel目录路径>")
        print("注意：这是增强版安全导入工具，具备完整的字段卡控保护")
        sys.exit(1)
    
    directory_path = sys.argv[1]
    success, result = import_excel_files_safely(directory_path)
    
    if success:
        print(f"✅ 增强版安全导入成功: {result['message']}")
        print(f"   处理时间: {result['processing_time']} 秒")
        print(f"   总记录数: {result['total_records']}")
        print(f"   字段保护: ✅ 已启用")
        
        # 显示保护状态统计
        protected_count = sum(1 for f in result['processed_files'] if f.get('protected', False))
        print(f"   保护表数: {protected_count}/{len(result['processed_files'])}")
    else:
        print(f"❌ 增强版安全导入失败: {result.get('error', '未知错误')}")
        if 'warnings' in result:
            for warning in result['warnings']:
                print(f"⚠️  {warning}")
        sys.exit(1) 