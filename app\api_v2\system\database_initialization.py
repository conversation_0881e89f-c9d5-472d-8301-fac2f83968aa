"""
数据库初始化API接口
提供数据库状态检查、初始化、迁移等REST API
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
import logging
from datetime import datetime
from dataclasses import asdict
from app.services.database_initialization_service import (
    DatabaseInitializationService,
    EnvironmentInfo,
    DatabaseStatus,
    InitializationResult,
    MigrationResult
)
from app.utils.response_utils import success_response, error_response
from app.decorators import admin_required
import traceback

# 创建蓝图
db_init_bp = Blueprint('database_initialization', __name__)
logger = logging.getLogger(__name__)

# 初始化服务
db_service = DatabaseInitializationService()

@db_init_bp.route('/environment/detect', methods=['GET'])
@login_required
@admin_required
def detect_environment():
    """
    环境检测API
    检测当前数据库环境状态，包括MySQL版本、数据库存在性、表数量等
    """
    try:
        logger.info(f"用户 {current_user.username} 请求环境检测")
        
        # 执行环境检测
        env_info = db_service.detect_environment()
        
        # 转换为字典
        result = asdict(env_info)
        
        logger.info(f"环境检测完成: {result}")
        
        return success_response(
            data=result,
            message="环境检测完成"
        )
        
    except Exception as e:
        logger.error(f"环境检测失败: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"环境检测失败: {str(e)}", status_code=500)


@db_init_bp.route('/status', methods=['GET'])
@login_required
@admin_required
def check_database_status():
    """
    数据库状态检查API
    检查数据库连接状态、现有表、缺失表等信息
    """
    try:
        logger.info(f"用户 {current_user.username} 请求数据库状态检查")
        
        # 执行状态检查
        status = db_service.check_database_status()
        
        # 转换为字典并格式化时间
        result = asdict(status)
        result['last_check_time'] = status.last_check_time.isoformat()
        
        logger.info(f"状态检查完成: 连接状态={status.connection_status}, "
                   f"现有表={len(status.existing_tables)}, 缺失表={len(status.missing_tables)}")
        
        return success_response(
            data=result,
            message="数据库状态检查完成"
        )
        
    except Exception as e:
        logger.error(f"数据库状态检查失败: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"数据库状态检查失败: {str(e)}", status_code=500)


@db_init_bp.route('/initialize', methods=['POST'])
@login_required
@admin_required
def initialize_database():
    """
    数据库初始化API - 安全模式
    不再支持自动表创建，引导用户使用安全的init_db.py
    """
    try:
        data = request.get_json() or {}
        mode = data.get('mode', 'auto')
        force_recreate = data.get('force_recreate', False)
        
        logger.warning(f"用户 {current_user.username} 尝试通过Web界面初始化数据库 (模式: {mode}, 强制重建: {force_recreate})")
        
        # 安全检查：禁用危险操作
        if force_recreate:
            logger.error(f"用户 {current_user.username} 尝试强制重建数据库 - 已被阻止")
            return error_response(
                "强制重建数据库功能已被禁用，为了数据安全。请使用命令行工具: python run.py init-db", 
                status_code=403
            )
        
        # 提供安全的替代方案
        return success_response(
            data={
                "status": "redirect_to_safe_method",
                "message": "为了数据安全，请使用命令行进行数据库初始化",
                "safe_commands": [
                    "python run.py init-db",
                    "python tools/database/init_db.py"
                ],
                "reason": "SQLAlchemy模型与真实数据库结构不匹配，自动创建可能导致数据损坏"
            },
            message="请使用安全的命令行工具进行数据库初始化"
        )
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"数据库初始化失败: {str(e)}", status_code=500)


@db_init_bp.route('/migrate', methods=['POST'])
@login_required
@admin_required
def migrate_system_database():
    """
    系统数据库迁移API
    将aps_system数据库的数据迁移到aps数据库
    """
    try:
        logger.info(f"用户 {current_user.username} 请求系统数据库迁移")
        
        # 执行迁移
        result = db_service.migrate_system_database()
        
        # 转换为字典
        result_dict = asdict(result)
        
        # 记录操作结果
        if result.success:
            logger.info(f"数据库迁移成功: 迁移了 {len(result.tables_migrated)} 个表，"
                       f"{result.records_migrated} 条记录，耗时 {result.execution_time:.2f}s")
        else:
            logger.error(f"数据库迁移失败: {result.error_messages}")
        
        return success_response(
            data=result_dict,
            message="数据库迁移完成" if result.success else "数据库迁移失败"
        )
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"数据库迁移失败: {str(e)}", status_code=500)


@db_init_bp.route('/validate', methods=['GET'])
@login_required
@admin_required
def validate_data_integrity():
    """
    数据完整性验证API
    验证数据库结构和数据的完整性
    """
    try:
        logger.info(f"用户 {current_user.username} 请求数据完整性验证")
        
        # 执行验证
        result = db_service.validate_data_integrity()
        
        logger.info(f"数据完整性验证完成: 状态={result['summary']['status']}")
        
        return success_response(
            data=result,
            message="数据完整性验证完成"
        )
        
    except Exception as e:
        logger.error(f"数据完整性验证失败: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"数据完整性验证失败: {str(e)}", status_code=500)


@db_init_bp.route('/config/test', methods=['POST'])
@login_required
@admin_required
def test_database_connection():
    """
    数据库连接测试API
    测试数据库连接配置是否正确
    """
    try:
        data = request.get_json() or {}
        
        # 如果提供了自定义配置，临时使用
        if 'config' in data:
            custom_config = data['config']
            logger.info(f"用户 {current_user.username} 使用自定义配置测试数据库连接")
            
            # 这里可以扩展支持自定义配置的测试
            # 当前先使用默认配置测试
            connection_ok = db_service._test_connection()
        else:
            logger.info(f"用户 {current_user.username} 测试当前数据库连接")
            connection_ok = db_service._test_connection()
        
        if connection_ok:
            # 获取数据库版本信息
            db_config = db_service._get_database_config()
            mysql_version = db_service._get_mysql_version(db_config)
            
            result = {
                'connection_status': True,
                'mysql_version': mysql_version,
                'database_config': {
                    'host': db_config['host'],
                    'port': db_config['port'],
                    'user': db_config['user'],
                    'db_name': db_config['db_name'],
                    'charset': db_config['charset']
                },
                'test_time': datetime.utcnow().isoformat()
            }
            
            logger.info(f"数据库连接测试成功: MySQL {mysql_version}")
            return success_response(data=result, message="数据库连接测试成功")
        else:
            result = {
                'connection_status': False,
                'test_time': datetime.utcnow().isoformat()
            }
            logger.warning("数据库连接测试失败")
            return success_response(data=result, message="数据库连接测试失败")
        
    except Exception as e:
        logger.error(f"数据库连接测试异常: {str(e)}\n{traceback.format_exc()}")
        return error_response(f"数据库连接测试异常: {str(e)}", status_code=500)


# 注册错误处理器
@db_init_bp.errorhandler(403)
def forbidden(error):
    return error_response("权限不足，需要管理员权限", status_code=403)


@db_init_bp.errorhandler(404)
def not_found(error):
    return error_response("API端点不存在", status_code=404)


@db_init_bp.errorhandler(500)
def internal_error(error):
    logger.error(f"数据库初始化API内部错误: {str(error)}")
    return error_response("内部服务器错误", status_code=500)