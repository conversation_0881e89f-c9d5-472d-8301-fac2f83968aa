#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构和数据 - 用于汇报数据分析
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_database_tables.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('DatabaseTableChecker')

def check_database_tables():
    """检查数据库表结构和关键数据"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection
            
            # 获取数据库连接
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print("\n📊 数据库表列表:")
            print("=" * 50)
            for table in tables:
                print(f"• {table[0]}")
            
            # 检查关键表的结构
            key_tables = [
                'et_wait_lot',      # 待测批次
                'lotprioritydone',  # 已排产批次  
                'final_scheduling_result', # 最终调度结果
                'wip_lot',          # 在制品批次
                'ct',               # CT数据
                'eqp_status',       # 设备状态
                'et_ft_test_spec',  # 测试规格
                'scheduling_failed_lots' # 失败批次
            ]
            
            print("\n🔍 关键表结构分析:")
            print("=" * 50)
            
            for table in key_tables:
                try:
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    
                    print(f"\n📋 {table} (记录数: {count})")
                    print("-" * 30)
                    for col in columns[:5]:  # 只显示前5个字段
                        print(f"  {col[0]} - {col[1]}")
                    if len(columns) > 5:
                        print(f"  ... 还有 {len(columns)-5} 个字段")
                        
                except Exception as e:
                    print(f"❌ 表 {table} 不存在或无法访问: {e}")
            
            # 检查产品相关数据
            print("\n🎯 产品数据分析:")
            print("=" * 50)
            
            # 检查JWQ7843和JWQ3510相关数据
            product_queries = [
                ("et_wait_lot中JWQ7843相关", "SELECT COUNT(*) FROM et_wait_lot WHERE lot_id LIKE '%JWQ7843%' OR product_id LIKE '%JWQ7843%'"),
                ("et_wait_lot中JWQ3510相关", "SELECT COUNT(*) FROM et_wait_lot WHERE lot_id LIKE '%JWQ3510%' OR product_id LIKE '%JWQ3510%'"),
                ("lotprioritydone中JWQ7843相关", "SELECT COUNT(*) FROM lotprioritydone WHERE lot_id LIKE '%JWQ7843%' OR product_id LIKE '%JWQ7843%'"),
                ("lotprioritydone中JWQ3510相关", "SELECT COUNT(*) FROM lotprioritydone WHERE lot_id LIKE '%JWQ3510%' OR product_id LIKE '%JWQ3510%'"),
            ]
            
            for desc, query in product_queries:
                try:
                    cursor.execute(query)
                    count = cursor.fetchone()[0]
                    print(f"• {desc}: {count} 条记录")
                except Exception as e:
                    print(f"❌ {desc}: 查询失败 - {e}")
            
            # 检查时间字段
            print("\n⏰ 时间相关字段分析:")
            print("=" * 50)
            
            time_queries = [
                ("et_wait_lot时间字段", "DESCRIBE et_wait_lot", "created_time|update_time|plan_time"),
                ("lotprioritydone时间字段", "DESCRIBE lotprioritydone", "created_time|update_time|plan_time"),
                ("ct表时间字段", "DESCRIBE ct", "created_time|update_time|test_time"),
            ]
            
            for desc, query, time_fields in time_queries:
                try:
                    cursor.execute(query)
                    columns = cursor.fetchall()
                    time_cols = [col for col in columns if any(tf in col[0].lower() for tf in time_fields.split('|'))]
                    print(f"• {desc}:")
                    for col in time_cols:
                        print(f"  - {col[0]} ({col[1]})")
                except Exception as e:
                    print(f"❌ {desc}: 查询失败 - {e}")
            
            cursor.close()
            conn.close()
            return True
            
    except Exception as e:
        logger.error(f"❌ 数据库检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_database_tables()
    print("\n🎉 检查完成" if success else "\n❌ 检查失败")

if __name__ == "__main__":
    main()