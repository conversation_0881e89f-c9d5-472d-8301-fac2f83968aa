
"""
会话监控中间件
监控和管理用户会话状态
"""
from flask import request, jsonify, session
from flask_login import current_user
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def require_json_auth(f):
    """
    API认证装饰器，确保API调用时用户已登录
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查是否是JSON API请求
        if request.is_json or request.headers.get('Content-Type') == 'application/json':
            if not current_user.is_authenticated:
                logger.warning(f"未认证的API请求: {request.endpoint}")
                return jsonify({
                    'success': False,
                    'message': '用户会话已过期，请刷新页面重新登录',
                    'error_type': 'session_expired',
                    'redirect_to_login': True
                }), 401
        
        return f(*args, **kwargs)
    
    return decorated_function

def check_session_health():
    """
    检查会话健康状态
    """
    if current_user.is_authenticated:
        return {
            'authenticated': True,
            'user_id': current_user.id,
            'username': getattr(current_user, 'username', 'unknown'),
            'session_id': session.get('_id', 'unknown')
        }
    else:
        return {
            'authenticated': False,
            'session_id': session.get('_id', 'unknown')
        }
