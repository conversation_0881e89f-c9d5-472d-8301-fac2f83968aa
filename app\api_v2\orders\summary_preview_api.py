#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单汇总预览API
提供订单数据预览、分类调整、汇总表生成等功能
"""

from flask import Blueprint, jsonify, request, current_app, send_file
from flask_login import login_required, current_user
from app import db
from app.models import OrderData, LotTypeClassificationRule
from app.utils.simple_logging import log_user_action
from sqlalchemy import func, desc, asc
from datetime import datetime
import os
import pandas as pd
from io import BytesIO

bp = Blueprint('summary_preview', __name__)

@bp.route('/summary-data', methods=['GET'])
@login_required
def get_summary_data():
    """获取订单汇总数据"""
    try:
        # 获取所有订单数据
        orders = db.session.query(OrderData).order_by(desc(OrderData.id)).all()
        
        # 转换为字典格式
        orders_data = []
        for order in orders:
            order_dict = order.to_dict()
            # 确保分类字段存在
            if not order_dict.get('classification'):
                # 尝试自动分类
                classification = order.auto_classify_by_lot_type()
                if classification:
                    order.classification = classification
                    db.session.commit()
                    order_dict['classification'] = classification
            
            orders_data.append(order_dict)
        
        log_user_action(current_user.id, 'query', f'获取订单汇总数据，共{len(orders_data)}条记录')
        
        return jsonify({
            'success': True,
            'data': orders_data,
            'total': len(orders_data),
            'message': f'成功获取{len(orders_data)}条订单数据'
        })
        
    except Exception as e:
        current_app.logger.error(f"获取订单汇总数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取订单数据失败'
        }), 500

@bp.route('/update-classification', methods=['POST'])
@login_required
def update_classification():
    """更新单个订单的分类"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')
        new_classification = data.get('classification')
        reason = data.get('reason', '')
        
        if not order_id or not new_classification:
            return jsonify({
                'success': False,
                'message': '订单ID和分类参数不能为空'
            }), 400
        
        # 验证分类值
        valid_classifications = ['engineering', 'production', 'unknown']
        if new_classification not in valid_classifications:
            return jsonify({
                'success': False,
                'message': f'无效的分类值，必须是: {", ".join(valid_classifications)}'
            }), 400
        
        # 查找订单
        order = db.session.query(OrderData).filter_by(id=order_id).first()
        if not order:
            return jsonify({
                'success': False,
                'message': '订单不存在'
            }), 404
        
        # 记录原分类
        old_classification = order.classification
        
        # 更新分类
        order.classification = new_classification
        order.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        log_user_action(
            current_user.id, 
            'update', 
            f'调整订单{order.order_number}分类: {old_classification} -> {new_classification}' + 
            (f', 原因: {reason}' if reason else '')
        )
        
        return jsonify({
            'success': True,
            'message': '分类调整成功',
            'data': {
                'order_id': order_id,
                'old_classification': old_classification,
                'new_classification': new_classification
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新订单分类失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '分类调整失败'
        }), 500

@bp.route('/batch-update-classification', methods=['POST'])
@login_required
def batch_update_classification():
    """批量更新订单分类"""
    try:
        data = request.get_json()
        order_ids = data.get('order_ids', [])
        new_classification = data.get('classification')
        
        if not order_ids or not new_classification:
            return jsonify({
                'success': False,
                'message': '订单ID列表和分类参数不能为空'
            }), 400
        
        # 验证分类值
        valid_classifications = ['engineering', 'production', 'unknown']
        if new_classification not in valid_classifications:
            return jsonify({
                'success': False,
                'message': f'无效的分类值，必须是: {", ".join(valid_classifications)}'
            }), 400
        
        # 批量更新
        updated_count = db.session.query(OrderData).filter(
            OrderData.id.in_(order_ids)
        ).update({
            'classification': new_classification,
            'updated_at': datetime.utcnow()
        }, synchronize_session=False)
        
        db.session.commit()
        
        log_user_action(
            current_user.id, 
            'batch_update', 
            f'批量调整{updated_count}条订单分类为: {new_classification}'
        )
        
        return jsonify({
            'success': True,
            'message': f'成功调整{updated_count}条订单的分类',
            'data': {
                'updated_count': updated_count,
                'classification': new_classification
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量更新订单分类失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '批量分类调整失败'
        }), 500

@bp.route('/classification-stats', methods=['GET'])
@login_required
def get_classification_stats():
    """获取分类统计信息"""
    try:
        # 统计各分类的数量
        stats = db.session.query(
            OrderData.classification,
            func.count(OrderData.id).label('count')
        ).group_by(OrderData.classification).all()
        
        # 统计Lot Type分布
        lot_type_stats = db.session.query(
            OrderData.lot_type,
            func.count(OrderData.id).label('count')
        ).group_by(OrderData.lot_type).all()
        
        # 格式化结果
        classification_data = {}
        for stat in stats:
            classification_data[stat.classification or 'unknown'] = stat.count
        
        lot_type_data = {}
        for stat in lot_type_stats:
            lot_type_data[stat.lot_type or 'unknown'] = stat.count
        
        return jsonify({
            'success': True,
            'data': {
                'classification_stats': classification_data,
                'lot_type_stats': lot_type_data,
                'total_orders': sum(classification_data.values())
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分类统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取统计信息失败'
        }), 500

@bp.route('/generate-engineering-summary', methods=['POST'])
@login_required
def generate_engineering_summary():
    """生成FT工程订单汇总表"""
    try:
        # 获取工程订单
        engineering_orders = db.session.query(OrderData).filter_by(
            classification='engineering'
        ).order_by(asc(OrderData.delivery_date)).all()
        
        if not engineering_orders:
            return jsonify({
                'success': False,
                'message': '没有工程订单数据可生成汇总表'
            }), 400
        
        # 创建DataFrame
        data = []
        for order in engineering_orders:
            data.append({
                '订单号': order.order_number,
                '产品名称': order.product_name,
                '电路名称': order.circuit_name,
                '芯片名称': order.chip_name,
                'Lot Type': order.lot_type,
                '圆片尺寸': order.wafer_size,
                '送包只数': order.package_qty,
                '送包片数': order.package_pieces,
                '交期': order.delivery_date,
                '承揽商': order.contractor_name,
                '来源文件': order.source_file,
                '导入时间': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else ''
            })
        
        df = pd.DataFrame(data)
        
        # 生成Excel文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'FT工程订单汇总表_{timestamp}.xlsx'
        
        # 确保导出目录存在
        export_dir = os.path.join(current_app.static_folder, 'exports')
        os.makedirs(export_dir, exist_ok=True)
        
        file_path = os.path.join(export_dir, filename)
        
        # 保存Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='FT工程订单汇总', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['FT工程订单汇总']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        log_user_action(
            current_user.id, 
            'export', 
            f'生成FT工程订单汇总表，共{len(engineering_orders)}条记录'
        )
        
        return jsonify({
            'success': True,
            'message': f'成功生成FT工程订单汇总表，共{len(engineering_orders)}条记录',
            'data': {
                'filename': filename,
                'record_count': len(engineering_orders),
                'download_url': f'/static/exports/{filename}'
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"生成工程汇总表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '生成工程汇总表失败'
        }), 500

@bp.route('/generate-production-summary', methods=['POST'])
@login_required
def generate_production_summary():
    """生成FT量产订单汇总表"""
    try:
        # 获取量产订单
        production_orders = db.session.query(OrderData).filter_by(
            classification='production'
        ).order_by(asc(OrderData.delivery_date)).all()
        
        if not production_orders:
            return jsonify({
                'success': False,
                'message': '没有量产订单数据可生成汇总表'
            }), 400
        
        # 创建DataFrame
        data = []
        for order in production_orders:
            data.append({
                '订单号': order.order_number,
                '产品名称': order.product_name,
                '电路名称': order.circuit_name,
                '芯片名称': order.chip_name,
                'Lot Type': order.lot_type,
                '圆片尺寸': order.wafer_size,
                '送包只数': order.package_qty,
                '送包片数': order.package_pieces,
                '交期': order.delivery_date,
                '承揽商': order.contractor_name,
                '来源文件': order.source_file,
                '导入时间': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else ''
            })
        
        df = pd.DataFrame(data)
        
        # 生成Excel文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'FT量产订单汇总表_{timestamp}.xlsx'
        
        # 确保导出目录存在
        export_dir = os.path.join(current_app.static_folder, 'exports')
        os.makedirs(export_dir, exist_ok=True)
        
        file_path = os.path.join(export_dir, filename)
        
        # 保存Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='FT量产订单汇总', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['FT量产订单汇总']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        log_user_action(
            current_user.id, 
            'export', 
            f'生成FT量产订单汇总表，共{len(production_orders)}条记录'
        )
        
        return jsonify({
            'success': True,
            'message': f'成功生成FT量产订单汇总表，共{len(production_orders)}条记录',
            'data': {
                'filename': filename,
                'record_count': len(production_orders),
                'download_url': f'/static/exports/{filename}'
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"生成量产汇总表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '生成量产汇总表失败'
        }), 500

@bp.route('/preview-summary', methods=['GET'])
@login_required
def preview_summary():
    """预览汇总表数据"""
    try:
        summary_type = request.args.get('type', 'all')  # all, engineering, production
        
        # 构建查询条件
        query = db.session.query(OrderData)
        
        if summary_type == 'engineering':
            query = query.filter_by(classification='engineering')
        elif summary_type == 'production':
            query = query.filter_by(classification='production')
        
        orders = query.order_by(asc(OrderData.delivery_date)).limit(100).all()
        
        # 转换为预览数据
        preview_data = []
        for order in orders:
            preview_data.append({
                'order_number': order.order_number,
                'product_name': order.product_name,
                'circuit_name': order.circuit_name,
                'lot_type': order.lot_type,
                'classification': order.classification,
                'delivery_date': order.delivery_date,
                'contractor_name': order.contractor_name,
                'package_qty': order.package_qty,
                'package_pieces': order.package_pieces
            })
        
        return jsonify({
            'success': True,
            'data': preview_data,
            'type': summary_type,
            'count': len(preview_data),
            'message': f'预览{summary_type}汇总表数据'
        })
        
    except Exception as e:
        current_app.logger.error(f"预览汇总表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '预览汇总表失败'
        }), 500

@bp.route('/download-summary/<filename>', methods=['GET'])
@login_required
def download_summary(filename):
    """下载汇总表文件"""
    try:
        export_dir = os.path.join(current_app.static_folder, 'exports')
        file_path = os.path.join(export_dir, filename)
        
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404
        
        log_user_action(current_user.id, 'download', f'下载汇总表文件: {filename}')
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        current_app.logger.error(f"下载汇总表文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '下载文件失败'
        }), 500 