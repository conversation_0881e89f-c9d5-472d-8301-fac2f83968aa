
"""
资源使用优化管理器
"""
import gc
import threading
import time
from datetime import datetime, timedelta

class ResourceManager:
    def __init__(self):
        self.cleanup_interval = 300  # 5分钟清理一次
        self.cleanup_thread = None
        self.running = False
    
    def start_cleanup_scheduler(self):
        """启动资源清理调度器"""
        if self.running:
            return
        
        self.running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def stop_cleanup_scheduler(self):
        """停止资源清理调度器"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join()
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                self.cleanup_resources()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                print(f"资源清理错误: {e}")
    
    def cleanup_resources(self):
        """清理系统资源"""
        # 强制垃圾回收
        collected = gc.collect()
        
        # 清理过期的缓存
        self._cleanup_expired_cache()
        
        # 清理过期的session
        self._cleanup_expired_sessions()
        
        print(f"📊 资源清理完成: 回收 {collected} 个对象")
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        try:
            from app.utils.cache_manager import cache_manager
            # 这里可以添加缓存过期清理逻辑
            pass
        except ImportError:
            pass
    
    def _cleanup_expired_sessions(self):
        """清理过期session"""
        try:
            from flask import current_app
            # 这里可以添加session清理逻辑
            pass
        except:
            pass
    
    def get_resource_stats(self):
        """获取资源使用统计"""
        import psutil
        import sys
        
        process = psutil.Process()
        
        return {
            'cpu_percent': process.cpu_percent(),
            'memory_mb': round(process.memory_info().rss / 1024 / 1024, 2),
            'memory_percent': process.memory_percent(),
            'num_threads': process.num_threads(),
            'num_fds': process.num_fds() if hasattr(process, 'num_fds') else None,
            'python_objects': len(gc.get_objects()),
            'python_version': sys.version
        }

# 全局资源管理器
resource_manager = ResourceManager()
