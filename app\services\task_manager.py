#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器
支持长时间运行任务的状态追踪和进度管理
"""

import json
import logging
import uuid
import threading
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, asdict
from .event_bus import get_event_bus

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = 'pending'         # 等待中
    RUNNING = 'running'         # 运行中
    PAUSED = 'paused'          # 暂停
    COMPLETED = 'completed'     # 已完成
    FAILED = 'failed'          # 失败
    CANCELLED = 'cancelled'     # 已取消

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class TaskProgress:
    """任务进度信息"""
    current: int = 0           # 当前进度
    total: int = 100          # 总进度
    percentage: float = 0.0    # 进度百分比
    message: str = ""         # 进度消息
    current_step: str = ""    # 当前步骤
    steps_completed: int = 0  # 已完成步骤数
    total_steps: int = 1      # 总步骤数
    
    def update(self, current: int = None, total: int = None, 
               message: str = None, current_step: str = None):
        """更新进度信息"""
        if current is not None:
            self.current = current
        if total is not None:
            self.total = total
        if message is not None:
            self.message = message
        if current_step is not None:
            self.current_step = current_step
            
        # 计算百分比
        if self.total > 0:
            self.percentage = (self.current / self.total) * 100
        else:
            self.percentage = 0.0

@dataclass
class Task:
    """任务信息"""
    id: str
    name: str
    description: str = ""
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.NORMAL
    progress: TaskProgress = None
    result: Dict[str, Any] = None
    error: str = ""
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    user_id: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.progress is None:
            self.progress = TaskProgress()
        if self.result is None:
            self.result = {}
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理日期时间字段
        for field in ['created_at', 'started_at', 'completed_at']:
            if data[field]:
                data[field] = data[field].isoformat()
        # 处理枚举字段
        data['status'] = self.status.value
        data['priority'] = self.priority.value
        return data

class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 500, app=None):
        """初始化任务管理器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
            app: Flask应用实例
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self._tasks: Dict[str, Task] = {}
        self._task_threads: Dict[str, threading.Thread] = {}
        self._task_lock = threading.Lock()
        self._event_bus = get_event_bus()
        self._running = True
        self._cleanup_thread = None
        self._app = app  # 保存应用实例引用
        
        # 如果没有传入app，尝试获取当前应用
        if self._app is None:
            try:
                from flask import current_app
                self._app = current_app._get_current_object()
            except (RuntimeError, AttributeError):
                self._app = None
        
        # 启动清理线程
        self._start_cleanup_thread()
    
    def create_task(self, name: str, description: str = "", 
                   priority: TaskPriority = TaskPriority.NORMAL,
                   user_id: str = "", metadata: Dict[str, Any] = None) -> str:
        """创建新任务
        
        Args:
            name: 任务名称
            description: 任务描述
            priority: 任务优先级
            user_id: 用户ID
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        with self._task_lock:
            task = Task(
                id=task_id,
                name=name,
                description=description,
                priority=priority,
                user_id=user_id,
                metadata=metadata or {}
            )
            self._tasks[task_id] = task
        
        # 发布任务创建事件
        self._event_bus.publish_event(
            self._event_bus.EVENT_TYPES['TASK_CREATED'],
            {'task': task.to_dict()},
            user_id=user_id
        )
        
        logger.info(f"任务已创建: {task_id} - {name}")
        return task_id
    
    def start_task(self, task_id: str, target_func: Callable, 
                  *args, **kwargs) -> bool:
        """启动任务
        
        Args:
            task_id: 任务ID
            target_func: 目标执行函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            bool: 是否成功启动
        """
        with self._task_lock:
            if task_id not in self._tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task = self._tasks[task_id]
            
            if task.status != TaskStatus.PENDING:
                logger.error(f"任务状态不正确: {task_id} - {task.status}")
                return False
            
            # 检查并发任务数量
            running_tasks = sum(1 for t in self._tasks.values() 
                              if t.status == TaskStatus.RUNNING)
            if running_tasks >= self.max_concurrent_tasks:
                logger.warning(f"达到最大并发任务数限制: {self.max_concurrent_tasks}")
                return False
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
        
        # 创建任务线程
        thread = threading.Thread(
            target=self._task_wrapper,
            args=(task_id, target_func, args, kwargs)
        )
        thread.daemon = True
        self._task_threads[task_id] = thread
        thread.start()
        
        # 发布任务更新事件
        self._event_bus.publish_event(
            self._event_bus.EVENT_TYPES['TASK_UPDATED'],
            {'task': task.to_dict()},
            user_id=task.user_id
        )
        
        logger.info(f"任务已启动: {task_id}")
        return True
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        with self._task_lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            if task.status == TaskStatus.RUNNING:
                task.status = TaskStatus.PAUSED
                # 发布任务更新事件
                self._event_bus.publish_event(
                    self._event_bus.EVENT_TYPES['TASK_UPDATED'],
                    {'task': task.to_dict()},
                    user_id=task.user_id
                )
                return True
        return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        with self._task_lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            if task.status == TaskStatus.PAUSED:
                task.status = TaskStatus.RUNNING
                # 发布任务更新事件
                self._event_bus.publish_event(
                    self._event_bus.EVENT_TYPES['TASK_UPDATED'],
                    {'task': task.to_dict()},
                    user_id=task.user_id
                )
                return True
        return False
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._task_lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]:
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.now()
                
                # 发布任务更新事件
                self._event_bus.publish_event(
                    self._event_bus.EVENT_TYPES['TASK_UPDATED'],
                    {'task': task.to_dict()},
                    user_id=task.user_id
                )
                return True
        return False
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务信息"""
        with self._task_lock:
            return self._tasks.get(task_id)
    
    def get_user_tasks(self, user_id: str, 
                      status_filter: List[TaskStatus] = None) -> List[Task]:
        """获取用户的任务列表"""
        with self._task_lock:
            tasks = [task for task in self._tasks.values() 
                    if task.user_id == user_id]
            
            if status_filter:
                tasks = [task for task in tasks if task.status in status_filter]
            
            # 按优先级和创建时间排序
            tasks.sort(key=lambda t: (t.priority.value, t.created_at), reverse=True)
            return tasks
    
    def update_task_progress(self, task_id: str, current: int = None, 
                           total: int = None, message: str = None,
                           current_step: str = None) -> bool:
        """更新任务进度"""
        with self._task_lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            task.progress.update(current, total, message, current_step)
            
            # 发布进度更新事件
            self._event_bus.publish_event(
                self._event_bus.EVENT_TYPES['PARSING_PROGRESS'],
                {
                    'task_id': task_id,
                    'progress': asdict(task.progress)
                },
                user_id=task.user_id
            )
            
            return True
    
    def _task_wrapper(self, task_id: str, target_func: Callable, args, kwargs):
        """任务包装器 - 在独立线程中执行任务"""
        try:
            # 将task_id注入到kwargs中，方便目标函数使用
            kwargs['task_id'] = task_id
            kwargs['task_manager'] = self
            
            # 使用保存的应用实例或创建新的应用上下文
            if self._app is not None:
                with self._app.app_context():
                    result = target_func(*args, **kwargs)
            else:
                # 如果没有应用实例，尝试直接执行（可能会有限制）
                logger.warning(f"任务 {task_id} 在没有Flask应用上下文的情况下执行")
                result = target_func(*args, **kwargs)
            
            # 更新任务状态为完成
            with self._task_lock:
                if task_id in self._tasks:
                    task = self._tasks[task_id]
                    if task.status == TaskStatus.RUNNING:  # 确保任务没有被取消
                        task.status = TaskStatus.COMPLETED
                        task.completed_at = datetime.now()
                        task.result = result if isinstance(result, dict) else {'result': result}
                        task.progress.current = task.progress.total
                        task.progress.percentage = 100.0
                        
                        # 发布任务完成事件
                        self._event_bus.publish_event(
                            self._event_bus.EVENT_TYPES['TASK_COMPLETED'],
                            {'task': task.to_dict()},
                            user_id=task.user_id
                        )
                        
        except Exception as e:
            # 更新任务状态为失败
            with self._task_lock:
                if task_id in self._tasks:
                    task = self._tasks[task_id]
                    task.status = TaskStatus.FAILED
                    task.completed_at = datetime.now()
                    task.error = str(e)
                    
                    # 发布错误事件
                    self._event_bus.publish_event(
                        self._event_bus.EVENT_TYPES['PROCESS_ERROR'],
                        {
                            'task_id': task_id,
                            'error': str(e),
                            'task': task.to_dict()
                        },
                        user_id=task.user_id
                    )
            
            logger.error(f"任务执行失败 {task_id}: {e}")
        
        finally:
            # 清理线程引用
            if task_id in self._task_threads:
                del self._task_threads[task_id]
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        self._cleanup_thread = threading.Thread(target=self._cleanup_worker)
        self._cleanup_thread.daemon = True
        self._cleanup_thread.start()
    
    def _cleanup_worker(self):
        """清理过期任务"""
        while self._running:
            try:
                # 每5分钟清理一次
                time.sleep(300)
                
                # 清理7天前的已完成/失败/取消的任务
                cutoff_time = datetime.now() - timedelta(days=7)
                
                with self._task_lock:
                    expired_task_ids = []
                    for task_id, task in self._tasks.items():
                        if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
                            and task.completed_at and task.completed_at < cutoff_time):
                            expired_task_ids.append(task_id)
                    
                    for task_id in expired_task_ids:
                        del self._tasks[task_id]
                        logger.debug(f"清理过期任务: {task_id}")
                        
            except Exception as e:
                logger.error(f"清理任务时出错: {e}")
    
    def is_healthy(self) -> bool:
        """检查任务管理器健康状态
        
        Returns:
            bool: 任务管理器是否健康
        """
        try:
            # 检查基本状态
            if not self._running:
                return False
            
            # 检查清理线程是否正常运行
            if self._cleanup_thread and not self._cleanup_thread.is_alive():
                return False
            
            # 检查是否有过多的失败任务
            with self._task_lock:
                total_tasks = len(self._tasks)
                if total_tasks > 0:
                    failed_tasks = sum(1 for t in self._tasks.values() 
                                     if t.status == TaskStatus.FAILED)
                    # 如果失败率超过50%，认为不健康
                    if failed_tasks / total_tasks > 0.5:
                        return False
            
            return True
        except Exception as e:
            logger.error(f"检查任务管理器健康状态时出错: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取任务管理器状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self._task_lock:
            total_tasks = len(self._tasks)
            status_counts = {}
            for status in TaskStatus:
                status_counts[status.value] = sum(1 for t in self._tasks.values() 
                                                 if t.status == status)
            
            return {
                'healthy': self.is_healthy(),
                'running': self._running,
                'total_tasks': total_tasks,
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'current_running': status_counts.get('running', 0),
                'status_counts': status_counts,
                'cleanup_thread_alive': self._cleanup_thread and self._cleanup_thread.is_alive()
            }

    def stop(self):
        """停止任务管理器"""
        self._running = False
        
        # 取消所有运行中的任务
        with self._task_lock:
            for task_id, task in self._tasks.items():
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = datetime.now()
        
        # 等待清理线程结束
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
        
        logger.info("任务管理器已停止")


# 全局任务管理器实例
_task_manager = None

def get_task_manager() -> TaskManager:
    """获取全局任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = TaskManager()
    return _task_manager

def init_task_manager(max_concurrent_tasks: int = 5, app=None) -> TaskManager:
    """初始化任务管理器"""
    global _task_manager
    _task_manager = TaskManager(max_concurrent_tasks=max_concurrent_tasks, app=app)
    return _task_manager 