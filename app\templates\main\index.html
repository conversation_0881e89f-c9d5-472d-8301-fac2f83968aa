{% extends "base.html" %}

{% block title %}车规芯片终测智能调度平台 - 主页
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .dashboard-container {
        padding: 15px;
        background-color: var(--secondary-background);
    }
    
    .stat-card {
        background-color: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    
    .stat-card:hover {
        transform: translateY(-3px);
    }
    
    .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: var(--theme-color);
    }
    
    .stat-label {
        color: #666;
        font-size: 13px;
    }
    
    .chart-card {
        background-color: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        min-height: 350px;
    }
    
    .chart-card h5 {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
    }
    
    .chart-container {
        width: 100%;
        height: 300px;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 12px;
        margin-bottom: 15px;
    }
    
    .action-card {
        background-color: white;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid #eee;
    }
    
    .action-card:hover {
        background-color: #f8f9fa;
        border-color: var(--theme-color);
    }
    
    .action-icon {
        font-size: 20px;
        margin-bottom: 8px;
        color: var(--theme-color);
    }
    
    .action-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
    }
    
    .action-desc {
        font-size: 12px;
        color: #666;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <h2 class="mb-4">系统概览</h2>
    
    <!-- 快捷操作 -->
    <div class="quick-actions mb-4">
        <div class="action-card" onclick="location.href='/production/auto'">
            <div class="action-icon">
                <i class="fas fa-industry"></i>
            </div>
            <div class="action-title">全自动排产</div>
            <div class="action-desc">智能排产计划生成</div>
        </div>
        
        <div class="action-card" onclick="location.href='/orders/auto'">
            <div class="action-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="action-title">订单管理</div>
            <div class="action-desc">查看和管理生产订单</div>
        </div>
        
        <div class="action-card" onclick="location.href='/wip/by-product'">
            <div class="action-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="action-title">WIP跟踪</div>
            <div class="action-desc">在制品状态监控</div>
        </div>
        
        <div class="action-card" onclick="location.href='/resources/tester'">
            <div class="action-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="action-title">资源管理</div>
            <div class="action-desc">设备和资源维护</div>
        </div>
    </div>
    
    <!-- 统计数据 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value" id="orderCount">--</div>
                <div class="stat-label">今日订单数</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value" id="productionCount">--</div>
                <div class="stat-label">生产计划数</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value" id="resourceUtilization">--</div>
                <div class="stat-label">资源利用率</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-value" id="wipCount">--</div>
                <div class="stat-label">在制品数量</div>
            </div>
        </div>
    </div>
    
    <!-- 图表 -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-card">
                <h5>订单完成情况</h5>
                <div class="chart-container" id="orderChart"></div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="chart-card">
                <h5>资源利用率趋势</h5>
                <div class="chart-container" id="resourceChart"></div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="chart-card">
                <h5>产品分布</h5>
                <div class="chart-container" id="productChart"></div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="chart-card">
                <h5>设备状态</h5>
                <div class="chart-container" id="equipmentChart"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='vendor/echarts/echarts.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/echarts-utils.js') }}"></script>
<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 加载统计数据
        loadStats();
        
        // 初始化图表
        initCharts();
        
        // 使用智能刷新管理器替代固定间隔刷新
        if (window.smartRefresh) {
            window.smartRefresh.registerRefreshCallback('dashboard_stats', loadStats);
            window.smartRefresh.registerRefreshCallback('dashboard_charts', updateCharts);
            console.log('✅ 已注册智能刷新回调');
        } else {
            // 降级到传统定时刷新（如果智能刷新不可用）
            setInterval(() => {
                loadStats();
                updateCharts();
            }, 60000);
            console.log('⚠️ 使用传统定时刷新');
        }
    });
    
    // 加载统计数据
    async function loadStats() {
        try {
            const response = await fetch('/api/v2/system/dashboard/stats');
            if (!response.ok) throw new Error('Failed to load stats');
            
            const result = await response.json();
            const stats = result.data || result; // 兼容新旧格式
            
            // 更新统计卡片
            document.getElementById('orderCount').textContent = stats.orderCount;
            document.getElementById('productionCount').textContent = stats.productionCount;
            document.getElementById('resourceUtilization').textContent = stats.resourceUtilization + '%';
            document.getElementById('wipCount').textContent = stats.wipCount;
        } catch (error) {
            console.error('Error loading stats:', error);
            // 如果新API失败，尝试使用旧API作为备用
            try {
                const response = await fetch('/api/v2/system/dashboard/stats');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('orderCount').textContent = stats.orderCount;
                    document.getElementById('productionCount').textContent = stats.productionCount;
                    document.getElementById('resourceUtilization').textContent = stats.resourceUtilization + '%';
                    document.getElementById('wipCount').textContent = stats.wipCount;
                }
            } catch (fallbackError) {
                console.error('Fallback API also failed:', fallbackError);
            }
        }
    }
    
    // 初始化图表
    function initCharts() {
        // 订单完成情况
        const orderData = {
            xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            series: [{
                name: '计划订单',
                data: [150, 230, 224, 218, 135, 147, 260]
            }, {
                name: '完成订单',
                data: [120, 200, 150, 180, 120, 132, 201]
            }]
        };
        ChartUtils.createLineChart(document.getElementById('orderChart'), orderData, '本周订单完成情况');
        
        // 资源利用率趋势
        const resourceData = {
            xAxis: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            series: [{
                name: '分选机',
                data: [30, 45, 80, 75, 65, 40]
            }, {
                name: '测试机',
                data: [45, 60, 85, 80, 70, 50]
            }]
        };
        ChartUtils.createLineChart(document.getElementById('resourceChart'), resourceData, '今日资源利用率');
        
        // 产品分布
        const productData = [
            { value: 335, name: '产品A' },
            { value: 310, name: '产品B' },
            { value: 234, name: '产品C' },
            { value: 135, name: '产品D' },
            { value: 1548, name: '产品E' }
        ];
        ChartUtils.createPieChart(document.getElementById('productChart'), productData, '产品订单分布');
        
        // 设备状态
        const equipmentData = {
            xAxis: ['分选机', '测试机', '工装夹具'],
            series: [{
                name: '使用中',
                data: [5, 8, 15]
            }, {
                name: '空闲',
                data: [2, 3, 5]
            }, {
                name: '维护中',
                data: [1, 1, 2]
            }]
        };
        ChartUtils.createBarChart(document.getElementById('equipmentChart'), equipmentData, '设备状态分布');
    }
    
    // 更新图表数据
    async function updateCharts() {
        try {
            const response = await fetch('/api/v2/system/dashboard/charts');
            if (!response.ok) throw new Error('Failed to load chart data');
            
            const result = await response.json();
            const data = result.data || result; // 兼容新旧格式
            
            // TODO: 使用实际数据更新图表
            // 这里添加更新图表的代码
        } catch (error) {
            console.error('Error updating charts:', error);
            // 如果新API失败，尝试使用旧API作为备用
            try {
                const response = await fetch('/api/v2/system/dashboard/charts');
                if (response.ok) {
                    const data = await response.json();
                    // TODO: 使用备用数据更新图表
                }
            } catch (fallbackError) {
                console.error('Fallback charts API also failed:', fallbackError);
            }
        }
    }
</script>

{% endblock %} 