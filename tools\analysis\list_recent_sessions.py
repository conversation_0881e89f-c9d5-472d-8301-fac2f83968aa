#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import argparse
import pymysql

def db_query(sql: str, params=None):
    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
    try:
        with conn.cursor() as cur:
            cur.execute(sql, params or ())
            cols = [c[0] for c in cur.description]
            return [dict(zip(cols, row)) for row in cur.fetchall()]
    finally:
        conn.close()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--like', required=False, help='session_id LIKE filter, e.g., 1757%')
    args = parser.parse_args()
    if args.like:
        rows = db_query("""
            SELECT SESSION_ID, HANDLER_ID, COUNT(*) AS cnt, MAX(id) AS max_id
            FROM lotprioritydone WHERE SESSION_ID LIKE %s
            GROUP BY SESSION_ID, HANDLER_ID ORDER BY max_id DESC LIMIT 50
        """, (args.like,))
    else:
        rows = db_query("""
            SELECT SESSION_ID, HANDLER_ID, COUNT(*) AS cnt, MAX(id) AS max_id
            FROM lotprioritydone WHERE SESSION_ID IS NOT NULL
            GROUP BY SESSION_ID, HANDLER_ID ORDER BY max_id DESC LIMIT 50
        """)
    if not rows:
        print('[EMPTY] no sessions found')
    else:
        for r in rows:
            print(f"SESSION={r['SESSION_ID']}, HANDLER={r['HANDLER_ID']}, cnt={r['cnt']}, max_id={r['max_id']}")

