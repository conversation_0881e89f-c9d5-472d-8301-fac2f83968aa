# -*- coding: utf-8 -*-
"""
排产历史记录模型
记录每次排产操作的完整历史，支持多版本排产结果共存

Author: AI Assistant  
Date: 2025-01-26
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class SchedulingHistory:
    """排产历史记录模型
    
    功能：
    1. 记录每次排产操作的完整信息
    2. 支持多版本排产结果
    3. 可按时间、算法、用户等维度查询
    4. 支持排产结果对比分析
    """
    
    def __init__(self):
        self.history_id = None
        self.version = None
        self.algorithm = None
        self.optimization_target = None
        self.user_id = None
        self.start_time = None
        self.end_time = None
        self.status = None
        self.input_summary = None
        self.output_summary = None
        self.parameters = None
        self.results_count = None
        self.error_message = None
        self.created_at = None
        
    @classmethod
    def create_new_record(cls, algorithm: str, optimization_target: str, 
                         user_id: str, parameters: Dict = None) -> 'SchedulingHistory':
        """创建新的排产历史记录
        
        Args:
            algorithm: 排产算法
            optimization_target: 优化目标
            user_id: 操作用户ID
            parameters: 排产参数
            
        Returns:
            SchedulingHistory: 新的历史记录实例
        """
        history = cls()
        history.history_id = str(uuid.uuid4())
        history.version = cls._generate_version()
        history.algorithm = algorithm
        history.optimization_target = optimization_target
        history.user_id = user_id
        history.start_time = datetime.now()
        history.status = 'RUNNING'
        history.parameters = parameters or {}
        history.created_at = datetime.now()
        
        logger.info(f"创建排产历史记录: {history.history_id}, 算法: {algorithm}")
        return history
    
    @classmethod
    def _generate_version(cls) -> str:
        """生成版本号（格式：YYYYMMDD_HHMMSS）"""
        now = datetime.now()
        return now.strftime("%Y%m%d_%H%M%S")
    
    def set_input_summary(self, wait_lots_count: int, equipment_count: int, 
                         uph_data_count: int, test_specs_count: int) -> None:
        """设置输入数据摘要
        
        Args:
            wait_lots_count: 待排产批次数量
            equipment_count: 可用设备数量
            uph_data_count: UPH数据条数
            test_specs_count: 测试规范数量
        """
        self.input_summary = {
            'wait_lots_count': wait_lots_count,
            'equipment_count': equipment_count,
            'uph_data_count': uph_data_count,
            'test_specs_count': test_specs_count,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.debug(f"设置输入摘要: {self.input_summary}")
    
    def set_output_summary(self, scheduled_lots_count: int, 
                          total_good_qty: int, equipment_utilization: Dict) -> None:
        """设置输出结果摘要
        
        Args:
            scheduled_lots_count: 已排产批次数量
            total_good_qty: 总良品数量
            equipment_utilization: 设备利用率统计
        """
        self.output_summary = {
            'scheduled_lots_count': scheduled_lots_count,
            'total_good_qty': total_good_qty,
            'equipment_utilization': equipment_utilization,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results_count = scheduled_lots_count
        logger.debug(f"设置输出摘要: {self.output_summary}")
    
    def complete_success(self) -> None:
        """标记排产成功完成"""
        self.end_time = datetime.now()
        self.status = 'COMPLETED'
        
        duration = (self.end_time - self.start_time).total_seconds()
        logger.info(f"排产完成: {self.history_id}, 耗时: {duration:.2f}秒, 结果: {self.results_count}条")
    
    def complete_error(self, error_message: str) -> None:
        """标记排产失败
        
        Args:
            error_message: 错误信息
        """
        self.end_time = datetime.now()
        self.status = 'FAILED'
        self.error_message = error_message
        
        duration = (self.end_time - self.start_time).total_seconds()
        logger.error(f"排产失败: {self.history_id}, 耗时: {duration:.2f}秒, 错误: {error_message}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'history_id': self.history_id,
            'version': self.version,
            'algorithm': self.algorithm,
            'optimization_target': self.optimization_target,
            'user_id': self.user_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'input_summary': self.input_summary,
            'output_summary': self.output_summary,
            'parameters': self.parameters,
            'results_count': self.results_count,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'duration_seconds': self._calculate_duration()
        }
    
    def _calculate_duration(self) -> Optional[float]:
        """计算执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class SchedulingHistoryManager:
    """排产历史记录管理器
    
    功能：
    1. 保存排产历史记录到数据库
    2. 查询和检索历史记录
    3. 统计分析功能
    4. 数据清理和归档
    """
    
    def __init__(self):
        pass
    
    def save_history(self, history: SchedulingHistory) -> bool:
        """保存排产历史记录到数据库
        
        Args:
            history: 排产历史记录对象
            
        Returns:
            bool: 是否保存成功
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            
            with get_mysql_connection_context('system') as conn:  # 保存到系统数据库
                cursor = conn.cursor()
                
                # 确保表存在
                self._ensure_table_exists(cursor)
                
                # 插入历史记录
                cursor.execute("""
                    INSERT INTO scheduling_history (
                        history_id, version, algorithm, optimization_target, user_id,
                        start_time, end_time, status, input_summary, output_summary,
                        parameters, results_count, error_message, created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    history.history_id,
                    history.version,
                    history.algorithm,
                    history.optimization_target,
                    history.user_id,
                    history.start_time,
                    history.end_time,
                    history.status,
                    json.dumps(history.input_summary, ensure_ascii=False) if history.input_summary else None,
                    json.dumps(history.output_summary, ensure_ascii=False) if history.output_summary else None,
                    json.dumps(history.parameters, ensure_ascii=False) if history.parameters else None,
                    history.results_count,
                    history.error_message,
                    history.created_at
                ))
                
                conn.commit()
                cursor.close()
            
            logger.info(f"保存排产历史记录成功: {history.history_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存排产历史记录失败: {e}")
            return False
    
    def get_history_by_id(self, history_id: str) -> Optional[SchedulingHistory]:
        """根据ID获取历史记录
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            SchedulingHistory: 历史记录对象，如果不存在返回None
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            from pymysql.cursors import DictCursor
            
            with get_mysql_connection_context('system') as conn:
                cursor = conn.cursor(DictCursor)  # 🔥 使用DictCursor返回字典格式
                
                cursor.execute("""
                    SELECT * FROM scheduling_history WHERE history_id = %s
                """, (history_id,))
                
                row = cursor.fetchone()
                cursor.close()
                
                if row:
                    return self._row_to_history(row)
                return None
                
        except Exception as e:
            logger.error(f"获取排产历史记录失败: {e}", exc_info=True)
            return None
    
    def get_by_id(self, history_id: str) -> Optional[SchedulingHistory]:
        """根据ID获取历史记录（别名方法）
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            SchedulingHistory: 历史记录对象，如果不存在返回None
        """
        return self.get_history_by_id(history_id)
    
    def delete_history(self, history_id: str) -> bool:
        """删除指定的排产历史记录
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            
            with get_mysql_connection_context('system') as conn:
                cursor = conn.cursor()
                
                # 检查记录是否存在
                cursor.execute("SELECT history_id FROM scheduling_history WHERE history_id = %s", (history_id,))
                if not cursor.fetchone():
                    cursor.close()
                    return False
                
                # 删除记录
                cursor.execute("DELETE FROM scheduling_history WHERE history_id = %s", (history_id,))
                conn.commit()
                
                deleted_rows = cursor.rowcount
                cursor.close()
                
                if deleted_rows > 0:
                    logger.info(f"✅ 成功删除排产历史记录: {history_id}")
                    return True
                else:
                    return False
                
        except Exception as e:
            logger.error(f"删除排产历史记录失败: {e}")
            return False
    
    def clear_all_history(self) -> bool:
        """清空所有排产历史记录
        
        Returns:
            bool: 清空是否成功
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            
            with get_mysql_connection_context('system') as conn:
                cursor = conn.cursor()
                
                # 使用 TRUNCATE TABLE 高效清空表
                cursor.execute("TRUNCATE TABLE scheduling_history")
                conn.commit()
                
                cursor.close()
                
                logger.info("✅ 成功清空所有排产历史记录")
                return True
                
        except Exception as e:
            logger.error(f"清空排产历史记录失败: {e}")
            return False
    
    def get_recent_history(self, limit: int = 10, user_id: str = None) -> List[Dict]:
        """获取最近的排产历史记录
        
        Args:
            limit: 返回记录数量限制
            user_id: 用户ID筛选（可选）
            
        Returns:
            List[Dict]: 历史记录列表
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            from pymysql.cursors import DictCursor
            
            with get_mysql_connection_context('system') as conn:
                cursor = conn.cursor(DictCursor)  # 🔥 使用DictCursor返回字典格式
                
                query = """
                    SELECT * FROM scheduling_history 
                    WHERE 1=1
                """
                params = []
                
                if user_id:
                    query += " AND user_id = %s"
                    params.append(user_id)
                
                query += " ORDER BY created_at DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                cursor.close()
                
                histories = []
                for row in rows:
                    history = self._row_to_history(row)
                    if history:
                        histories.append(history.to_dict())
                
                logger.info(f"✅ 成功获取 {len(histories)} 条排产历史记录")
                return histories
                
        except Exception as e:
            logger.error(f"获取最近排产历史失败: {e}", exc_info=True)
            return []
    
    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取排产统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            Dict: 统计信息
        """
        try:
            from app.utils.db_helper import get_mysql_connection_context
            from pymysql.cursors import DictCursor
            from datetime import timedelta
            
            with get_mysql_connection_context('system') as conn:
                cursor = conn.cursor(DictCursor)  # 🔥 使用DictCursor返回字典格式
                
                start_date = datetime.now() - timedelta(days=days)
                
                # 总体统计
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_runs,
                        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as success_runs,
                        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_runs,
                        AVG(CASE WHEN status = 'COMPLETED' THEN results_count ELSE NULL END) as avg_results,
                        SUM(CASE WHEN status = 'COMPLETED' THEN results_count ELSE 0 END) as total_results
                    FROM scheduling_history 
                    WHERE created_at >= %s
                """, (start_date,))
                
                stats = cursor.fetchone()
                
                # 按算法统计
                cursor.execute("""
                    SELECT 
                        algorithm,
                        COUNT(*) as runs,
                        AVG(CASE WHEN status = 'COMPLETED' THEN results_count ELSE NULL END) as avg_results
                    FROM scheduling_history 
                    WHERE created_at >= %s
                    GROUP BY algorithm
                    ORDER BY runs DESC
                """, (start_date,))
                
                algorithm_stats = cursor.fetchall()
                
                cursor.close()
                
                return {
                    'period_days': days,
                    'total_runs': stats['total_runs'] or 0,
                    'success_runs': stats['success_runs'] or 0,
                    'failed_runs': stats['failed_runs'] or 0,
                    'success_rate': (stats['success_runs'] / stats['total_runs'] * 100) if stats['total_runs'] > 0 else 0,
                    'avg_results_per_run': float(stats['avg_results'] or 0),
                    'total_scheduled_lots': stats['total_results'] or 0,
                    'algorithm_breakdown': [dict(row) for row in algorithm_stats]
                }
                
        except Exception as e:
            logger.error(f"获取排产统计失败: {e}", exc_info=True)
            return {}
    
    def _ensure_table_exists(self, cursor) -> None:
        """确保历史记录表存在"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS scheduling_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                history_id VARCHAR(36) NOT NULL UNIQUE,
                version VARCHAR(20) NOT NULL,
                algorithm VARCHAR(50) NOT NULL,
                optimization_target VARCHAR(50),
                user_id VARCHAR(50),
                start_time DATETIME,
                end_time DATETIME,
                status VARCHAR(20) NOT NULL,
                input_summary JSON,
                output_summary JSON,
                parameters JSON,
                results_count INT DEFAULT 0,
                error_message TEXT,
                created_at DATETIME NOT NULL,
                INDEX idx_history_id (history_id),
                INDEX idx_created_at (created_at),
                INDEX idx_user_id (user_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
    
    def _row_to_history(self, row: Dict) -> Optional[SchedulingHistory]:
        """将数据库行转换为SchedulingHistory对象"""
        try:
            history = SchedulingHistory()
            history.history_id = row['history_id']
            history.version = row['version']
            history.algorithm = row['algorithm']
            history.optimization_target = row['optimization_target']
            history.user_id = row['user_id']
            history.start_time = row['start_time']
            history.end_time = row['end_time']
            history.status = row['status']
            history.input_summary = json.loads(row['input_summary']) if row['input_summary'] else None
            history.output_summary = json.loads(row['output_summary']) if row['output_summary'] else None
            history.parameters = json.loads(row['parameters']) if row['parameters'] else None
            history.results_count = row['results_count']
            history.error_message = row['error_message']
            history.created_at = row['created_at']
            
            return history
            
        except Exception as e:
            logger.error(f"转换数据库行失败: {e}")
            return None


# 全局历史管理器实例
_history_manager = None

def get_history_manager() -> SchedulingHistoryManager:
    """获取全局历史管理器实例"""
    global _history_manager
    if _history_manager is None:
        _history_manager = SchedulingHistoryManager()
    return _history_manager 