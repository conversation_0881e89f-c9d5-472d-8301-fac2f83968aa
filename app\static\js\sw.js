/**
 * Service Worker - 离线缓存和性能优化
 * 提升页面加载速度和离线体验
 */

const CACHE_NAME = 'aec-ft-v1.0';
const STATIC_CACHE_NAME = 'aec-ft-static-v1.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/static/vendor/bootstrap/bootstrap.min.css',
    '/static/vendor/bootstrap/bootstrap.bundle.min.js',
    '/static/vendor/fontawesome/all.min.css',
    '/static/js/unified_api_client.js',
    '/static/js/memory-manager.js',
    '/static/js/smart-refresh.js',
    '/static/js/quick-navigation.js',
    '/static/css/main.css'
];

// 需要缓存的页面
const PAGES_TO_CACHE = [
    '/',
    '/index',
    '/production/semi-auto',
    '/orders/semi-auto'
];

// 安装事件 - 预缓存静态资源
self.addEventListener('install', event => {
    console.log('🔧 Service Worker 安装中...');
    
    event.waitUntil(
        Promise.all([
            // 缓存静态资源
            caches.open(STATIC_CACHE_NAME).then(cache => {
                console.log('📦 缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            }),
            
            // 缓存主要页面
            caches.open(CACHE_NAME).then(cache => {
                console.log('📄 缓存主要页面...');
                return cache.addAll(PAGES_TO_CACHE);
            })
        ]).then(() => {
            console.log('✅ Service Worker 安装完成');
            // 立即激活新的Service Worker
            return self.skipWaiting();
        }).catch(error => {
            console.error('❌ Service Worker 安装失败:', error);
        })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    // 删除旧版本的缓存
                    if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
                        console.log('🗑️ 删除旧缓存:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('✅ Service Worker 激活完成');
            // 立即控制所有页面
            return self.clients.claim();
        })
    );
});

// 拦截网络请求
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== location.origin) {
        return;
    }
    
    // 静态资源缓存策略：缓存优先
    if (isStaticAsset(request.url)) {
        event.respondWith(cacheFirst(request));
        return;
    }
    
    // HTML页面缓存策略：网络优先，缓存降级
    if (request.destination === 'document') {
        event.respondWith(networkFirst(request));
        return;
    }
    
    // API请求策略：网络优先
    if (request.url.includes('/api/')) {
        event.respondWith(networkFirst(request, 3000)); // 3秒超时
        return;
    }
    
    // 其他请求：网络优先
    event.respondWith(networkFirst(request));
});

/**
 * 判断是否为静态资源
 */
function isStaticAsset(url) {
    return url.includes('/static/') || 
           url.endsWith('.css') || 
           url.endsWith('.js') || 
           url.endsWith('.png') || 
           url.endsWith('.jpg') || 
           url.endsWith('.jpeg') || 
           url.endsWith('.gif') || 
           url.endsWith('.svg') || 
           url.endsWith('.ico');
}

/**
 * 缓存优先策略
 */
async function cacheFirst(request) {
    try {
        // 先从缓存查找
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 缓存未命中，从网络获取
        const networkResponse = await fetch(request);
        
        // 缓存成功的响应
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('缓存优先策略失败:', error);
        // 返回离线页面或默认响应
        return new Response('离线状态', { status: 503 });
    }
}

/**
 * 网络优先策略
 */
async function networkFirst(request, timeout = 5000) {
    try {
        // 设置网络请求超时
        const networkPromise = fetch(request);
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('网络超时')), timeout);
        });
        
        const networkResponse = await Promise.race([networkPromise, timeoutPromise]);
        
        // 缓存成功的响应
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.warn('网络请求失败，尝试缓存:', error);
        
        // 网络失败，从缓存获取
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 缓存也没有，返回错误响应
        return new Response('网络错误且无缓存', { 
            status: 503,
            statusText: 'Service Unavailable' 
        });
    }
}

/**
 * 清理过期缓存
 */
async function cleanupCache() {
    const cacheNames = await caches.keys();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    const now = Date.now();
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            const dateHeader = response.headers.get('date');
            
            if (dateHeader) {
                const responseDate = new Date(dateHeader).getTime();
                if (now - responseDate > maxAge) {
                    await cache.delete(request);
                    console.log('🧹 清理过期缓存:', request.url);
                }
            }
        }
    }
}

// 定期清理缓存
setInterval(cleanupCache, 24 * 60 * 60 * 1000); // 每天清理一次

// 监听消息
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        const urls = event.data.urls;
        caches.open(CACHE_NAME).then(cache => {
            cache.addAll(urls);
        });
    }
});

console.log('🎉 Service Worker 已加载');
