# -*- coding: utf-8 -*-
"""
前端资源统一管理配置
用于管理CSS、JavaScript和其他静态资源的版本和路径
"""

from flask import url_for

class FrontendResourceManager:
    """前端资源管理器"""
    
    # 版本信息
    VERSIONS = {
        'bootstrap': '5.1.3',
        'fontawesome': '5.15.4',
        'echarts': '5.4.0',
        'datatables': '1.13.0',
        'jquery': '3.6.0'
    }
    
    # 核心CSS资源（按加载顺序）
    CORE_CSS = [
        'vendor/bootstrap/bootstrap.min.css',
        'vendor/fontawesome/all.min.css',
        'css/compatibility.css'
    ]
    
    # 核心JavaScript资源（按加载顺序）
    CORE_JS = [
        'vendor/bootstrap/bootstrap.bundle.min.js',
        'js/menu-optimizer.js'
    ]
    
    # 页面特定资源映射
    PAGE_SPECIFIC_RESOURCES = {
        'login': {
            'css': ['css/login/login.css'],
            'js': ['js/login/login.js']
        },
        'production': {
            'css': ['css/production/production.css'],
            'js': [
                'js/echarts.min.js',
                'js/echarts-utils.js',
                'js/production/production.js'
            ]
        },
        'orders': {
            'css': ['css/production/orders.css'],
            'js': [
                'js/xlsx.full.min.js',
                'js/orders/orders.js'
            ]
        },
        'datatables': {
            'css': [
                'js/datatables/datatables.min.css',
                'js/datatables/responsive.bootstrap5.min.css'
            ],
            'js': [
                'js/datatables/datatables.min.js',
                'js/datatables/dataTables.bootstrap5.min.js',
                'js/datatables/dataTables.responsive.min.js',
                'js/datatables/responsive.bootstrap5.min.js'
            ]
        },
        'email': {
            'js': ['js/email_attachment.js']
        }
    }
    
    # CDN备用资源（当本地资源不可用时使用）
    CDN_FALLBACK = {
        'bootstrap_css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
        'bootstrap_js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
        'fontawesome_css': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
        'echarts_js': 'https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js'
    }
    
    @classmethod
    def get_core_css_urls(cls):
        """获取核心CSS资源URL列表"""
        return [url_for('static', filename=css) for css in cls.CORE_CSS]
    
    @classmethod
    def get_core_js_urls(cls):
        """获取核心JavaScript资源URL列表"""
        return [url_for('static', filename=js) for js in cls.CORE_JS]
    
    @classmethod
    def get_page_css_urls(cls, page_type):
        """获取页面特定CSS资源URL列表"""
        if page_type not in cls.PAGE_SPECIFIC_RESOURCES:
            return []
        
        css_files = cls.PAGE_SPECIFIC_RESOURCES[page_type].get('css', [])
        return [url_for('static', filename=css) for css in css_files]
    
    @classmethod
    def get_page_js_urls(cls, page_type):
        """获取页面特定JavaScript资源URL列表"""
        if page_type not in cls.PAGE_SPECIFIC_RESOURCES:
            return []
        
        js_files = cls.PAGE_SPECIFIC_RESOURCES[page_type].get('js', [])
        return [url_for('static', filename=js) for js in js_files]
    
    @classmethod
    def get_all_css_urls(cls, page_type=None):
        """获取所有CSS资源URL列表"""
        urls = cls.get_core_css_urls()
        if page_type:
            urls.extend(cls.get_page_css_urls(page_type))
        return urls
    
    @classmethod
    def get_all_js_urls(cls, page_type=None):
        """获取所有JavaScript资源URL列表"""
        urls = cls.get_core_js_urls()
        if page_type:
            urls.extend(cls.get_page_js_urls(page_type))
        return urls
    
    @classmethod
    def get_resource_integrity(cls, resource_type, resource_name):
        """获取资源完整性校验值（SRI）"""
        # 这里可以添加SRI校验值以提高安全性
        # 实际项目中应该为每个资源生成对应的SRI值
        sri_values = {
            'bootstrap_css': 'sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3',
            'bootstrap_js': 'sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p',
            'fontawesome_css': 'sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=='
        }
        
        key = f"{resource_name}_{resource_type}"
        return sri_values.get(key, '')
    
    @classmethod
    def generate_preload_links(cls, page_type=None):
        """生成资源预加载链接"""
        preload_links = []
        
        # 预加载核心CSS
        for css_url in cls.get_core_css_urls():
            preload_links.append(f'<link rel="preload" href="{css_url}" as="style">')
        
        # 预加载核心JS
        for js_url in cls.get_core_js_urls():
            preload_links.append(f'<link rel="preload" href="{js_url}" as="script">')
        
        return preload_links
    
    @classmethod
    def get_version_info(cls):
        """获取所有资源版本信息"""
        return cls.VERSIONS.copy()


# 创建全局实例
frontend_resources = FrontendResourceManager()

# 模板上下文处理器
def inject_frontend_resources():
    """向模板注入前端资源管理器"""
    return {
        'frontend_resources': frontend_resources,
        'resource_versions': frontend_resources.get_version_info()
    } 