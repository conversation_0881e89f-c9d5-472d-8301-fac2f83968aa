/**
 * 已排产批次导出管理器
 * 
 * 功能：
 * - 基于统一数据服务的导出功能
 * - 支持三种模式的数据导出（查看、调整、最终结果）
 * - 支持筛选结果导出和全量数据导出
 * - 支持Excel和CSV格式导出
 * - 显示导出进度和状态
 * 
 * 作者：Claude Code Assistant
 * 版本：阶段3.1
 */

class DoneLotsExportManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.isExporting = false;
        this.exportHistory = [];
        
        console.log('📤 DoneLotsExportManager 初始化完成');
    }
    
    /**
     * 导出当前筛选的数据（智能选择同步或异步模式）
     * @param {Object} options 导出选项
     */
    async exportFilteredData(options = {}) {
        // 先评估数据量，决定使用同步还是异步导出
        const dataEstimate = await this.estimateDataVolume(options);
        
        if (dataEstimate.large_dataset) {
            console.log('🚀 检测到大数据量，使用异步导出模式');
            return await this.exportAsyncData(options);
        } else {
            console.log('⚡ 使用同步导出模式');
            return await this.exportSyncData(options);
        }
    }
    
    /**
     * 评估数据量
     * @param {Object} options 导出选项
     */
    async estimateDataVolume(options) {
        try {
            // 调用统计API获取数据量估计
            const currentFilters = this.dataManager.getFilters();
            const response = await fetch('/api/v2/production/done-lots', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                const totalRecords = data.pagination?.total || 0;
                
                return {
                    total_records: totalRecords,
                    large_dataset: totalRecords > 1000, // 超过1000条使用异步
                    estimated_time: Math.ceil(totalRecords / 1000) * 2 // 每1000条约2秒
                };
            }
        } catch (error) {
            console.warn('数据量估计失败，使用默认策略:', error);
        }
        
        return { large_dataset: false, total_records: 0 };
    }
    
    /**
     * 同步导出数据（小数据量）
     * @param {Object} options 导出选项
     */
    async exportSyncData(options = {}) {
        const defaults = {
            format: 'excel',  // 'excel' 或 'csv'
            export_type: 'filtered',  // 'filtered' 或 'all'
            include_failed: false  // 是否包含失败批次
        };
        
        const config = { ...defaults, ...options };
        
        try {
            console.log('📤 开始导出筛选数据...', config);
            
            if (this.isExporting) {
                this.showNotification('正在导出中，请稍候...', 'warning');
                return;
            }
            
            this.isExporting = true;
            this.showExportProgress('准备导出数据...', 0);
            
            // 获取当前模式和筛选条件
            const currentMode = this.dataManager.getCurrentMode();
            const currentFilters = this.dataManager.getFilters();
            
            // 构建导出参数
            const exportParams = {
                mode: currentMode,
                export_type: config.export_type,
                format: config.format,
                ...currentFilters
            };
            
            console.log('📊 导出参数:', exportParams);
            this.showExportProgress('正在请求数据...', 30);
            
            // 调用API
            const response = await this.callExportAPI(exportParams);
            
            if (response.ok) {
                this.showExportProgress('正在处理文件...', 80);
                
                // 处理文件下载
                await this.handleFileDownload(response, config);
                
                this.showExportProgress('导出完成', 100);
                this.showNotification('数据导出成功', 'success');
                
                // 记录导出历史
                this.recordExportHistory({
                    mode: currentMode,
                    type: config.export_type,
                    format: config.format,
                    timestamp: new Date(),
                    status: 'success'
                });
                
            } else {
                const errorData = await response.json();
                throw new Error(errorData.message || '导出失败');
            }
            
        } catch (error) {
            console.error('❌ 导出失败:', error);
            this.showNotification(`导出失败: ${error.message}`, 'danger');
            
            // 记录失败历史
            this.recordExportHistory({
                mode: this.dataManager.getCurrentMode(),
                type: config.export_type,
                format: config.format,
                timestamp: new Date(),
                status: 'failed',
                error: error.message
            });
            
        } finally {
            this.isExporting = false;
            setTimeout(() => this.hideExportProgress(), 2000);
        }
    }
    
    /**
     * 导出全量数据
     */
    async exportAllData(options = {}) {
        const config = { ...options, export_type: 'all' };
        
        // 确认导出大量数据
        if (!confirm('导出全量数据可能需要较长时间，确定继续吗？')) {
            return;
        }
        
        await this.exportFilteredData(config);
    }
    
    /**
     * 异步导出数据（大数据量）
     * @param {Object} options 导出选项
     */
    async exportAsyncData(options = {}) {
        const defaults = {
            format: 'excel',
            export_type: 'filtered',
            include_failed: false
        };
        
        const config = { ...defaults, ...options };
        
        try {
            console.log('📤 开始创建异步导出任务...', config);
            
            if (this.isExporting) {
                this.showNotification('已有导出任务在执行中，请稍候...', 'warning');
                return;
            }
            
            this.isExporting = true;
            
            // 获取当前模式和筛选条件
            const currentMode = this.dataManager.getCurrentMode();
            const currentFilters = this.dataManager.getFilters();
            
            // 构建导出参数
            const exportParams = {
                mode: currentMode,
                export_type: config.export_type,
                format: config.format,
                ...currentFilters
            };
            
            console.log('📊 异步导出参数:', exportParams);
            
            // 创建导出任务
            const response = await fetch('/api/v2/production/done-lots/export-async', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportParams)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '创建导出任务失败');
            }
            
            const result = await response.json();
            const taskId = result.task_id;
            
            console.log(`✅ 异步导出任务已创建: ${taskId}`);
            
            // 显示进度跟踪界面
            this.showAsyncProgress(taskId);
            
            // 开始监控任务进度
            this.monitorTask(taskId);
            
            // 记录导出历史
            this.recordExportHistory({
                mode: currentMode,
                type: config.export_type,
                format: config.format,
                timestamp: new Date(),
                status: 'async_created',
                task_id: taskId
            });
            
        } catch (error) {
            console.error('❌ 异步导出失败:', error);
            this.showNotification(`异步导出失败: ${error.message}`, 'danger');
            
            // 记录失败历史
            this.recordExportHistory({
                mode: this.dataManager.getCurrentMode(),
                type: config.export_type,
                format: config.format,
                timestamp: new Date(),
                status: 'failed',
                error: error.message
            });
            
        } finally {
            this.isExporting = false;
        }
    }
    
    /**
     * 显示异步导出进度
     * @param {string} taskId 任务ID
     */
    showAsyncProgress(taskId) {
        // 创建进度跟踪界面
        let progressContainer = document.getElementById('asyncExportProgress');
        
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'asyncExportProgress';
            progressContainer.className = 'async-export-progress position-fixed';
            progressContainer.style.cssText = `
                top: 20px;
                right: 20px;
                width: 400px;
                z-index: 9999;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 1rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            `;
            
            document.body.appendChild(progressContainer);
        }
        
        progressContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                    <i class="fas fa-cloud-download-alt me-2 text-primary"></i>
                    <strong>大数据导出</strong>
                </div>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            
            <div class="task-info mb-3">
                <div class="text-muted small mb-1">任务ID: ${taskId}</div>
                <div class="status-message">正在准备任务...</div>
            </div>
            
            <div class="progress mb-2" style="height: 12px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%"></div>
            </div>
            
            <div class="progress-details small text-muted mb-3">
                <div>进度: <span class="progress-text">0%</span></div>
                <div>状态: <span class="status-text">准备中</span></div>
                <div>预计时间: <span class="time-text">计算中...</span></div>
            </div>
            
            <div class="task-actions text-center">
                <button class="btn btn-outline-danger btn-sm me-2" onclick="doneLotsExportManager.cancelAsyncTask('${taskId}')">
                    <i class="fas fa-times me-1"></i>取消任务
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="doneLotsExportManager.showTaskList()">
                    <i class="fas fa-list me-1"></i>查看所有任务
                </button>
            </div>
        `;
    }
    
    /**
     * 监控任务进度
     * @param {string} taskId 任务ID
     */
    async monitorTask(taskId) {
        const progressContainer = document.getElementById('asyncExportProgress');
        if (!progressContainer) return;
        
        const progressBar = progressContainer.querySelector('.progress-bar');
        const statusMessage = progressContainer.querySelector('.status-message');
        const progressText = progressContainer.querySelector('.progress-text');
        const statusText = progressContainer.querySelector('.status-text');
        const timeText = progressContainer.querySelector('.time-text');
        
        const updateProgress = async () => {
            try {
                const response = await fetch(`/api/v2/production/done-lots/export-status/${taskId}`);
                if (!response.ok) {
                    throw new Error('获取任务状态失败');
                }
                
                const result = await response.json();
                const status = result.data;
                
                // 更新进度条
                progressBar.style.width = `${status.progress}%`;
                progressText.textContent = `${status.progress}%`;
                statusMessage.textContent = status.message;
                statusText.textContent = this.getStatusDisplayName(status.status);
                
                // 更新时间信息
                if (status.estimated_duration) {
                    timeText.textContent = `约${Math.ceil(status.estimated_duration)}秒`;
                } else if (status.actual_duration) {
                    timeText.textContent = `耗时${Math.ceil(status.actual_duration)}秒`;
                }
                
                // 根据状态更新样式
                if (status.status === 'completed') {
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.add('bg-success');
                    statusMessage.innerHTML = `
                        ✅ 导出完成！共 ${status.records_count} 条记录
                        <br>
                        <a href="/api/v2/production/done-lots/export-download/${taskId}" 
                           class="btn btn-success btn-sm mt-2">
                            <i class="fas fa-download me-1"></i>下载文件
                        </a>
                    `;
                    
                    this.showNotification('导出任务完成，可以下载文件了', 'success');
                    return; // 停止监控
                    
                } else if (status.status === 'failed') {
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.add('bg-danger');
                    statusMessage.textContent = `❌ 导出失败: ${status.error_message}`;
                    
                    this.showNotification('导出任务失败', 'danger');
                    return; // 停止监控
                    
                } else if (status.status === 'cancelled') {
                    progressBar.classList.remove('progress-bar-animated');
                    progressBar.classList.add('bg-warning');
                    statusMessage.textContent = '❌ 任务已取消';
                    
                    return; // 停止监控
                }
                
                // 继续监控
                setTimeout(updateProgress, 1000); // 每秒更新一次
                
            } catch (error) {
                console.error('监控任务进度失败:', error);
                statusMessage.textContent = '❌ 获取进度失败，请手动刷新';
                setTimeout(updateProgress, 5000); // 出错后5秒重试
            }
        };
        
        // 开始监控
        updateProgress();
    }
    
    /**
     * 取消异步任务
     * @param {string} taskId 任务ID
     */
    async cancelAsyncTask(taskId) {
        if (!confirm('确定要取消这个导出任务吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/v2/production/done-lots/export-cancel/${taskId}`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showNotification('任务已取消', 'info');
                
                // 关闭进度界面
                const progressContainer = document.getElementById('asyncExportProgress');
                if (progressContainer) {
                    progressContainer.remove();
                }
            } else {
                const error = await response.json();
                throw new Error(error.message);
            }
            
        } catch (error) {
            console.error('取消任务失败:', error);
            this.showNotification(`取消任务失败: ${error.message}`, 'danger');
        }
    }
    
    /**
     * 显示任务列表
     */
    async showTaskList() {
        try {
            const response = await fetch('/api/v2/production/done-lots/export-tasks?limit=10');
            if (!response.ok) {
                throw new Error('获取任务列表失败');
            }
            
            const result = await response.json();
            const tasks = result.data;
            
            const modalHtml = `
                <div class="modal fade" id="exportTaskListModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-tasks me-2"></i>导出任务列表
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${tasks.length === 0 ? 
                                    '<p class="text-muted text-center py-4">暂无导出任务</p>' :
                                    this.renderTaskListTable(tasks)
                                }
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除旧的modal
            const oldModal = document.getElementById('exportTaskListModal');
            if (oldModal) {
                oldModal.remove();
            }
            
            // 添加新modal
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示modal
            const modal = new bootstrap.Modal(document.getElementById('exportTaskListModal'));
            modal.show();
            
        } catch (error) {
            console.error('显示任务列表失败:', error);
            this.showNotification('获取任务列表失败', 'danger');
        }
    }
    
    /**
     * 渲染任务列表表格
     * @param {Array} tasks 任务列表
     */
    renderTaskListTable(tasks) {
        const rows = tasks.map(task => {
            const statusBadge = this.getStatusBadge(task.status);
            const downloadButton = task.status === 'completed' ? 
                `<a href="/api/v2/production/done-lots/export-download/${task.task_id}" 
                   class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download"></i>
                </a>` : '-';
            
            return `
                <tr>
                    <td class="text-muted small">${task.task_id.substring(0, 8)}</td>
                    <td>${this.getModeDisplayName(task.config?.mode)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" style="width: ${task.progress}%"></div>
                        </div>
                        <small class="text-muted">${task.progress}%</small>
                    </td>
                    <td>${task.records_count || '-'}</td>
                    <td class="small">${this.formatDateTime(task.created_at)}</td>
                    <td>${downloadButton}</td>
                </tr>
            `;
        }).join('');
        
        return `
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>任务ID</th>
                            <th>模式</th>
                            <th>状态</th>
                            <th>进度</th>
                            <th>记录数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${rows}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    /**
     * 获取状态显示名称
     */
    getStatusDisplayName(status) {
        const statusNames = {
            'pending': '等待中',
            'running': '执行中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        return statusNames[status] || status;
    }
    
    /**
     * 获取状态徽章
     */
    getStatusBadge(status) {
        const badgeMap = {
            'pending': '<span class="badge bg-secondary">等待中</span>',
            'running': '<span class="badge bg-primary">执行中</span>',
            'completed': '<span class="badge bg-success">已完成</span>',
            'failed': '<span class="badge bg-danger">失败</span>',
            'cancelled': '<span class="badge bg-warning">已取消</span>'
        };
        return badgeMap[status] || status;
    }
    
    /**
     * 获取模式显示名称
     */
    getModeDisplayName(mode) {
        const modeNames = {
            'view': '查看模式',
            'adjust': '调整模式',
            'final_result': '最终结果'
        };
        return modeNames[mode] || mode;
    }
    
    /**
     * 调用导出API
     * @param {Object} params 导出参数
     */
    async callExportAPI(params) {
        const url = '/api/v2/production/done-lots/export';
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(params)
        });
        
        return response;
    }
    
    /**
     * 处理文件下载
     * @param {Response} response 响应对象
     * @param {Object} config 配置
     */
    async handleFileDownload(response, config) {
        const blob = await response.blob();
        
        // 从响应头获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `已排产批次_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.${config.format === 'csv' ? 'csv' : 'xlsx'}`;
        
        if (contentDisposition) {
            const matches = contentDisposition.match(/filename="(.+)"/);
            if (matches) {
                filename = matches[1];
            }
        }
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        console.log('📁 文件下载完成:', filename);
    }
    
    /**
     * 显示导出进度
     * @param {string} message 进度消息
     * @param {number} percent 进度百分比
     */
    showExportProgress(message, percent) {
        // 创建或更新进度条
        let progressContainer = document.getElementById('exportProgressContainer');
        
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'exportProgressContainer';
            progressContainer.className = 'export-progress-container position-fixed';
            progressContainer.style.cssText = `
                top: 20px;
                right: 20px;
                width: 300px;
                z-index: 9999;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 1rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            `;
            
            progressContainer.innerHTML = `
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-download me-2 text-primary"></i>
                    <strong>导出进度</strong>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
                <div class="progress mb-2" style="height: 8px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div class="export-status text-muted small">准备中...</div>
            `;
            
            document.body.appendChild(progressContainer);
        }
        
        // 更新进度
        const progressBar = progressContainer.querySelector('.progress-bar');
        const statusText = progressContainer.querySelector('.export-status');
        
        progressBar.style.width = `${percent}%`;
        statusText.textContent = message;
        
        if (percent >= 100) {
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-success');
        }
    }
    
    /**
     * 隐藏导出进度
     */
    hideExportProgress() {
        const progressContainer = document.getElementById('exportProgressContainer');
        if (progressContainer) {
            progressContainer.remove();
        }
    }
    
    /**
     * 记录导出历史
     * @param {Object} record 导出记录
     */
    recordExportHistory(record) {
        this.exportHistory.unshift(record);
        
        // 只保留最近20条记录
        if (this.exportHistory.length > 20) {
            this.exportHistory = this.exportHistory.slice(0, 20);
        }
        
        // 保存到localStorage
        try {
            localStorage.setItem('done_lots_export_history', JSON.stringify(this.exportHistory));
        } catch (e) {
            console.warn('无法保存导出历史到localStorage:', e);
        }
        
        console.log('📝 导出历史已记录:', record);
    }
    
    /**
     * 获取导出历史
     */
    getExportHistory() {
        if (this.exportHistory.length === 0) {
            // 从localStorage加载
            try {
                const saved = localStorage.getItem('done_lots_export_history');
                if (saved) {
                    this.exportHistory = JSON.parse(saved);
                }
            } catch (e) {
                console.warn('无法从localStorage加载导出历史:', e);
            }
        }
        
        return this.exportHistory;
    }
    
    /**
     * 显示导出历史对话框
     */
    showExportHistory() {
        const history = this.getExportHistory();
        
        const modalHtml = `
            <div class="modal fade" id="exportHistoryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-history me-2"></i>导出历史
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${history.length === 0 ? 
                                '<p class="text-muted text-center py-4">暂无导出历史</p>' :
                                this.renderExportHistoryTable(history)
                            }
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-danger" onclick="doneLotsExportManager.clearExportHistory()">
                                <i class="fas fa-trash me-1"></i>清空历史
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除旧的modal
        const oldModal = document.getElementById('exportHistoryModal');
        if (oldModal) {
            oldModal.remove();
        }
        
        // 添加新modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示modal
        const modal = new bootstrap.Modal(document.getElementById('exportHistoryModal'));
        modal.show();
    }
    
    /**
     * 渲染导出历史表格
     * @param {Array} history 历史记录
     */
    renderExportHistoryTable(history) {
        const rows = history.map(record => {
            const statusBadge = record.status === 'success' 
                ? '<span class="badge bg-success">成功</span>'
                : '<span class="badge bg-danger">失败</span>';
            
            const modeNames = {
                'view': '查看模式',
                'adjust': '调整模式', 
                'final_result': '最终结果'
            };
            
            const typeNames = {
                'filtered': '筛选结果',
                'all': '全部数据'
            };
            
            return `
                <tr>
                    <td>${this.formatDateTime(record.timestamp)}</td>
                    <td>${modeNames[record.mode] || record.mode}</td>
                    <td>${typeNames[record.type] || record.type}</td>
                    <td>${record.format.toUpperCase()}</td>
                    <td>${statusBadge}</td>
                    <td class="text-muted small">${record.error || '-'}</td>
                </tr>
            `;
        }).join('');
        
        return `
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>导出时间</th>
                            <th>模式</th>
                            <th>类型</th>
                            <th>格式</th>
                            <th>状态</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${rows}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    /**
     * 清空导出历史
     */
    clearExportHistory() {
        if (confirm('确定要清空所有导出历史吗？')) {
            this.exportHistory = [];
            localStorage.removeItem('done_lots_export_history');
            
            // 关闭modal并显示通知
            const modal = bootstrap.Modal.getInstance(document.getElementById('exportHistoryModal'));
            if (modal) {
                modal.hide();
            }
            
            this.showNotification('导出历史已清空', 'info');
        }
    }
    
    /**
     * 创建导出选项对话框
     */
    showExportOptionsDialog() {
        const currentMode = this.dataManager.getCurrentMode();
        const modeNames = {
            'view': '查看模式',
            'adjust': '调整模式',
            'final_result': '最终结果'
        };
        
        const modalHtml = `
            <div class="modal fade" id="exportOptionsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-download me-2"></i>导出选项
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="exportOptionsForm">
                                <div class="mb-3">
                                    <label class="form-label">当前模式</label>
                                    <div class="alert alert-info py-2">
                                        <i class="fas fa-info-circle me-2"></i>
                                        ${modeNames[currentMode]}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">导出范围</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="export_type" 
                                               id="exportFiltered" value="filtered" checked>
                                        <label class="form-check-label" for="exportFiltered">
                                            筛选结果
                                            <small class="text-muted d-block">仅导出当前筛选条件的数据</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="export_type" 
                                               id="exportAll" value="all">
                                        <label class="form-check-label" for="exportAll">
                                            全部数据
                                            <small class="text-muted d-block">导出该模式下的所有数据</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">文件格式</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="format" 
                                               id="formatExcel" value="excel" checked>
                                        <label class="form-check-label" for="formatExcel">
                                            Excel (.xlsx)
                                            <small class="text-muted d-block">支持复杂格式，推荐使用</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="format" 
                                               id="formatCsv" value="csv">
                                        <label class="form-check-label" for="formatCsv">
                                            CSV (.csv)
                                            <small class="text-muted d-block">纯文本格式，兼容性好</small>
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" onclick="doneLotsExportManager.showExportHistory()">
                                <i class="fas fa-history me-1"></i>查看历史
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="doneLotsExportManager.executeExport()">
                                <i class="fas fa-download me-1"></i>开始导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除旧的modal
        const oldModal = document.getElementById('exportOptionsModal');
        if (oldModal) {
            oldModal.remove();
        }
        
        // 添加新modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示modal
        const modal = new bootstrap.Modal(document.getElementById('exportOptionsModal'));
        modal.show();
    }
    
    /**
     * 执行导出（从选项对话框调用）
     */
    async executeExport() {
        const form = document.getElementById('exportOptionsForm');
        if (!form) return;
        
        const formData = new FormData(form);
        const options = {
            export_type: formData.get('export_type'),
            format: formData.get('format')
        };
        
        // 关闭对话框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportOptionsModal'));
        if (modal) {
            modal.hide();
        }
        
        // 开始导出
        await this.exportFilteredData(options);
    }
    
    /**
     * 显示通知
     * @param {string} message 通知消息
     * @param {string} type 通知类型
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 如果有全局通知函数，使用它
        if (typeof showNotification === 'function') {
            showNotification(message, type);
            return;
        }
        
        // 简单的通知实现
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        `;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
    
    /**
     * 格式化日期时间
     * @param {Date|string} date 日期
     */
    formatDateTime(date) {
        const d = typeof date === 'string' ? new Date(date) : date;
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// 全局导出管理器实例（将在页面加载时初始化）
let doneLotsExportManager = null;

console.log('📤 DoneLotsExportManager 类定义完成');