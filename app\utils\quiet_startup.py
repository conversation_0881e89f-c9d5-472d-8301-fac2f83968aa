#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用静默启动配置
用于减少启动时的冗余日志输出
"""

import os
import logging
from functools import wraps

def configure_quiet_startup(app):
    """配置应用的静默启动模式"""
    
    # 检查是否启用静默模式
    quiet_mode = os.environ.get('FLASK_QUIET_STARTUP', '0') == '1'
    
    if quiet_mode:
        # 设置Flask应用日志级别
        app.logger.setLevel(logging.ERROR)
        
        # 创建自定义日志过滤器
        class QuietFilter(logging.Filter):
            def filter(self, record):
                # 过滤掉常见的启动信息
                filtered_messages = [
                    '蓝图注册成功',
                    'API蓝图注册成功', 
                    '✅',
                    'Server initialized',
                    '已禁用',
                    '初始化成功',
                    '初始化完成'
                ]
                
                message = record.getMessage()
                for filtered in filtered_messages:
                    if filtered in message:
                        return False
                
                # 只保留错误和关键警告
                return record.levelno >= logging.WARNING
        
        # 应用过滤器到所有处理器
        for handler in app.logger.handlers:
            handler.addFilter(QuietFilter())
        
        # 设置第三方库日志级别
        third_party_loggers = [
            'werkzeug', 'engineio', 'socketio', 'apscheduler',
            'sqlalchemy', 'flask.app', 'app.services'
        ]
        
        for logger_name in third_party_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)
            
            # 为这些日志器也添加过滤器
            for handler in logger.handlers:
                handler.addFilter(QuietFilter())

def quiet_logger(func):
    """装饰器：使函数的日志输出静默"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 临时提高日志级别
        original_level = logging.getLogger().level
        if os.environ.get('FLASK_QUIET_STARTUP', '0') == '1':
            logging.getLogger().setLevel(logging.ERROR)
        
        try:
            return func(*args, **kwargs)
        finally:
            # 恢复原始日志级别
            logging.getLogger().setLevel(original_level)
    
    return wrapper

def get_essential_startup_info(app):
    """获取启动必要信息摘要"""
    info = {
        'status': 'success',
        'version': app.config.get('APP_VERSION', 'v2.0'),
        'environment': app.config.get('ENV', 'production'),
        'database_status': 'connected',
        'registered_blueprints': len(app.blueprints),
        'total_routes': len(list(app.url_map.iter_rules()))
    }
    return info

def print_startup_summary(app):
    """打印启动摘要信息"""
    if os.environ.get('FLASK_QUIET_STARTUP', '0') == '1':
        info = get_essential_startup_info(app)
        print(f"📊 系统摘要: {info['registered_blueprints']}个模块, {info['total_routes']}个路由")
        print(f"🗄️  数据库: {info['database_status']}")
        print(f"🏷️  版本: {info['version']} ({info['environment']})") 