# 排产结果验收工具使用指南

本目录包含用于验证排产逻辑正确性的工具集，确保匹配分类、候选选择与优先级排序符合业务标准。

## 文件说明

### 1. 验收标准文档
- `../docs/排产结果验收标准.md` - 完整的验收标准与业务口径定义

### 2. SQL巡检清单
- `sql_checks.sql` - 可直接执行的SQL检查语句（A~K）
- 使用方式：`mysql -uroot -pWWWwww123! aps < sql_checks.sql`

### 3. 一键化巡检工具
- `run_sql_checks.py` - Python自动化巡检脚本，输出CSV问题清单

## 使用方法

### 基础巡检（检查所有历史数据）
```bash
$env:PYTHONPATH='.'; python tools/analysis/run_sql_checks.py
```

### 按会话ID过滤
```bash
$env:PYTHONPATH='.'; python tools/analysis/run_sql_checks.py --session-id SESSION_123
```

### 按日期过滤
```bash
$env:PYTHONPATH='.'; python tools/analysis/run_sql_checks.py --date 2025-09-10
```

### 自定义输出文件
```bash
$env:PYTHONPATH='.'; python tools/analysis/run_sql_checks.py --output my_check_results.csv
```

## 检查项说明

| 检查项 | 说明 | 期望结果 |
|--------|------|----------|
| A_同配置应命中 | HC+KIT+TESTER+STAGE一致的批次应为0分钟 | 无问题项 |
| B_小改机应命中 | HC+KIT+TESTER一致的批次应为45分钟 | 无问题项 |
| C_换测试机小改机应命中 | HC+KIT一致、TESTER不一致应为55分钟 | 无问题项 |
| D_大改机合理性 | HC一致、KIT不一致应为120分钟 | 仅显示合理的120分钟分类 |
| E_同产品续排命中 | DEVICE+STAGE完全一致应为0分钟特例 | 仅显示合理的续排 |
| F_跨工序合法性 | 跨工序时HC必须一致 | 无违规项 |
| G_更优未选 | 不应存在更优候选被忽略 | 无问题项 |
| H_跨工序A优先性 | 跨工序A类应优先于B类 | 无问题项 |
| I_设备内优先级 | 三段式排序不被违反 | 无违规项 |
| J_解析失败但可续排 | 解析失败但可续排的不应记为失败 | 无问题项 |
| K_候选池计数 | 候选池统计，用于审计 | 仅统计信息 |

## 输出文件

执行后会在 `reports/` 目录生成CSV文件，包含以下字段：
- `check_type`: 检查类型（A~K）
- `LOT_ID`: 批次ID
- `HANDLER_ID`: 设备ID
- `issue_desc`: 问题描述
- `timestamp`: 检查时间

## 注意事项

1. **数据库权限**：需要对 `aps` 数据库有读取权限
2. **执行环境**：需要设置 `PYTHONPATH=.` 以正确导入项目模块
3. **结果解读**：
   - 问题项数量 > 0 不一定表示错误，需要结合业务场景判断
   - A/B/C类检查显示的是"满足充分条件"的批次，需要进一步核实是否正确分类
   - D/E/K类检查主要用于统计和审计，不一定是问题
4. **性能考虑**：大数据量时建议使用 `--session-id` 或 `--date` 过滤

## 故障排除

### 常见错误
1. **ModuleNotFoundError**: 确保设置了 `PYTHONPATH=.`
2. **数据库连接失败**: 检查数据库配置和网络连接
3. **SQL执行错误**: 检查数据库表结构是否与预期一致

### 联系支持
如遇到问题，请提供：
- 完整的错误信息
- 执行的命令
- 数据库环境信息
