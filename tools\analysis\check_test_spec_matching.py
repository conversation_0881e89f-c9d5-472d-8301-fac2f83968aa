#!/usr/bin/env python3
"""
检查测试规范匹配情况的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.db_connection_pool import get_connection

def check_test_spec_matching():
    """检查测试规范匹配情况"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        print("🔍 检查et_ft_test_spec表数据...")
        
        # 检查表结构和数据
        cursor.execute("DESCRIBE et_ft_test_spec")
        columns = cursor.fetchall()
        print("\n📋 表结构:")
        for col in columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec")
        total = cursor.fetchone()[0]
        print(f"\n📊 总记录数: {total}")
        
        # 检查APPROVAL状态分布
        cursor.execute("""
            SELECT APPROVAL, COUNT(*) as cnt 
            FROM et_ft_test_spec 
            GROUP BY APPROVAL 
            ORDER BY cnt DESC
        """)
        approvals = cursor.fetchall()
        print("\n📋 APPROVAL状态分布:")
        for approval in approvals:
            print(f"  - {approval[0]}: {approval[1]} 条记录")
        
        # 检查Released状态的数据
        cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec WHERE APPROVAL = 'Released'")
        released_count = cursor.fetchone()[0]
        print(f"\n✅ Released状态记录数: {released_count}")
        
        if released_count > 0:
            # 检查Released状态的DEVICE分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                WHERE APPROVAL = 'Released'
                GROUP BY DEVICE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            devices = cursor.fetchall()
            print("\n📋 Released状态DEVICE分布（前10）:")
            for device in devices:
                print(f"  - {device[0]}: {device[1]} 条记录")
            
            # 检查Released状态的STAGE分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                WHERE APPROVAL = 'Released'
                GROUP BY STAGE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            stages = cursor.fetchall()
            print("\n📋 Released状态STAGE分布（前10）:")
            for stage in stages:
                print(f"  - {stage[0]}: {stage[1]} 条记录")
        
        # 检查具体的批次需求与测试规范匹配情况
        print("\n🔍 检查批次需求与测试规范匹配情况...")
        
        # 从排产日志中提取的批次DEVICE
        test_devices = [
            'JWQ5103CSFQFNAT_TR0',
            'JWQ7101SOTB-J115_TR1', 
            'JWQ5276QFNA-J127_TR1',
            'JW3655E',
            'JWH5087AQFNAG-M001_TR1',
            'JWQ7806-3.3SOTA'
        ]
        
        for device in test_devices:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM et_ft_test_spec 
                WHERE DEVICE = %s AND APPROVAL = 'Released'
            """, (device,))
            count = cursor.fetchone()[0]
            print(f"  - {device}: {count} 条Released测试规范")
            
            if count > 0:
                cursor.execute("""
                    SELECT STAGE, TESTER, HB_PN, TB_PN
                    FROM et_ft_test_spec 
                    WHERE DEVICE = %s AND APPROVAL = 'Released'
                    LIMIT 3
                """, (device,))
                specs = cursor.fetchall()
                for spec in specs:
                    print(f"    └─ STAGE={spec[0]}, TESTER={spec[1]}, HB_PN={spec[2]}, TB_PN={spec[3]}")
        
        # 检查批次表中的数据
        print("\n🔍 检查et_wait_lot表中的批次数据...")
        cursor.execute("""
            SELECT DEVICE, STAGE, COUNT(*) as cnt
            FROM et_wait_lot 
            GROUP BY DEVICE, STAGE
            ORDER BY cnt DESC
            LIMIT 10
        """)
        lot_data = cursor.fetchall()
        print("\n📋 批次DEVICE+STAGE分布（前10）:")
        for lot in lot_data:
            print(f"  - {lot[0]}/{lot[1]}: {lot[2]} 个批次")
        
        # 检查匹配情况
        print("\n🔍 检查批次与测试规范的匹配情况...")
        cursor.execute("""
            SELECT 
                l.DEVICE, l.STAGE,
                COUNT(l.LOT_ID) as lot_count,
                COUNT(s.DEVICE) as spec_count
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE 
                AND UPPER(l.STAGE) = UPPER(s.STAGE) 
                AND s.APPROVAL = 'Released'
            GROUP BY l.DEVICE, l.STAGE
            HAVING spec_count = 0
            ORDER BY lot_count DESC
            LIMIT 10
        """)
        unmatched = cursor.fetchall()
        
        if unmatched:
            print("\n❌ 无匹配Released测试规范的批次（前10）:")
            for case in unmatched:
                print(f"  - {case[0]}/{case[1]}: {case[2]} 个批次，{case[3]} 条测试规范")
        else:
            print("\n✅ 所有批次都有匹配的Released测试规范")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_test_spec_matching()
