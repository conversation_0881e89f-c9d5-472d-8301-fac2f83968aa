# 数据导入模式使用指南

## 概述

系统现在支持两种数据导入模式，帮助用户更灵活地管理Excel数据导入：

- **智能追加模式**：只添加不重复的数据，避免数据重复累积
- **刷新替换模式**：清空现有数据后重新导入，确保数据完全更新

## 导入模式详解

### 1. 智能追加模式（默认）

**特点：**
- ✅ 只添加不重复的数据
- ✅ 保留现有数据
- ✅ 自动去重，避免数据累积
- ✅ 安全性高，不会丢失现有数据

**适用场景：**
- 日常数据更新
- 增量数据导入
- 多次导入同一数据源
- 不确定数据是否重复时

**工作原理：**
1. 系统首先尝试使用MySQL的`INSERT IGNORE`语句（适用于有主键或唯一索引的表）
2. 如果表没有唯一约束，则使用内容比较去重算法
3. 通过哈希比较识别完全相同的记录
4. 只插入不重复的新数据

### 2. 刷新替换模式

**特点：**
- 🔄 清空现有数据后重新导入
- 🔄 确保数据完全一致
- ⚠️ 会丢失现有数据
- ⚠️ 需要谨慎使用

**适用场景：**
- 数据完全重置
- 修正错误数据
- 确保数据版本一致性
- 清理测试数据

**工作原理：**
1. 使用`TRUNCATE TABLE`清空目标表
2. 重新插入所有Excel数据
3. 确保最终数据与Excel文件完全一致

## 使用方法

### 界面操作

1. **选择导入模式**
   - 在数据导入界面，选择相应的导入模式单选按钮
   - 默认选择"智能追加"模式

2. **智能追加模式**
   - 选择"智能追加"
   - 点击"导入"按钮
   - 系统自动去重并只添加新数据

3. **刷新替换模式**
   - 选择"刷新替换"
   - 系统会弹出确认对话框
   - 确认后清空现有数据并重新导入

### API调用

```javascript
// 智能追加模式
fetch('/api/production/import-from-directory', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        path: 'Excel文件路径',
        import_mode: 'append'
    })
});

// 刷新替换模式
fetch('/api/production/import-from-directory', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        path: 'Excel文件路径',
        import_mode: 'refresh'
    })
});
```

## 去重算法详解

### 策略1：INSERT IGNORE（优先使用）

**适用表：** 有主键或唯一索引的表

**优势：**
- 数据库级别的去重，性能最高
- 自动跳过重复记录
- 不需要额外的内存开销

**工作流程：**
```sql
INSERT IGNORE INTO table_name (columns) VALUES (data);
```

### 策略2：内容比较去重（备用方案）

**适用表：** 没有唯一约束的表

**优势：**
- 基于完整记录内容比较
- 处理任何表结构
- 精确识别重复数据

**工作流程：**
1. 读取现有数据并生成哈希值
2. 对新数据逐行计算哈希值
3. 比较哈希值，过滤重复记录
4. 只插入不重复的数据

## 导入结果说明

### 成功导入后的信息

系统会显示详细的导入统计：

```
📋 导入状态：智能追加模式导入完成，成功处理 5 个文件，失败 0 个
➕ 导入模式：只添加不重复的数据

📊 文件处理统计：
✅ 成功文件：5 个
❌ 失败文件：0 个
📝 总记录数：1,250 条
⏱️ 处理时间：3.45 秒

🎯 智能去重统计：
   📊 尝试插入：1,500 条记录
   ✅ 实际插入：1,250 条记录
   🔄 跳过重复：250 条记录
```

### 日志信息解读

**智能追加模式日志：**
```
🔒 表 et_wait_lot 处于保护模式，使用安全导入 (模式: append)
📝 智能追加模式：只添加不重复的数据到表 et_wait_lot
🎯 智能去重完成: et_wait_lot
   📊 尝试插入: 500 条记录
   ✅ 实际插入: 350 条记录
   🔄 跳过重复: 150 条记录
```

**刷新替换模式日志：**
```
🔒 表 et_wait_lot 处于保护模式，使用安全导入 (模式: refresh)
🧹 刷新模式：已清空表 et_wait_lot 的现有数据 (800 条记录)
✅ 保护模式刷新导入完成: et_wait_lot, 插入 500 条记录
```

## 最佳实践

### 1. 日常使用建议

- **默认使用智能追加模式**：安全可靠，避免数据丢失
- **定期使用刷新模式**：清理测试数据或修正错误数据
- **重要数据备份**：使用刷新模式前先备份重要数据

### 2. 性能优化建议

- **大数据量导入**：优先为表创建适当的主键或唯一索引
- **频繁导入**：使用智能追加模式减少数据处理时间
- **批量更新**：考虑使用刷新模式确保数据一致性

### 3. 错误处理

- **导入失败**：检查Excel文件格式和数据完整性
- **部分成功**：查看详细日志确定失败原因
- **性能问题**：考虑分批导入大数据量文件

## 技术实现

### 数据库表支持

系统支持以下类型的表：

1. **保护模式表**：业务关键表，使用安全导入机制
2. **常规表**：普通表，支持结构自动调整
3. **有索引表**：优先使用INSERT IGNORE去重
4. **无索引表**：使用内容比较去重

### 兼容性说明

- 完全向后兼容现有API
- 默认导入模式为"智能追加"
- 现有代码无需修改即可使用新功能

## 故障排除

### 常见问题

**Q: 为什么智能追加模式还是有重复数据？**
A: 可能是表没有合适的唯一约束，系统使用了内容比较去重。建议为表添加主键或唯一索引。

**Q: 刷新模式导入后数据量不对？**
A: 检查Excel文件数据完整性，查看导入日志中的错误信息。

**Q: 导入速度太慢？**
A: 大数据量建议为表添加适当索引，或考虑分批导入。

### 联系支持

如遇到问题，请查看系统日志或联系技术支持，提供以下信息：
- 导入模式选择
- Excel文件信息
- 错误日志内容
- 数据库表结构 