#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版智能排产服务 - Phase 1 示例实现
简化架构，提升性能，优化业务逻辑

核心改进：
1. 统一执行入口，移除冗余方法
2. 按需数据加载，减少预加载开销  
3. 简化设备匹配算法，提升性能
4. 集中特殊阶段处理逻辑
5. 模块化设计，提高可维护性
"""

import logging
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass

def safe_float(value, default=0.0):
    """安全的浮点数转换，避免类型错误"""
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

logger = logging.getLogger(__name__)

@dataclass
class SchedulingResult:
    """标准化的排产结果格式"""
    success: bool
    total_lots: int
    scheduled_lots: int
    failed_lots: int
    execution_time: float
    schedule_data: List[Dict]
    metrics: Dict[str, Any]
    error_message: Optional[str] = None

class SpecialStageHandler:
    """特殊阶段处理器 - 集中管理BTT/BAKING/LSTR逻辑"""
    
    def __init__(self):
        self.stage_rules = {
            'BTT': self._handle_btt_stage,
            'BAKING': self._handle_baking_stage, 
            'LSTR': self._handle_lstr_stage
        }
    
    def is_special_stage(self, lot: Dict) -> Optional[str]:
        """判断是否为特殊阶段"""
        stage = lot.get('STAGE', '').upper()
        device = lot.get('DEVICE', '').upper()
        
        if 'BTT' in stage or 'BURN' in stage:
            return 'BTT'
        elif 'BAKING' in stage or 'OVEN' in device:
            return 'BAKING'
        elif 'LSTR' in device:
            return 'LSTR'
        return None
    
    def handle_special_stage(self, lot: Dict, stage_type: str, equipment_list: List[Dict]) -> List[Dict]:
        """统一的特殊阶段处理入口"""
        handler = self.stage_rules.get(stage_type)
        if handler:
            return handler(lot, equipment_list)
        return equipment_list
    
    def _handle_btt_stage(self, lot: Dict, equipment_list: List[Dict]) -> List[Dict]:
        """BTT阶段专用逻辑 - 老化测试设备"""
        compatible_equipment = []
        for equipment in equipment_list:
            eqp_class = equipment.get('EQP_CLASS', '').upper()
            handler_type = equipment.get('HANDLER_TYPE', '')
            
            if any(keyword in eqp_class for keyword in ['TEST', 'BURN', 'BTT']) or '老化' in handler_type:
                compatible_equipment.append(equipment)
        
        logger.info(f"BTT阶段筛选: {len(equipment_list)} -> {len(compatible_equipment)}台设备")
        return compatible_equipment
    
    def _handle_baking_stage(self, lot: Dict, equipment_list: List[Dict]) -> List[Dict]:
        """BAKING阶段专用逻辑 - 烘箱设备"""
        compatible_equipment = []
        for equipment in equipment_list:
            handler_type = equipment.get('HANDLER_TYPE', '')
            eqp_class = equipment.get('EQP_CLASS', '').upper()
            handler_config = equipment.get('HANDLER_CONFIG', '').upper()
            
            if (handler_type == '烘箱' or 
                'OVEN' in eqp_class or 
                'BAKING' in eqp_class or
                'OVEN' in handler_config):
                compatible_equipment.append(equipment)
        
        logger.info(f"BAKING阶段筛选: {len(equipment_list)} -> {len(compatible_equipment)}台设备")
        return compatible_equipment
    
    def _handle_lstr_stage(self, lot: Dict, equipment_list: List[Dict]) -> List[Dict]:
        """LSTR阶段专用逻辑 - 激光修整设备"""
        compatible_equipment = []
        pkg_pn = lot.get('PKG_PN', '')
        
        for equipment in equipment_list:
            handler_id = equipment.get('HANDLER_ID', '')
            if 'LSTR' in handler_id:
                # LSTR设备的封装兼容性检查
                if self._check_lstr_pkg_compatibility(pkg_pn, equipment):
                    compatible_equipment.append(equipment)
        
        logger.info(f"LSTR阶段筛选: {len(equipment_list)} -> {len(compatible_equipment)}台设备")
        return compatible_equipment
    
    def _check_lstr_pkg_compatibility(self, lot_pkg_pn: str, equipment: Dict) -> bool:
        """LSTR封装兼容性检查"""
        # 简化的兼容性检查逻辑
        equipment_pkg = equipment.get('PKG_PN', '')
        if not equipment_pkg:
            return True  # 没有限制时允许
        
        # 提取封装类型进行匹配
        lot_pkg_type = lot_pkg_pn.split('-')[0] if '-' in lot_pkg_pn else lot_pkg_pn
        eqp_pkg_type = equipment_pkg.split('-')[0] if '-' in equipment_pkg else equipment_pkg
        
        return lot_pkg_type == eqp_pkg_type

class OptimizedSchedulingService:
    """优化版智能排产服务"""
    
    def __init__(self):
        from app.services.data_source_manager import DataSourceManager
        
        self.data_manager = DataSourceManager()
        self.special_stage_handler = SpecialStageHandler()
        
        # 简化的配置缓存 - 1小时过期
        self._config_cache = {}
        self._config_cache_time = 0
        
        # 设备索引缓存 - 提高匹配效率
        self._equipment_index = {}
        self._index_build_time = 0
        
        # 策略权重配置
        self.strategy_weights = {
            'intelligent': {'tech_match': 0.4, 'load_balance': 0.3, 'priority': 0.3},
            'deadline': {'tech_match': 0.3, 'load_balance': 0.2, 'priority': 0.5},
            'product': {'tech_match': 0.5, 'load_balance': 0.2, 'priority': 0.3},
            'value': {'tech_match': 0.3, 'load_balance': 0.4, 'priority': 0.3}
        }

    def execute_scheduling(self, strategy: str = 'intelligent', user_id: str = None, target: str = 'balanced') -> SchedulingResult:
        """
        统一排产执行入口 - 替换原有的3个冗余方法
        
        Args:
            strategy: 排产策略 (intelligent/deadline/product/value)
            user_id: 用户ID (用于个性化配置)
            target: 优化目标 (balanced/makespan/efficiency)
        
        Returns:
            SchedulingResult: 标准化排产结果
        """
        start_time = time.time()
        logger.info(f"🚀 开始优化版排产: 策略={strategy}, 用户={user_id}, 目标={target}")
        
        try:
            # 1. 获取策略权重配置
            weights = self._get_strategy_weights(strategy, user_id, target)
            
            # 2. 按需加载数据
            scheduling_data = self._get_scheduling_data_optimized()
            
            if not scheduling_data['wait_lots']:
                return SchedulingResult(
                    success=True,
                    total_lots=0,
                    scheduled_lots=0,
                    failed_lots=0,
                    execution_time=time.time() - start_time,
                    schedule_data=[],
                    metrics={'message': '没有待排产批次'},
                    error_message=None
                )
            
            # 3. 执行核心算法
            results = self._execute_core_algorithm(scheduling_data, weights)
            
            # 4. 保存结果到数据库
            self._save_scheduling_results(results)
            
            # 5. 返回标准化结果
            execution_time = time.time() - start_time
            return self._format_scheduling_results(results, execution_time)
            
        except Exception as e:
            logger.error(f"❌ 排产执行失败: {e}")
            return SchedulingResult(
                success=False,
                total_lots=0,
                scheduled_lots=0,
                failed_lots=0,
                execution_time=time.time() - start_time,
                schedule_data=[],
                metrics={},
                error_message=str(e)
            )

    def _get_scheduling_data_optimized(self) -> Dict:
        """优化的数据加载策略 - 按需加载，避免预加载开销"""
        data = {}
        
        # 核心数据：每次都重新加载
        wait_lots_result = self.data_manager.get_wait_lot_data()
        data['wait_lots'] = wait_lots_result[0] if wait_lots_result else []
        
        equipment_result = self.data_manager.get_table_data('EQP_STATUS')
        all_equipment = equipment_result.get('data', []) if equipment_result.get('success') else []
        
        # 过滤可用设备
        available_statuses = ['Run', 'IDLE', 'Wait', 'READY', 'ONLINE', 'SetupRun', '']
        data['equipment'] = [eqp for eqp in all_equipment 
                           if eqp.get('STATUS', '').strip() in available_statuses]
        
        # 配置数据：缓存1小时
        current_time = time.time()
        if not self._config_cache or current_time - self._config_cache_time > 3600:
            logger.info("🔄 重新加载配置数据")
            
            test_specs_result = self.data_manager.get_table_data('ET_FT_TEST_SPEC')
            data['test_specs'] = test_specs_result.get('data', []) if test_specs_result.get('success') else []
            
            stage_mapping_result = self.data_manager.get_table_data('stage_mapping')
            data['stage_mappings'] = stage_mapping_result.get('data', []) if stage_mapping_result.get('success') else []
            
            # 更新缓存
            self._config_cache = {
                'test_specs': data['test_specs'],
                'stage_mappings': data['stage_mappings']
            }
            self._config_cache_time = current_time
        else:
            logger.info("✅ 使用缓存的配置数据")
            data.update(self._config_cache)
        
        logger.info(f"📊 数据加载完成: {len(data['wait_lots'])}批次, {len(data['equipment'])}设备")
        return data

    def _execute_core_algorithm(self, scheduling_data: Dict, weights: Dict) -> List[Dict]:
        """执行核心排产算法 - 简化版"""
        wait_lots = scheduling_data['wait_lots']
        equipment = scheduling_data['equipment']
        test_specs = scheduling_data['test_specs']
        
        scheduled_lots = []
        equipment_workload = {eqp['HANDLER_ID']: 0 for eqp in equipment}
        
        # 1. 计算批次优先级并排序
        prioritized_lots = self._calculate_lot_priorities(wait_lots, weights)
        
        # 2. 为每个批次分配设备
        for lot in prioritized_lots:
            # 获取批次配置需求
            requirements = self._get_lot_requirements(lot, test_specs)
            if not requirements:
                logger.debug(f"⚠️ 批次 {lot.get('LOT_ID')} 无配置需求，跳过")
                continue
            
            # 寻找合适设备
            suitable_equipment = self._find_suitable_equipment_fast(lot, requirements, equipment)
            if not suitable_equipment:
                logger.debug(f"⚠️ 批次 {lot.get('LOT_ID')} 无合适设备，跳过")
                continue
            
            # 选择最佳设备
            best_equipment = suitable_equipment[0]
            handler_id = best_equipment['equipment']['HANDLER_ID']
            
            # 创建排产记录
            scheduled_lot = self._create_scheduled_lot_record(
                lot, best_equipment, equipment_workload[handler_id] + 1
            )
            scheduled_lots.append(scheduled_lot)
            
            # 更新设备负载
            equipment_workload[handler_id] += 1
        
        logger.info(f"✅ 排产完成: {len(scheduled_lots)}/{len(wait_lots)} 批次")
        return scheduled_lots

    def _find_suitable_equipment_fast(self, lot: Dict, requirements: Dict, equipment_list: List[Dict]) -> List[Dict]:
        """快速设备匹配算法 - O(n)复杂度"""
        
        # 1. 特殊阶段处理
        special_stage = self.special_stage_handler.is_special_stage(lot)
        if special_stage:
            equipment_list = self.special_stage_handler.handle_special_stage(
                lot, special_stage, equipment_list
            )
        
        # 2. 基础技术匹配筛选
        device_type = requirements.get('DEVICE', '')
        stage = requirements.get('STAGE', '')
        
        compatible_equipment = []
        for equipment in equipment_list:
            eqp_device = equipment.get('DEVICE', '')
            eqp_stage = equipment.get('STAGE', '')
            
            # 简化的匹配逻辑
            if (not device_type or not eqp_device or device_type == eqp_device) and \
               (not stage or not eqp_stage or self._is_stage_compatible(stage, eqp_stage)):
                compatible_equipment.append(equipment)
        
        if not compatible_equipment:
            return []
        
        # 3. 计算设备评分
        scored_candidates = []
        for equipment in compatible_equipment:
            score = self._calculate_simple_score(lot, requirements, equipment)
            if score > 0:
                scored_candidates.append({
                    'equipment': equipment,
                    'score': score,
                    'changeover_time': self._get_changeover_time(requirements, equipment)
                })
        
        # 4. 按评分排序返回
        return sorted(scored_candidates, key=lambda x: x['score'], reverse=True)

    def _calculate_simple_score(self, lot: Dict, requirements: Dict, equipment: Dict) -> float:
        """简化的设备评分算法"""
        
        # 基础匹配评分 (0-100)
        base_score = self._get_technical_match_score(requirements, equipment)
        if base_score == 0:
            return 0
        
        # 负载调整 (-20 to +20)
        handler_id = equipment.get('HANDLER_ID', '')
        current_load = self._get_current_equipment_load(handler_id)
        load_adjustment = max(-20, min(20, 10 - current_load * 5))
        
        # 优先级加权 (x1.0 to x2.0)  
        priority_weight = self._get_lot_priority_weight(lot)
        
        final_score = (base_score + load_adjustment) * priority_weight
        return min(100, max(0, final_score))

    def _get_technical_match_score(self, requirements: Dict, equipment: Dict) -> float:
        """技术匹配评分 - 简化版"""
        score = 60  # 基础分
        
        # KIT_PN匹配
        req_kit = requirements.get('KIT_PN', '')
        eqp_kit = equipment.get('KIT_PN', '')
        if req_kit and eqp_kit:
            if req_kit == eqp_kit:
                score += 30  # 完全匹配
            elif self._is_kit_family_compatible(req_kit, eqp_kit):
                score += 15  # 系列兼容
        
        # HANDLER_CONFIG匹配
        req_config = requirements.get('HANDLER_CONFIG', '')
        eqp_config = equipment.get('HANDLER_CONFIG', '')
        if req_config and eqp_config and req_config == eqp_config:
            score += 10
        
        return min(100, score)

    def _calculate_lot_priorities(self, wait_lots: List[Dict], weights: Dict) -> List[Dict]:
        """计算批次优先级并排序"""
        for lot in wait_lots:
            # 产品优先级=0的绝对优先权
            if self._is_absolute_priority_lot(lot):
                lot['priority_score'] = 10000
            else:
                # 常规优先级计算
                priority_score = self._get_lot_priority_from_config(lot)
                fifo_score = self._get_fifo_score(lot)
                lot['priority_score'] = priority_score * weights.get('priority', 0.5) + fifo_score * 0.3
        
        return sorted(wait_lots, key=lambda x: x.get('priority_score', 0), reverse=True)

    def _save_scheduling_results(self, results: List[Dict]) -> None:
        """保存排产结果到数据库"""
        if not results:
            return
            
        try:
            from app import db
            # 简化的数据库保存逻辑
            for result in results:
                # 构建插入SQL
                insert_sql = """
                INSERT INTO lotprioritydone 
                (LOT_ID, DEVICE, STAGE, STEP, PKG_PN, QTY, HANDLER_ID, PRIORITY, 
                 CREATE_TIME, UPDATE_TIME, STATUS)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), 'SCHEDULED')
                """
                
                params = (
                    result.get('LOT_ID'),
                    result.get('DEVICE'),
                    result.get('STAGE'),
                    result.get('STEP', ''),
                    result.get('PKG_PN'),
                    result.get('QTY', 0),
                    result.get('HANDLER_ID'),
                    result.get('PRIORITY', 99)
                )
                
                db.session.execute(insert_sql, params)
            
            db.session.commit()
            logger.info(f"✅ 保存排产结果: {len(results)}条记录")
            
        except Exception as e:
            logger.error(f"❌ 保存排产结果失败: {e}")
            db.session.rollback()

    def _format_scheduling_results(self, results: List[Dict], execution_time: float) -> SchedulingResult:
        """格式化排产结果"""
        total_lots = len(results)
        
        metrics = {
            'execution_time': execution_time,
            'average_score': sum(safe_float(r.get('score', 0)) for r in results) / total_lots if total_lots > 0 else 0,
            'equipment_utilization': self._calculate_equipment_utilization(results),
            'timestamp': datetime.now().isoformat()
        }
        
        return SchedulingResult(
            success=True,
            total_lots=total_lots,
            scheduled_lots=total_lots,
            failed_lots=0,
            execution_time=execution_time,
            schedule_data=results,
            metrics=metrics,
            error_message=None
        )

    # 辅助方法实现 (简化版)
    def _get_strategy_weights(self, strategy: str, user_id: str, target: str) -> Dict:
        """获取策略权重配置"""
        base_weights = self.strategy_weights.get(strategy, self.strategy_weights['intelligent'])
        
        # 根据优化目标调整权重
        if target == 'makespan':
            base_weights['load_balance'] += 0.1
        elif target == 'efficiency':
            base_weights['tech_match'] += 0.1
        
        return base_weights

    def _get_lot_requirements(self, lot: Dict, test_specs: List[Dict]) -> Optional[Dict]:
        """获取批次配置需求"""
        device = lot.get('DEVICE', '')
        stage = lot.get('STAGE', '')
        pkg_pn = lot.get('PKG_PN', '')
        
        for spec in test_specs:
            if (spec.get('DEVICE') == device and 
                spec.get('STAGE') == stage and 
                spec.get('PKG_PN') == pkg_pn):
                return spec
        return None

    def _is_stage_compatible(self, lot_stage: str, eqp_stage: str) -> bool:
        """阶段兼容性检查"""
        return lot_stage.upper() == eqp_stage.upper()

    def _get_changeover_time(self, requirements: Dict, equipment: Dict) -> int:
        """获取改机时间 - 简化版"""
        # 根据匹配类型返回不同的改机时间
        req_kit = requirements.get('KIT_PN', '')
        eqp_kit = equipment.get('KIT_PN', '')
        
        if req_kit == eqp_kit:
            return 5  # 同设置，5分钟
        elif self._is_kit_family_compatible(req_kit, eqp_kit):
            return 15  # 小改机，15分钟
        else:
            return 30  # 大改机，30分钟

    def _is_kit_family_compatible(self, req_kit: str, eqp_kit: str) -> bool:
        """KIT系列兼容性检查"""
        if not req_kit or not eqp_kit:
            return False
        # 简化的系列匹配：前缀相同
        return req_kit.split('-')[0] == eqp_kit.split('-')[0] if '-' in req_kit and '-' in eqp_kit else False

    def _get_current_equipment_load(self, handler_id: str) -> int:
        """获取设备当前负载"""
        # 简化实现：返回随机负载值
        import random
        return random.randint(0, 5)

    def _get_lot_priority_weight(self, lot: Dict) -> float:
        """获取批次优先级权重"""
        priority = lot.get('PRIORITY', 99)
        if priority <= 3:
            return 2.0  # 高优先级 x2.0
        elif priority <= 6:
            return 1.5  # 中优先级 x1.5
        else:
            return 1.0  # 低优先级 x1.0

    def _is_absolute_priority_lot(self, lot: Dict) -> bool:
        """判断是否为绝对优先权批次"""
        # 产品优先级=0的批次具有绝对优先权
        return lot.get('PRIORITY', 99) == 0

    def _get_lot_priority_from_config(self, lot: Dict) -> float:
        """从配置获取批次优先级分数"""
        priority = lot.get('PRIORITY', 99)
        return max(0, 100 - priority * 10)  # priority越小，分数越高

    def _get_fifo_score(self, lot: Dict) -> float:
        """FIFO评分"""
        create_time = lot.get('CREATE_TIME')
        if not create_time:
            return 0
        
        # 简化的FIFO评分：根据创建时间
        if isinstance(create_time, str):
            from datetime import datetime
            try:
                create_dt = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
                hours_ago = (datetime.now() - create_dt).total_seconds() / 3600
                return min(50, hours_ago * 2)  # 每小时+2分，最高50分
            except:
                return 0
        return 0

    def _create_scheduled_lot_record(self, lot: Dict, best_equipment: Dict, priority: int) -> Dict:
        """创建排产记录"""
        equipment = best_equipment['equipment']
        
        return {
            'LOT_ID': lot.get('LOT_ID'),
            'DEVICE': lot.get('DEVICE'),
            'STAGE': lot.get('STAGE'),
            'PKG_PN': lot.get('PKG_PN'),
            'QTY': lot.get('QTY', 0),
            'HANDLER_ID': equipment.get('HANDLER_ID'),
            'PRIORITY': priority,
            'score': best_equipment.get('score', 0),
            'changeover_time': best_equipment.get('changeover_time', 0),
            'scheduled_time': datetime.now().isoformat()
        }

    def _calculate_equipment_utilization(self, results: List[Dict]) -> Dict:
        """计算设备利用率"""
        if not results:
            return {}
            
        equipment_count = {}
        for result in results:
            handler_id = result.get('HANDLER_ID')
            equipment_count[handler_id] = equipment_count.get(handler_id, 0) + 1
        
        return {
            'total_equipment_used': len(equipment_count),
            'max_lots_per_equipment': max(equipment_count.values()) if equipment_count else 0,
            'avg_lots_per_equipment': sum(equipment_count.values()) / len(equipment_count) if equipment_count else 0,
            'equipment_distribution': equipment_count
        } 