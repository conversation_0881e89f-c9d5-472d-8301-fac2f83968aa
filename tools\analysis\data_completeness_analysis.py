#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性分析脚本
分析eqp_status表中HB_PN/TB_PN字段的完整性
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.utils.db_connection_pool import get_db_connection

def analyze_data_completeness():
    """分析数据完整性"""
    print("=== 数据完整性分析 ===")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 1. 分析eqp_status表的HB_PN/TB_PN完整性
        print("\n1. eqp_status表HB_PN/TB_PN完整性分析:")
        
        cursor.execute("SELECT COUNT(*) as total FROM eqp_status")
        total_eqp = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as count FROM eqp_status WHERE HB_PN IS NOT NULL AND HB_PN != ''")
        hb_pn_count = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM eqp_status WHERE TB_PN IS NOT NULL AND TB_PN != ''")
        tb_pn_count = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM eqp_status WHERE (HB_PN IS NULL OR HB_PN = '') AND (TB_PN IS NULL OR TB_PN = '')")
        both_empty_count = cursor.fetchone()['count']
        
        print(f"   总设备数: {total_eqp}")
        print(f"   有HB_PN的设备: {hb_pn_count} ({hb_pn_count/total_eqp*100:.1f}%)")
        print(f"   有TB_PN的设备: {tb_pn_count} ({tb_pn_count/total_eqp*100:.1f}%)")
        print(f"   HB_PN和TB_PN都为空的设备: {both_empty_count} ({both_empty_count/total_eqp*100:.1f}%)")
        
        # 2. 分析待排产批次与设备的匹配情况
        print("\n2. 待排产批次与设备匹配情况分析:")
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT w.LOT_ID) as total_lots,
                COUNT(DISTINCT CONCAT(w.DEVICE, '-', w.STAGE)) as unique_device_stage
            FROM et_wait_lot w
        """)
        lot_stats = cursor.fetchone()
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT CONCAT(e.DEVICE, '-', e.STAGE)) as available_device_stage
            FROM eqp_status e
            WHERE e.DEVICE IS NOT NULL AND e.DEVICE != ''
            AND e.STAGE IS NOT NULL AND e.STAGE != ''
        """)
        eqp_stats = cursor.fetchone()
        
        print(f"   待排产批次总数: {lot_stats['total_lots']}")
        print(f"   待排产的DEVICE-STAGE组合数: {lot_stats['unique_device_stage']}")
        print(f"   设备可处理的DEVICE-STAGE组合数: {eqp_stats['available_device_stage']}")
        
        # 3. 分析有完整配置的DEVICE-STAGE组合
        print("\n3. 有完整配置的DEVICE-STAGE组合分析:")
        
        cursor.execute("""
            SELECT 
                w.DEVICE, w.STAGE,
                COUNT(w.LOT_ID) as lot_count,
                COUNT(DISTINCT e.HANDLER_ID) as equipment_count,
                SUM(CASE WHEN e.HB_PN IS NOT NULL AND e.HB_PN != '' THEN 1 ELSE 0 END) as eqp_with_hb,
                SUM(CASE WHEN e.TB_PN IS NOT NULL AND e.TB_PN != '' THEN 1 ELSE 0 END) as eqp_with_tb
            FROM et_wait_lot w
            LEFT JOIN eqp_status e ON e.DEVICE = w.DEVICE AND e.STAGE = w.STAGE
            WHERE EXISTS (
                SELECT 1 FROM et_ft_test_spec t 
                WHERE t.DEVICE = w.DEVICE AND t.STAGE = w.STAGE
            )
            AND EXISTS (
                SELECT 1 FROM et_recipe_file r 
                WHERE r.DEVICE = w.DEVICE AND r.STAGE = w.STAGE
            )
            GROUP BY w.DEVICE, w.STAGE
            ORDER BY lot_count DESC
            LIMIT 10
        """)
        
        complete_configs = cursor.fetchall()
        
        print(f"   有完整配置的DEVICE-STAGE组合 (前10个):")
        for config in complete_configs:
            print(f"     {config['DEVICE']}-{config['STAGE']}: {config['lot_count']}批次, {config['equipment_count']}设备")
            print(f"       设备有HB_PN: {config['eqp_with_hb']}/{config['equipment_count']}, 有TB_PN: {config['eqp_with_tb']}/{config['equipment_count']}")
        
        # 4. 分析缺失配置的情况
        print("\n4. 缺失配置分析:")
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT w.LOT_ID) as lots_missing_test_spec
            FROM et_wait_lot w
            WHERE NOT EXISTS (
                SELECT 1 FROM et_ft_test_spec t 
                WHERE t.DEVICE = w.DEVICE AND t.STAGE = w.STAGE
            )
        """)
        missing_test_spec = cursor.fetchone()['lots_missing_test_spec']
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT w.LOT_ID) as lots_missing_recipe
            FROM et_wait_lot w
            WHERE NOT EXISTS (
                SELECT 1 FROM et_recipe_file r 
                WHERE r.DEVICE = w.DEVICE AND r.STAGE = w.STAGE
            )
        """)
        missing_recipe = cursor.fetchone()['lots_missing_recipe']
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT w.LOT_ID) as lots_no_equipment
            FROM et_wait_lot w
            WHERE NOT EXISTS (
                SELECT 1 FROM eqp_status e 
                WHERE e.DEVICE = w.DEVICE AND e.STAGE = w.STAGE
            )
        """)
        no_equipment = cursor.fetchone()['lots_no_equipment']
        
        print(f"   缺失测试规格的批次: {missing_test_spec}")
        print(f"   缺失配方的批次: {missing_recipe}")
        print(f"   没有对应设备的批次: {no_equipment}")
        
        cursor.close()
        conn.close()
        
        return {
            'total_eqp': total_eqp,
            'both_empty_count': both_empty_count,
            'missing_test_spec': missing_test_spec,
            'missing_recipe': missing_recipe,
            'no_equipment': no_equipment
        }
        
    except Exception as e:
        print(f"✗ 数据完整性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def suggest_solutions(stats):
    """根据分析结果提出解决方案"""
    print("\n=== 解决方案建议 ===")
    
    if stats:
        empty_ratio = stats['both_empty_count'] / stats['total_eqp'] * 100
        
        print(f"1. 设备HB_PN/TB_PN数据完整性问题:")
        print(f"   - {stats['both_empty_count']}/{stats['total_eqp']} ({empty_ratio:.1f}%) 的设备HB_PN和TB_PN都为空")
        
        if empty_ratio > 50:
            print(f"   🔧 建议: 调整匹配逻辑，当设备HB_PN/TB_PN为空时，如果其他条件匹配，应视为'同配置匹配'")
        
        print(f"\n2. 配置数据缺失问题:")
        if stats['missing_test_spec'] > 0:
            print(f"   - {stats['missing_test_spec']} 个批次缺失测试规格")
            print(f"   🔧 建议: 补齐et_ft_test_spec表中的数据")
        
        if stats['missing_recipe'] > 0:
            print(f"   - {stats['missing_recipe']} 个批次缺失配方")
            print(f"   🔧 建议: 补齐et_recipe_file表中的数据")
        
        if stats['no_equipment'] > 0:
            print(f"   - {stats['no_equipment']} 个批次没有对应设备")
            print(f"   🔧 建议: 检查设备配置或批次DEVICE/STAGE信息")

if __name__ == "__main__":
    print("🔍 数据完整性分析开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    stats = analyze_data_completeness()
    suggest_solutions(stats)
    
    print("\n" + "=" * 60)
    print("🎉 分析完成")
