#!/usr/bin/env python3
"""
简单的数据库检查脚本
"""
import mysql.connector

def simple_check():
    """简单检查数据库"""
    try:
        # 直接连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'et_ft_test_spec'")
        result = cursor.fetchone()
        if result:
            print("✅ et_ft_test_spec表存在")
            
            # 检查记录数
            cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec")
            count = cursor.fetchone()[0]
            print(f"📊 et_ft_test_spec表记录数: {count}")
            
            if count > 0:
                # 先检查表结构
                cursor.execute("DESCRIBE et_ft_test_spec")
                columns = cursor.fetchall()
                print("\n📋 表结构:")
                for col in columns:
                    print(f"  - {col[0]}: {col[1]}")

                # 检查前几条记录
                cursor.execute("SELECT DEVICE, STAGE, TESTER FROM et_ft_test_spec LIMIT 5")
                records = cursor.fetchall()
                print("\n📋 前5条记录:")
                for record in records:
                    print(f"  - DEVICE={record[0]}, STAGE={record[1]}, TESTER={record[2]}")

                # 检查是否有APPROVAL_STATE字段
                cursor.execute("SHOW COLUMNS FROM et_ft_test_spec LIKE '%APPROVAL%'")
                approval_cols = cursor.fetchall()
                if approval_cols:
                    print(f"\n📋 APPROVAL相关字段:")
                    for col in approval_cols:
                        print(f"  - {col[0]}")

                    # 使用正确的字段名检查
                    approval_field = approval_cols[0][0]
                    cursor.execute(f"SELECT COUNT(*) FROM et_ft_test_spec WHERE {approval_field} = 'Released'")
                    released_count = cursor.fetchone()[0]
                    print(f"\n✅ Released状态记录数: {released_count}")
                else:
                    print("\n❌ 没有找到APPROVAL相关字段")
        else:
            print("❌ et_ft_test_spec表不存在")
        
        # 检查et_wait_lot表
        cursor.execute("SHOW TABLES LIKE 'et_wait_lot'")
        result = cursor.fetchone()
        if result:
            print("\n✅ et_wait_lot表存在")
            
            cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
            count = cursor.fetchone()[0]
            print(f"📊 et_wait_lot表记录数: {count}")
            
            if count > 0:
                cursor.execute("SELECT DEVICE, STAGE FROM et_wait_lot LIMIT 5")
                records = cursor.fetchall()
                print("\n📋 前5条批次记录:")
                for record in records:
                    print(f"  - DEVICE={record[0]}, STAGE={record[1]}")
        else:
            print("❌ et_wait_lot表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_check()
