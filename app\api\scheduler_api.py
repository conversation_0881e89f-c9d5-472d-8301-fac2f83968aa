# -*- coding: utf-8 -*-
"""
APScheduler调度器管理API
提供任务CRUD操作、控制、监控等REST API接口

Author: AI Assistant
Date: 2025-06-13
"""

import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user

from app import db

# 创建蓝图
scheduler_api_bp = Blueprint('scheduler_api', __name__, url_prefix='/api/scheduler')

# 设置日志
logger = logging.getLogger(__name__)

@scheduler_api_bp.route('/status', methods=['GET'])
@login_required
def get_scheduler_status():
    """获取调度器状态"""
    try:
        # 延迟导入以避免循环依赖
        from app.services.scheduler_service import scheduler_service
        from app.models import SchedulerJob, SchedulerJobLog, SchedulerConfig
        
        # 获取调度器基本状态
        scheduler_running = scheduler_service.is_running
        
        # 获取任务统计
        total_jobs = SchedulerJob.query.count()
        active_jobs = SchedulerJob.query.filter_by(enabled=True).count()
        
        # 获取最近的执行日志
        recent_logs = SchedulerJobLog.query.order_by(SchedulerJobLog.start_time.desc()).limit(5).all()
        
        # 获取系统配置
        config = {
            'enabled': SchedulerConfig.get_config('scheduler_enabled', 'true'),
            'timezone': SchedulerConfig.get_config('timezone', 'Asia/Shanghai'),
            'max_workers': SchedulerConfig.get_config('max_workers', '10')
        }
        
        return jsonify({
            'success': True,
            'scheduler': {
                'running': scheduler_running,
                'total_jobs': total_jobs,
                'active_jobs': active_jobs,
                'config': config,
                'recent_logs': [log.to_dict() for log in recent_logs]
            }
        })
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/jobs', methods=['GET'])
@login_required
def get_all_jobs():
    """获取所有任务列表"""
    try:
        from app.services.scheduler_service import scheduler_service
        from app.models import SchedulerJob
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        job_type = request.args.get('job_type', None)
        enabled = request.args.get('enabled', None)
        
        # 构建查询
        query = SchedulerJob.query
        
        if job_type:
            query = query.filter_by(job_type=job_type)
        
        if enabled is not None:
            enabled_bool = enabled.lower() == 'true'
            query = query.filter_by(enabled=enabled_bool)
        
        # 分页查询
        pagination = query.order_by(SchedulerJob.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 获取详细状态
        jobs_with_status = []
        for job in pagination.items:
            job_status = scheduler_service.get_job_status(job.id)
            jobs_with_status.append(job_status)
        
        return jsonify({
            'success': True,
            'jobs': jobs_with_status,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/jobs', methods=['POST'])
@login_required
def create_job():
    """创建新任务"""
    try:
        from app.services.scheduler_service import scheduler_service
        
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['id', 'name', 'func', 'trigger', 'trigger_args']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需参数: {field}'
                }), 400
        
        # 验证触发器参数
        trigger_type = data['trigger']
        trigger_args = data['trigger_args']
        
        if trigger_type not in ['cron', 'interval', 'date']:
            return jsonify({
                'success': False,
                'error': f'不支持的触发器类型: {trigger_type}'
            }), 400
        
        # 创建任务
        success = scheduler_service.add_job(
            job_id=data['id'],
            name=data['name'],
            func_name=data['func'],
            trigger_type=trigger_type,
            trigger_args=trigger_args,
            args=data.get('args', []),
            kwargs=data.get('kwargs', {}),
            description=data.get('description'),
            created_by=current_user.username if current_user else 'system'
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': f'任务 "{data["name"]}" 创建成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '任务创建失败，请检查参数或任务ID是否已存在'
            }), 400
            
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/test', methods=['POST'])
@login_required
def create_test_job():
    """创建测试任务"""
    try:
        from app.services.scheduler_service import scheduler_service
        import uuid
        
        # 生成唯一的任务ID
        job_id = f"test_job_{uuid.uuid4().hex[:8]}"
        
        # 创建一个简单的测试任务，每分钟执行一次
        success = scheduler_service.add_job(
            job_id=job_id,
            name=f"测试任务 {datetime.now().strftime('%H:%M:%S')}",
            func_name='test_task',
            trigger_type='interval',
            trigger_args={'minutes': 1},
            kwargs={'message': 'APScheduler测试任务'},
            description='用于测试APScheduler功能的任务',
            created_by=current_user.username if current_user else 'system'
        )
        
        if success:
            return jsonify({
                'success': True,
                'job_id': job_id,
                'message': f'测试任务 {job_id} 创建成功，将每分钟执行一次'
            })
        else:
            return jsonify({
                'success': False,
                'error': '测试任务创建失败'
            }), 400
            
    except Exception as e:
        logger.error(f"创建测试任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/jobs/<job_id>', methods=['DELETE'])
@login_required
def delete_job(job_id):
    """删除任务"""
    try:
        from app.services.scheduler_service import scheduler_service
        
        success = scheduler_service.remove_job(job_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'任务 {job_id} 删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'任务不存在或删除失败: {job_id}'
            }), 404
            
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/jobs/<job_id>/run', methods=['POST'])
@login_required
def run_job_now(job_id):
    """立即执行任务"""
    try:
        from app.services.scheduler_service import scheduler_service
        
        success = scheduler_service.run_job_now(job_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'任务 {job_id} 已加入执行队列'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'任务执行失败: {job_id}'
            }), 400
            
    except Exception as e:
        logger.error(f"立即执行任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/logs', methods=['GET'])
@login_required
def get_execution_logs():
    """获取执行日志"""
    try:
        from app.models import SchedulerJobLog
        
        # 获取查询参数
        job_id = request.args.get('job_id')
        status = request.args.get('status')
        limit = request.args.get('limit', 50, type=int)
        
        # 构建查询
        query = SchedulerJobLog.query
        
        if job_id:
            query = query.filter_by(job_id=job_id)
        
        if status:
            query = query.filter_by(status=status)
        
        # 获取日志
        logs = query.order_by(SchedulerJobLog.start_time.desc()).limit(limit).all()
        
        return jsonify({
            'success': True,
            'logs': [log.to_dict() for log in logs],
            'count': len(logs)
        })
        
    except Exception as e:
        logger.error(f"获取执行日志失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/start', methods=['POST'])
@login_required
def start_scheduler():
    """启动调度器"""
    try:
        from app.services.scheduler_service import scheduler_service
        
        success = scheduler_service.start()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'APScheduler调度器启动成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '调度器启动失败或已在运行'
            }), 400
            
    except Exception as e:
        logger.error(f"启动调度器失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@scheduler_api_bp.route('/stop', methods=['POST'])
@login_required
def stop_scheduler():
    """停止调度器"""
    try:
        from app.services.scheduler_service import scheduler_service
        
        success = scheduler_service.stop()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'APScheduler调度器停止成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '调度器停止失败或未在运行'
            }), 400
            
    except Exception as e:
        logger.error(f"停止调度器失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500