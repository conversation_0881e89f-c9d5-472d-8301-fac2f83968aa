/**
 * 车规芯片终测智能调度平台 - 统一API客户端 v2
 * 支持API v2和向后兼容v1
 * 
 * 功能特性:
 * - 优先使用API v2，自动降级到v1
 * - 统一错误处理和响应格式
 * - 自动重试和超时管理
 * - 请求/响应拦截器
 */

class APSAPIClient {
    constructor(options = {}) {
        this.baseURL = options.baseURL || '';
        this.v2Prefix = '/api/v2';
        this.v1Prefix = '/api';
        this.timeout = options.timeout || 30000;
        this.retryCount = options.retryCount || 2;
        this.enableV2 = options.enableV2 !== false; // 默认启用v2
        
        // 请求拦截器
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        // 错误处理
        this.errorHandlers = {
            network: this.handleNetworkError.bind(this),
            timeout: this.handleTimeoutError.bind(this),
            server: this.handleServerError.bind(this),
            validation: this.handleValidationError.bind(this)
        };
        
        // API端点映射 (v2 -> v1)
        this.endpointMappings = {
            '/resources/data/': '/resources/data/',
            '/production/scheduling': '/production/schedule',
            '/production/priorities': '/production/priority',
            '/wip/batches': '/resources/data/wip_lot'
        };
        
        this.init();
    }
    
    init() {
        // 添加默认请求拦截器
        this.addRequestInterceptor((config) => {
            // 添加认证头
            const token = this.getAuthToken();
            if (token) {
                config.headers = config.headers || {};
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            
            // 添加通用头
            config.headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...config.headers
            };
            
            return config;
        });
        
        // 添加默认响应拦截器
        this.addResponseInterceptor((response) => {
            // 统一响应格式
            return this.normalizeResponse(response);
        });
    }
    
    /**
     * 主要API方法 - GET请求
     */
    async get(endpoint, params = {}, options = {}) {
        return this.request('GET', endpoint, null, params, options);
    }
    
    /**
     * 主要API方法 - POST请求  
     */
    async post(endpoint, data = null, options = {}) {
        return this.request('POST', endpoint, data, {}, options);
    }
    
    /**
     * 主要API方法 - PUT请求
     */
    async put(endpoint, data = null, options = {}) {
        return this.request('PUT', endpoint, data, {}, options);
    }
    
    /**
     * 主要API方法 - DELETE请求
     */
    async delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, {}, options);
    }
    
    /**
     * 核心请求方法
     */
    async request(method, endpoint, data = null, params = {}, options = {}) {
        const config = {
            method,
            endpoint,
            data,
            params,
            ...options,
            headers: options.headers || {}
        };
        
        // 应用请求拦截器
        const finalConfig = await this.applyRequestInterceptors(config);
        
        let lastError = null;
        
        // 尝试API v2（如果启用）
        if (this.enableV2) {
            try {
                const v2Response = await this.makeRequest(finalConfig, true);
                return await this.applyResponseInterceptors(v2Response);
            } catch (error) {
                console.warn(`API v2请求失败，准备降级到v1: ${error.message}`);
                lastError = error;
            }
        }
        
        // 降级到API v1
        try {
            const v1Response = await this.makeRequest(finalConfig, false);
            return await this.applyResponseInterceptors(v1Response);
        } catch (error) {
            console.error(`API v1请求也失败: ${error.message}`);
            // 抛出最有意义的错误（v2或v1）
            throw lastError && lastError.status !== 404 ? lastError : error;
        }
    }
    
    /**
     * 执行实际的HTTP请求
     */
    async makeRequest(config, useV2 = true) {
        const url = this.buildURL(config.endpoint, config.params, useV2);
        
        const fetchOptions = {
            method: config.method,
            headers: config.headers,
            credentials: 'same-origin'
        };
        
        // 添加请求体（如果有）
        if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            if (config.headers['Content-Type'] === 'application/json') {
                fetchOptions.body = JSON.stringify(config.data);
            } else {
                fetchOptions.body = config.data;
            }
        }
        
        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        fetchOptions.signal = controller.signal;
        
        try {
            const response = await fetch(url, fetchOptions);
            clearTimeout(timeoutId);
            
            // 检查响应状态
            if (!response.ok) {
                throw new APSAPIError(
                    `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    await response.text()
                );
            }
            
            // 解析响应
            const contentType = response.headers.get('content-type');
            let responseData;
            
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }
            
            return {
                data: responseData,
                status: response.status,
                headers: response.headers,
                url: url,
                apiVersion: useV2 ? '2.0' : '1.0'
            };
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new APSAPIError('请求超时', 408, null);
            }
            throw error;
        }
    }
    
    /**
     * 构建完整URL
     */
    buildURL(endpoint, params = {}, useV2 = true) {
        let baseURL = this.baseURL;
        let apiPrefix = useV2 ? this.v2Prefix : this.v1Prefix;
        
        // 端点映射 (v2 -> v1)
        if (!useV2) {
            for (const [v2Pattern, v1Pattern] of Object.entries(this.endpointMappings)) {
                if (endpoint.startsWith(v2Pattern)) {
                    endpoint = endpoint.replace(v2Pattern, v1Pattern);
                    break;
                }
            }
        }
        
        // 构建完整URL
        let fullURL = `${baseURL}${apiPrefix}${endpoint}`;
        
        // 添加查询参数
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams();
            for (const [key, value] of Object.entries(params)) {
                if (value !== null && value !== undefined) {
                    searchParams.append(key, value);
                }
            }
            fullURL += `?${searchParams.toString()}`;
        }
        
        return fullURL;
    }
    
    /**
     * 标准化响应格式
     */
    normalizeResponse(response) {
        const { data, status, headers, apiVersion } = response;
        
        // 如果是v2 API响应，直接返回
        if (apiVersion === '2.0' && data && typeof data === 'object' && 'success' in data) {
            return {
                success: data.success,
                data: data.data,
                error: data.error,
                meta: data.meta,
                _response: response
            };
        }
        
        // 标准化v1 API响应
        if (status >= 200 && status < 300) {
            return {
                success: true,
                data: data,
                error: null,
                meta: {
                    api_version: apiVersion,
                    status: status
                },
                _response: response
            };
        } else {
            return {
                success: false,
                data: null,
                error: data.error || data.message || '请求失败',
                meta: {
                    api_version: apiVersion,
                    status: status
                },
                _response: response
            };
        }
    }
    
    /**
     * 添加请求拦截器
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }
    
    /**
     * 应用请求拦截器
     */
    async applyRequestInterceptors(config) {
        let finalConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            try {
                finalConfig = await interceptor(finalConfig) || finalConfig;
            } catch (error) {
                console.error('请求拦截器执行失败:', error);
            }
        }
        
        return finalConfig;
    }
    
    /**
     * 应用响应拦截器
     */
    async applyResponseInterceptors(response) {
        let finalResponse = response;
        
        for (const interceptor of this.responseInterceptors) {
            try {
                finalResponse = await interceptor(finalResponse) || finalResponse;
            } catch (error) {
                console.error('响应拦截器执行失败:', error);
            }
        }
        
        return finalResponse;
    }
    
    /**
     * 获取认证令牌
     */
    getAuthToken() {
        return localStorage.getItem('aps_auth_token') || sessionStorage.getItem('aps_auth_token');
    }
    
    /**
     * 错误处理方法
     */
    handleNetworkError(error) {
        console.error('网络错误:', error);
        return {
            success: false,
            error: '网络连接失败，请检查网络设置',
            data: null
        };
    }
    
    handleTimeoutError(error) {
        console.error('请求超时:', error);
        return {
            success: false,
            error: '请求超时，请稍后重试',
            data: null
        };
    }
    
    handleServerError(error) {
        console.error('服务器错误:', error);
        return {
            success: false,
            error: '服务器内部错误，请联系管理员',
            data: null
        };
    }
    
    handleValidationError(error) {
        console.error('数据验证错误:', error);
        return {
            success: false,
            error: '数据格式不正确，请检查输入',
            data: null
        };
    }
}

/**
 * 自定义API错误类
 */
class APSAPIError extends Error {
    constructor(message, status = 0, response = null) {
        super(message);
        this.name = 'APSAPIError';
        this.status = status;
        this.response = response;
    }
}

/**
 * 便捷的资源API方法
 */
class APSResourceAPI {
    constructor(client) {
        this.client = client;
    }
    
    /**
     * 获取表格数据
     */
    async getTableData(tableName, options = {}) {
        const { page = 1, per_page = 50, filters = [] } = options;
        
        const params = { page, per_page };
        if (filters.length > 0) {
            params.advanced_filters = JSON.stringify(filters);
        }
        
        return this.client.get(`/resources/data/${tableName}`, params);
    }
    
    /**
     * 获取表格列信息
     */
    async getTableColumns(tableName) {
        return this.client.get(`/resources/data/${tableName}/columns`);
    }
    
    /**
     * 导出表格数据
     */
    async exportTableData(tableName, options = {}) {
        const { format = 'excel', advanced_filters = [] } = options;
        
        const params = { format };
        if (advanced_filters.length > 0) {
            params.advanced_filters = JSON.stringify(advanced_filters);
        }
        
        return this.client.get(`/resources/data/${tableName}/export`, params);
    }
    
    /**
     * 获取可用表格列表
     */
    async getAvailableTables() {
        return this.client.get('/resources/tables');
    }
}

/**
 * 便捷的生产API方法
 */
class APSProductionAPI {
    constructor(client) {
        this.client = client;
    }
    
    /**
     * 执行智能排产
     */
    async executeScheduling(options = {}) {
        const { algorithm = 'intelligent', optimization_target = 'balanced' } = options;
        
        return this.client.post('/production/scheduling/execute', {
            algorithm,
            optimization_target
        });
    }
    
    /**
     * 获取排产历史
     */
    async getSchedulingHistory(page = 1, per_page = 20) {
        return this.client.get('/production/scheduling/history', { page, per_page });
    }
    
    /**
     * 获取数据源状态
     */
    async getDataSourceStatus() {
        return this.client.get('/production/data-source/status');
    }
    
    /**
     * 切换数据源
     */
    async switchDataSource(source) {
        return this.client.post('/production/data-source/switch', { source });
    }
}

// 创建全局API客户端实例
window.apsAPI = new APSAPIClient({
    enableV2: true,
    timeout: 30000,
    retryCount: 2
});

// 创建便捷API实例
window.apsAPI.resources = new APSResourceAPI(window.apsAPI);
window.apsAPI.production = new APSProductionAPI(window.apsAPI);

// 导出为模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APSAPIClient, APSResourceAPI, APSProductionAPI, APSAPIError };
}

console.log('🚀 APS API客户端v2已初始化，支持智能降级机制'); 