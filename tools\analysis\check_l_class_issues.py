#!/usr/bin/env python3
"""
检查L类问题（跨工序A类应命中）的具体情况
"""
import pymysql

def check_l_class_issues():
    """检查L类问题的具体情况"""
    print("🔍 检查L类问题（跨工序A类应命中）...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 选择几个典型的L类问题批次
            test_lots = ['YX0125GB0001', 'YX0125HA0175', 'YX0125IA0082']
            
            for lot_id in test_lots:
                print(f"\n🔍 分析批次: {lot_id}")
                
                # 获取当前排产结果
                cursor.execute("""
                    SELECT LOT_ID, DEVICE, STAGE, HANDLER_ID, match_type, changeover_time
                    FROM lotprioritydone
                    WHERE LOT_ID = %s
                """, (lot_id,))
                
                lot_result = cursor.fetchone()
                if not lot_result:
                    print(f"   ❌ 未找到排产结果")
                    continue
                
                print(f"   批次DEVICE: {lot_result['DEVICE']}")
                print(f"   批次STAGE: {lot_result['STAGE']}")
                print(f"   分配设备: {lot_result['HANDLER_ID']}")
                print(f"   匹配类型: {lot_result['match_type']}")
                print(f"   改机时间: {lot_result['changeover_time']}分钟")
                
                # 获取设备详细信息
                cursor.execute("""
                    SELECT DEVICE, STAGE, HANDLER_CONFIG, KIT_PN, TESTER
                    FROM eqp_status
                    WHERE HANDLER_ID = %s
                """, (lot_result['HANDLER_ID'],))
                
                equipment = cursor.fetchone()
                if equipment:
                    print(f"   设备DEVICE: {equipment['DEVICE']}")
                    print(f"   设备STAGE: {equipment['STAGE']}")
                    print(f"   HANDLER_CONFIG: {equipment['HANDLER_CONFIG']}")
                    print(f"   KIT_PN: {equipment['KIT_PN']}")
                    print(f"   TESTER: {equipment['TESTER']}")
                    
                    # 检查是否为跨工序
                    device_match = lot_result['DEVICE'] == equipment['DEVICE']
                    stage_match = lot_result['STAGE'].upper() == equipment['STAGE'].upper()
                    
                    print(f"   DEVICE匹配: {device_match}")
                    print(f"   STAGE匹配: {stage_match}")
                    
                    if not device_match or not stage_match:
                        print(f"   ✅ 确实是跨工序情况")
                        
                        # 检查是否满足跨工序A类条件（HC+KIT一致）
                        cursor.execute("""
                            SELECT COUNT(*) as count
                            FROM et_recipe_file r
                            WHERE r.DEVICE = %s AND UPPER(r.STAGE) = UPPER(%s)
                              AND r.HANDLER_CONFIG = %s AND r.KIT_PN = %s
                        """, (lot_result['DEVICE'], lot_result['STAGE'], 
                              equipment['HANDLER_CONFIG'], equipment['KIT_PN']))
                        
                        a_class_count = cursor.fetchone()['count']
                        print(f"   跨工序A类匹配条件满足: {a_class_count > 0} (配方数: {a_class_count})")
                        
                        if a_class_count > 0:
                            print(f"   💡 应该被分类为: 跨工序A类匹配(60分钟)")
                            print(f"   ❌ 实际分类为: {lot_result['match_type']}({lot_result['changeover_time']}分钟)")
                            
                            # 分析为什么没有被正确分类
                            if lot_result['match_type'] == '换测试机小改机匹配':
                                print(f"   🔍 问题分析: 被错误分类为换测试机小改机匹配")
                                print(f"   💡 可能原因: 小改机匹配逻辑没有正确检查DEVICE+STAGE一致性")
                        else:
                            # 检查跨工序B类条件（仅HC一致）
                            cursor.execute("""
                                SELECT COUNT(*) as count
                                FROM et_recipe_file r
                                WHERE r.DEVICE = %s AND UPPER(r.STAGE) = UPPER(%s)
                                  AND r.HANDLER_CONFIG = %s
                            """, (lot_result['DEVICE'], lot_result['STAGE'], 
                                  equipment['HANDLER_CONFIG']))
                            
                            b_class_count = cursor.fetchone()['count']
                            print(f"   跨工序B类匹配条件满足: {b_class_count > 0} (配方数: {b_class_count})")
                            
                            if b_class_count > 0:
                                print(f"   💡 应该被分类为: 跨工序B类匹配(90分钟)")
                                print(f"   ❌ 实际分类为: {lot_result['match_type']}({lot_result['changeover_time']}分钟)")
                    else:
                        print(f"   ❌ 不是跨工序情况，L类检查可能有误")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_l_class_issues()
