import os
import json
import logging
import pandas as pd
import datetime
from app import db
from app.models import ExcelMapping, OrderData

# 设置日志
logger = logging.getLogger(__name__)

class ExcelProcessor:
    """Excel处理器类，用于处理Excel文件和提取数据"""
    
    def __init__(self, mapping_id=None):
        """初始化Excel处理器
        
        参数:
            mapping_id: Excel映射配置ID，为None时不加载配置
        """
        self.mapping = None
        
        if mapping_id:
            self.mapping = ExcelMapping.query.get(mapping_id)
    
    def process_file(self, file_path, mapping=None, save_to_db=True):
        """处理Excel文件
        
        参数:
            file_path: Excel文件路径
            mapping: Excel映射配置对象，为None时使用已有配置
            save_to_db: 是否保存到数据库，默认为True
            
        返回:
            dict: 处理结果
        """
        if mapping:
            self.mapping = mapping
        
        if not self.mapping:
            raise ValueError("未提供Excel映射配置")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            # 加载Excel文件
            sheet_name = self.mapping.sheet_name if self.mapping.sheet_name else 0
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 配置信息
            header_row = self.mapping.header_row - 1  # pandas行索引从0开始
            data_start_row = self.mapping.start_row - 1
            
            # 设置表头
            if header_row >= 0:
                headers = df.iloc[header_row].values
                df.columns = headers
                
                # 重新加载数据，从数据起始行开始
                df = df.iloc[data_start_row:]
                df = df.reset_index(drop=True)
            
            # 获取字段映射
            field_mappings = json.loads(self.mapping.field_mappings)
            
            # 提取数据
            extracted_data = []
            
            # 获取关键字段
            key_fields = [field.strip() for field in self.mapping.key_fields.split(',') if field.strip()]
            
            # 处理每一行数据
            for index, row in df.iterrows():
                data = {}
                
                # 原始数据
                raw_data = row.to_dict()
                
                # 根据映射提取数据
                for excel_field, system_field in field_mappings.items():
                    if excel_field in row:
                        data[system_field] = row[excel_field]
                
                # 检查关键字段是否存在
                key_values = {}
                for key_field in key_fields:
                    if key_field in data:
                        key_values[key_field] = data[key_field]
                
                # 保存到数据库
                if save_to_db and key_values:
                    # 构建查询条件
                    query_conditions = []
                    for field, value in key_values.items():
                        query_conditions.append(getattr(OrderData, field) == value)
                    
                    # 查找是否存在相同记录
                    existing_order = OrderData.query.filter(*query_conditions).first()
                    
                    if existing_order:
                        # 更新现有记录
                        for field, value in data.items():
                            setattr(existing_order, field, value)
                        existing_order.updated_at = datetime.datetime.utcnow()
                        existing_order.source_file = file_path
                        existing_order.raw_data = json.dumps(raw_data, ensure_ascii=False)
                        db.session.commit()
                        
                        data['id'] = existing_order.id
                        data['updated'] = True
                    else:
                        # 创建新记录
                        order = OrderData(**data)
                        order.source_file = file_path
                        order.raw_data = json.dumps(raw_data, ensure_ascii=False)
                        db.session.add(order)
                        db.session.commit()
                        
                        data['id'] = order.id
                        data['created'] = True
                
                extracted_data.append(data)
            
            # 创建输出目录
            output_dir = "downloads"
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建输出文件名
            output_filename = f"订单汇总_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            output_path = os.path.join(output_dir, output_filename)
            
            # 将提取的数据保存为Excel
            output_df = pd.DataFrame(extracted_data)
            output_df.to_excel(output_path, index=False)
            
            return {
                'status': 'success',
                'message': f'成功处理 {len(extracted_data)} 条数据',
                'data': extracted_data,
                'output_file': output_path
            }
        
        except Exception as e:
            logger.error(f"处理Excel文件失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'处理Excel文件失败: {str(e)}',
                'data': None
            }
    
    def batch_process_files(self, file_paths, mapping=None, save_to_db=True):
        """批量处理文件
        
        参数:
            file_paths: 文件路径列表
            mapping: Excel映射配置对象，为None时使用已有配置
            save_to_db: 是否保存到数据库，默认为True
            
        返回:
            dict: 处理结果
        """
        if mapping:
            self.mapping = mapping
            
        if not self.mapping:
            raise ValueError("未提供Excel映射配置")
        
        results = {
            'success': 0,
            'error': 0,
            'total_records': 0,
            'successful_files': [],
            'failed_files': [],
            'summary_file': None
        }
        
        all_data = []
        
        # 处理每个文件
        for file_path in file_paths:
            try:
                result = self.process_file(file_path, self.mapping, save_to_db)
                
                if result['status'] == 'success':
                    results['success'] += 1
                    results['total_records'] += len(result['data'])
                    results['successful_files'].append({
                        'path': file_path,
                        'records': len(result['data'])
                    })
                    all_data.extend(result['data'])
                else:
                    results['error'] += 1
                    results['failed_files'].append({
                        'path': file_path,
                        'error': result['message']
                    })
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                results['error'] += 1
                results['failed_files'].append({
                    'path': file_path,
                    'error': str(e)
                })
        
        # 生成汇总文件
        if all_data:
            try:
                # 创建输出目录
                output_dir = "downloads"
                os.makedirs(output_dir, exist_ok=True)
                
                # 构建输出文件名
                output_filename = f"生产工单信息汇总表_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                output_path = os.path.join(output_dir, output_filename)
                
                # 将汇总数据保存为Excel
                output_df = pd.DataFrame(all_data)
                output_df.to_excel(output_path, index=False)
                
                results['summary_file'] = output_path
            except Exception as e:
                logger.error(f"生成汇总文件失败: {str(e)}")
        
        return results 