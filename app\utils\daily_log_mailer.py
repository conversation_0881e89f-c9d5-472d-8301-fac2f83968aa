#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日日志邮件发送器 - 优化版
使用数据库中现有的邮箱配置，每天晚上8点自动发送日志到指定邮箱
"""

import os
import sys
import smtplib
import zipfile
import logging
from datetime import datetime, timedelta
from pathlib import Path
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

logger = logging.getLogger(__name__)

class DailyLogMailer:
    """每日日志邮件发送器 - 数据库版"""
    
    def __init__(self):
        # 固定接收邮箱
        self.recipient = "<EMAIL>"
        
        # 从数据库获取邮箱配置
        self.email_config = self._get_email_config_from_db()
        
        if self.email_config:
            # 将IMAP配置转换为SMTP配置
            self.smtp_config = self._convert_to_smtp_config(self.email_config)
            logger.info(f"✅ 使用数据库邮箱配置: {self.email_config.name} ({self.email_config.email})")
        else:
            logger.warning("⚠️ 未找到可用的数据库邮箱配置，使用备用配置")
            self.smtp_config = self._get_fallback_smtp_config()
    
    def _get_email_config_from_db(self):
        """从数据库获取邮箱配置"""
        try:
            from flask import current_app, has_app_context
            if has_app_context() and current_app:
                return self._query_email_config()
            else:
                # 没有应用上下文时，使用直接数据库连接
                return self._query_email_config_direct()
        except Exception as e:
            logger.error(f"❌ 从数据库获取邮箱配置失败: {e}")
            return None
    
    def _query_email_config(self):
        """查询邮箱配置"""
        from app.models import EmailConfig
        
        # 优先使用启用的配置
        config = EmailConfig.query.filter_by(enabled=True).first()
        if config:
            return config
        
        # 如果没有启用的，使用第一个配置
        config = EmailConfig.query.first()
        if config:
            logger.info(f"📧 使用未启用的邮箱配置: {config.name}")
            return config
        
        return None
    
    def _query_email_config_direct(self):
        """直接查询邮箱配置（无Flask上下文）"""
        try:
            from app.utils.db_helper import get_mysql_connection_context
            import json
            
            with get_mysql_connection_context('aps') as conn:
                cursor = conn.cursor()
                
                # 查询启用的配置
                cursor.execute("""
                    SELECT id, name, server, port, email, password, enabled 
                    FROM email_config 
                    WHERE enabled = 1 
                    ORDER BY id ASC 
                    LIMIT 1
                """)
                
                row = cursor.fetchone()
                cursor.close()
                
                if row:
                    # 创建简化的配置对象
                    class SimpleEmailConfig:
                        def __init__(self, id, name, server, port, email, password, enabled):
                            self.id = id
                            self.name = name
                            self.server = server
                            self.port = port
                            self.email = email
                            self.password = password
                            self.enabled = enabled
                    
                    return SimpleEmailConfig(*row)
                
                # 如果没有启用的，查询第一个
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, name, server, port, email, password, enabled 
                    FROM email_config 
                    ORDER BY id ASC 
                    LIMIT 1
                """)
                
                row = cursor.fetchone()
                cursor.close()
                
                if row:
                    logger.info(f"📧 使用未启用的邮箱配置: {row[1]}")
                    return SimpleEmailConfig(*row)
                
                return None
                
        except Exception as e:
            logger.error(f"❌ 直接查询邮箱配置失败: {e}")
            return None
    
    def _convert_to_smtp_config(self, email_config):
        """将IMAP配置转换为SMTP配置"""
        server_mappings = {
            # 企业邮箱
            'imaphz.qiye.163.com': {'server': 'smtphz.qiye.163.com', 'port': 465, 'use_ssl': True},
            'imap.qiye.163.com': {'server': 'smtp.qiye.163.com', 'port': 465, 'use_ssl': True},
            
            # 163邮箱
            'imap.163.com': {'server': 'smtp.163.com', 'port': 465, 'use_ssl': True},
            
            # QQ邮箱
            'imap.qq.com': {'server': 'smtp.qq.com', 'port': 587, 'use_ssl': False},
            
            # Gmail
            'imap.gmail.com': {'server': 'smtp.gmail.com', 'port': 587, 'use_ssl': False},
            
            # Outlook
            'outlook.office365.com': {'server': 'smtp.office365.com', 'port': 587, 'use_ssl': False}
        }
        
        # 查找对应的SMTP配置
        imap_server = email_config.server
        smtp_config = server_mappings.get(imap_server)
        
        if smtp_config:
            return {
                'name': f'{email_config.name}_SMTP',
                'server': smtp_config['server'],
                'port': smtp_config['port'],
                'use_ssl': smtp_config['use_ssl'],
                'email': email_config.email,
                'password': email_config.password
            }
        else:
            # 如果没有找到映射，尝试智能推测
            return self._guess_smtp_config(email_config)
    
    def _guess_smtp_config(self, email_config):
        """智能推测SMTP配置"""
        server = email_config.server.replace('imap', 'smtp').replace('pop', 'smtp')
        
        # 根据端口推测SSL设置
        if email_config.port == 993 or email_config.port == 995:
            # IMAP SSL 或 POP SSL，对应SMTP SSL
            port = 465
            use_ssl = True
        else:
            # 其他情况使用STARTTLS
            port = 587
            use_ssl = False
        
        return {
            'name': f'{email_config.name}_推测SMTP',
            'server': server,
            'port': port,
            'use_ssl': use_ssl,
            'email': email_config.email,
            'password': email_config.password
        }
    
    def _get_fallback_smtp_config(self):
        """获取备用SMTP配置"""
        return {
            'name': '备用配置',
            'server': 'smtp.163.com',
            'port': 465,
            'use_ssl': True,
            'email': '<EMAIL>',
            'password': ''
        }
    
    def collect_logs(self):
        """收集当天的日志文件"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 确定日志目录
        if getattr(sys, 'frozen', False):
            # exe环境
            exe_dir = Path(sys.executable).parent
            logs_dir = exe_dir / 'logs'
        else:
            # 开发环境
            logs_dir = Path('logs')
        
        if not logs_dir.exists():
            logger.warning(f"日志目录不存在: {logs_dir}")
            return None
            
        # 收集所有日志文件
        log_files = []
        for log_file in logs_dir.glob('*.log*'):
            if log_file.is_file():
                # 检查文件修改时间是否是今天或昨天
                modified_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if modified_time.date() >= (datetime.now() - timedelta(days=1)).date():
                    log_files.append(log_file)
        
        if not log_files:
            logger.warning("没有找到需要发送的日志文件")
            return None
        
        # 创建zip压缩包
        zip_filename = f"APS_logs_{today}.zip"
        zip_path = logs_dir / zip_filename
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for log_file in log_files:
                    zipf.write(log_file, log_file.name)
                    logger.info(f"已添加日志文件: {log_file.name}")
            
            logger.info(f"日志压缩包创建成功: {zip_path}")
            return zip_path
            
        except Exception as e:
            logger.error(f"创建日志压缩包失败: {e}")
            return None
    
    def generate_report_content(self, log_files_count, zip_size):
        """生成邮件内容"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 获取系统运行状态
        system_info = self._get_system_info()
        
        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; }}
                .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .info-box {{ background-color: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .footer {{ background-color: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }}
                .config-info {{ background-color: #e3f2fd; padding: 10px; margin: 10px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚀 APS智能排产平台 - 每日日志报告</h2>
                <p>{today}</p>
            </div>
            
            <div class="content">
                <h3>📊 系统运行状态</h3>
                <div class="info-box">
                    <p><strong>🖥️ 运行环境:</strong> {system_info['environment']}</p>
                    <p><strong>⏰ 运行时间:</strong> {system_info['uptime']}</p>
                    <p><strong>💾 内存使用:</strong> {system_info['memory']}</p>
                    <p><strong>💽 磁盘使用:</strong> {system_info['disk']}</p>
                </div>
                
                <h3>📝 日志文件信息</h3>
                <div class="info-box">
                    <p><strong>📄 日志文件数量:</strong> {log_files_count} 个</p>
                    <p><strong>📦 压缩包大小:</strong> {zip_size}</p>
                    <p><strong>📅 收集日期:</strong> {today}</p>
                </div>
                
                <h3>📧 邮件配置信息</h3>
                <div class="config-info">
                    <p><strong>发送邮箱:</strong> {self.smtp_config['email']}</p>
                    <p><strong>SMTP服务器:</strong> {self.smtp_config['server']}:{self.smtp_config['port']}</p>
                    <p><strong>加密方式:</strong> {'SSL' if self.smtp_config['use_ssl'] else 'STARTTLS'}</p>
                    <p><strong>配置来源:</strong> {self.smtp_config['name']}</p>
                </div>
                
                <h3>📋 说明</h3>
                <div class="info-box">
                    <p>• 本邮件包含系统当日运行的所有日志文件</p>
                    <p>• 日志文件已压缩打包，请下载附件查看详情</p>
                    <p>• 系统每天晚上8点自动发送日志报告</p>
                    <p>• 如有问题，请及时联系技术支持</p>
                </div>
            </div>
            
            <div class="footer">
                <p>AEC-FT智能排产指挥平台 | 自动发送 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def _get_system_info(self):
        """获取系统运行信息"""
        try:
            import psutil
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_info = f"{memory.percent}% ({memory.used // 1024 // 1024}MB / {memory.total // 1024 // 1024}MB)"
            
            # 磁盘信息  
            if os.name == 'nt':  # Windows
                disk = psutil.disk_usage('C:')
            else:  # Linux/Unix
                disk = psutil.disk_usage('/')
            disk_info = f"{disk.percent}% ({disk.used // 1024 // 1024 // 1024}GB / {disk.total // 1024 // 1024 // 1024}GB)"
            
            # 运行环境
            environment = "EXE打包环境" if getattr(sys, 'frozen', False) else "开发环境"
            
            return {
                'environment': environment,
                'uptime': '正常运行',
                'memory': memory_info,
                'disk': disk_info
            }
        except Exception:
            return {
                'environment': "EXE打包环境" if getattr(sys, 'frozen', False) else "开发环境",
                'uptime': '正常运行',
                'memory': '获取失败',
                'disk': '获取失败'
            }
    
    def send_daily_logs(self):
        """发送每日日志邮件"""
        logger.info("🚀 开始发送每日日志邮件")
        
        # 检查邮箱配置
        if not self.smtp_config or not self.smtp_config['password']:
            logger.error("❌ 邮箱配置不完整，请检查数据库中的邮箱配置")
            return False
        
        # 收集日志文件
        zip_path = self.collect_logs()
        if not zip_path:
            logger.error("❌ 日志收集失败，取消发送")
            return False
        
        try:
            # 获取压缩包信息
            zip_size = f"{zip_path.stat().st_size / 1024 / 1024:.1f}MB"
            log_files_count = self._count_files_in_zip(zip_path)
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config['email']
            msg['To'] = self.recipient
            msg['Subject'] = f"APS智能排产平台 - 每日日志报告 [{datetime.now().strftime('%Y-%m-%d')}]"
            
            # 添加HTML内容
            html_content = self.generate_report_content(log_files_count, zip_size)
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))
            
            # 添加附件
            with open(zip_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {zip_path.name}'
            )
            msg.attach(part)
            
            # 发送邮件
            success = self._send_email(msg)
            
            if success:
                logger.info("✅ 每日日志邮件发送成功")
                # 删除临时压缩包
                try:
                    zip_path.unlink()
                    logger.info(f"🗑️ 临时文件已删除: {zip_path}")
                except:
                    pass
                return True
            else:
                logger.error("❌ 邮件发送失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 发送每日日志邮件失败: {e}")
            return False
    
    def _count_files_in_zip(self, zip_path):
        """统计zip文件中的文件数量"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                return len(zipf.namelist())
        except:
            return 0
    
    def _send_email(self, msg):
        """发送邮件"""
        try:
            logger.info(f"🔄 通过 {self.smtp_config['name']} 发送邮件...")
            logger.info(f"📧 服务器: {self.smtp_config['server']}:{self.smtp_config['port']}")
            
            if self.smtp_config['use_ssl']:
                server = smtplib.SMTP_SSL(self.smtp_config['server'], self.smtp_config['port'])
            else:
                server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
                server.starttls()
            
            server.login(self.smtp_config['email'], self.smtp_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"✅ 邮件发送成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 邮件发送失败: {e}")
            return False
    
    def test_email_config(self):
        """测试邮件配置"""
        logger.info("🧪 测试邮件配置")
        
        if not self.smtp_config or not self.smtp_config['password']:
            logger.error("❌ 邮箱配置不完整")
            return False
        
        # 创建测试邮件
        msg = MIMEMultipart()
        msg['From'] = self.smtp_config['email']
        msg['To'] = self.recipient
        msg['Subject'] = "APS系统 - 邮件配置测试"
        
        test_content = f"""
        <html>
        <body>
            <h3>📧 APS系统邮件配置测试</h3>
            <p>如果您收到这封邮件，说明邮件配置正常！</p>
            <p><strong>发送时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>发送邮箱:</strong> {self.smtp_config['email']}</p>
            <p><strong>接收邮箱:</strong> {self.recipient}</p>
            <p><strong>SMTP服务器:</strong> {self.smtp_config['server']}:{self.smtp_config['port']}</p>
            <p><strong>配置来源:</strong> {self.smtp_config['name']}</p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(test_content, 'html', 'utf-8'))
        
        return self._send_email(msg)

def send_daily_logs():
    """每日日志发送任务入口函数"""
    mailer = DailyLogMailer()
    return mailer.send_daily_logs()

def test_email_send():
    """测试邮件发送功能"""
    mailer = DailyLogMailer()
    return mailer.test_email_config()

if __name__ == "__main__":
    # 支持命令行测试
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        print("🧪 测试邮件发送...")
        success = test_email_send()
        print("✅ 测试成功" if success else "❌ 测试失败")
    else:
        print("🚀 执行每日日志发送...")
        success = send_daily_logs()
        print("✅ 发送成功" if success else "❌ 发送失败") 