#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产数据中“大改机匹配”分析脚本（只读）
- 统计大改机匹配数量与占比
- 导出关键清单
- 粗略校验是否存在更优匹配（同配置/小改机/同产品续排）
依赖: 本项目内置连接池与表结构
"""

import json
import sys
import os
from collections import defaultdict
from datetime import datetime

# ensure project root on sys.path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

RUN_STATUSES = {"RUN", "IDLE", "SETUP", "READY", "ACTIVE", "ONLINE"}


def fetchall_dict(cursor):
    rows = cursor.fetchall()
    if not rows:
        return []
    if isinstance(rows[0], dict):
        return rows
    # when cursor is not DictCursor
    cols = [d[0] for d in cursor.description]
    return [dict(zip(cols, r)) for r in rows]


def load_big_change_data(conn):
    cur = conn.cursor()
    cur.execute("SELECT COUNT(*) AS total FROM lotprioritydone")
    total = fetchall_dict(cur)[0]["total"]

    cur.execute(
        """
        SELECT PRIORITY, LOT_ID, DEVICE, HANDLER_ID, STAGE, STEP, GOOD_QTY,
               comprehensive_score, changeover_time, match_type, CREATE_TIME
        FROM lotprioritydone
        WHERE match_type = '大改机匹配'
        ORDER BY PRIORITY ASC
        """
    )
    big_rows = fetchall_dict(cur)

    cur.execute(
        """SELECT DEVICE, COUNT(*) AS cnt
             FROM lotprioritydone WHERE match_type='大改机匹配'
             GROUP BY DEVICE ORDER BY cnt DESC"""
    )
    by_device = fetchall_dict(cur)

    cur.execute(
        """SELECT HANDLER_ID, COUNT(*) AS cnt
             FROM lotprioritydone WHERE match_type='大改机匹配'
             GROUP BY HANDLER_ID ORDER BY cnt DESC"""
    )
    by_handler = fetchall_dict(cur)

    return total, big_rows, by_device, by_handler


def get_eqp_status(conn):
    cur = conn.cursor()
    cur.execute(
        """
        SELECT HANDLER_ID, DEVICE AS EQP_DEVICE, STAGE AS EQP_STAGE, STATUS,
               HANDLER_CONFIG, KIT_PN, HB_PN, TB_PN
        FROM eqp_status
        """
    )
    rows = fetchall_dict(cur)
    # 以HANDLER_ID索引，也保留列表
    by_handler = defaultdict(list)
    for r in rows:
        by_handler[str(r.get("HANDLER_ID") or "")].append(r)
    return rows, by_handler


def get_required_kit_from_spec(conn, device, stage):
    # 优先小写表名，其次大写
    cur = conn.cursor()
    for table in ("et_ft_test_spec", "ET_FT_TEST_SPEC"):
        try:
            cur.execute(
                f"""
                SELECT KIT_PN, HB_PN, TB_PN
                FROM {table}
                WHERE DEVICE = %s AND STAGE = %s
                ORDER BY updated_at DESC
                LIMIT 1
                """,
                (device, stage),
            )
            rows = fetchall_dict(cur)
            if rows:
                return rows[0]
        except Exception:
            continue
    return {"KIT_PN": None, "HB_PN": None, "TB_PN": None}


def flexible_equal(a, b):
    if not a or not b:
        return False
    a = str(a).strip().upper()
    b = str(b).strip().upper()
    return a == b or (a in b) or (b in a)


def analyze_better_options(conn, big_rows, eqp_all):
    results = []
    better_same_config = 0
    better_small_change = 0
    better_continuation = 0

    for row in big_rows:
        lot_id = row.get("LOT_ID")
        device = row.get("DEVICE")
        stage = row.get("STAGE")
        assigned_handler = str(row.get("HANDLER_ID") or "")

        spec = get_required_kit_from_spec(conn, device, stage)
        req_kit = spec.get("KIT_PN")
        req_hb = spec.get("HB_PN")
        req_tb = spec.get("TB_PN")

        same_config_handlers = []
        small_change_handlers = []
        continuation_handlers = []

        for eq in eqp_all:
            status = str(eq.get("STATUS") or "").upper()
            if status not in RUN_STATUSES:
                continue

            h = str(eq.get("HANDLER_ID") or "")
            eq_dev = eq.get("EQP_DEVICE")
            eq_stg = eq.get("EQP_STAGE")
            kit = eq.get("KIT_PN")
            hb = eq.get("HB_PN")
            tb = eq.get("TB_PN")

            # 同产品续排（同设备正在跑同产品同工序）
            if (eq_dev == device) and (eq_stg == stage):
                continuation_handlers.append(h)

            # 同配置（KIT/HB/TB全匹配，宽松包含匹配）
            if req_kit and req_hb and req_tb:
                if flexible_equal(kit, req_kit) and flexible_equal(hb, req_hb) and flexible_equal(tb, req_tb):
                    same_config_handlers.append(h)
            # 小改机可能（仅KIT匹配，HB/TB可换）
            if req_kit and flexible_equal(kit, req_kit):
                small_change_handlers.append(h)

        suggestion = None
        reason = None
        if same_config_handlers:
            # 若存在非当前分配设备的同配置
            alt = [x for x in same_config_handlers if x != assigned_handler]
            if alt:
                suggestion = "同配置匹配"
                reason = f"存在同配置设备: {', '.join(sorted(set(alt)))}"
                better_same_config += 1
        if not suggestion and small_change_handlers:
            alt = [x for x in small_change_handlers if x != assigned_handler]
            if alt:
                suggestion = "小改机可能"
                reason = f"存在仅需更换HB/TB的设备: {', '.join(sorted(set(alt)))}"
                better_small_change += 1
        if not suggestion and continuation_handlers:
            alt = [x for x in continuation_handlers if x != assigned_handler]
            if alt:
                suggestion = "同产品续排"
                reason = f"有设备正在跑同产品同工序: {', '.join(sorted(set(alt)))}"
                better_continuation += 1

        results.append({
            "LOT_ID": lot_id,
            "DEVICE": device,
            "STAGE": stage,
            "ASSIGNED_HANDLER": assigned_handler,
            "SUGGESTION": suggestion or "保持大改机",
            "REASON": reason or "未发现更优候选"
        })

    return {
        "per_lot": results,
        "summary": {
            "could_same_config": better_same_config,
            "could_small_change": better_small_change,
            "could_continue": better_continuation
        }
    }


def main():
    # 延迟导入，避免模块路径问题
    from app.utils.db_connection_pool import get_connection_pool

    pool = get_connection_pool()
    output = {
        "ok": True,
        "error": None,
        "data": {}
    }
    try:
        with pool.get_connection_context('aps') as conn:
            total, big_rows, by_device, by_handler = load_big_change_data(conn)
            eqp_all, _ = get_eqp_status(conn)

            better = analyze_better_options(conn, big_rows, eqp_all)

            big_count = len(big_rows)
            proportion = (big_count / total) if total else 0.0

            output["data"] = {
                "stats": {
                    "total": total,
                    "big_change_count": big_count,
                    "big_change_ratio": round(proportion * 100, 2)
                },
                "by_device": by_device,
                "by_handler": by_handler,
                "lots": big_rows[:200],  # 输出前200条以免过大
                "better_analysis": better
            }
    except Exception as e:
        output["ok"] = False
        output["error"] = str(e)

    print(json.dumps(output, ensure_ascii=False, default=str))


if __name__ == "__main__":
    main()

