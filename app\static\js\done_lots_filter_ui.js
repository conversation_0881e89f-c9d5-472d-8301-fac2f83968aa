/**
 * 已排产表筛选UI组件
 * 
 * 功能：
 * - 提供统一的筛选界面
 * - 支持多种筛选条件类型
 * - 与DoneLotsDataManager集成
 * - 支持筛选条件的保存和恢复
 * - 提供筛选状态的可视化反馈
 * 
 * 重构原因：
 * - 统一三种模式的筛选体验
 * - 提供更直观的筛选界面
 * - 支持复杂筛选条件组合
 * - 改善用户交互体验
 */

class DoneLotsFilterUI {
    constructor(containerId, dataManager) {
        this.containerId = containerId;
        this.dataManager = dataManager;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            throw new Error(`找不到容器元素: ${containerId}`);
        }
        
        if (!this.dataManager) {
            throw new Error('需要提供 DoneLotsDataManager 实例');
        }
        
        // 筛选配置
        this.filterConfig = {
            global_search: {
                type: 'text',
                label: '全局搜索',
                placeholder: '搜索批次号、产品名称、分选机ID等',
                icon: '🔍'
            },
            lot_id: {
                type: 'text',
                label: '批次号',
                placeholder: '输入内部工单号',
                icon: '📋'
            },
            device: {
                type: 'text',
                label: '产品名称',
                placeholder: '输入产品名称',
                icon: '🔧'
            },
            handler_id: {
                type: 'text',
                label: '分选机ID',
                placeholder: '输入分选机编号',
                icon: '🏭'
            },
            stage: {
                type: 'text',
                label: '工序',
                placeholder: '输入工序名称',
                icon: '⚙️'
            },
            priority_range: {
                type: 'range',
                label: '优先级范围',
                min: 1,
                max: 10000,
                icon: '📊'
            },
            date_range: {
                type: 'daterange',
                label: '日期范围',
                icon: '📅'
            },
            status: {
                type: 'select',
                label: '状态筛选',
                options: [
                    { value: '', label: '全部状态' },
                    { value: 'WAIT', label: '等待中' },
                    { value: 'PROCESSING', label: '处理中' },
                    { value: 'COMPLETED', label: '已完成' },
                    { value: 'FAILED', label: '已失败' }
                ],
                icon: '🏷️'
            }
        };
        
        // 当前筛选值
        this.currentFilters = {};
        
        // 是否展开高级筛选
        this.advancedExpanded = false;
        
        // 初始化UI
        this.init();
        
        console.log('🔍 DoneLotsFilterUI 初始化完成');
    }
    
    /**
     * 初始化UI
     */
    init() {
        this.createFilterUI();
        this.bindEvents();
        this.loadSavedFilters();
    }
    
    /**
     * 创建筛选UI
     */
    createFilterUI() {
        this.container.innerHTML = `
            <div class="filter-panel">
                <!-- 快速搜索栏 -->
                <div class="quick-search-bar">
                    <div class="search-input-group">
                        <span class="search-icon">🔍</span>
                        <input type="text" 
                               id="quickSearch" 
                               class="quick-search-input" 
                               placeholder="快速搜索：批次号、产品名称、分选机ID等..."
                               value="${this.currentFilters.global_search || ''}">
                        <button class="search-btn" onclick="doneLotsFilterUI.applyQuickSearch()">搜索</button>
                        <button class="clear-btn" onclick="doneLotsFilterUI.clearQuickSearch()">清空</button>
                    </div>
                    <div class="quick-actions">
                        <button class="toggle-advanced-btn" onclick="doneLotsFilterUI.toggleAdvanced()">
                            <span id="advancedToggleText">高级筛选</span>
                            <span id="advancedToggleIcon">▼</span>
                        </button>
                        <button class="filter-status-btn" onclick="doneLotsFilterUI.showFilterStatus()">
                            <span id="filterStatusCount">0</span> 个筛选条件
                        </button>
                    </div>
                </div>
                
                <!-- 高级筛选面板 -->
                <div class="advanced-filters" id="advancedFilters" style="display: none;">
                    <div class="advanced-filters-grid">
                        ${this.createAdvancedFiltersHTML()}
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-primary" onclick="doneLotsFilterUI.applyFilters()">应用筛选</button>
                        <button class="btn btn-secondary" onclick="doneLotsFilterUI.resetFilters()">重置</button>
                        <button class="btn btn-info" onclick="doneLotsFilterUI.saveFilters()">保存筛选</button>
                        <button class="btn btn-warning" onclick="doneLotsFilterUI.loadSavedFilters()">加载保存</button>
                    </div>
                </div>
                
                <!-- 筛选状态显示 -->
                <div class="filter-status" id="filterStatus" style="display: none;">
                    <div class="filter-tags" id="filterTags"></div>
                </div>
            </div>
        `;
        
        this.addFilterStyles();
    }
    
    /**
     * 创建高级筛选HTML
     */
    createAdvancedFiltersHTML() {
        let html = '';
        
        Object.entries(this.filterConfig).forEach(([key, config]) => {
            if (key === 'global_search') return; // 全局搜索在快速搜索栏中
            
            html += `<div class="filter-item">`;
            html += `<label class="filter-label">
                        <span class="filter-icon">${config.icon}</span>
                        ${config.label}
                     </label>`;
            
            switch (config.type) {
                case 'text':
                    html += `<input type="text" 
                                   id="filter_${key}" 
                                   class="filter-input" 
                                   placeholder="${config.placeholder}"
                                   value="${this.currentFilters[key] || ''}">`;
                    break;
                
                case 'select':
                    html += `<select id="filter_${key}" class="filter-select">`;
                    config.options.forEach(option => {
                        const selected = this.currentFilters[key] === option.value ? 'selected' : '';
                        html += `<option value="${option.value}" ${selected}>${option.label}</option>`;
                    });
                    html += `</select>`;
                    break;
                
                case 'range':
                    const minVal = this.currentFilters[`${key}_min`] || config.min || '';
                    const maxVal = this.currentFilters[`${key}_max`] || config.max || '';
                    html += `<div class="range-inputs">
                                <input type="number" 
                                       id="filter_${key}_min" 
                                       class="filter-input range-input" 
                                       placeholder="最小值"
                                       min="${config.min || ''}"
                                       max="${config.max || ''}"
                                       value="${minVal}">
                                <span class="range-separator">-</span>
                                <input type="number" 
                                       id="filter_${key}_max" 
                                       class="filter-input range-input" 
                                       placeholder="最大值"
                                       min="${config.min || ''}"
                                       max="${config.max || ''}"
                                       value="${maxVal}">
                             </div>`;
                    break;
                
                case 'daterange':
                    const startDate = this.currentFilters[`${key}_start`] || '';
                    const endDate = this.currentFilters[`${key}_end`] || '';
                    html += `<div class="date-inputs">
                                <input type="date" 
                                       id="filter_${key}_start" 
                                       class="filter-input date-input"
                                       value="${startDate}">
                                <span class="date-separator">至</span>
                                <input type="date" 
                                       id="filter_${key}_end" 
                                       class="filter-input date-input"
                                       value="${endDate}">
                             </div>`;
                    break;
            }
            
            html += `</div>`;
        });
        
        return html;
    }
    
    /**
     * 添加样式
     */
    addFilterStyles() {
        const existingStyle = document.getElementById('done-lots-filter-styles');
        if (existingStyle) return;
        
        const style = document.createElement('style');
        style.id = 'done-lots-filter-styles';
        style.textContent = `
            .filter-panel {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .quick-search-bar {
                padding: 15px 20px;
                background: white;
                border-bottom: 1px solid #dee2e6;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 15px;
            }
            
            .search-input-group {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .search-icon {
                font-size: 18px;
                color: #6c757d;
            }
            
            .quick-search-input {
                flex: 1;
                padding: 10px 15px;
                border: 2px solid #e9ecef;
                border-radius: 25px;
                font-size: 14px;
                transition: border-color 0.3s ease;
            }
            
            .quick-search-input:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            }
            
            .search-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                cursor: pointer;
                font-weight: bold;
                transition: background-color 0.3s ease;
            }
            
            .search-btn:hover {
                background: #0056b3;
            }
            
            .clear-btn {
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 20px;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
            
            .clear-btn:hover {
                background: #545b62;
            }
            
            .quick-actions {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .toggle-advanced-btn {
                background: #17a2b8;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 15px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 5px;
                transition: background-color 0.3s ease;
            }
            
            .toggle-advanced-btn:hover {
                background: #138496;
            }
            
            .filter-status-btn {
                background: #28a745;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 15px;
                cursor: pointer;
                font-size: 12px;
                transition: background-color 0.3s ease;
            }
            
            .filter-status-btn:hover {
                background: #1e7e34;
            }
            
            .advanced-filters {
                padding: 20px;
                background: #f8f9fa;
                border-radius: 0 0 8px 8px;
            }
            
            .advanced-filters-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }
            
            .filter-item {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .filter-label {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                color: #495057;
                font-size: 14px;
            }
            
            .filter-icon {
                font-size: 16px;
            }
            
            .filter-input,
            .filter-select {
                padding: 10px 12px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                background: white;
                font-size: 14px;
                transition: border-color 0.3s ease, box-shadow 0.3s ease;
            }
            
            .filter-input:focus,
            .filter-select:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            }
            
            .range-inputs,
            .date-inputs {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .range-input,
            .date-input {
                flex: 1;
            }
            
            .range-separator,
            .date-separator {
                font-weight: bold;
                color: #6c757d;
            }
            
            .filter-actions {
                display: flex;
                justify-content: center;
                gap: 15px;
                padding-top: 15px;
                border-top: 1px solid #dee2e6;
            }
            
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-weight: bold;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-block;
            }
            
            .btn-primary {
                background: #007bff;
                color: white;
            }
            
            .btn-primary:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }
            
            .btn-secondary {
                background: #6c757d;
                color: white;
            }
            
            .btn-secondary:hover {
                background: #545b62;
                transform: translateY(-1px);
            }
            
            .btn-info {
                background: #17a2b8;
                color: white;
            }
            
            .btn-info:hover {
                background: #138496;
                transform: translateY(-1px);
            }
            
            .btn-warning {
                background: #ffc107;
                color: #212529;
            }
            
            .btn-warning:hover {
                background: #e0a800;
                transform: translateY(-1px);
            }
            
            .filter-status {
                padding: 15px 20px;
                background: #e3f2fd;
                border-top: 1px solid #dee2e6;
                border-radius: 0 0 8px 8px;
            }
            
            .filter-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .filter-tag {
                background: #007bff;
                color: white;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 5px;
                animation: slideIn 0.3s ease;
            }
            
            .filter-tag-remove {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 10px;
                transition: background-color 0.3s ease;
            }
            
            .filter-tag-remove:hover {
                background: rgba(255, 255, 255, 0.5);
            }
            
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .quick-search-bar {
                    flex-direction: column;
                    gap: 15px;
                }
                
                .search-input-group {
                    width: 100%;
                }
                
                .quick-actions {
                    width: 100%;
                    justify-content: space-between;
                }
                
                .advanced-filters-grid {
                    grid-template-columns: 1fr;
                }
                
                .filter-actions {
                    flex-wrap: wrap;
                    gap: 10px;
                }
                
                .btn {
                    flex: 1;
                    min-width: 120px;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 快速搜索回车键
        const quickSearchInput = document.getElementById('quickSearch');
        if (quickSearchInput) {
            quickSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyQuickSearch();
                }
            });
            
            // 实时搜索建议（防抖）
            let searchTimeout;
            quickSearchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (e.target.value.length > 2 || e.target.value.length === 0) {
                        this.showSearchSuggestions(e.target.value);
                    }
                }, 300);
            });
        }
        
        // 监听数据管理器事件
        this.dataManager.on('filtersChanged', (filters) => {
            this.currentFilters = filters;
            this.updateFilterStatus();
            this.updateFilterUI();
        });
        
        this.dataManager.on('dataLoaded', () => {
            this.updateFilterStatus();
        });
    }
    
    /**
     * 应用快速搜索
     */
    applyQuickSearch() {
        const searchValue = document.getElementById('quickSearch').value.trim();
        
        console.log('🔍 应用快速搜索:', searchValue);
        
        this.dataManager.applyFilters({
            global_search: searchValue
        });
    }
    
    /**
     * 清空快速搜索
     */
    clearQuickSearch() {
        document.getElementById('quickSearch').value = '';
        this.applyQuickSearch();
    }
    
    /**
     * 切换高级筛选
     */
    toggleAdvanced() {
        this.advancedExpanded = !this.advancedExpanded;
        
        const panel = document.getElementById('advancedFilters');
        const toggleText = document.getElementById('advancedToggleText');
        const toggleIcon = document.getElementById('advancedToggleIcon');
        
        if (this.advancedExpanded) {
            panel.style.display = 'block';
            toggleText.textContent = '收起筛选';
            toggleIcon.textContent = '▲';
            panel.style.animation = 'slideDown 0.3s ease';
        } else {
            panel.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                panel.style.display = 'none';
            }, 300);
            toggleText.textContent = '高级筛选';
            toggleIcon.textContent = '▼';
        }
        
        console.log('🔧 切换高级筛选:', this.advancedExpanded);
    }
    
    /**
     * 应用筛选
     */
    applyFilters() {
        const filters = this.collectFilterValues();
        
        console.log('🔍 应用筛选条件:', filters);
        
        this.dataManager.applyFilters(filters);
        
        // 可选择自动收起高级筛选
        if (this.advancedExpanded && Object.keys(filters).length > 1) {
            // this.toggleAdvanced();
        }
    }
    
    /**
     * 收集筛选值
     */
    collectFilterValues() {
        const filters = {};
        
        // 快速搜索
        const quickSearch = document.getElementById('quickSearch')?.value?.trim();
        if (quickSearch) {
            filters.global_search = quickSearch;
        }
        
        // 高级筛选
        Object.keys(this.filterConfig).forEach(key => {
            if (key === 'global_search') return;
            
            const config = this.filterConfig[key];
            
            switch (config.type) {
                case 'text':
                case 'select':
                    const element = document.getElementById(`filter_${key}`);
                    if (element && element.value.trim()) {
                        filters[key] = element.value.trim();
                    }
                    break;
                
                case 'range':
                    const minElement = document.getElementById(`filter_${key}_min`);
                    const maxElement = document.getElementById(`filter_${key}_max`);
                    if (minElement && maxElement) {
                        const minVal = minElement.value.trim();
                        const maxVal = maxElement.value.trim();
                        if (minVal && maxVal) {
                            filters[`${key}_min`] = minVal;
                            filters[`${key}_max`] = maxVal;
                        }
                    }
                    break;
                
                case 'daterange':
                    const startElement = document.getElementById(`filter_${key}_start`);
                    const endElement = document.getElementById(`filter_${key}_end`);
                    if (startElement && endElement) {
                        const startVal = startElement.value;
                        const endVal = endElement.value;
                        if (startVal && endVal) {
                            filters[`${key}_start`] = startVal;
                            filters[`${key}_end`] = endVal;
                        }
                    }
                    break;
            }
        });
        
        return filters;
    }
    
    /**
     * 重置筛选
     */
    resetFilters() {
        console.log('🧹 重置筛选条件');
        
        // 清空快速搜索
        const quickSearch = document.getElementById('quickSearch');
        if (quickSearch) {
            quickSearch.value = '';
        }
        
        // 清空高级筛选
        Object.keys(this.filterConfig).forEach(key => {
            if (key === 'global_search') return;
            
            const config = this.filterConfig[key];
            
            switch (config.type) {
                case 'text':
                    const textElement = document.getElementById(`filter_${key}`);
                    if (textElement) textElement.value = '';
                    break;
                
                case 'select':
                    const selectElement = document.getElementById(`filter_${key}`);
                    if (selectElement) selectElement.value = '';
                    break;
                
                case 'range':
                    const minElement = document.getElementById(`filter_${key}_min`);
                    const maxElement = document.getElementById(`filter_${key}_max`);
                    if (minElement) minElement.value = '';
                    if (maxElement) maxElement.value = '';
                    break;
                
                case 'daterange':
                    const startElement = document.getElementById(`filter_${key}_start`);
                    const endElement = document.getElementById(`filter_${key}_end`);
                    if (startElement) startElement.value = '';
                    if (endElement) endElement.value = '';
                    break;
            }
        });
        
        // 应用空筛选
        this.dataManager.clearFilters();
    }
    
    /**
     * 保存筛选条件
     */
    saveFilters() {
        const filters = this.collectFilterValues();
        localStorage.setItem('doneLotsFilters', JSON.stringify(filters));
        
        console.log('💾 筛选条件已保存:', filters);
        
        // 显示保存成功提示
        this.showNotification('筛选条件已保存', 'success');
    }
    
    /**
     * 加载保存的筛选条件
     */
    loadSavedFilters() {
        try {
            const savedFilters = localStorage.getItem('doneLotsFilters');
            if (savedFilters) {
                const filters = JSON.parse(savedFilters);
                this.applyFiltersToUI(filters);
                this.dataManager.setFilters(filters);
                
                console.log('📂 已加载保存的筛选条件:', filters);
                this.showNotification('已加载保存的筛选条件', 'info');
            }
        } catch (error) {
            console.error('加载保存的筛选条件失败:', error);
            this.showNotification('加载保存的筛选条件失败', 'error');
        }
    }
    
    /**
     * 将筛选条件应用到UI
     */
    applyFiltersToUI(filters) {
        // 快速搜索
        const quickSearch = document.getElementById('quickSearch');
        if (quickSearch && filters.global_search) {
            quickSearch.value = filters.global_search;
        }
        
        // 高级筛选
        Object.entries(filters).forEach(([key, value]) => {
            if (key === 'global_search') return;
            
            // 处理范围筛选
            if (key.endsWith('_min') || key.endsWith('_max') || 
                key.endsWith('_start') || key.endsWith('_end')) {
                const element = document.getElementById(`filter_${key}`);
                if (element) element.value = value;
                return;
            }
            
            // 处理普通筛选
            const element = document.getElementById(`filter_${key}`);
            if (element) element.value = value;
        });
    }
    
    /**
     * 更新筛选状态
     */
    updateFilterStatus() {
        const activeFilters = Object.entries(this.currentFilters)
            .filter(([key, value]) => value && value.toString().trim());
        
        const statusCount = document.getElementById('filterStatusCount');
        if (statusCount) {
            statusCount.textContent = activeFilters.length.toString();
        }
        
        const statusElement = document.getElementById('filterStatus');
        const tagsContainer = document.getElementById('filterTags');
        
        if (activeFilters.length > 0) {
            statusElement.style.display = 'block';
            
            const tags = activeFilters.map(([key, value]) => {
                const config = this.findFilterConfig(key);
                const label = config ? config.label : key;
                const displayValue = this.formatFilterValue(key, value);
                
                return `
                    <span class="filter-tag" data-filter-key="${key}">
                        ${config ? config.icon : '🏷️'} ${label}: ${displayValue}
                        <span class="filter-tag-remove" onclick="doneLotsFilterUI.removeFilter('${key}')">×</span>
                    </span>
                `;
            }).join('');
            
            tagsContainer.innerHTML = tags;
        } else {
            statusElement.style.display = 'none';
        }
    }
    
    /**
     * 查找筛选配置
     */
    findFilterConfig(key) {
        // 处理范围筛选的配置查找
        if (key.endsWith('_min') || key.endsWith('_max')) {
            const baseKey = key.replace(/_min$|_max$/, '');
            return this.filterConfig[baseKey];
        }
        
        if (key.endsWith('_start') || key.endsWith('_end')) {
            const baseKey = key.replace(/_start$|_end$/, '');
            return this.filterConfig[baseKey];
        }
        
        return this.filterConfig[key];
    }
    
    /**
     * 格式化筛选值显示
     */
    formatFilterValue(key, value) {
        if (typeof value === 'string' && value.length > 20) {
            return value.substring(0, 20) + '...';
        }
        return value.toString();
    }
    
    /**
     * 移除单个筛选条件
     */
    removeFilter(key) {
        const newFilters = { ...this.currentFilters };
        delete newFilters[key];
        
        // 同时清除相关的范围筛选
        if (key.includes('priority_')) {
            delete newFilters.priority_min;
            delete newFilters.priority_max;
        }
        if (key.includes('date_')) {
            delete newFilters.date_start;
            delete newFilters.date_end;
        }
        
        console.log('🗑️ 移除筛选条件:', key);
        
        this.dataManager.setFilters(newFilters);
        this.applyFiltersToUI(newFilters);
        this.dataManager.loadData({ forceRefresh: true });
    }
    
    /**
     * 更新筛选UI
     */
    updateFilterUI() {
        this.applyFiltersToUI(this.currentFilters);
    }
    
    /**
     * 显示筛选状态
     */
    showFilterStatus() {
        const activeFilters = Object.entries(this.currentFilters)
            .filter(([key, value]) => value && value.toString().trim());
        
        let message = '当前筛选条件：\\n';
        if (activeFilters.length === 0) {
            message += '无筛选条件';
        } else {
            activeFilters.forEach(([key, value]) => {
                const config = this.findFilterConfig(key);
                const label = config ? config.label : key;
                message += `${label}: ${value}\\n`;
            });
        }
        
        alert(message);
    }
    
    /**
     * 显示搜索建议
     */
    showSearchSuggestions(query) {
        // TODO: 实现搜索建议功能
        // 可以基于历史搜索、常用关键词等提供建议
        console.log('🔍 搜索建议:', query);
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 简单的通知实现，可以替换为更复杂的通知组件
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        console.log('🗑️ DoneLotsFilterUI 已销毁');
    }
}

// 导出到全局
window.DoneLotsFilterUI = DoneLotsFilterUI;

console.log('📦 DoneLotsFilterUI 模块已加载');