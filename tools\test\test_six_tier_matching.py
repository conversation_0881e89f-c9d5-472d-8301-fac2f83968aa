#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六类严格匹配规则测试脚本
验证重构后的上机匹配逻辑是否正常工作
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.services.real_scheduling_service import RealSchedulingService, normalize_pn, pn_equal

def test_pn_normalization():
    """测试PN正规化功能"""
    print("=== 测试PN正规化功能 ===")
    
    test_cases = [
        ("20210009_C_04", "20210009_C"),
        ("TB_SU8192_16", "TB_SU8192"),
        ("ABC-SN001", "ABC"),
        ("XYZ_123", "XYZ"),
        ("20250163_B-05", "20250163_B"),
        ("TB_CC4812A_V41-19", "TB_CC4812A_V41"),
        ("", ""),
        ("SIMPLE", "SIMPLE"),
    ]
    
    for original, expected in test_cases:
        normalized = normalize_pn(original)
        status = "✓" if normalized == expected else "✗"
        print(f"   {status} {original} -> {normalized} (期望: {expected})")
    
    # 测试pn_equal函数
    print("\n=== 测试PN相等比较 ===")
    equal_cases = [
        ("20210009_C_04", "20210009_C", True),
        ("TB_SU8192_16", "TB_SU8192_26", True),
        ("ABC-SN001", "ABC-SN002", True),
        ("XYZ_123", "XYZ_456", True),
        ("DIFFERENT", "OTHER", False),
        ("", "", True),
    ]
    
    for pn1, pn2, expected in equal_cases:
        result = pn_equal(pn1, pn2)
        status = "✓" if result == expected else "✗"
        print(f"   {status} pn_equal('{pn1}', '{pn2}') = {result} (期望: {expected})")

def test_six_tier_matching():
    """测试六类匹配逻辑"""
    print("\n=== 测试六类匹配逻辑 ===")
    
    try:
        # 初始化服务
        service = RealSchedulingService()
        
        # 构造测试数据
        lot_requirements = {
            'DEVICE': 'TEST_DEVICE',
            'STAGE': 'FT1'
        }
        
        # 模拟预加载数据
        preloaded_data = {
            'test_specs': [
                {
                    'DEVICE': 'TEST_DEVICE',
                    'STAGE': 'FT1',
                    'TESTER': 'TESTER_A',
                    'HB_PN': '20210009_C_04',
                    'TB_PN': 'TB_SU8192_16'
                }
            ],
            'recipe_files': [
                {
                    'DEVICE': 'TEST_DEVICE',
                    'STAGE': 'FT1',
                    'HANDLER_CONFIG': 'CONFIG_A',
                    'KIT_PN': 'KIT_001'
                }
            ]
        }
        
        # 测试用例：不同匹配类型的设备
        test_equipments = [
            # 1. 同配置匹配
            {
                'HANDLER_ID': 'EQP_001',
                'DEVICE': 'TEST_DEVICE',
                'STAGE': 'FT1',
                'HANDLER_CONFIG': 'CONFIG_A',
                'KIT_PN': 'KIT_001',
                'TESTER': 'TESTER_A',
                'HB_PN': '20210009_C_05',  # 基础PN相同
                'TB_PN': 'TB_SU8192_26',   # 基础PN相同
                'STATUS': 'IDLE'
            },
            # 2. 小改机匹配
            {
                'HANDLER_ID': 'EQP_002',
                'DEVICE': 'TEST_DEVICE',
                'STAGE': 'FT1',
                'HANDLER_CONFIG': 'CONFIG_A',
                'KIT_PN': 'KIT_001',
                'TESTER': 'TESTER_A',
                'HB_PN': '20220001_A',     # 基础PN不同
                'TB_PN': 'TB_SU8192_26',   # 基础PN相同
                'STATUS': 'IDLE'
            },
            # 3. 换测试机小改机匹配
            {
                'HANDLER_ID': 'EQP_003',
                'DEVICE': 'TEST_DEVICE',
                'STAGE': 'FT1',
                'HANDLER_CONFIG': 'CONFIG_A',
                'KIT_PN': 'KIT_001',
                'TESTER': 'TESTER_B',      # TESTER不同
                'HB_PN': '20210009_C_05',
                'TB_PN': 'TB_SU8192_26',
                'STATUS': 'IDLE'
            },
            # 4. 大改机匹配
            {
                'HANDLER_ID': 'EQP_004',
                'DEVICE': 'TEST_DEVICE',
                'STAGE': 'FT1',
                'HANDLER_CONFIG': 'CONFIG_A',
                'KIT_PN': 'KIT_002',       # KIT不同
                'TESTER': 'TESTER_B',
                'HB_PN': '20220001_A',
                'TB_PN': 'TB_OTHER',
                'STATUS': 'IDLE'
            },
            # 5. 无法上机
            {
                'HANDLER_ID': 'EQP_005',
                'DEVICE': 'TEST_DEVICE',
                'STAGE': 'FT1',
                'HANDLER_CONFIG': 'CONFIG_B',  # HANDLER_CONFIG不同
                'KIT_PN': 'KIT_002',
                'TESTER': 'TESTER_B',
                'HB_PN': '20220001_A',
                'TB_PN': 'TB_OTHER',
                'STATUS': 'IDLE'
            }
        ]
        
        expected_results = [
            (100, "同配置匹配", 0),
            (80, "小改机匹配", 45),
            (75, "换测试机小改机匹配", 55),
            (60, "大改机匹配", 120),
            (0, "无法上机", 9999)
        ]
        
        print("   测试设备匹配评分:")
        for i, equipment in enumerate(test_equipments):
            try:
                score, match_type, changeover_time = service.calculate_equipment_match_score_optimized(
                    lot_requirements, equipment, preloaded_data
                )
                
                expected_score, expected_type, expected_time = expected_results[i]
                
                score_ok = score == expected_score
                type_ok = match_type == expected_type
                time_ok = changeover_time == expected_time
                
                status = "✓" if (score_ok and type_ok and time_ok) else "✗"
                
                print(f"   {status} {equipment['HANDLER_ID']}: {match_type}({score}分, {changeover_time}分钟)")
                if not (score_ok and type_ok and time_ok):
                    print(f"      期望: {expected_type}({expected_score}分, {expected_time}分钟)")
                    
            except Exception as e:
                print(f"   ✗ {equipment['HANDLER_ID']}: 测试异常 - {e}")
        
        print("\n✅ 六类匹配逻辑测试完成")
        
    except Exception as e:
        print(f"✗ 六类匹配测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_failure_details():
    """测试失败详情生成"""
    print("\n=== 测试失败详情生成 ===")
    
    try:
        service = RealSchedulingService()
        
        lot = {'DEVICE': 'TEST_DEVICE', 'STAGE': 'FT1', 'LOT_ID': 'TEST_LOT'}
        lot_requirements = {'DEVICE': 'TEST_DEVICE', 'STAGE': 'FT1'}
        
        # 模拟无匹配设备的情况
        preloaded_data = {
            'test_specs': [],
            'recipe_files': [],
            'equipment_status': [
                {
                    'DEVICE': 'OTHER_DEVICE',  # 不匹配
                    'STAGE': 'FT1',
                    'HANDLER_CONFIG': 'CONFIG_A',
                    'KIT_PN': 'KIT_001'
                }
            ]
        }
        
        failure_details = service._generate_failure_details(lot, lot_requirements, preloaded_data)
        details_obj = json.loads(failure_details)
        
        print(f"   失败详情JSON: {json.dumps(details_obj, indent=2, ensure_ascii=False)}")
        
        # 验证关键字段
        if details_obj.get('device_stage_mismatch') == True:
            print("   ✓ 正确识别DEVICE+STAGE不匹配")
        else:
            print("   ✗ 未正确识别DEVICE+STAGE不匹配")
            
        print("\n✅ 失败详情生成测试完成")
        
    except Exception as e:
        print(f"✗ 失败详情测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 六类严格匹配规则测试开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_pn_normalization()
    test_six_tier_matching()
    test_failure_details()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")
