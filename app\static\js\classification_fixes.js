
// 修复前端的分类管理和进度显示问题

/**
 * 改进的扫描Lot Type函数 - 带错误处理和重试
 */
function scanLotTypesImproved() {
    const scanBtn = document.getElementById('scanLotTypesBtn');
    const originalBtnText = scanBtn.innerHTML;
    
    // 设置按钮为加载状态
    scanBtn.disabled = true;
    scanBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 扫描中...';
    
    // 显示进度
    const progressContainer = document.getElementById('scanProgress');
    progressContainer.style.display = 'block';
    updateScanProgress(0, '正在扫描Excel文件...');
    
    // 获取源目录配置
    const sourceDir = document.getElementById('sourceDir')?.value || 'downloads';
    const searchSubdirs = document.getElementById('searchSubdirs')?.checked !== false;
    
    // 发送扫描请求
    fetch('/api/orders/scan-lot-types', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'  // 确保是AJAX请求
        },
        body: JSON.stringify({
            source_dir: sourceDir,
            search_subdirs: searchSubdirs
        }),
        credentials: 'same-origin'  // 包含认证cookie
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // 恢复按钮状态
        scanBtn.disabled = false;
        scanBtn.innerHTML = originalBtnText;
        
        if (data.success) {
            updateScanProgress(100, '扫描完成！');
            displayLotTypes(data.data);
            showAlert('success', `扫描完成，找到 ${Object.keys(data.data).length} 种不同的Lot Type`);
        } else {
            updateScanProgress(0, '扫描失败');
            showAlert('error', data.message || '扫描Lot Type失败');
        }
    })
    .catch(error => {
        // 恢复按钮状态
        scanBtn.disabled = false;
        scanBtn.innerHTML = originalBtnText;
        
        updateScanProgress(0, '扫描出错');
        console.error('扫描Lot Type失败:', error);
        
        // 提供重试选项
        showAlert('error', 
            `扫描请求失败: ${error.message}<br>` + 
            '<button class="btn btn-sm btn-warning mt-2" onclick="scanLotTypesImproved()">重试</button>'
        );
    })
    .finally(() => {
        // 隐藏进度条
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}

/**
 * 保存分类规则到服务器
 */
function saveClassificationRulesImproved() {
    const saveBtn = document.getElementById('saveClassificationBtn');
    const originalText = saveBtn?.innerHTML || '保存规则';
    
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 保存中...';
    }
    
    // 发送保存请求
    fetch('/api/orders/classification-rules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            rules: classificationRules
        }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 同时保存到本地存储作为备份
            localStorage.setItem('lotTypeClassificationRules', JSON.stringify(classificationRules));
            
            // 更新状态显示
            const ruleCount = Object.keys(classificationRules).length;
            const statusInput = document.getElementById('classificationStatus');
            if (statusInput) {
                statusInput.value = `已配置 ${ruleCount} 种分类规则 (已同步)`;
            }
            
            showAlert('success', data.message || '分类规则已保存');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('classificationManagerModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            showAlert('error', data.message || '保存分类规则失败');
        }
    })
    .catch(error => {
        console.error('保存分类规则失败:', error);
        showAlert('error', '保存分类规则失败: ' + error.message);
    })
    .finally(() => {
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    });
}

/**
 * 从服务器加载分类规则
 */
function loadClassificationRulesImproved() {
    return fetch('/api/orders/classification-rules', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            classificationRules = data.data || {};
            
            // 更新状态显示
            const ruleCount = Object.keys(classificationRules).length;
            const statusInput = document.getElementById('classificationStatus');
            if (statusInput) {
                statusInput.value = ruleCount > 0 
                    ? `已配置 ${ruleCount} 种分类规则 (已同步)`
                    : '未配置分类规则';
            }
            
            console.log(`加载了 ${ruleCount} 条分类规则`);
            return classificationRules;
        } else {
            // 如果服务器没有，尝试从本地存储加载
            const savedRules = localStorage.getItem('lotTypeClassificationRules');
            if (savedRules) {
                classificationRules = JSON.parse(savedRules);
                console.log('从本地存储加载分类规则');
            }
            return classificationRules;
        }
    })
    .catch(error => {
        console.warn('从服务器加载分类规则失败，尝试本地存储:', error);
        
        // 回退到本地存储
        const savedRules = localStorage.getItem('lotTypeClassificationRules');
        if (savedRules) {
            classificationRules = JSON.parse(savedRules);
        }
        return classificationRules;
    });
}

// 在页面加载时自动加载分类规则
document.addEventListener('DOMContentLoaded', function() {
    if (typeof classificationRules !== 'undefined') {
        loadClassificationRulesImproved();
    }
});
