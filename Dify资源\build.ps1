# 这个脚本使用 PyInstaller 将 APS 智能排产平台打包成一个可分发的文件夹。

# --- PyInstaller 参数详解 ---
# run.py: 我们应用的启动入口。
# --name APS-Platform: 指定生成的可执行文件和文件夹的名称。
# --icon: 指定应用程序的图标文件。
# --clean: 在每次打包前，自动清理上一次的构建文件，确保环境干净。
# --noconfirm: 当需要覆盖旧文件时，自动确认，无需手动干预。
# --add-data <源路径;目标路径>: 这是打包Web应用最关键的一步。它告诉PyInstaller
#   将代码之外的必要文件（如HTML模板、CSS/JS静态资源、配置文件、数据库实例等）
#   一同打包进去。分号前的是源文件路径，分号后的是打包后在程序包内的相对路径。
# --hidden-import <模块名>: 有些库是"间接"或动态引用的，PyInstaller可能无法自动
#   探测到它们。这里我们明确告诉它需要包含这些"隐藏"的依赖项，以防止程序在
#   运行时因找不到模块而出错。

Write-Host "--- 开始使用 PyInstaller 打包 APS 平台 ---" -ForegroundColor Cyan

# 确保所有依赖都已安装
Write-Host "正在检查并安装项目依赖..."
pip install -r requirements.txt

# 执行 PyInstaller 命令
Write-Host "正在执行 PyInstaller 打包命令，这个过程可能需要几分钟..."
pyinstaller run.py `
    --name APS-Platform `
    --icon "icon/icon.ico" `
    --clean `
    --noconfirm `
    --hidden-import "pymysql" `
    --hidden-import "pandas._libs.tslibs.base" `
    --hidden-import "jinja2.ext" `
    --hidden-import "apscheduler.triggers.cron" `
    --hidden-import "app.services.data_source_manager" `
    --hidden-import "app.api_v2.production.done_lots_api" `
    --hidden-import "app.api_v2.wip_lot_api" `
    --hidden-import "app.api_v2.resources.routes" `
    --hidden-import "app.api_v2.system.dashboard" `
    --hidden-import "app.utils.simple_logging" `
    --hidden-import "app.utils.socketio_handlers" `
    --hidden-import "flask_socketio" `
    --hidden-import "sqlalchemy.dialects.mysql" `
    --hidden-import "scheduling_failure_fix" `
    --hidden-import "tools.monitoring.scheduling_failure_fix"

# 检查打包是否成功
if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✅ PyInstaller build completed. Now copying necessary application directories..." -ForegroundColor Green
    $destination = "dist/APS-Platform"
    
    # 手动复制必要的文件夹，这是比 --add-data 更可靠的方法
    Write-Host "Copying 'app' directory..."
    Copy-Item -Path "app" -Destination "$destination\app" -Recurse -Force
    
    Write-Host "Copying 'instance' directory..."
    Copy-Item -Path "instance" -Destination "$destination\instance" -Recurse -Force

    Write-Host "Copying 'config' directory..."
    Copy-Item -Path "config" -Destination "$destination\config" -Recurse -Force

    Write-Host ""
    Write-Host "🎉 恭喜！打包成功！" -ForegroundColor Green
    Write-Host "你的应用程序已经准备就绪，位于 'dist/APS-Platform' 文件夹中。"
    Write-Host "你可以将整个 'APS-Platform' 文件夹分发给用户。"
} else {
    Write-Host ""
    Write-Host "❌ 打包失败。" -ForegroundColor Red
    Write-Host "请检查上面的输出来定位问题。通常可能是缺少某个依赖或路径配置错误。"
} 