"""
高精度计算模块 - 确保不同硬件配置下计算结果一致
"""

from decimal import Decimal, getcontext, ROUND_HALF_UP
import logging

logger = logging.getLogger(__name__)

# 设置全局精度：50位小数，足以处理排产计算
getcontext().prec = 50
getcontext().rounding = ROUND_HALF_UP

class PrecisionCalculator:
    """高精度计算器 - 硬件无关"""
    
    @staticmethod
    def multiply_with_precision(value: float, multiplier: float) -> Decimal:
        """高精度乘法"""
        return Decimal(str(value)) * Decimal(str(multiplier))
    
    @staticmethod  
    def divide_with_precision(value: float, divisor: float) -> Decimal:
        """高精度除法"""
        if divisor == 0:
            return Decimal('0')
        return Decimal(str(value)) / Decimal(str(divisor))
    
    @staticmethod
    def weight_calculation(score: float, weight: float) -> Decimal:
        """权重计算 - 排产系统专用"""
        # score * weight / 100.0
        return (Decimal(str(score)) * Decimal(str(weight))) / Decimal('100.0')
    
    @staticmethod
    def comprehensive_score_calculation(scores: list, weights: list) -> Decimal:
        """综合评分计算 - 确保硬件无关"""
        if len(scores) != len(weights):
            raise ValueError("评分和权重数量不匹配")
        
        total_score = Decimal('0')
        for score, weight in zip(scores, weights):
            total_score += PrecisionCalculator.weight_calculation(score, weight)
        
        return total_score
    
    @staticmethod
    def to_comparable_float(decimal_value: Decimal, precision: int = 6) -> float:
        """转换为可比较的浮点数"""
        # 四舍五入到指定精度，确保比较一致性
        rounded = decimal_value.quantize(Decimal('0.' + '0' * precision))
        return float(rounded)

# 便捷函数
def precise_multiply(value: float, multiplier: float) -> float:
    """精确乘法（返回float便于现有代码兼容）"""
    result = PrecisionCalculator.multiply_with_precision(value, multiplier)
    return PrecisionCalculator.to_comparable_float(result)

def precise_divide(value: float, divisor: float) -> float:
    """精确除法（返回float便于现有代码兼容）"""
    result = PrecisionCalculator.divide_with_precision(value, divisor)
    return PrecisionCalculator.to_comparable_float(result)

def precise_weight_calc(score: float, weight: float) -> float:
    """精确权重计算"""
    result = PrecisionCalculator.weight_calculation(score, weight)
    return PrecisionCalculator.to_comparable_float(result)
