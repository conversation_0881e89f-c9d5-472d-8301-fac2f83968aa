﻿/**
 * APS Progress Bar Module with Socket.IO Support
 * Handles real-time progress updates via WebSocket
 */
class APSProgressBar {
    constructor() {
        this.socket = null;
        this.currentUserId = null;
        this.isConnected = false;
        this.progressOverlay = null;
        this.progressBar = null;
        this.progressText = null;
        this.progressPercent = null;
        
        this.init();
    }
    
    init() {
        // Initialize DOM elements
        this.initDOMElements();
        
        // Initialize Socket.IO connection
        this.initSocketIO();
        
        // Get current user ID
        this.getCurrentUserId();
    }
    
    initDOMElements() {
        this.progressOverlay = document.getElementById('progressOverlay') || this.createProgressOverlay();
        this.progressBar = this.progressOverlay.querySelector('.progress-bar');
        this.progressText = this.progressOverlay.querySelector('.progress-text');
        this.progressPercent = this.progressOverlay.querySelector('.progress-percent');
    }
    
    createProgressOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'progressOverlay';
        overlay.className = 'progress-overlay';
        overlay.style.cssText = 
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0, 0, 0, 0.5); display: none;
            justify-content: center; align-items: center; z-index: 9999;
        ;
        
        overlay.innerHTML = 
            <div class="progress-container" style="background: white; padding: 30px; border-radius: 8px; width: 400px; text-align: center; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                <div class="progress-text" style="margin-bottom: 15px; font-weight: bold; font-size: 1.1rem;">姝ｅ湪鍒濆鍖?..</div>
                <div class="progress" style="height: 25px; margin: 20px 0; border-radius: 5px; overflow: hidden; position: relative; background-color: #f0f0f0;">
                    <div class="progress-bar" style="background-color: #b72424; height: 100%; position: relative; width: 0%; transition: width 0.3s ease;">
                        <div class="progress-percent" style="position: absolute; top: 0; left: 0; right: 0; line-height: 25px; color: white; text-align: center; font-weight: bold; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);">0%</div>
                    </div>
                </div>
                <div class="status-message" style="font-size: 0.9rem; color: #666;">璇风瓑寰?..</div>
            </div>
        ;
        
        document.body.appendChild(overlay);
        return overlay;
    }
    
    initSocketIO() {
        if (typeof io === 'undefined') {
            console.warn('Socket.IO not available, falling back to HTTP polling');
            this.setupHttpPolling();
            return;
        }
        
        try {
            this.socket = io();
            
            this.socket.on('connect', () => {
                console.log('鉁?Socket.IO connected');
                this.isConnected = true;
            });
            
            this.socket.on('disconnect', () => {
                console.log('鉂?Socket.IO disconnected');
                this.isConnected = false;
            });
            
            // Listen for scheduling progress updates
            this.socket.on('scheduling_progress', (data) => {
                console.log('馃摗 Progress update received:', data);
                if (data.user_id === this.currentUserId || !data.user_id) {
                    this.updateProgress(data.percent, data.message);
                }
            });
            
        } catch (error) {
            console.error('Socket.IO initialization failed:', error);
            this.setupHttpPolling();
        }
    }
    
    getCurrentUserId() {
        // Try to get user ID from various sources
        if (window.currentUserId) {
            this.currentUserId = window.currentUserId;
        } else if (window.user && window.user.id) {
            this.currentUserId = window.user.id;
        } else {
            // Extract from DOM or make an API call
            const userElement = document.querySelector('[data-user-id]');
            if (userElement) {
                this.currentUserId = userElement.dataset.userId;
            }
        }
        console.log('Current user ID:', this.currentUserId);
    }
    
    setupHttpPolling() {
        console.log('Setting up HTTP polling for progress updates');
        // Fallback mechanism when Socket.IO is not available
        this.pollingInterval = null;
    }
    
    showProgress(message = '姝ｅ湪澶勭悊...') {
        if (this.progressOverlay) {
            this.progressOverlay.style.display = 'flex';
            this.updateProgress(0, message);
        }
    }
    
    hideProgress() {
        if (this.progressOverlay) {
            this.progressOverlay.style.display = 'none';
        }
    }
    
    updateProgress(percent, message) {
        if (this.progressBar) {
            this.progressBar.style.width = percent + '%';
        }
        
        if (this.progressText) {
            this.progressText.textContent = message || '澶勭悊涓?..';
        }
        
        if (this.progressPercent) {
            this.progressPercent.textContent = Math.round(percent) + '%';
        }
        
        // Auto-hide when complete
        if (percent >= 100) {
            setTimeout(() => {
                this.hideProgress();
            }, 2000);
        }
    }
    
    startScheduling() {
        this.showProgress('姝ｅ湪鍚姩鎺掍骇绠楁硶...');
        
        // If Socket.IO is not available, start HTTP polling
        if (!this.isConnected && this.currentUserId) {
            this.startHttpPolling();
        }
    }
    
    startHttpPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
        
        this.pollingInterval = setInterval(() => {
            if (this.currentUserId) {
                fetch('/api/v2/production/get-scheduling-progress/' + this.currentUserId)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.progress) {
                            this.updateProgress(data.progress.percent, data.progress.message);
                            
                            // Stop polling when complete
                            if (data.progress.percent >= 100) {
                                clearInterval(this.pollingInterval);
                                this.pollingInterval = null;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Progress polling failed:', error);
                    });
            }
        }, 1000); // Poll every second
    }
}

// Initialize progress bar when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.apsProgressBar = new APSProgressBar();
});

// Export for manual initialization
window.APSProgressBar = APSProgressBar;
