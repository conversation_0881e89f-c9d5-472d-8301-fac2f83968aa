#!/usr/bin/env python3
"""
表结构验证工具 - 确保数据库表结构与业务逻辑一致
专门针对排产系统的关键业务表进行验证

Author: AI Assistant
Date: 2025-01-16
Version: 1.0
"""

import os
import sys
import pymysql
from app.utils.db_connection_pool import get_db_connection_context, get_db_connection
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('table_validation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 从主工具导入表结构定义
try:
    from .import_excel_to_mysql import BUSINESS_TABLE_SCHEMAS, get_mysql_connection
except ImportError:
    logger.error("无法导入主工具配置，请确保import_excel_to_mysql.py文件存在")
    sys.exit(1)

class TableStructureValidator:
    """表结构验证器"""
    
    def __init__(self):
        self.validation_results = {
            'timestamp': datetime.now().isoformat(),
            'database': 'aps',
            'total_tables': 0,
            'validated_tables': 0,
            'missing_tables': [],
            'structure_mismatches': [],
            'warnings': [],
            'recommendations': []
        }
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = get_db_connection()  # 原参数: 'aps'
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def validate_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"检查表存在性失败: {table_name} - {e}")
            return False
    
    def get_table_structure(self, table_name):
        """获取实际表结构"""
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                
                structure = {}
                for col_info in columns_info:
                    field_name = col_info[0]
                    data_type = col_info[1]
                    is_nullable = col_info[2] == 'YES'
                    key_type = col_info[3]
                    default_value = col_info[4]
                    
                    structure[field_name] = {
                        'type': data_type,
                        'nullable': is_nullable,
                        'key': key_type,
                        'default': default_value
                    }
                
                return structure
        except Exception as e:
            logger.error(f"获取表结构失败: {table_name} - {e}")
            return None
    
    def validate_required_fields(self, table_name, actual_structure, expected_schema):
        """验证必填字段"""
        missing_fields = []
        required_fields = expected_schema.get('required_fields', [])
        
        for req_field in required_fields:
            if req_field not in actual_structure:
                missing_fields.append(req_field)
        
        return missing_fields
    
    def validate_field_types(self, table_name, actual_structure, expected_schema):
        """验证字段类型"""
        type_mismatches = []
        field_types = expected_schema.get('field_types', {})
        
        for field_name, expected_type in field_types.items():
            if field_name in actual_structure:
                actual_type = actual_structure[field_name]['type'].upper()
                expected_type_clean = expected_type.upper()
                
                # 简化类型比较（只检查主要类型）
                if 'VARCHAR' in expected_type_clean:
                    if 'VARCHAR' not in actual_type and 'CHAR' not in actual_type:
                        type_mismatches.append({
                            'field': field_name,
                            'expected': expected_type,
                            'actual': actual_structure[field_name]['type']
                        })
                elif 'INT' in expected_type_clean:
                    if 'INT' not in actual_type:
                        type_mismatches.append({
                            'field': field_name,
                            'expected': expected_type,
                            'actual': actual_structure[field_name]['type']
                        })
                elif 'DECIMAL' in expected_type_clean:
                    if 'DECIMAL' not in actual_type and 'FLOAT' not in actual_type:
                        type_mismatches.append({
                            'field': field_name,
                            'expected': expected_type,
                            'actual': actual_structure[field_name]['type']
                        })
                elif 'DATETIME' in expected_type_clean or 'TIMESTAMP' in expected_type_clean:
                    if 'DATETIME' not in actual_type and 'TIMESTAMP' not in actual_type:
                        type_mismatches.append({
                            'field': field_name,
                            'expected': expected_type,
                            'actual': actual_structure[field_name]['type']
                        })
                elif 'DATE' in expected_type_clean:
                    if 'DATE' not in actual_type:
                        type_mismatches.append({
                            'field': field_name,
                            'expected': expected_type,
                            'actual': actual_structure[field_name]['type']
                        })
        
        return type_mismatches
    
    def validate_single_table(self, table_name, expected_schema):
        """验证单个表"""
        logger.info(f"🔍 验证表: {table_name}")
        
        # 检查表是否存在
        if not self.validate_table_exists(table_name):
            self.validation_results['missing_tables'].append({
                'table': table_name,
                'description': expected_schema.get('description', ''),
                'protect_mode': expected_schema.get('protect_mode', False)
            })
            logger.warning(f"⚠️  表 {table_name} 不存在")
            return
        
        # 获取实际表结构
        actual_structure = self.get_table_structure(table_name)
        if actual_structure is None:
            logger.error(f"❌ 无法获取表 {table_name} 的结构")
            return
        
        # 验证必填字段
        missing_fields = self.validate_required_fields(table_name, actual_structure, expected_schema)
        
        # 验证字段类型
        type_mismatches = self.validate_field_types(table_name, actual_structure, expected_schema)
        
        # 记录验证结果
        if missing_fields or type_mismatches:
            mismatch = {
                'table': table_name,
                'description': expected_schema.get('description', ''),
                'missing_fields': missing_fields,
                'type_mismatches': type_mismatches,
                'protect_mode': expected_schema.get('protect_mode', False)
            }
            self.validation_results['structure_mismatches'].append(mismatch)
            
            if missing_fields:
                logger.warning(f"⚠️  表 {table_name} 缺少字段: {missing_fields}")
            if type_mismatches:
                logger.warning(f"⚠️  表 {table_name} 字段类型不匹配: {len(type_mismatches)} 个")
        else:
            logger.info(f"✅ 表 {table_name} 结构验证通过")
        
        self.validation_results['validated_tables'] += 1
    
    def validate_all_tables(self):
        """验证所有业务表"""
        logger.info("🚀 开始验证所有业务表结构")
        
        self.validation_results['total_tables'] = len(BUSINESS_TABLE_SCHEMAS)
        
        for table_name, schema in BUSINESS_TABLE_SCHEMAS.items():
            self.validate_single_table(table_name, schema)
    
    def generate_recommendations(self):
        """生成修复建议"""
        recommendations = []
        
        # 缺失表的建议
        if self.validation_results['missing_tables']:
            recommendations.append({
                'type': 'missing_tables',
                'priority': 'high',
                'message': '存在缺失的关键业务表，建议运行 init_db.py 创建表结构',
                'action': 'python init_db.py',
                'affected_tables': [t['table'] for t in self.validation_results['missing_tables']]
            })
        
        # 结构不匹配的建议
        if self.validation_results['structure_mismatches']:
            critical_mismatches = [m for m in self.validation_results['structure_mismatches'] if m.get('protect_mode')]
            if critical_mismatches:
                recommendations.append({
                    'type': 'critical_structure_mismatch',
                    'priority': 'critical',
                    'message': '保护模式表存在结构不匹配，可能影响排产逻辑',
                    'action': '请检查表结构并手动修复，或重新运行 init_db.py',
                    'affected_tables': [m['table'] for m in critical_mismatches]
                })
        
        # 数据导入安全建议
        if self.validation_results['missing_tables'] or self.validation_results['structure_mismatches']:
            recommendations.append({
                'type': 'import_safety',
                'priority': 'medium',
                'message': '建议使用字段卡控版本的导入工具确保数据安全',
                'action': '使用 import_excel_to_mysql.py 进行安全导入',
                'note': '该工具会严格验证字段映射，保护表结构'
            })
        
        self.validation_results['recommendations'] = recommendations
    
    def save_validation_report(self, filename=None):
        """保存验证报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'table_validation_report_{timestamp}.json'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.validation_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 验证报告已保存: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存验证报告失败: {e}")
            return None
    
    def print_summary(self):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("🔍 表结构验证报告")
        print("="*60)
        
        print(f"📊 验证统计:")
        print(f"   总表数: {self.validation_results['total_tables']}")
        print(f"   已验证: {self.validation_results['validated_tables']}")
        print(f"   缺失表: {len(self.validation_results['missing_tables'])}")
        print(f"   结构不匹配: {len(self.validation_results['structure_mismatches'])}")
        
        # 缺失表详情
        if self.validation_results['missing_tables']:
            print(f"\n❌ 缺失的表:")
            for missing in self.validation_results['missing_tables']:
                protection = "🔒 保护模式" if missing.get('protect_mode') else ""
                print(f"   • {missing['table']} - {missing.get('description', '')} {protection}")
        
        # 结构不匹配详情
        if self.validation_results['structure_mismatches']:
            print(f"\n⚠️  结构不匹配的表:")
            for mismatch in self.validation_results['structure_mismatches']:
                protection = "🔒 保护模式" if mismatch.get('protect_mode') else ""
                print(f"   • {mismatch['table']} {protection}")
                if mismatch['missing_fields']:
                    print(f"     缺少字段: {', '.join(mismatch['missing_fields'])}")
                if mismatch['type_mismatches']:
                    print(f"     类型不匹配: {len(mismatch['type_mismatches'])} 个字段")
        
        # 修复建议
        if self.validation_results['recommendations']:
            print(f"\n💡 修复建议:")
            for i, rec in enumerate(self.validation_results['recommendations'], 1):
                priority_icon = {"critical": "🚨", "high": "⚠️", "medium": "💡"}.get(rec['priority'], "ℹ️")
                print(f"   {i}. {priority_icon} {rec['message']}")
                print(f"      执行: {rec['action']}")
                if 'affected_tables' in rec:
                    print(f"      影响表: {', '.join(rec['affected_tables'])}")
        
        # 安全状态评估
        if not self.validation_results['missing_tables'] and not self.validation_results['structure_mismatches']:
            print(f"\n✅ 表结构验证通过！可以安全进行数据导入")
        else:
            critical_issues = len([r for r in self.validation_results['recommendations'] if r['priority'] == 'critical'])
            if critical_issues > 0:
                print(f"\n🚨 发现 {critical_issues} 个严重问题，建议先修复后再进行数据导入")
            else:
                print(f"\n⚠️  发现结构问题，建议使用字段卡控模式进行安全导入")
        
        print("="*60)
    
    def run_validation(self):
        """执行完整验证流程"""
        if not self.connect_database():
            return False
        
        try:
            self.validate_all_tables()
            self.generate_recommendations()
            
            # 保存报告
            report_file = self.save_validation_report()
            
            # 打印摘要
            self.print_summary()
            
            return True
        finally:
            if hasattr(self, 'conn'):
                self.conn.close()
def main():
    """主函数"""
    print("🔍 表结构验证工具 - 排产系统专用")
    print("确保数据库表结构与业务逻辑一致")
    print("-" * 50)
    
    validator = TableStructureValidator()
    success = validator.run_validation()
    
    if success:
        print("\n✅ 验证完成！请查看上述报告和建议。")
    else:
        print("\n❌ 验证失败！请检查数据库连接。")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main()) 