#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析排产结果 - 抽样核查具体LOT_ID的匹配逻辑
"""

import os
import csv
import argparse
from datetime import datetime
import pandas as pd
from app import create_app
from app.utils.db_connection_pool import get_db_cursor
from app.services.real_scheduling_service import RealSchedulingService

def analyze_lot_details(lot_id, session_id):
    """分析单个批次的详细匹配情况"""
    print(f"\n{'='*60}")
    print(f"🔍 深入分析批次: {lot_id}")
    print(f"{'='*60}")
    
    with get_db_cursor('aps') as cur:
        # 1. 获取排产结果（兼容不同字段版本）
        cur.execute("""
            SELECT *
            FROM lotprioritydone
            WHERE LOT_ID = %s AND SESSION_ID = %s
            LIMIT 1
        """, (lot_id, session_id))

        result = cur.fetchone()
        if not result:
            print(f"❌ 未找到批次 {lot_id} 的排产结果")
            return

        print(f"📋 排产结果:")
        print(f"  - 分配设备: {result.get('HANDLER_ID', 'N/A')}")
        print(f"  - 分类(STEP): {result.get('STEP', 'N/A')}")
        print(f"  - 选择原因: {result.get('SELECTION_REASON', 'N/A')}")

        # 2. 获取批次基础信息（兼容不同字段版本）
        cur.execute("""
            SELECT *
            FROM et_wait_lot
            WHERE LOT_ID = %s
            LIMIT 1
        """, (lot_id,))

        lot_info = cur.fetchone()
        if lot_info:
            print(f"\n📦 批次信息:")
            print(f"  - DEVICE: {lot_info.get('DEVICE', 'N/A')}")
            print(f"  - STAGE: {lot_info.get('STAGE', 'N/A')}")
            print(f"  - PKG_PN: {lot_info.get('PKG_PN', 'N/A')}")
            print(f"  - DUE_DATE: {lot_info.get('DUE_DATE', 'N/A')}")
            print(f"  - PRIORITY: {lot_info.get('PRIORITY', 'N/A')}")

        # 3. 获取需求侧配置（测试规范）
        cur.execute("""
            SELECT TESTER, HB_PN, TB_PN
            FROM et_ft_test_spec
            WHERE DEVICE = %s AND STAGE = %s
        """, (lot_info.get('DEVICE'), lot_info.get('STAGE')))

        test_specs = cur.fetchall() or []
        print(f"\n🧪 测试规范 (最多展示前5条，共{len(test_specs)}条):")
        for i, spec in enumerate(test_specs[:5], 1):
            print(f"  {i}. TESTER={spec.get('TESTER')}, HB_PN={spec.get('HB_PN')}, TB_PN={spec.get('TB_PN')}")

        # 4. 获取需求侧配置（配方文件）
        cur.execute("""
            SELECT HANDLER_CONFIG, KIT_PN
            FROM et_recipe_file
            WHERE DEVICE = %s AND STAGE = %s
        """, (lot_info.get('DEVICE'), lot_info.get('STAGE')))

        recipes = cur.fetchall() or []
        print(f"\n🔧 配方文件 (最多展示前5条，共{len(recipes)}条):")
        for i, recipe in enumerate(recipes[:5], 1):
            print(f"  {i}. HANDLER_CONFIG={recipe['HANDLER_CONFIG']}, KIT_PN={recipe['KIT_PN']}")

        # 5. 获取分配设备的配置
        cur.execute("""
            SELECT HANDLER_CONFIG, KIT_PN, TESTER, HB_PN, TB_PN, DEVICE, STAGE
            FROM eqp_status 
            WHERE HANDLER_ID = %s
        """, (result['HANDLER_ID'],))
        
        eqp_info = cur.fetchone()
        if eqp_info:
            print(f"\n⚙️ 分配设备配置:")
            print(f"  - HANDLER_CONFIG: {eqp_info['HANDLER_CONFIG']}")
            print(f"  - KIT_PN: {eqp_info['KIT_PN']}")
            print(f"  - TESTER: {eqp_info['TESTER']}")
            print(f"  - HB_PN: {eqp_info['HB_PN']}")
            print(f"  - TB_PN: {eqp_info['TB_PN']}")
            print(f"  - DEVICE: {eqp_info['DEVICE']}")
            print(f"  - STAGE: {eqp_info['STAGE']}")
        
        # 6. 分析匹配类型（EXISTS任一满足语义）
        print(f"\n🎯 匹配分析:")
        if test_specs and recipes and eqp_info:
            # STAGE/DEVICE 安全获取
            lot_device = (lot_info.get('DEVICE') or '').upper()
            lot_stage = (lot_info.get('STAGE') or '').upper()
            eqp_stage = (eqp_info.get('STAGE') or '').upper()
            eqp_device = (eqp_info.get('DEVICE') or '').upper()

            # 组合判定（存在任一组合即可）
            def exists_combo(pred):
                for r in recipes:
                    for s in test_specs:
                        try:
                            if pred(r, s):
                                return True
                        except Exception:
                            continue
                return False

            exists_same_config = exists_combo(lambda r,s: eqp_info['HANDLER_CONFIG']==r['HANDLER_CONFIG'] and eqp_info['KIT_PN']==r['KIT_PN'] and eqp_info['TESTER']==s['TESTER'] and eqp_stage==lot_stage)
            exists_45 = exists_combo(lambda r,s: eqp_info['HANDLER_CONFIG']==r['HANDLER_CONFIG'] and eqp_info['KIT_PN']==r['KIT_PN'] and eqp_info['TESTER']==s['TESTER'])
            exists_55 = exists_combo(lambda r,s: eqp_info['HANDLER_CONFIG']==r['HANDLER_CONFIG'] and eqp_info['KIT_PN']==r['KIT_PN'] and (eqp_info['TESTER'] or '')!=(s.get('TESTER') or ''))
            exists_120 = exists_combo(lambda r,s: eqp_info['HANDLER_CONFIG']==r['HANDLER_CONFIG'] and ((eqp_info['KIT_PN'] or '')!=(r.get('KIT_PN') or '') or (r.get('KIT_PN') is None) or (r.get('KIT_PN')=='')))

            print(f"  - 存在同配置(0): {exists_same_config}")
            print(f"  - 存在45: {exists_45}")
            print(f"  - 存在55: {exists_55}")
            print(f"  - 存在120: {exists_120}")

            # STAGE/DEVICE 匹配
            stage_match = eqp_stage == lot_stage
            device_match = eqp_device == lot_device
            print(f"  - STAGE匹配: {stage_match} ({eqp_info.get('STAGE','N/A')} vs {lot_info.get('STAGE','N/A')})")
            print(f"  - DEVICE匹配: {device_match} ({eqp_info.get('DEVICE','N/A')} vs {lot_info.get('DEVICE','N/A')})")

            # 跨工序分析（基于配方集合）
            cross_a_count = 0
            cross_b_count = 0
            if recipes:
                # A: 存在HC+KIT一致的设备
                cur.execute("""
                    SELECT COUNT(*) as cnt FROM eqp_status
                    WHERE HANDLER_CONFIG IN (%s) AND KIT_PN IN (%s)
                """.replace('%s','%s'), (recipe['HANDLER_CONFIG'] if recipes else '', recipe['KIT_PN'] if recipes else ''))
            # 简化：沿用下方候选统计打印

        # 7. 查找更优候选
        print(f"\n🔍 候选设备分析:")
        if recipes:
            recipe = recipes[0]
            
            # 严格池
            cur.execute("""
                SELECT COUNT(*) as cnt FROM eqp_status
                WHERE UPPER(DEVICE) = UPPER(%s) AND UPPER(STAGE) = UPPER(%s)
            """, (lot_info.get('DEVICE'), lot_info.get('STAGE')))
            strict_count = cur.fetchone()['cnt']

            # 空闲池
            cur.execute("""
                SELECT COUNT(*) as cnt FROM eqp_status 
                WHERE (DEVICE = '' OR DEVICE IS NULL) 
                  AND (STAGE = '' OR STAGE IS NULL)
                  AND HANDLER_CONFIG = %s 
                  AND KIT_PN = %s
            """, (recipe['HANDLER_CONFIG'], recipe['KIT_PN']))
            idle_count = cur.fetchone()['cnt']
            
            # 跨工序A类
            cur.execute("""
                SELECT COUNT(*) as cnt FROM eqp_status 
                WHERE HANDLER_CONFIG = %s AND KIT_PN = %s
            """, (recipe['HANDLER_CONFIG'], recipe['KIT_PN']))
            cross_a_count = cur.fetchone()['cnt']
            
            # 跨工序B类
            cur.execute("""
                SELECT COUNT(*) as cnt FROM eqp_status 
                WHERE HANDLER_CONFIG = %s 
                  AND (KIT_PN != %s OR KIT_PN = '' OR KIT_PN IS NULL)
            """, (recipe['HANDLER_CONFIG'], recipe['KIT_PN']))
            cross_b_count = cur.fetchone()['cnt']
            
            print(f"  - 严格池候选: {strict_count} 台")
            print(f"  - 空闲池候选: {idle_count} 台")
            print(f"  - 跨工序A类候选: {cross_a_count} 台")
            print(f"  - 跨工序B类候选: {cross_b_count} 台")

def analyze_failed_lots(session_id):
    """分析失败批次"""
    print(f"\n{'='*60}")
    print(f"🔍 分析失败批次")
    print(f"{'='*60}")
    
    with get_db_cursor('aps') as cur:
        cur.execute("""
            SELECT lot_id, device, stage, failure_reason, failure_details
            FROM scheduling_failed_lots 
            WHERE session_id = %s
            LIMIT 10
        """, (session_id,))
        
        failed_lots = cur.fetchall()
        
        for lot in failed_lots:
            print(f"\n❌ 失败批次: {lot['lot_id']}")
            print(f"  - DEVICE: {lot['device']}")
            print(f"  - STAGE: {lot['stage']}")
            print(f"  - 失败原因: {lot['failure_reason']}")
            
            # 检查是否存在同产品同工序设备
            cur.execute("""
                SELECT COUNT(*) as cnt FROM eqp_status 
                WHERE UPPER(DEVICE) = UPPER(%s) AND UPPER(STAGE) = UPPER(%s)
            """, (lot['device'], lot['stage']))
            
            same_product_count = cur.fetchone()['cnt']
            if same_product_count > 0:
                print(f"  ⚠️ 存在 {same_product_count} 台同产品同工序设备，应为续排成功")

def get_latest_session_id():
    with get_db_cursor('aps') as cur:
        cur.execute('SELECT SESSION_ID FROM lotprioritydone ORDER BY CREATE_TIME DESC LIMIT 1')
        row = cur.fetchone()
        return row['SESSION_ID'] if row else None


def load_sample_lots(check_type: str, sample: int):
    df = pd.read_csv('reports/latest_scheduling_check.csv')
    ids = df[df['check_type']==check_type]['LOT_ID'].dropna().unique().tolist()
    return ids[:sample]


def main():
    parser = argparse.ArgumentParser(description='深入分析 - 抽样核查')
    parser.add_argument('--check-type', default='A_同配置应命中', help='A_同配置应命中 | H_跨工序A优先性 | J_解析失败但可续排')
    parser.add_argument('--sample', type=int, default=5, help='每类抽样数量')
    parser.add_argument('--session-id', help='会话ID，未提供则取最新')
    parser.add_argument('--output', default='reports/deep_analysis_samples.csv', help='输出CSV')
    args = parser.parse_args()

    app, _ = create_app()
    with app.app_context():
        session_id = args.session_id or get_latest_session_id() or ''
        print("🔍 开始深入分析排产结果...")

        lot_ids = load_sample_lots(args.check_type, args.sample)
        rows = []
        for lot_id in lot_ids:
            analyze_lot_details(lot_id, session_id)
            rows.append({'check_type': args.check_type, 'LOT_ID': lot_id, 'SESSION_ID': session_id, 'analyzed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')})

        if args.check_type == 'J_解析失败但可续排':
            analyze_failed_lots(session_id)

        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        with open(args.output, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['check_type','LOT_ID','SESSION_ID','analyzed_at'])
            writer.writeheader()
            for r in rows:
                writer.writerow(r)

        print(f"\n✅ 深入分析完成，结果已保存到 {args.output}")

if __name__ == '__main__':
    main()
