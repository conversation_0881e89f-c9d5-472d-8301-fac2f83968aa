from sqlalchemy import text
# -*- coding: utf-8 -*-
"""
WIP批次管理 API v2（缓存统一化修复版）
专门处理WIP批次数据的CRUD操作

🚀 缓存优化：
- 使用APIDataCacheAdapter替代直接数据库查询
- 保持API接口兼容性
- 提升性能，降低数据库负载
"""

from flask import Blueprint, request, jsonify
from app.utils.db_helper import get_mysql_connection
from app.utils.api_config import get_api_route
# 🚀 新增：统一缓存适配器
from app.utils.api_cache_adapter import get_api_cache_adapter, get_cached_paginated_data
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# 创建蓝图 - 使用配置化的URL前缀
wip_lot_api_bp = Blueprint('wip_lot_api', __name__, url_prefix=get_api_route('tables/wip_lot'))

@wip_lot_api_bp.route('/data', methods=['GET'])
def get_wip_lot_data():
    """获取WIP批次数据（缓存优化版）"""
    try:
        # 获取查询参数
        database = request.args.get('database', 'aps')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '')
        
        # 🚀 使用缓存适配器获取数据
        api_cache = get_api_cache_adapter()
        
        # 构建过滤条件
        filters = []
        if search:
            filters.append({
                'field': 'LOT_ID,DEVICE,STAGE,WIP_STATE',
                'operator': 'contains',
                'value': search
            })
        
        # 🚀 使用缓存获取分页数据
        result = api_cache.get_paginated_data(
            table_name='wip_lot',
            page=page,
            per_page=per_page,
            filters=filters,
            search=search,
            sort_by='created_at',
            sort_order='DESC'
        )
        
        if result.get('success'):
            # 确保数据格式标准化
            data = []
            for record in result.get('data', []):
                if isinstance(record, dict):
                    # 处理日期时间字段
                    formatted_record = {}
                    for key, value in record.items():
                        if key in ['PLAN_DUE_DATE', 'created_at', 'updated_at'] and value:
                            if hasattr(value, 'isoformat'):
                                formatted_record[key] = value.isoformat()
                            else:
                                formatted_record[key] = str(value)
                        else:
                            formatted_record[key] = value
                    data.append(formatted_record)
                else:
                    # 处理元组/列表格式（兼容性）
                    data.append(record)
            
            response_data = {
                'success': True,
                'data': data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': result.get('total', len(data)),
                    'pages': result.get('pages', (result.get('total', len(data)) + per_page - 1) // per_page)
                },
                'message': f"获取到 {len(data)} 条WIP批次记录（缓存）"
            }
            
            logger.info(f"✅ 获取WIP批次数据成功（缓存）: {len(data)}条记录")
            return jsonify(response_data)
        else:
            # 🚀 缓存失败时返回错误信息
            logger.warning(f"缓存查询失败: {result.get('error', 'Unknown error')}")
            return jsonify({
                'success': False,
                'error': f'缓存查询失败: {result.get("error", "Unknown error")}',
                'data': []
            }), 500
        
    except Exception as e:
        logger.error(f"❌ 获取WIP批次数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500

@wip_lot_api_bp.route('/summary', methods=['GET'])
def get_wip_lot_summary():
    """获取WIP批次汇总信息"""
    try:
        # 确保连接到aps主业务数据库
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 获取总记录数
            cursor.execute("SELECT COUNT(*) as total FROM wip_lot")
            total_result = cursor.fetchone()
            total_lots = total_result['total']
            
            # 获取状态分布
            cursor.execute("""
                SELECT WIP_STATE, COUNT(*) as count 
                FROM wip_lot 
                WHERE WIP_STATE IS NOT NULL 
                GROUP BY WIP_STATE 
                ORDER BY count DESC
            """)
            status_distribution = cursor.fetchall()
            
            # 获取产品分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as count 
                FROM wip_lot 
                WHERE DEVICE IS NOT NULL 
                GROUP BY DEVICE 
                ORDER BY count DESC 
                LIMIT 10
            """)
            device_distribution = cursor.fetchall()
            
            # 获取工序分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as count 
                FROM wip_lot 
                WHERE STAGE IS NOT NULL 
                GROUP BY STAGE 
                ORDER BY count DESC
            """)
            stage_distribution = cursor.fetchall()
            
            summary = {
                'total_lots': total_lots,
                'status_distribution': status_distribution,
                'device_distribution': device_distribution,
                'stage_distribution': stage_distribution,
                'last_updated': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 获取WIP批次汇总信息成功: {total_lots}条记录")
            return jsonify({
                'success': True,
                'data': summary
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 获取WIP批次汇总信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/create', methods=['POST'])
def create_wip_lot():
    """创建WIP批次记录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        # 检查必需字段
        if 'LOT_ID' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必需字段: LOT_ID'
            }), 400
        
        # 确保连接到aps主业务数据库
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建插入SQL
            fields = list(data.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            field_names = ', '.join([f"`{field}`" for field in fields])
            
            insert_sql = f"INSERT INTO wip_lot ({field_names}) VALUES ({placeholders})"
            values = list(data.values())
            
            cursor.execute(insert_sql, values)
            conn.commit()
            
            new_id = cursor.lastrowid
            
            logger.info(f"✅ 创建WIP批次记录成功: ID={new_id}, LOT_ID={data.get('LOT_ID')}")
            return jsonify({
                'success': True,
                'message': '创建WIP批次记录成功',
                'record_id': new_id
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 创建WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/update/<int:record_id>', methods=['PUT'])
def update_wip_lot(record_id):
    """更新WIP批次记录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        # 确保连接到aps主业务数据库
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建更新SQL
            set_clauses = []
            values = []
            
            for field, value in data.items():
                if field != 'id':  # 不更新主键
                    set_clauses.append(f"`{field}` = %s")
                    values.append(value)
            
            if not set_clauses:
                return jsonify({
                    'success': False,
                    'error': '没有可更新的字段'
                }), 400
            
            # 添加更新时间
            set_clauses.append("`updated_at` = NOW()")
            
            update_sql = f"UPDATE wip_lot SET {', '.join(set_clauses)} WHERE id = %s"
            values.append(record_id)
            
            cursor.execute(update_sql, values)
            conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({
                    'success': False,
                    'error': '记录不存在'
                }), 404
            
            logger.info(f"✅ 更新WIP批次记录成功: ID={record_id}")
            return jsonify({
                'success': True,
                'message': '更新WIP批次记录成功'
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 更新WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@wip_lot_api_bp.route('/delete/<int:record_id>', methods=['DELETE'])
def delete_wip_lot(record_id):
    """删除WIP批次记录"""
    try:
        # 确保连接到aps主业务数据库
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 先检查记录是否存在
            cursor.execute("SELECT LOT_ID FROM wip_lot WHERE id = %s", (record_id,))
            record = cursor.fetchone()
            
            if not record:
                return jsonify({
                    'success': False,
                    'error': '记录不存在'
                }), 404
            
            # 删除记录
            cursor.execute("DELETE FROM wip_lot WHERE id = %s", (record_id,))
            conn.commit()
            
            logger.info(f"✅ 删除WIP批次记录成功: ID={record_id}, LOT_ID={record['LOT_ID']}")
            return jsonify({
                'success': True,
                'message': '删除WIP批次记录成功'
            })
            
        finally:
            cursor.close()
    except Exception as e:
        logger.error(f"❌ 删除WIP批次记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 