#!/usr/bin/env python3
"""
直接查询数据库的测试规范数据
"""
import pymysql
from collections import Counter

def check_database():
    """直接查询数据库"""
    print("🔍 直接查询数据库中的测试规范数据...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询所有Released状态的测试规范
            cursor.execute("""
                SELECT DEVICE, STAGE, TESTER, HB_PN, TB_PN, APPROVAL_STATE
                FROM et_ft_test_spec 
                WHERE APPROVAL_STATE = 'Released'
                ORDER BY DEVICE, STAGE
            """)
            
            results = cursor.fetchall()
            print(f"📊 数据库中Released状态的测试规范总数: {len(results)}")
            
            if results:
                # 统计DEVICE分布
                devices = [row['DEVICE'] for row in results]
                device_counts = Counter(devices)
                
                print(f"\n📊 DEVICE分布（前20）:")
                for device, count in device_counts.most_common(20):
                    print(f"  - {device}: {count} 条记录")
                
                print(f"\n📊 唯一DEVICE数量: {len(device_counts)}")
                
                # 显示前10条记录
                print(f"\n📋 前10条记录详情:")
                for i, row in enumerate(results[:10], 1):
                    device = row['DEVICE']
                    stage = row['STAGE']
                    tester = row['TESTER']
                    approval = row['APPROVAL_STATE']
                    print(f"  {i}. DEVICE='{device}' STAGE='{stage}' TESTER='{tester}' APPROVAL='{approval}'")
                
                # 检查是否只有一个DEVICE
                if len(device_counts) == 1:
                    print(f"\n⚠️ 警告：所有记录都是同一个DEVICE: {list(device_counts.keys())[0]}")
                else:
                    print(f"\n✅ 正常：包含 {len(device_counts)} 个不同的DEVICE")
                    
                # 检查批次需要的DEVICE
                print(f"\n🔍 检查批次需要的DEVICE...")
                cursor.execute("""
                    SELECT DISTINCT DEVICE, COUNT(*) as batch_count
                    FROM et_wait_lot 
                    GROUP BY DEVICE 
                    ORDER BY batch_count DESC
                    LIMIT 10
                """)
                
                batch_devices = cursor.fetchall()
                print(f"📊 批次中的DEVICE分布（前10）:")
                for row in batch_devices:
                    device = row['DEVICE']
                    count = row['batch_count']
                    # 检查这个DEVICE是否有测试规范
                    has_spec = device in device_counts
                    status = "✅" if has_spec else "❌"
                    print(f"  {status} {device}: {count} 个批次, 测试规范: {device_counts.get(device, 0)} 条")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
