#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 数据一致性校验器
专门负责排产前的数据一致性检查，确保排产算法基于一致的数据状态运行

核心功能：
1. 跨数据源一致性检查
2. 时间窗口一致性验证
3. 依赖关系完整性检查
4. 数据冲突检测和修复建议
"""

import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from .deterministic_cache import deterministic_cache, CacheSnapshot

logger = logging.getLogger(__name__)

class ConsistencyLevel(Enum):
    """一致性级别"""
    STRICT = "strict"          # 严格一致性：不允许任何冲突
    MODERATE = "moderate"      # 中等一致性：允许轻微时间差异
    RELAXED = "relaxed"        # 宽松一致性：允许一定程度的数据差异

class InconsistencyType(Enum):
    """不一致类型"""
    TIME_SKEW = "time_skew"                    # 时间偏移
    VERSION_MISMATCH = "version_mismatch"      # 版本不匹配
    DEPENDENCY_BROKEN = "dependency_broken"    # 依赖关系破坏
    DATA_CORRUPTION = "data_corruption"        # 数据损坏
    MISSING_DATA = "missing_data"              # 数据缺失

@dataclass
class InconsistencyIssue:
    """不一致问题"""
    issue_type: InconsistencyType
    severity: str  # "critical", "major", "minor"
    description: str
    affected_data_types: List[str]
    suggested_action: str
    auto_fixable: bool

@dataclass
class ConsistencyReport:
    """一致性报告"""
    validation_time: float
    consistency_level: ConsistencyLevel
    overall_score: float  # 0.0-1.0
    is_consistent: bool
    issues: List[InconsistencyIssue]
    snapshot_id: str
    data_summary: Dict[str, Any]
    recommendation: str

class DataConsistencyValidator:
    """
    🔍 数据一致性校验器
    
    确保排产算法运行在一致的数据状态上，解决因数据不一致导致的排产结果差异问题
    """
    
    def __init__(self, consistency_level: ConsistencyLevel = ConsistencyLevel.MODERATE):
        self.consistency_level = consistency_level
        self.cache_manager = deterministic_cache
        
        # 一致性规则配置
        self._consistency_rules = {
            ConsistencyLevel.STRICT: {
                'max_time_skew_seconds': 30,
                'min_overall_score': 0.95,
                'allow_missing_data': False,
                'allow_version_mismatch': False
            },
            ConsistencyLevel.MODERATE: {
                'max_time_skew_seconds': 300,  # 5分钟
                'min_overall_score': 0.80,
                'allow_missing_data': True,
                'allow_version_mismatch': True
            },
            ConsistencyLevel.RELAXED: {
                'max_time_skew_seconds': 1800,  # 30分钟
                'min_overall_score': 0.60,
                'allow_missing_data': True,
                'allow_version_mismatch': True
            }
        }
        
        # 排产必需的数据类型
        self._required_data_types = [
            'wait_lot_data',
            'equipment_status_data', 
            'test_spec_data',
            'uph_data',
            'device_priority_config',
            'lot_priority_config'
        ]
        
        # 可选的数据类型
        self._optional_data_types = [
            'recipe_file_data',
            'stage_mapping_config',
            'lotprioritydone_data'
        ]
        
        logger.info(f"🔍 数据一致性校验器初始化 - 一致性级别: {consistency_level.value}")

    def validate_for_scheduling(self, data_types: List[str] = None) -> ConsistencyReport:
        """
        为排产操作验证数据一致性
        
        Args:
            data_types: 要验证的数据类型列表，None表示验证所有必需数据
            
        Returns:
            ConsistencyReport: 一致性报告
        """
        validation_start = time.time()
        
        if data_types is None:
            data_types = self._required_data_types + self._optional_data_types
        
        logger.info(f"🔍 开始排产数据一致性验证 - 数据类型: {len(data_types)}")
        
        # 1. 创建当前数据快照
        snapshot = self._create_validation_snapshot(data_types)
        
        # 2. 执行一致性检查
        issues = self._perform_comprehensive_validation(snapshot, data_types)
        
        # 3. 计算整体评分
        overall_score = self._calculate_overall_score(issues, snapshot)
        
        # 4. 判断是否一致
        rules = self._consistency_rules[self.consistency_level]
        is_consistent = (overall_score >= rules['min_overall_score'] and 
                        not any(issue.severity == "critical" for issue in issues))
        
        # 5. 生成建议
        recommendation = self._generate_recommendation(issues, is_consistent, overall_score)
        
        # 6. 构建报告
        report = ConsistencyReport(
            validation_time=time.time() - validation_start,
            consistency_level=self.consistency_level,
            overall_score=overall_score,
            is_consistent=is_consistent,
            issues=issues,
            snapshot_id=snapshot.snapshot_id,
            data_summary=self._generate_data_summary(snapshot),
            recommendation=recommendation
        )
        
        logger.info(f"✅ 数据一致性验证完成 - 评分: {overall_score:.2f}, 一致: {is_consistent}, 问题: {len(issues)}")
        
        return report

    def _create_validation_snapshot(self, data_types: List[str]) -> CacheSnapshot:
        """创建验证用的数据快照"""
        snapshot_id = f"validation_{int(time.time())}"
        return self.cache_manager.create_snapshot(data_types, snapshot_id)

    def _perform_comprehensive_validation(self, snapshot: CacheSnapshot, data_types: List[str]) -> List[InconsistencyIssue]:
        """执行全面的一致性验证"""
        issues = []
        
        # 1. 数据完整性检查
        issues.extend(self._check_data_completeness(snapshot, data_types))
        
        # 2. 时间一致性检查
        issues.extend(self._check_temporal_consistency(snapshot))
        
        # 3. 版本依赖性检查
        issues.extend(self._check_version_dependencies(snapshot))
        
        # 4. 数据质量检查
        issues.extend(self._check_data_quality(snapshot))
        
        # 5. 业务逻辑一致性检查
        issues.extend(self._check_business_logic_consistency(snapshot))
        
        return issues

    def _check_data_completeness(self, snapshot: CacheSnapshot, data_types: List[str]) -> List[InconsistencyIssue]:
        """检查数据完整性"""
        issues = []
        
        # 检查必需数据
        for required_type in self._required_data_types:
            if required_type in data_types and required_type not in snapshot.data_versions:
                issues.append(InconsistencyIssue(
                    issue_type=InconsistencyType.MISSING_DATA,
                    severity="critical",
                    description=f"缺失必需数据类型: {required_type}",
                    affected_data_types=[required_type],
                    suggested_action=f"重新加载 {required_type} 数据",
                    auto_fixable=True
                ))
        
        # 检查数据是否为空
        snapshot_data = self.cache_manager.get_snapshot_data(snapshot.snapshot_id)
        for data_type, data in snapshot_data.items():
            if not data or (isinstance(data, (list, dict)) and len(data) == 0):
                severity = "critical" if data_type in self._required_data_types else "major"
                issues.append(InconsistencyIssue(
                    issue_type=InconsistencyType.MISSING_DATA,
                    severity=severity,
                    description=f"数据类型 {data_type} 为空",
                    affected_data_types=[data_type],
                    suggested_action=f"检查 {data_type} 数据源",
                    auto_fixable=False
                ))
        
        return issues

    def _check_temporal_consistency(self, snapshot: CacheSnapshot) -> List[InconsistencyIssue]:
        """检查时间一致性"""
        issues = []
        rules = self._consistency_rules[self.consistency_level]
        
        # 收集所有数据的时间戳
        timestamps = []
        data_type_timestamps = {}
        
        for data_type, version_hash in snapshot.data_versions.items():
            version_info = self.cache_manager.get_version_info(data_type, version_hash)
            if version_info:
                timestamps.append(version_info.timestamp)
                data_type_timestamps[data_type] = version_info.timestamp
        
        if len(timestamps) < 2:
            return issues  # 没有足够的数据进行时间比较
        
        # 计算时间跨度
        min_time = min(timestamps)
        max_time = max(timestamps)
        time_skew = max_time - min_time
        
        if time_skew > rules['max_time_skew_seconds']:
            severity = "critical" if time_skew > rules['max_time_skew_seconds'] * 2 else "major"
            issues.append(InconsistencyIssue(
                issue_type=InconsistencyType.TIME_SKEW,
                severity=severity,
                description=f"数据时间跨度过大: {time_skew:.1f}秒 (允许: {rules['max_time_skew_seconds']}秒)",
                affected_data_types=list(data_type_timestamps.keys()),
                suggested_action="重新创建数据快照，确保数据在相近时间获取",
                auto_fixable=True
            ))
        
        # 检查关键数据的时间一致性
        critical_types = ['equipment_status_data', 'wait_lot_data']
        critical_timestamps = [data_type_timestamps.get(dt, 0) for dt in critical_types if dt in data_type_timestamps]
        
        if len(critical_timestamps) >= 2:
            critical_skew = max(critical_timestamps) - min(critical_timestamps)
            if critical_skew > 60:  # 关键数据1分钟内
                issues.append(InconsistencyIssue(
                    issue_type=InconsistencyType.TIME_SKEW,
                    severity="major",
                    description=f"关键数据时间不一致: {critical_skew:.1f}秒",
                    affected_data_types=critical_types,
                    suggested_action="优先刷新关键数据缓存",
                    auto_fixable=True
                ))
        
        return issues

    def _check_version_dependencies(self, snapshot: CacheSnapshot) -> List[InconsistencyIssue]:
        """检查版本依赖关系"""
        issues = []
        
        for data_type, version_hash in snapshot.data_versions.items():
            version_info = self.cache_manager.get_version_info(data_type, version_hash)
            if version_info and version_info.dependencies:
                
                for dep_type, dep_version in version_info.dependencies.items():
                    if dep_type in snapshot.data_versions:
                        actual_version = snapshot.data_versions[dep_type]
                        if actual_version != dep_version:
                            issues.append(InconsistencyIssue(
                                issue_type=InconsistencyType.DEPENDENCY_BROKEN,
                                severity="major",
                                description=f"{data_type} 依赖 {dep_type}:{dep_version[:8]}, 但实际版本是 {actual_version[:8]}",
                                affected_data_types=[data_type, dep_type],
                                suggested_action=f"重新加载 {data_type} 和其依赖数据",
                                auto_fixable=True
                            ))
        
        return issues

    def _check_data_quality(self, snapshot: CacheSnapshot) -> List[InconsistencyIssue]:
        """检查数据质量"""
        issues = []
        snapshot_data = self.cache_manager.get_snapshot_data(snapshot.snapshot_id)
        
        # 检查设备状态数据质量
        if 'equipment_status_data' in snapshot_data:
            equipment_data = snapshot_data['equipment_status_data']
            if isinstance(equipment_data, dict):
                invalid_status_count = 0
                for handler_id, eqp_info in equipment_data.items():
                    status = eqp_info.get('STATUS', '').upper()
                    if status not in ['RUN', 'IDLE', 'DOWN', 'WAIT']:
                        invalid_status_count += 1
                
                if invalid_status_count > 0:
                    issues.append(InconsistencyIssue(
                        issue_type=InconsistencyType.DATA_CORRUPTION,
                        severity="major",
                        description=f"发现 {invalid_status_count} 个设备状态异常",
                        affected_data_types=['equipment_status_data'],
                        suggested_action="检查设备状态数据源，清理无效状态",
                        auto_fixable=False
                    ))
        
        # 检查待排产批次数据质量
        if 'wait_lot_data' in snapshot_data:
            wait_lots = snapshot_data['wait_lot_data']
            if isinstance(wait_lots, list):
                invalid_lots = []
                for lot in wait_lots:
                    if not lot.get('LOT_ID') or not lot.get('DEVICE') or lot.get('GOOD_QTY', 0) <= 0:
                        invalid_lots.append(lot.get('LOT_ID', 'unknown'))
                
                if invalid_lots:
                    issues.append(InconsistencyIssue(
                        issue_type=InconsistencyType.DATA_CORRUPTION,
                        severity="major",
                        description=f"发现 {len(invalid_lots)} 个无效待排产批次",
                        affected_data_types=['wait_lot_data'],
                        suggested_action="清理无效的待排产批次数据",
                        auto_fixable=True
                    ))
        
        return issues

    def _check_business_logic_consistency(self, snapshot: CacheSnapshot) -> List[InconsistencyIssue]:
        """检查业务逻辑一致性"""
        issues = []
        snapshot_data = self.cache_manager.get_snapshot_data(snapshot.snapshot_id)
        
        # 检查设备与测试规格的匹配性
        equipment_data = snapshot_data.get('equipment_status_data', {})
        test_spec_data = snapshot_data.get('test_spec_data', {})
        
        if equipment_data and test_spec_data:
            # 统计设备可处理的工序
            available_stages = set()
            for eqp_info in equipment_data.values():
                if eqp_info.get('STATUS') in ['RUN', 'IDLE']:
                    stage = eqp_info.get('STAGE')
                    if stage:
                        available_stages.add(stage)
            
            # 统计测试规格中需要的工序
            required_stages = set()
            for spec_info in test_spec_data.values():
                stage = spec_info.get('STAGE')
                if stage:
                    required_stages.add(stage)
            
            # 检查是否有工序无设备可用
            unsupported_stages = required_stages - available_stages
            if unsupported_stages:
                issues.append(InconsistencyIssue(
                    issue_type=InconsistencyType.DATA_CORRUPTION,
                    severity="minor",
                    description=f"检测到无可用设备的工序: {', '.join(unsupported_stages)}",
                    affected_data_types=['equipment_status_data', 'test_spec_data'],
                    suggested_action="检查设备配置和测试规格匹配性",
                    auto_fixable=False
                ))
        
        return issues

    def _calculate_overall_score(self, issues: List[InconsistencyIssue], snapshot: CacheSnapshot) -> float:
        """计算整体一致性评分"""
        if not issues:
            return 1.0
        
        # 基础评分从快照的一致性评分开始
        base_score = snapshot.consistency_score
        
        # 根据问题严重程度扣分
        penalty = 0.0
        for issue in issues:
            if issue.severity == "critical":
                penalty += 0.30
            elif issue.severity == "major":
                penalty += 0.15
            elif issue.severity == "minor":
                penalty += 0.05
        
        # 应用惩罚，但确保评分不低于0
        final_score = max(0.0, base_score - penalty)
        
        return final_score

    def _generate_recommendation(self, issues: List[InconsistencyIssue], 
                               is_consistent: bool, overall_score: float) -> str:
        """生成修复建议"""
        if is_consistent and overall_score >= 0.9:
            return "✅ 数据一致性良好，可以安全进行排产操作"
        
        if not is_consistent:
            critical_issues = [issue for issue in issues if issue.severity == "critical"]
            if critical_issues:
                return f"❌ 检测到 {len(critical_issues)} 个严重问题，建议修复后再进行排产"
        
        auto_fixable_count = len([issue for issue in issues if issue.auto_fixable])
        if auto_fixable_count > 0:
            return f"⚠️ 检测到 {len(issues)} 个问题，其中 {auto_fixable_count} 个可自动修复，建议执行自动修复"
        
        return f"⚠️ 数据一致性评分 {overall_score:.2f}，建议检查和修复相关问题后再进行排产"

    def _generate_data_summary(self, snapshot: CacheSnapshot) -> Dict[str, Any]:
        """生成数据摘要"""
        snapshot_data = self.cache_manager.get_snapshot_data(snapshot.snapshot_id)
        summary = {}
        
        for data_type, data in snapshot_data.items():
            if isinstance(data, list):
                summary[data_type] = {
                    'type': 'list',
                    'count': len(data),
                    'sample': data[:3] if data else []
                }
            elif isinstance(data, dict):
                summary[data_type] = {
                    'type': 'dict',
                    'count': len(data),
                    'keys': list(data.keys())[:5] if data else []
                }
            else:
                summary[data_type] = {
                    'type': type(data).__name__,
                    'value': str(data)[:100] if data else None
                }
        
        return summary

    def auto_fix_issues(self, report: ConsistencyReport) -> Dict[str, Any]:
        """自动修复可修复的问题"""
        auto_fixable_issues = [issue for issue in report.issues if issue.auto_fixable]
        
        if not auto_fixable_issues:
            return {'fixed_count': 0, 'message': '没有可自动修复的问题'}
        
        fixed_count = 0
        
        for issue in auto_fixable_issues:
            try:
                if issue.issue_type == InconsistencyType.MISSING_DATA:
                    # 清理相关缓存，触发重新加载
                    for data_type in issue.affected_data_types:
                        self.cache_manager.clear_cache(data_type)
                    fixed_count += 1
                
                elif issue.issue_type == InconsistencyType.TIME_SKEW:
                    # 清理相关缓存，触发重新创建快照
                    for data_type in issue.affected_data_types:
                        self.cache_manager.clear_cache(data_type)
                    fixed_count += 1
                
                elif issue.issue_type == InconsistencyType.DEPENDENCY_BROKEN:
                    # 清理有依赖问题的数据类型
                    for data_type in issue.affected_data_types:
                        self.cache_manager.clear_cache(data_type)
                    fixed_count += 1
                
            except Exception as e:
                logger.error(f"❌ 自动修复失败: {issue.description}, 错误: {e}")
        
        return {
            'fixed_count': fixed_count,
            'total_auto_fixable': len(auto_fixable_issues),
            'message': f'成功修复 {fixed_count}/{len(auto_fixable_issues)} 个问题'
        }


# 全局实例
data_consistency_validator = DataConsistencyValidator()
