"""
Excel自动保存服务
统一管理排产结果Excel文件的自动保存功能
"""
import os
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class ExcelAutoSaveService:
    """排产结果Excel自动保存服务"""
    
    def __init__(self):
        self.config_file = os.path.join(current_app.instance_path, 'excel_auto_save_config.json')
        self.ensure_config_file()
    
    def ensure_config_file(self):
        """确保配置文件存在"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            if not os.path.exists(self.config_file):
                default_config = {
                    'auto_save_enabled': False,
                    'save_path': '',
                    'filename_pattern': '排产结果_{timestamp}.xlsx',
                    'overwrite_mode': 'timestamp',  # timestamp, overwrite, ask
                    'max_backup_files': 10
                }
                self.save_config(default_config)
        except Exception as e:
            logger.error(f"创建配置文件失败: {e}")
    
    def get_config(self) -> Dict:
        """获取配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
            return {}
    
    def save_config(self, config: Dict):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def update_auto_save_config(self, enabled: bool, save_path: str = '', 
                              filename_pattern: str = '', overwrite_mode: str = 'timestamp'):
        """更新自动保存配置"""
        config = self.get_config()
        config['auto_save_enabled'] = enabled
        if save_path:
            config['save_path'] = save_path
        if filename_pattern:
            config['filename_pattern'] = filename_pattern
        config['overwrite_mode'] = overwrite_mode
        self.save_config(config)
        logger.info(f"更新Excel自动保存配置: enabled={enabled}, path={save_path}")
    
    def is_auto_save_enabled(self) -> bool:
        """检查是否启用自动保存"""
        config = self.get_config()
        return config.get('auto_save_enabled', False)
    
    def get_save_path(self) -> str:
        """获取保存路径"""
        config = self.get_config()
        return config.get('save_path', '')
    
    def auto_save_schedule_result(self, schedule_data: List[Dict], 
                                source: str = 'manual', 
                                metrics: Dict = None) -> Dict:
        """
        自动保存排产结果为Excel
        
        Args:
            schedule_data: 排产结果数据
            source: 数据来源 ('manual', 'scheduled', 'publish')
            metrics: 排产指标信息
            
        Returns:
            Dict: 保存结果
        """
        try:
            if not self.is_auto_save_enabled():
                return {'success': False, 'message': '自动保存未启用'}
            
            if not schedule_data:
                return {'success': False, 'message': '没有数据可保存'}
            
            config = self.get_config()
            save_path = config.get('save_path', '')
            
            if not save_path:
                return {'success': False, 'message': '未设置保存路径'}
            
            # 尝试创建目录（如果不存在）
            try:
                if not os.path.exists(save_path):
                    # 检查是否是有效的路径格式
                    if save_path.startswith('\\\\') and not ':' in save_path:
                        # UNC路径但可能格式不正确
                        return {'success': False, 'message': f'UNC网络路径格式无效: {save_path}。请使用完整路径如 \\\\server\\share\\folder 或本地路径如 C:\\folder'}
                    
                    # 尝试创建本地目录
                    os.makedirs(save_path, exist_ok=True)
                    logger.info(f"📁 创建保存目录: {save_path}")
                
                # 再次验证路径是否可访问
                if not os.path.exists(save_path):
                    return {'success': False, 'message': f'无法访问保存路径: {save_path}'}
                    
                # 测试写入权限
                test_file = os.path.join(save_path, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                
            except PermissionError:
                return {'success': False, 'message': f'没有权限访问路径: {save_path}'}
            except OSError as e:
                return {'success': False, 'message': f'路径无效或无法创建: {save_path} - {str(e)}'}
            except Exception as e:
                return {'success': False, 'message': f'路径验证失败: {save_path} - {str(e)}'}
            
            # 生成文件名
            filename_pattern = config.get('filename_pattern', '排产结果_{timestamp}.xlsx')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = filename_pattern.format(
                timestamp=timestamp,
                source=source,
                date=datetime.now().strftime('%Y%m%d')
            )
            
            filepath = os.path.join(save_path, filename)
            
            # 处理文件名冲突
            filepath = self._handle_file_conflict(filepath, config.get('overwrite_mode', 'timestamp'))
            
            # 准备导出数据 - 使用与现有导出功能一致的字段格式
            export_data = self._prepare_export_data(schedule_data)
            
            # 创建Excel文件
            success = self._create_excel_file(filepath, export_data, metrics)
            
            if success:
                # 清理旧文件
                self._cleanup_old_files(save_path, config.get('max_backup_files', 10))
                
                logger.info(f"✅ 排产结果自动保存成功: {filepath}")
                return {
                    'success': True,
                    'message': f'排产结果已自动保存',
                    'filepath': filepath,
                    'filename': os.path.basename(filepath),
                    'records_count': len(schedule_data)
                }
            else:
                return {'success': False, 'message': '创建Excel文件失败'}
                
        except Exception as e:
            logger.error(f"❌ 排产结果自动保存失败: {e}")
            return {'success': False, 'message': f'自动保存失败: {str(e)}'}
    
    def _prepare_export_data(self, schedule_data: List[Dict]) -> List[Dict]:
        """准备导出数据 - 保持与现有导出格式一致"""
        export_data = []
        
        for index, item in enumerate(schedule_data):
            # 统一字段映射 - 与done_lots.html的exportData函数保持一致
            export_item = {
                '优先级': item.get('PRIORITY', item.get('priority', index + 1)),
                '分选机编号': item.get('HANDLER_ID', item.get('handler_id', '')),
                '内部工单号': item.get('LOT_ID', item.get('lot_id', '')),
                '批次类型': item.get('LOT_TYPE', item.get('lot_type', '')),
                '良品数量': item.get('GOOD_QTY', item.get('good_qty', 0)),
                '产品ID': item.get('PROD_ID', item.get('prod_id', '')),
                '产品名称': item.get('DEVICE', item.get('device', '')),
                '芯片名称': item.get('CHIP_ID', item.get('chip_id', '')),
                '封装': item.get('PKG_PN', item.get('pkg_pn', '')),
                '订单号': item.get('PO_ID', item.get('po_id', '')),
                '工序': item.get('STAGE', item.get('stage', '')),
                '工步': item.get('STEP', item.get('step', '')),
                'WIP状态': item.get('WIP_STATE', item.get('wip_state', '')),
                '流程状态': item.get('PROC_STATE', item.get('proc_state', '')),
                '扣留状态': item.get('HOLD_STATE', item.get('hold_state', '')),
                '流程ID': item.get('FLOW_ID', item.get('flow_id', '')),
                '流程版本': item.get('FLOW_VER', item.get('flow_ver', '')),
                '释放时间': item.get('RELEASE_TIME', item.get('release_time', '')),
                '工厂ID': item.get('FAC_ID', item.get('fac_id', '')),
                '创建时间': item.get('CREATE_TIME', item.get('create_time', '')),
                '综合评分': item.get('comprehensive_score', item.get('score', '')),
                '预计加工时间(h)': item.get('processing_time', ''),
                '改机时间(min)': item.get('changeover_time', ''),
                '算法版本': item.get('algorithm_version', ''),
                '匹配类型': item.get('match_type', ''),
                '优先级评分': item.get('priority_score', '')
            }
            export_data.append(export_item)
        
        return export_data
    
    def _create_excel_file(self, filepath: str, export_data: List[Dict], 
                          metrics: Dict = None) -> bool:
        """创建Excel文件"""
        try:
            with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
                # 主数据表
                df = pd.DataFrame(export_data)
                df.to_excel(writer, sheet_name='排产结果', index=False)
                
                # 如果有指标信息，创建统计表
                if metrics:
                    metrics_data = []
                    for key, value in metrics.items():
                        metrics_data.append({
                            '指标名称': key,
                            '指标值': value
                        })
                    
                    if metrics_data:
                        metrics_df = pd.DataFrame(metrics_data)
                        metrics_df.to_excel(writer, sheet_name='排产指标', index=False)
                
                # 设置工作表格式
                workbook = writer.book
                worksheet = writer.sheets['排产结果']
                
                # 设置列宽
                col_widths = [8, 12, 18, 12, 12, 15, 15, 15, 15, 15, 10, 10, 12, 12, 12, 12, 12, 20, 10, 20, 12, 15, 12, 15, 12, 12]
                for i, width in enumerate(col_widths):
                    if i < len(df.columns):
                        worksheet.set_column(i, i, width)
                
                # 设置表头格式
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })
                
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
            
            return True
            
        except Exception as e:
            logger.error(f"创建Excel文件失败: {e}")
            return False
    
    def _handle_file_conflict(self, filepath: str, overwrite_mode: str) -> str:
        """处理文件名冲突"""
        if not os.path.exists(filepath):
            return filepath
        
        if overwrite_mode == 'overwrite':
            return filepath
        elif overwrite_mode == 'timestamp':
            base_name, ext = os.path.splitext(filepath)
            timestamp = datetime.now().strftime('%H%M%S')
            return f"{base_name}_{timestamp}{ext}"
        else:
            # 默认添加序号
            base_name, ext = os.path.splitext(filepath)
            counter = 1
            while os.path.exists(f"{base_name}_{counter}{ext}"):
                counter += 1
            return f"{base_name}_{counter}{ext}"
    
    def _cleanup_old_files(self, save_path: str, max_files: int):
        """清理旧文件"""
        try:
            # 获取所有排产结果Excel文件
            files = []
            for filename in os.listdir(save_path):
                if filename.startswith('排产结果') and filename.endswith('.xlsx'):
                    filepath = os.path.join(save_path, filename)
                    files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序，保留最新的文件
            files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出限制的旧文件
            if len(files) > max_files:
                for filepath, _ in files[max_files:]:
                    try:
                        os.remove(filepath)
                        logger.info(f"清理旧文件: {filepath}")
                    except Exception as e:
                        logger.error(f"删除旧文件失败 {filepath}: {e}")
                        
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")

# 全局实例
_excel_auto_save_service = None

def get_excel_auto_save_service() -> ExcelAutoSaveService:
    """获取Excel自动保存服务实例"""
    global _excel_auto_save_service
    if _excel_auto_save_service is None:
        _excel_auto_save_service = ExcelAutoSaveService()
    return _excel_auto_save_service