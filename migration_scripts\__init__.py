#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移脚本包
"""

from .progressive_migrator import ProgressiveMigrator
from .data_migrator import DataMigrator
from .migration_validator import MigrationValidator
from .rollback_manager import RollbackManager
from .model_updater import ModelUpdater
from .batch_configs import (
    get_batch_config, get_all_batches, get_validation_config,
    get_rollback_config, get_database_config
)

__version__ = "1.0.0"
__author__ = "APS Migration Team"

__all__ = [
    'ProgressiveMigrator',
    'DataMigrator', 
    'MigrationValidator',
    'RollbackManager',
    'ModelUpdater',
    'get_batch_config',
    'get_all_batches',
    'get_validation_config',
    'get_rollback_config',
    'get_database_config'
] 