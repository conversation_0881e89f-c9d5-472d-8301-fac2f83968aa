#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FT订单汇总表数据模型
严格按照Excel表头字段顺序创建 - 已更新为与Excel完全一致
"""

from datetime import datetime
from app import db

class FtOrderSummary(db.Model):
    """FT订单汇总表 - 严格按照Excel表头字段顺序"""
    __tablename__ = 'ft_order_summary'
    __table_args__ = {'extend_existing': True}
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, comment='主键ID')
    
    # 严格按照Excel表头从左到右的顺序 (1-34)
    order_date = db.Column(db.Date, comment='1. 下单日期')
    order_number = db.Column(db.String(64), comment='2. 订单号', index=True)
    label_name = db.Column(db.String(200), comment='3. 标签名称')
    circuit_name = db.Column(db.String(100), comment='4. 电路名称')
    chip_name = db.Column(db.String(100), comment='5. 芯片名称')
    wafer_size = db.Column(db.String(20), comment='6. 圆片尺寸')
    package_qty1 = db.Column(db.String(50), comment='7. 送包')
    package_qty2 = db.Column(db.String(50), comment='8. 送包.1')
    diffusion_batch = db.Column(db.String(100), comment='9. 扩散批号')
    wafer_number = db.Column(db.String(100), comment='10. 片号')
    assembly_method = db.Column(db.String(100), comment='11. 装片方式')
    drawing_number = db.Column(db.String(100), comment='12. 图号')
    package_form = db.Column(db.String(100), comment='13. 封装')
    stamp_line1 = db.Column(db.String(200), comment='14. 印章(印章第一行)')
    stamp_line2 = db.Column(db.String(200), comment='15. 周记(印章第二行)')
    stamp_line3 = db.Column(db.String(200), comment='16. Unnamed: 15')
    other_notes = db.Column(db.Text, comment='17. 其他说明')
    delivery_date = db.Column(db.Date, comment='18. 交期', index=True)
    env_requirement = db.Column(db.String(100), comment='19. 产品环保要求')
    msl_requirement = db.Column(db.String(100), comment='20. MSL要求')
    reliability_requirement = db.Column(db.String(200), comment='21. 可靠性要求')
    print_pin_dot = db.Column(db.String(20), comment='22. 是否打印pin点')
    pin_dot_position = db.Column(db.String(100), comment='23. pin点位置')
    item_code = db.Column(db.String(100), comment='24. Item Code（ITEM编码）')
    shipping_address = db.Column(db.String(500), comment='25. 出货地址')
    wafer_lot = db.Column(db.String(100), comment='26. 标签 wafer lot')
    order_attribute = db.Column(db.String(100), comment='27. 订单属性')
    lot_type1 = db.Column(db.String(50), comment='28. Lot Tpye')  # 注意：保持Excel中的拼写错误
    lot_type2 = db.Column(db.String(50), comment='29. Unnamed: 28')
    lot_type3 = db.Column(db.String(50), comment='30. Lot Type')
    source_file = db.Column(db.String(512), comment='31. 源文件')
    import_time = db.Column(db.DateTime, comment='32. 导入时间')
    classification_result = db.Column(db.String(50), comment='33. 分类结果', index=True)
    column_14 = db.Column(db.String(100), comment='34. Column_14')
    
    # 系统字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    @classmethod
    def create_from_parsed_data(cls, parsed_data, source_file=None):
        """从解析的数据创建FT订单汇总记录"""
        
        # 字段映射 - Excel字段名 -> 数据库字段名 (完全匹配Excel顺序)
        field_mapping = {
            '下单日期': 'order_date',
            '订单号': 'order_number', 
            '标签名称': 'label_name',
            '电路名称': 'circuit_name',
            '芯片名称': 'chip_name',
            '圆片尺寸': 'wafer_size',
            '送包': 'package_qty1',
            '送包.1': 'package_qty2',
            '扩散批号': 'diffusion_batch',
            '片号': 'wafer_number',
            '装片方式': 'assembly_method',
            '图号': 'drawing_number',
            '封装': 'package_form',
            '印章(印章第一行)': 'stamp_line1',
            '周记(印章第二行)': 'stamp_line2',
            'Unnamed: 15': 'stamp_line3',
            '其他说明': 'other_notes',
            '交期': 'delivery_date',
            '产品环保要求': 'env_requirement',
            'MSL要求': 'msl_requirement',
            '可靠性要求': 'reliability_requirement',
            '是否打印pin点': 'print_pin_dot',
            'pin点位置': 'pin_dot_position',
            'Item Code（ITEM编码）': 'item_code',
            '出货地址': 'shipping_address',
            '标签 wafer lot': 'wafer_lot',
            '订单属性': 'order_attribute',
            'Lot Tpye': 'lot_type1',        # Excel中的拼写错误
            'Unnamed: 28': 'lot_type2',
            'Lot Type': 'lot_type3',
            '源文件': 'source_file',
            '导入时间': 'import_time',
            '分类结果': 'classification_result',
            'Column_14': 'column_14'
        }
        
        # 创建实例
        instance = cls()
        
        # 映射数据
        for excel_field, db_field in field_mapping.items():
            if excel_field in parsed_data:
                value = parsed_data[excel_field]
                
                # 处理日期字段
                if db_field in ['order_date', 'delivery_date'] and value:
                    try:
                        if isinstance(value, str):
                            from datetime import datetime
                            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y年%m月%d日']:
                                try:
                                    setattr(instance, db_field, datetime.strptime(value, fmt).date())
                                    break
                                except:
                                    continue
                        else:
                            setattr(instance, db_field, value)
                    except:
                        pass
                        
                # 处理时间字段
                elif db_field == 'import_time' and value:
                    try:
                        if isinstance(value, str):
                            setattr(instance, db_field, datetime.fromisoformat(value.replace('T', ' ')))
                        else:
                            setattr(instance, db_field, value)
                    except:
                        pass
                        
                # 处理其他字段
                else:
                    setattr(instance, db_field, value)
        
        # 设置源文件
        if source_file:
            instance.source_file = source_file
            
        # 设置导入时间
        if not instance.import_time:
            instance.import_time = datetime.utcnow()
            
        return instance
    
    def to_dict(self):
        """转换为字典格式 - 按Excel列顺序"""
        return {
            'id': self.id,
            'order_date': self.order_date.isoformat() if self.order_date else None,
            'order_number': self.order_number,
            'label_name': self.label_name,
            'circuit_name': self.circuit_name,
            'chip_name': self.chip_name,
            'wafer_size': self.wafer_size,
            'package_qty1': self.package_qty1,
            'package_qty2': self.package_qty2,
            'diffusion_batch': self.diffusion_batch,
            'wafer_number': self.wafer_number,
            'assembly_method': self.assembly_method,
            'drawing_number': self.drawing_number,
            'package_form': self.package_form,
            'stamp_line1': self.stamp_line1,
            'stamp_line2': self.stamp_line2,
            'stamp_line3': self.stamp_line3,
            'other_notes': self.other_notes,
            'delivery_date': self.delivery_date.isoformat() if self.delivery_date else None,
            'env_requirement': self.env_requirement,
            'msl_requirement': self.msl_requirement,
            'reliability_requirement': self.reliability_requirement,
            'print_pin_dot': self.print_pin_dot,
            'pin_dot_position': self.pin_dot_position,
            'item_code': self.item_code,
            'shipping_address': self.shipping_address,
            'wafer_lot': self.wafer_lot,
            'order_attribute': self.order_attribute,
            'lot_type1': self.lot_type1,
            'lot_type2': self.lot_type2,
            'lot_type3': self.lot_type3,
            'source_file': self.source_file,
            'import_time': self.import_time.isoformat() if self.import_time else None,
            'classification_result': self.classification_result,
            'column_14': self.column_14,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<FtOrderSummary {self.order_number}: {self.label_name}>' 
