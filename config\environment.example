# APS 车规芯片终测智能调度平台 - 环境变量配置示例
# 复制此文件为 .env 放在项目根目录，并修改相应的配置值

# ==============================================
# 数据库配置 (MySQL)
# ==============================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=WWWwww123!
MYSQL_CHARSET=utf8mb4

# 通用数据库配置 (如果不设置MYSQL_*，将使用这些值)
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=WWWwww123!
DB_NAME=aps
DB_CHARSET=utf8mb4

# ==============================================
# Redis配置
# ==============================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# ==============================================
# Flask应用配置
# ==============================================
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
SECRET_KEY=dev-secret-key-change-in-production
FLASK_ENV=development

# ==============================================
# 系统配置
# ==============================================
# 时区设置
TIMEZONE=Asia/Shanghai

# 分页配置
DEFAULT_PAGE_SIZE=1000

# UPH默认值
DEFAULT_UPH=1000

# 工作线程数
MAX_WORKERS=10

# ==============================================
# 文件路径配置
# ==============================================
# 基础目录配置
LOG_DIR=logs
UPLOAD_DIR=uploads
DOWNLOAD_DIR=downloads
INSTANCE_DIR=instance
STATIC_EXPORTS_DIR=static/exports

# 数据库文件路径 (SQLite备用)
SQLITE_DB_PATH=instance/aps.db

# Excel数据目录
EXCEL_BASE_PATH=Excellist2025.06.05

# ==============================================
# 管理员配置
# ==============================================
# 默认管理员账户 (首次安装时创建)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# ==============================================
# 业务常量配置
# ==============================================
# 批量处理阈值
LARGE_BATCH_THRESHOLD=10000
MEDIUM_BATCH_THRESHOLD=1000

# 业务计算参数
BASE_VALUE_RATE=10000.0
MAX_PRIORITY_SCORE=100.0
MAX_PROCESSING_TIME_HOURS=24

# ==============================================
# 调试和性能配置
# ==============================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 静默启动 (减少启动日志)
QUIET_STARTUP=0

# 调试开关
DEBUG=True
TESTING=False

# ==============================================
# 安全配置
# ==============================================
# 跨域请求配置
CORS_ENABLED=True
CSRF_ENABLED=True

# 会话超时 (秒)
SESSION_TIMEOUT=3600

# ==============================================
# API配置
# ==============================================
# API基础配置
API_PREFIX=/api
API_VERSION=v2
API_HOST=0.0.0.0
API_PORT=5000
API_DOCS_ENABLED=True
API_TIMEOUT=30
API_MAX_CONTENT_LENGTH=16777216

# API CORS配置
API_CORS_ENABLED=True
API_CORS_ORIGINS=*
API_CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS

# API认证配置
API_AUTH_ENABLED=True
API_TOKEN_EXPIRE_HOURS=24
API_REFRESH_TOKEN_EXPIRE_DAYS=30

# API限流配置
API_RATE_LIMIT_ENABLED=True
API_RATE_LIMIT_DEFAULT=100 per hour
API_RATE_LIMIT_STORAGE_URL=

# ==============================================
# 性能配置
# ==============================================
# 缓存配置
CACHE_ENABLED=True
CACHE_TIMEOUT=300

# 数据库连接池配置
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=30

# ==============================================
# 使用说明
# ==============================================
# 1. 复制此文件为 .env 放在项目根目录
# 2. 根据实际环境修改配置值
# 3. 生产环境请务必修改密码和密钥
# 4. 删除不需要的配置项（将使用默认值） 