#!/usr/bin/env python3
"""
检查特定批次的排产结果
"""
import pymysql

def check_specific_lots():
    """检查特定批次的排产结果"""
    print("🔍 检查特定批次的排产结果...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 检查这些批次是否在排产结果中
            lot_ids = ['YX0125IB0058', 'YX0125HB0458', 'YX0125GB0347']
            
            for lot_id in lot_ids:
                print(f"\n🔍 检查批次: {lot_id}")
                
                # 检查是否在成功排产结果中
                cursor.execute("""
                    SELECT LOT_ID, DEVICE, STAGE, HANDLER_ID, match_type, changeover_time
                    FROM lotprioritydone
                    WHERE LOT_ID = %s
                """, (lot_id,))
                
                success_result = cursor.fetchone()
                if success_result:
                    print(f"  ✅ 成功排产: 设备={success_result['HANDLER_ID']}, 匹配类型={success_result['match_type']}, 改机时间={success_result['changeover_time']}")
                else:
                    print(f"  ❌ 未在成功排产结果中找到")
                    
                    # 检查是否在失败记录中
                    cursor.execute("""
                        SELECT LOT_ID, DEVICE, STAGE, FAILURE_REASON
                        FROM scheduling_failed_lots 
                        WHERE LOT_ID = %s
                        ORDER BY CREATED_AT DESC
                        LIMIT 1
                    """, (lot_id,))
                    
                    failed_result = cursor.fetchone()
                    if failed_result:
                        print(f"  ❌ 排产失败: 原因={failed_result['FAILURE_REASON']}")
                    else:
                        print(f"  ❓ 既不在成功也不在失败记录中")
                        
                        # 检查是否在待排产批次中
                        cursor.execute("""
                            SELECT LOT_ID, DEVICE, STAGE, PKG_PN
                            FROM et_wait_lot 
                            WHERE LOT_ID = %s
                        """, (lot_id,))
                        
                        wait_result = cursor.fetchone()
                        if wait_result:
                            print(f"  📋 在待排产批次中: DEVICE={wait_result['DEVICE']}, STAGE={wait_result['STAGE']}, PKG_PN={wait_result['PKG_PN']}")
                        else:
                            print(f"  ❓ 不在待排产批次中")
            
            # 检查当前排产结果的总体情况
            print(f"\n📊 当前排产结果总体情况:")
            cursor.execute("SELECT COUNT(*) as total FROM lotprioritydone")
            total_success = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM scheduling_failed_lots WHERE SESSION_ID LIKE 'session_20250115_%'")
            total_failed = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM et_wait_lot")
            total_wait = cursor.fetchone()['total']
            
            print(f"  ✅ 成功排产: {total_success} 个批次")
            print(f"  ❌ 排产失败: {total_failed} 个批次")
            print(f"  📋 待排产: {total_wait} 个批次")
            
            # 显示最新的几个成功排产结果
            print(f"\n📋 最新的5个成功排产结果:")
            cursor.execute("""
                SELECT LOT_ID, DEVICE, STAGE, HANDLER_ID, match_type, changeover_time
                FROM lotprioritydone
                ORDER BY PRIORITY DESC
                LIMIT 5
            """)
            
            recent_results = cursor.fetchall()
            for i, result in enumerate(recent_results, 1):
                print(f"  {i}. {result['LOT_ID']}: {result['DEVICE']}+{result['STAGE']} -> {result['HANDLER_ID']} ({result['match_type']}, {result['changeover_time']}分钟)")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_specific_lots()
