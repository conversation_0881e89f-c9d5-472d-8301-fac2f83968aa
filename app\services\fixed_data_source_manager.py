"""
数据源管理器 - 修复版：仅支持MySQL数据源，确保数据一致性
删除Excel数据源切换逻辑，解决运算中途数据不完整问题
"""

import logging
import os
import sys
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection
from config.aps_config import config
import psutil
import time
import hashlib

logger = logging.getLogger(__name__)

# 统一配置读取函数（使用缓存）
def _get_database_config():
    """获取数据库配置，使用新的统一配置系统"""
    return {
        'host': config.DB_HOST,
        'port': config.DB_PORT,
        'user': config.DB_USER,
        'password': config.DB_PASSWORD,
        'database': config.DB_NAME,
        'charset': config.DB_CHARSET
    }

class FixedDataSourceManager:
    """
    修复版数据源管理器 - 仅支持MySQL，确保数据一致性
    
    主要修复：
    1. 删除Excel数据源切换逻辑
    2. 实施事务级数据快照
    3. 修复缓存竞争条件
    4. 统一缓存机制
    5. 增强连接稳定性
    """
    
    # 性能优化配置
    PERFORMANCE_OPTIMIZATION = {
        'max_query_timeout': 30,  # 最大查询超时时间（秒）
        'batch_size': 5000,       # 批量处理大小
        'memory_threshold': 0.8,  # 内存使用阈值
        'connection_retry_times': 3,  # 连接重试次数
        'connection_retry_delay': 1,  # 连接重试间隔（秒）
    }

    # 🔧 修复版缓存机制 - 移除版本检测竞争条件
    _cache = {}
    _cache_timeout = 300  # 5分钟缓存（默认）
    
    # 缓存超时配置 - 仅保留MySQL相关
    _cache_timeouts = {
        # aps 数据库表缓存配置
        'eqp_status': 60,                    # 1分钟 - 设备状态变化频繁
        'et_wait_lot': 30,                   # 30秒 - 待排产数据变化很频繁
        'et_ft_test_spec': 300,              # 5分钟 - 测试规格相对稳定
        'et_uph_eqp': 300,                   # 5分钟 - UPH数据相对稳定
        'et_recipe_file': 600,               # 10分钟 - 配方文件变化较少
        'lotprioritydone': 120,              # 2分钟 - 已排产批次数据
        'stage_mapping_config': 300,         # 5分钟 - STAGE映射配置
        
        # 向后兼容缓存键
        'equipment_status_data': 60,         # 1分钟
        'wait_lot_data': 30,                 # 30秒
        'test_spec_data': 300,               # 5分钟
        'uph_data': 300,                     # 5分钟
        'recipe_file_data': 600,             # 10分钟
        'lotprioritydone_data': 120,         # 2分钟
        'stage_mapping_config_data': 300,    # 5分钟
    }
    
    def __init__(self):
        """初始化修复版数据源管理器"""
        self.current_source = 'mysql'  # 🔧 修复：仅支持MySQL
        self._mysql_available = None  # 延迟检查
        self._connection_pool_initialized = False
        
        # 🔧 新增：事务隔离级别配置
        self.isolation_level = 'READ_COMMITTED'
        
        # 🔧 新增：数据快照时间戳记录
        self._data_snapshot_timestamp = None
        self._snapshot_transaction_id = None
    
    @property
    def mysql_available(self):
        """MySQL可用性检查 - 增强稳定性"""
        if self._mysql_available is None:
            self._check_mysql_availability()
        return self._mysql_available
    
    def _check_mysql_availability(self):
        """检查MySQL可用性 - 增强版（多重试+连接池）"""
        max_retries = self.PERFORMANCE_OPTIMIZATION['connection_retry_times']
        retry_delay = self.PERFORMANCE_OPTIMIZATION['connection_retry_delay']
        
        for attempt in range(max_retries):
            try:
                # 使用SQLAlchemy测试连接
                from sqlalchemy import text
                with db.session.begin():  # 🔧 修复：使用事务确保连接稳定
                    result = db.session.execute(text("SELECT 1"))
                    result.fetchone()
                
                self._mysql_available = True
                if attempt > 0:
                    logger.info(f"✅ MySQL连接恢复成功 (重试{attempt}次)")
                else:
                    logger.debug("✅ MySQL数据源可用")
                return
                
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"❌ MySQL连接尝试{attempt+1}失败，{retry_delay}秒后重试: {e}")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"❌ MySQL连接最终失败: {e}")
                    self._mysql_available = False
    
    def get_transactional_data_snapshot(self, table_names: List[str]) -> Dict[str, Any]:
        """
        🔧 新增：获取事务级数据快照，确保数据一致性
        
        Args:
            table_names: 需要获取的表名列表
            
        Returns:
            Dict: 包含所有表数据的一致性快照
        """
        if not self.mysql_available:
            raise RuntimeError("MySQL数据源不可用，无法获取数据快照")
        
        snapshot = {}
        snapshot_time = time.time()
        
        try:
            # 🔧 使用事务隔离确保数据一致性
            with db.session.begin() as transaction:
                # 设置事务隔离级别
                db.session.execute(text(f"SET TRANSACTION ISOLATION LEVEL {self.isolation_level}"))
                
                # 记录事务信息
                self._data_snapshot_timestamp = snapshot_time
                
                logger.info(f"🔒 开始事务级数据快照获取，表数量: {len(table_names)}")
                
                for table_name in table_names:
                    try:
                        if table_name == 'eqp_status':
                            snapshot[table_name] = self._get_equipment_status_in_transaction()
                        elif table_name == 'et_ft_test_spec':
                            snapshot[table_name] = self._get_test_spec_in_transaction()
                        elif table_name == 'et_wait_lot':
                            snapshot[table_name] = self._get_wait_lot_in_transaction()
                        elif table_name == 'et_uph_eqp':
                            snapshot[table_name] = self._get_uph_in_transaction()
                        elif table_name == 'et_recipe_file':
                            snapshot[table_name] = self._get_recipe_file_in_transaction()
                        else:
                            # 通用表数据获取
                            snapshot[table_name] = self._get_generic_table_in_transaction(table_name)
                        
                        data_count = len(snapshot[table_name])
                        logger.debug(f"  ✅ {table_name}: {data_count}条记录")
                        
                    except Exception as e:
                        logger.error(f"  ❌ {table_name}获取失败: {e}")
                        snapshot[table_name] = []
                
                # 记录快照元信息
                snapshot['_metadata'] = {
                    'snapshot_timestamp': snapshot_time,
                    'isolation_level': self.isolation_level,
                    'table_count': len(table_names),
                    'total_records': sum(len(data) for data in snapshot.values() if isinstance(data, list))
                }
                
                logger.info(f"✅ 事务级数据快照完成，总记录数: {snapshot['_metadata']['total_records']}")
                
                return snapshot
                
        except Exception as e:
            logger.error(f"❌ 事务级数据快照失败: {e}")
            raise
    
    def _get_equipment_status_in_transaction(self) -> List[Dict]:
        """在事务中获取设备状态数据"""
        result = db.session.execute(text("""
            SELECT * FROM eqp_status 
            WHERE id IS NOT NULL
            ORDER BY id
        """))
        return [dict(row) for row in result.mappings()]
    
    def _get_test_spec_in_transaction(self) -> List[Dict]:
        """在事务中获取测试规范数据"""
        result = db.session.execute(text("""
            SELECT * FROM et_ft_test_spec 
            WHERE ACTV_YN = 1 AND APPROVAL_STATE = 'Released'
            ORDER BY id
        """))
        return [dict(row) for row in result.mappings()]
    
    def _get_wait_lot_in_transaction(self) -> List[Dict]:
        """在事务中获取待排产批次数据"""
        result = db.session.execute(text("""
            SELECT LOT_ID, LOT_TYPE, PROD_ID, PO_ID, DEVICE, STAGE, GOOD_QTY, 
                   PKG_PN, CHIP_ID, CREATE_TIME, FAC_ID, FLOW_ID, FLOW_VER, 
                   WIP_STATE, PROC_STATE, HOLD_STATE
            FROM et_wait_lot
            WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
            ORDER BY CREATE_TIME DESC
        """))
        return [dict(row) for row in result.mappings()]
    
    def _get_uph_in_transaction(self) -> List[Dict]:
        """在事务中获取UPH数据"""
        result = db.session.execute(text("""
            SELECT * FROM et_uph_eqp 
            WHERE UPH > 0
            ORDER BY id
        """))
        return [dict(row) for row in result.mappings()]
    
    def _get_recipe_file_in_transaction(self) -> List[Dict]:
        """在事务中获取配方文件数据"""
        result = db.session.execute(text("""
            SELECT * FROM et_recipe_file 
            ORDER BY id
        """))
        return [dict(row) for row in result.mappings()]
    
    def _get_generic_table_in_transaction(self, table_name: str) -> List[Dict]:
        """在事务中获取通用表数据"""
        result = db.session.execute(text(f"""
            SELECT * FROM {table_name} 
            ORDER BY id
        """))
        return [dict(row) for row in result.mappings()]
    
    def get_equipment_status_data(self) -> Tuple[List[Dict], str]:
        """
        获取设备状态数据 - 修复版（仅MySQL，事务级一致性）
        
        🔧 修复内容：
        1. 删除Excel数据源切换
        2. 使用事务级数据获取
        3. 移除缓存版本检测竞争条件
        """
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取设备状态数据")
            return [], "None"
        
        try:
            # 🔧 修复：使用事务级数据快照
            snapshot = self.get_transactional_data_snapshot(['eqp_status'])
            equipment_data = snapshot.get('eqp_status', [])
            
            logger.info(f"🏭 从MySQL获取到 {len(equipment_data)} 条设备状态数据（事务快照）")
            return equipment_data, "MySQL"
            
        except Exception as e:
            logger.error(f"❌ MySQL获取设备状态数据失败: {e}")
            return [], "None"
    
    def get_test_spec_data(self) -> Tuple[List[Dict], str]:
        """获取测试规范数据 - 修复版"""
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取测试规范数据")
            return [], "None"
        
        try:
            snapshot = self.get_transactional_data_snapshot(['et_ft_test_spec'])
            test_spec_data = snapshot.get('et_ft_test_spec', [])
            
            logger.info(f"📋 从MySQL获取到 {len(test_spec_data)} 条测试规范数据（事务快照）")
            return test_spec_data, "MySQL"
            
        except Exception as e:
            logger.error(f"❌ MySQL获取测试规范数据失败: {e}")
            return [], "None"
    
    def get_wait_lot_data(self) -> Tuple[List[Dict], str]:
        """获取待排产批次数据 - 修复版"""
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取待排产批次数据")
            return [], "None"
        
        try:
            snapshot = self.get_transactional_data_snapshot(['et_wait_lot'])
            wait_lot_data = snapshot.get('et_wait_lot', [])
            
            logger.info(f"📊 从MySQL获取到 {len(wait_lot_data)} 条待排产批次数据（事务快照）")
            return wait_lot_data, "MySQL"
            
        except Exception as e:
            logger.error(f"❌ MySQL获取待排产批次数据失败: {e}")
            return [], "None"
    
    def get_uph_data(self) -> Tuple[List[Dict], str]:
        """获取UPH数据 - 修复版"""
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取UPH数据")
            return [], "None"
        
        try:
            snapshot = self.get_transactional_data_snapshot(['et_uph_eqp'])
            uph_data = snapshot.get('et_uph_eqp', [])
            
            logger.info(f"⚡ 从MySQL获取到 {len(uph_data)} 条UPH数据（事务快照）")
            return uph_data, "MySQL"
            
        except Exception as e:
            logger.error(f"❌ MySQL获取UPH数据失败: {e}")
            return [], "None"
    
    def get_all_scheduling_data(self) -> Tuple[Dict[str, List[Dict]], str]:
        """
        🔧 新增：一次性获取所有排产相关数据的一致性快照
        
        这是推荐的方法，确保所有数据来自同一个事务快照
        """
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取排产数据")
            return {}, "None"
        
        try:
            # 获取所有排产相关表的数据快照
            table_names = [
                'eqp_status',
                'et_ft_test_spec', 
                'et_wait_lot',
                'et_uph_eqp',
                'et_recipe_file'
            ]
            
            snapshot = self.get_transactional_data_snapshot(table_names)
            
            # 移除元数据
            data_snapshot = {k: v for k, v in snapshot.items() if not k.startswith('_')}
            
            total_records = sum(len(data) for data in data_snapshot.values())
            logger.info(f"🎯 获取完整排产数据快照成功，总记录数: {total_records}")
            
            return data_snapshot, "MySQL_Snapshot"
            
        except Exception as e:
            logger.error(f"❌ 获取排产数据快照失败: {e}")
            return {}, "None"
    
    def clear_cache(self, cache_key: str = None):
        """清理缓存"""
        if cache_key:
            if cache_key in self._cache:
                del self._cache[cache_key]
                logger.info(f"🧹 已清理缓存: {cache_key}")
        else:
            cache_count = len(self._cache)
            self._cache.clear()
            logger.info(f"🧹 缓存已清理，共清理 {cache_count} 个缓存项")
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态信息"""
        return {
            'mysql_available': self.mysql_available,
            'excel_available': False,  # 🔧 修复：不再支持Excel
            'current_source': self.current_source,
            'snapshot_timestamp': self._data_snapshot_timestamp,
            'cache_count': len(self._cache),
            'isolation_level': self.isolation_level
        }
