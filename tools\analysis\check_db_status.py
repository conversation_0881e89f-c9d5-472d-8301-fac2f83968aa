#!/usr/bin/env python3
"""
检查数据库状态的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.db_connection_pool import get_connection

def check_db_status():
    """检查数据库状态"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # 检查lotprioritydone表
        cursor.execute("SELECT COUNT(*) FROM lotprioritydone")
        total_success = cursor.fetchone()[0]
        print(f"📊 lotprioritydone表总记录数: {total_success}")
        
        if total_success > 0:
            cursor.execute("SELECT SESSION_ID, COUNT(*) FROM lotprioritydone GROUP BY SESSION_ID ORDER BY SESSION_ID DESC LIMIT 5")
            sessions = cursor.fetchall()
            print("📋 最近的会话:")
            for session in sessions:
                print(f"  - {session[0]}: {session[1]} 个批次")
        
        # 检查scheduling_failed_lots表
        cursor.execute("SELECT COUNT(*) FROM scheduling_failed_lots")
        total_failed = cursor.fetchone()[0]
        print(f"❌ scheduling_failed_lots表总记录数: {total_failed}")
        
        if total_failed > 0:
            cursor.execute("SELECT session_id, COUNT(*) FROM scheduling_failed_lots GROUP BY session_id ORDER BY session_id DESC LIMIT 5")
            failed_sessions = cursor.fetchall()
            print("📋 最近的失败会话:")
            for session in failed_sessions:
                print(f"  - {session[0]}: {session[1]} 个失败批次")
        
        # 检查et_wait_lot表
        cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
        total_lots = cursor.fetchone()[0]
        print(f"📦 et_wait_lot表总记录数: {total_lots}")
        
        # 检查eqp_status表
        cursor.execute("SELECT COUNT(*) FROM eqp_status")
        total_eqp = cursor.fetchone()[0]
        print(f"🏭 eqp_status表总记录数: {total_eqp}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_db_status()
