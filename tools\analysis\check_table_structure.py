#!/usr/bin/env python3
"""
检查表结构
"""
import pymysql

def check_table_structure():
    """检查表结构"""
    print("🔍 检查表结构...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 检查lotprioritydone表结构
            print(f"\n📋 lotprioritydone表结构:")
            cursor.execute("DESCRIBE lotprioritydone")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col['Field']}: {col['Type']}")
            
            # 检查表中的数据
            print(f"\n📊 lotprioritydone表数据样本:")
            cursor.execute("SELECT * FROM lotprioritydone LIMIT 3")
            samples = cursor.fetchall()
            if samples:
                for i, sample in enumerate(samples, 1):
                    print(f"  样本{i}: LOT_ID={sample.get('LOT_ID', 'N/A')}, DEVICE={sample.get('DEVICE', 'N/A')}, STAGE={sample.get('STAGE', 'N/A')}")
                    # 显示所有字段
                    for key, value in sample.items():
                        if key in ['LOT_ID', 'DEVICE', 'STAGE']:
                            continue
                        print(f"    {key}={value}")
                    print()
            else:
                print("  表中没有数据")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
