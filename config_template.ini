# =================================================================
# APS排产系统配置文件
# 请将此文件重命名为 config.ini 并放在exe程序同一目录
# =================================================================

[DATABASE]
# 数据库主机地址 (请修改为您的MySQL服务器地址)
host = *************
# 数据库端口
port = 3306  
# 数据库用户名
user = root
# 数据库密码 (请修改为您的MySQL密码)
password = WWWwww123!
# 数据库名称
database = aps
# 字符集
charset = utf8mb4

[APPLICATION]
# 应用监听地址 (0.0.0.0允许外部访问，127.0.0.1仅本机)
host = 0.0.0.0
# 应用端口
port = 5000
# 调试模式 (生产环境设为False)
debug = False
# Excel数据路径 (相对路径基于exe所在目录)
excel_path = ./Excel数据2025.7.23

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
log_level = INFO
# 最大工作线程数
max_workers = 10 