"""
数据模型包 - PyInstaller完全兼容版本

重构后的模型架构，完美支持开发环境和PyInstaller运行时环境
"""

import os
import sys
import logging
import importlib.util

# 设置日志
logger = logging.getLogger(__name__)

# === PyInstaller兼容性检测 ===
def is_pyinstaller_runtime():
    """检测是否在PyInstaller运行时环境"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def get_runtime_models_path():
    """获取运行时环境下的models.py路径"""
    if is_pyinstaller_runtime():
        # PyInstaller环境：尝试多个可能的路径
        base_path = getattr(sys, '_MEIPASS', os.path.dirname(__file__))
        possible_paths = [
            os.path.join(base_path, 'app', 'models.py'),
            os.path.join(base_path, 'models.py'),
            os.path.join(os.path.dirname(__file__), '..', 'models.py'),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"✅ PyInstaller环境找到models.py: {path}")
                return path
        
        logger.warning(f"⚠️ PyInstaller环境未找到models.py，尝试路径: {possible_paths}")
        return None
    else:
        # 开发环境：使用相对路径
        current_dir = os.path.dirname(__file__)
        models_file_path = os.path.join(current_dir, '..', 'models.py')
        return os.path.abspath(models_file_path)

# === 智能占位符类系统 ===
class SmartMockQuery:
    """智能模拟SQLAlchemy Query对象 - 能够在有数据库连接时执行真实查询"""
    
    def __init__(self, model_class=None):
        self.model_class = model_class
        self._filter_conditions = {}
    
    def filter_by(self, **kwargs):
        """模拟filter_by方法，支持链式调用"""
        new_query = SmartMockQuery(self.model_class)
        new_query._filter_conditions.update(self._filter_conditions)
        new_query._filter_conditions.update(kwargs)
        return new_query
    
    def filter(self, *args, **kwargs):
        """模拟filter方法，支持位置参数和关键字参数"""
        return self
    
    def first(self):
        """智能first方法 - 尝试真实查询，失败则返回None"""
        if self.model_class and hasattr(self.model_class, '__table__'):
            try:
                # 检查是否在Flask应用上下文中
                from flask import has_app_context
                if has_app_context():
                    # 尝试执行真实查询
                    from app import db
                    if hasattr(self.model_class, 'query'):
                        real_query = self.model_class.query
                        for key, value in self._filter_conditions.items():
                            real_query = real_query.filter_by(**{key: value})
                        result = real_query.first()
                        if result:
                            logger.debug(f"✅ SmartMockQuery找到真实数据: {self.model_class.__name__}")
                            return result
            except Exception as e:
                logger.debug(f"SmartMockQuery真实查询失败: {e}")
        
        # 如果真实查询失败，返回None
        logger.debug(f"SmartMockQuery返回None: {self.model_class.__name__ if self.model_class else 'Unknown'}")
        return None
    
    def all(self):
        """智能all方法"""
        if self.model_class and hasattr(self.model_class, '__table__'):
            try:
                from flask import has_app_context
                if has_app_context():
                    from app import db
                    if hasattr(self.model_class, 'query'):
                        real_query = self.model_class.query
                        for key, value in self._filter_conditions.items():
                            real_query = real_query.filter_by(**{key: value})
                        return real_query.all()
            except Exception as e:
                logger.debug(f"SmartMockQuery.all()失败: {e}")
        
        return []
    
    def count(self):
        """智能count方法"""
        try:
            return len(self.all())
        except:
            return 0
    
    def get(self, id):
        """智能get方法"""
        if self.model_class and hasattr(self.model_class, '__table__'):
            try:
                from flask import has_app_context
                if has_app_context():
                    from app import db
                    if hasattr(self.model_class, 'query'):
                        return self.model_class.query.get(id)
            except Exception as e:
                logger.debug(f"SmartMockQuery.get()失败: {e}")
        return None

class EnhancedPlaceholderBase:
    """增强的占位符基类，提供完整的SQLAlchemy模型接口"""
    
    # 类属性模拟
    __table__ = None
    __tablename__ = 'placeholder_table'
    
    @classmethod
    def query(cls):
        """提供智能query接口"""
        return SmartMockQuery(cls)
    
    @classmethod
    def get_config(cls, key, default=None):
        """SchedulerConfig专用配置获取方法"""
        logger.debug(f"🔧 占位符{cls.__name__}.get_config({key}) -> {default}")
        return default
    
    def __init__(self, **kwargs):
        """支持初始化"""
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def save(self):
        """模拟保存方法"""
        pass
    
    def delete(self):
        """模拟删除方法"""
        pass

# === 核心传统模型导入 ===
TRADITIONAL_MODELS_AVAILABLE = False

try:
    models_file_path = get_runtime_models_path()
    
    if models_file_path and os.path.exists(models_file_path):
        # 使用独特的模块名避免冲突
        module_name = "aps_legacy_models_runtime"
        spec = importlib.util.spec_from_file_location(module_name, models_file_path)
        legacy_models = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(legacy_models)
        
        # 导入具体的模型类
        User = getattr(legacy_models, 'User', None)
        UserPermission = getattr(legacy_models, 'UserPermission', None)
        UserActionLog = getattr(legacy_models, 'UserActionLog', None)
        MenuSetting = getattr(legacy_models, 'MenuSetting', None)
        SystemSetting = getattr(legacy_models, 'SystemSetting', None)
        Settings = getattr(legacy_models, 'Settings', None)
        AISettings = getattr(legacy_models, 'AISettings', None)
        DifyConfig = getattr(legacy_models, 'DifyConfig', None)
        ET_WAIT_LOT = getattr(legacy_models, 'ET_WAIT_LOT', None)
        WIP_LOT = getattr(legacy_models, 'WIP_LOT', None)
        TestSpec = getattr(legacy_models, 'TestSpec', None)
        EQP_STATUS = getattr(legacy_models, 'EQP_STATUS', None)
        ET_FT_TEST_SPEC = getattr(legacy_models, 'ET_FT_TEST_SPEC', None)
        ET_UPH_EQP = getattr(legacy_models, 'ET_UPH_EQP', None)
        CT = getattr(legacy_models, 'CT', None)
        TCC_INV = getattr(legacy_models, 'TCC_INV', None)
        ET_RECIPE_FILE = getattr(legacy_models, 'ET_RECIPE_FILE', None)
        DevicePriorityConfig = getattr(legacy_models, 'DevicePriorityConfig', None)
        LotPriorityConfig = getattr(legacy_models, 'LotPriorityConfig', None)
        ProductPriorityConfig = getattr(legacy_models, 'ProductPriorityConfig', None)
        UserFilterPresets = getattr(legacy_models, 'UserFilterPresets', None)
        SchedulingTasks = getattr(legacy_models, 'SchedulingTasks', None)
        DatabaseInfo = getattr(legacy_models, 'DatabaseInfo', None)
        EmailConfig = getattr(legacy_models, 'EmailConfig', None)
        ExcelMapping = getattr(legacy_models, 'ExcelMapping', None)
        EmailAttachment = getattr(legacy_models, 'EmailAttachment', None)
        OrderData = getattr(legacy_models, 'OrderData', None)
        LotTypeClassificationRule = getattr(legacy_models, 'LotTypeClassificationRule', None)
        SchedulerJob = getattr(legacy_models, 'SchedulerJob', None)
        SchedulerJobLog = getattr(legacy_models, 'SchedulerJobLog', None)
        SchedulerConfig = getattr(legacy_models, 'SchedulerConfig', None)
        SchedulingConfig = getattr(legacy_models, 'SchedulingConfig', None)
        CpOrderData = getattr(legacy_models, 'CpOrderData', None)
        Product = getattr(legacy_models, 'Product', None)
        ProductionOrder = getattr(legacy_models, 'ProductionOrder', None)
        CustomerOrder = getattr(legacy_models, 'CustomerOrder', None)
        Resource = getattr(legacy_models, 'Resource', None)
        ProductionSchedule = getattr(legacy_models, 'ProductionSchedule', None)
        OrderItem = getattr(legacy_models, 'OrderItem', None)
        MaintenanceRecord = getattr(legacy_models, 'MaintenanceRecord', None)
        ResourceUsageLog = getattr(legacy_models, 'ResourceUsageLog', None)
        WIPRecord = getattr(legacy_models, 'WIPRecord', None)
        MigrationLog = getattr(legacy_models, 'MigrationLog', None)
        
        # 特殊处理SchedulerConfig，确保有get_config方法
        if SchedulerConfig and not hasattr(SchedulerConfig, 'get_config'):
            def get_config(cls, key, default=None):
                """获取调度器配置值"""
                try:
                    # 检查是否在Flask应用上下文中
                    try:
                        from flask import has_app_context
                        if not has_app_context():
                            logger.debug(f"SchedulerConfig.get_config: 无应用上下文，返回默认值")
                            return default
                    except ImportError:
                        pass
                    
                    from app import db
                    config = cls.query.filter_by(key=key).first()
                    return config.value if config else default
                except Exception as e:
                    logger.debug(f"SchedulerConfig.get_config异常: {e}")
                    return default
            
            # 将方法绑定到类
            SchedulerConfig.get_config = classmethod(get_config)
        
        # 验证关键模型是否成功加载
        loaded_models = []
        for model_name in ['User', 'ET_WAIT_LOT', 'WIP_LOT', 'TestSpec']:
            model_class = locals().get(model_name)
            if model_class and hasattr(model_class, '__table__'):
                loaded_models.append(model_name)
        
        if len(loaded_models) >= 3:
            TRADITIONAL_MODELS_AVAILABLE = True
            if os.environ.get('FLASK_QUIET_STARTUP') != '1':
                runtime_type = "PyInstaller运行时" if is_pyinstaller_runtime() else "开发环境"
                logger.info(f"✅ {runtime_type}模型导入成功，加载了{len(loaded_models)}个核心模型")
                logger.info(f"✅ 智能占位符系统已激活，支持登录功能")
        else:
            raise ImportError(f"关键模型加载不足，仅加载了: {loaded_models}")
            
    else:
        raise ImportError(f"models.py文件不存在: {models_file_path}")
        
except Exception as e:
    logger.error(f"❌ 传统模型导入失败: {e}")
    logger.info(f"🔄 使用智能占位符系统，保持基本功能")

# === 统一模型导入 ===
UNIFIED_MODELS_AVAILABLE = False

try:
    from app.models.unified.unified_lot_model import UnifiedLotModel
    from app.models.unified.unified_test_spec import UnifiedTestSpec
    from app.models.unified.migration_services import (
        LotDataMigrationService,
        TestSpecMigrationService, 
        DataMigrationManager
    )
    
    # 导入数据库配置模型
    from app.models.system.database_config import DatabaseConfig, DatabaseMapping
    from app.models.system.stage_mapping import StageMappingConfig
    
    UNIFIED_MODELS_AVAILABLE = True
    
    if os.environ.get('FLASK_QUIET_STARTUP') != '1':
        logger.info("✅ 统一模型静态导入成功")
        
except ImportError as e:
    logger.warning(f"⚠️ 统一模型导入失败: {e}")
    UNIFIED_MODELS_AVAILABLE = False
    
    # 创建占位符类
    class UnifiedLotModel(EnhancedPlaceholderBase): 
        __tablename__ = 'unified_lot_model'
    
    class UnifiedTestSpec(EnhancedPlaceholderBase): 
        __tablename__ = 'unified_test_spec'
    
    class LotDataMigrationService(EnhancedPlaceholderBase): 
        __tablename__ = 'lot_data_migration_service'
    
    class TestSpecMigrationService(EnhancedPlaceholderBase): 
        __tablename__ = 'test_spec_migration_service'
    
    class DataMigrationManager(EnhancedPlaceholderBase): 
        __tablename__ = 'data_migration_manager'
    
    class DatabaseConfig(EnhancedPlaceholderBase): 
        __tablename__ = 'database_config'
    
    class DatabaseMapping(EnhancedPlaceholderBase): 
        __tablename__ = 'database_mapping'
    
    class StageMappingConfig(EnhancedPlaceholderBase): 
        __tablename__ = 'stage_mapping_config'

# === 模型验证函数 ===
def validate_models():
    """验证所有模型是否正确加载"""
    core_models = [
        'User', 'UserPermission', 'UserActionLog', 'MenuSetting',
        'ET_WAIT_LOT', 'WIP_LOT', 'TestSpec', 'EQP_STATUS'
    ]
    
    missing_models = []
    working_models = []
    
    for model_name in core_models:
        model_obj = globals().get(model_name, None)
        if not model_obj:
            missing_models.append(model_name)
        elif hasattr(model_obj, '__table__') or hasattr(model_obj, '__tablename__'):
            working_models.append(model_name)
        else:
            missing_models.append(model_name)
    
    if missing_models and os.environ.get('FLASK_QUIET_STARTUP') != '1':
        logger.warning(f"⚠️ 以下模型可能未正确加载: {missing_models}")
    
    if working_models and os.environ.get('FLASK_QUIET_STARTUP') != '1':
        logger.info(f"✅ 已验证模型正常: {working_models}")
    
    return len(missing_models) == 0

# 执行验证
if os.environ.get('FLASK_QUIET_STARTUP') != '1':
    is_valid = validate_models()
    runtime_info = f"PyInstaller运行时" if is_pyinstaller_runtime() else "开发环境"
    if is_valid:
        logger.info(f"✅ {runtime_info}所有核心模型验证通过")
    else:
        logger.warning(f"⚠️ {runtime_info}部分模型验证未通过，但系统可继续运行")

# === 导出列表 ===
__all__ = [
    # 标志和工具
    'UNIFIED_MODELS_AVAILABLE', 'TRADITIONAL_MODELS_AVAILABLE',
    'is_pyinstaller_runtime', 'validate_models',
    
    # 核心用户模型
    'User', 'UserPermission', 'UserActionLog', 'MenuSetting',
    
    # 系统配置
    'SystemSetting', 'Settings', 'AISettings', 'DifyConfig',
    
    # 生产核心模型
    'ET_WAIT_LOT', 'WIP_LOT', 'TestSpec', 'EQP_STATUS', 
    'ET_FT_TEST_SPEC', 'ET_UPH_EQP', 'CT', 'TCC_INV', 'ET_RECIPE_FILE',
    
    # 配置模型
    'DevicePriorityConfig', 'LotPriorityConfig', 'ProductPriorityConfig',
    'UserFilterPresets', 'SchedulingTasks', 'DatabaseInfo',
    
    # 邮箱和任务
    'EmailConfig', 'ExcelMapping', 'EmailAttachment', 'OrderData',
    'LotTypeClassificationRule', 'SchedulerJob', 'SchedulerJobLog',
    'SchedulerConfig', 'SchedulingConfig', 'CpOrderData',
    
    # 业务模型
    'Product', 'ProductionOrder', 'CustomerOrder', 'Resource',
    'ProductionSchedule', 'OrderItem', 'MaintenanceRecord',
    'ResourceUsageLog', 'WIPRecord', 'MigrationLog',
    
    # 统一模型
    'UnifiedLotModel', 'UnifiedTestSpec',
    'LotDataMigrationService', 'TestSpecMigrationService', 'DataMigrationManager',
    
    # 数据库配置模型
    'DatabaseConfig', 'DatabaseMapping', 'StageMappingConfig'
] 