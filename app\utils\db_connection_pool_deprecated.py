#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚠️ DEPRECATED - 已被统一连接管理器取代
原APS平台数据库连接池管理器 - 现已停用

新的统一连接管理器位于: app/utils/unified_connection_manager.py
请使用新的接口：
- from app.utils.unified_connection_manager import get_db_connection, ConnectionPriority
- with get_db_connection(ConnectionPriority.HIGH) as session: ...

此文件保留用于向后兼容和迁移参考。
"""

import warnings
import logging

logger = logging.getLogger(__name__)

class DatabaseConnectionPool:
    """
    ⚠️ DEPRECATED 类 - 请使用 unified_connection_manager
    """
    
    def __init__(self):
        warnings.warn(
            "DatabaseConnectionPool已被弃用，请使用unified_connection_manager.get_db_connection()",
            DeprecationWarning,
            stacklevel=2
        )
        logger.warning("⚠️ DatabaseConnectionPool已被弃用，请迁移到统一连接管理器")
        
        # 导入新的管理器作为后备
        try:
            from app.utils.unified_connection_manager import unified_connection_manager, ConnectionPriority
            self._new_manager = unified_connection_manager
            self._priority = ConnectionPriority.NORMAL
            logger.info("✅ 已自动切换到统一连接管理器")
        except ImportError as e:
            logger.error(f"❌ 无法导入统一连接管理器: {e}")
            raise
    
    def get_connection(self):
        """兼容性方法 - 重定向到新管理器"""
        warnings.warn("请使用 get_db_connection() 替代", DeprecationWarning)
        return self._new_manager.get_connection(self._priority)
    
    def get_pool_status(self):
        """兼容性方法 - 重定向到新管理器"""
        warnings.warn("请使用 get_connection_status() 替代", DeprecationWarning)
        return self._new_manager.get_pool_status()

# 保留原有导入兼容性
def get_connection_pool():
    """兼容性函数 - 返回弃用警告"""
    warnings.warn(
        "get_connection_pool()已被弃用，请使用unified_connection_manager",
        DeprecationWarning,
        stacklevel=2
    )
    return DatabaseConnectionPool()

logger.info("📦 db_connection_pool模块已标记为弃用，请迁移到unified_connection_manager")