"""
Excel自动保存配置API
"""
import os
from flask import Blueprint, request, jsonify
from app.decorators import login_required
from app.services.excel_auto_save_service import get_excel_auto_save_service
import logging

logger = logging.getLogger(__name__)

excel_auto_save_bp = Blueprint('excel_auto_save', __name__)

@excel_auto_save_bp.route('/api/v2/system/excel-auto-save/config', methods=['GET'])
@login_required
def get_excel_auto_save_config():
    """获取Excel自动保存配置"""
    try:
        service = get_excel_auto_save_service()
        config = service.get_config()
        
        return jsonify({
            'success': True,
            'data': config
        })
        
    except Exception as e:
        logger.error(f"获取Excel自动保存配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取配置失败: {str(e)}'
        }), 500

@excel_auto_save_bp.route('/api/v2/system/excel-auto-save/config', methods=['POST'])
@login_required
def update_excel_auto_save_config():
    """更新Excel自动保存配置"""
    try:
        data = request.get_json()
        
        enabled = data.get('auto_save_enabled', False)
        save_path = data.get('save_path', '')
        filename_pattern = data.get('filename_pattern', '排产结果_{timestamp}.xlsx')
        overwrite_mode = data.get('overwrite_mode', 'timestamp')
        max_backup_files = data.get('max_backup_files', 10)
        
        # 验证保存路径
        if enabled and save_path:
            if not os.path.exists(save_path):
                try:
                    os.makedirs(save_path, exist_ok=True)
                except Exception as e:
                    return jsonify({
                        'success': False,
                        'message': f'无法创建保存路径: {str(e)}'
                    }), 400
            
            # 测试路径是否可写
            try:
                test_file = os.path.join(save_path, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'保存路径不可写: {str(e)}'
                }), 400
        
        service = get_excel_auto_save_service()
        
        # 更新配置
        config = service.get_config()
        config.update({
            'auto_save_enabled': enabled,
            'save_path': save_path,
            'filename_pattern': filename_pattern,
            'overwrite_mode': overwrite_mode,
            'max_backup_files': max_backup_files
        })
        service.save_config(config)
        
        logger.info(f"Excel自动保存配置已更新: enabled={enabled}, path={save_path}")
        
        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新Excel自动保存配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新配置失败: {str(e)}'
        }), 500

@excel_auto_save_bp.route('/api/v2/system/excel-auto-save/test-path', methods=['POST'])
@login_required
def test_save_path():
    """测试保存路径是否有效"""
    try:
        data = request.get_json()
        save_path = data.get('save_path', '')
        
        if not save_path:
            return jsonify({
                'success': False,
                'message': '保存路径不能为空'
            }), 400
        
        # 检查路径是否存在
        if not os.path.exists(save_path):
            try:
                os.makedirs(save_path, exist_ok=True)
                created = True
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'无法创建路径: {str(e)}'
                }), 400
        else:
            created = False
        
        # 测试写入权限
        try:
            test_file = os.path.join(save_path, 'test_write.tmp')
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write('测试写入权限')
            os.remove(test_file)
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'路径不可写: {str(e)}'
            }), 400
        
        return jsonify({
            'success': True,
            'message': '路径有效' + (' (已创建)' if created else ''),
            'path_info': {
                'exists': os.path.exists(save_path),
                'writable': True,
                'created': created
            }
        })
        
    except Exception as e:
        logger.error(f"测试保存路径失败: {e}")
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500