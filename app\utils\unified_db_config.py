#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库配置管理器
支持开发环境和exe环境的灵活配置
"""

import os
import sys
import logging
from typing import Dict, Any, Optional
import configparser

logger = logging.getLogger(__name__)

class UnifiedDatabaseConfig:
    """统一数据库配置管理器"""
    
    def __init__(self):
        self.config = None
        self.config_loaded = False
        self._config_cache_time = 0
        self._config_cache_ttl = 300  # 配置缓存5分钟
        self._load_config()
    
    def _get_config_path(self) -> str:
        """获取配置文件路径，支持开发和exe环境"""
        possible_paths = []
        
        # 如果是打包后的exe环境
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            possible_paths.extend([
                os.path.join(exe_dir, 'config.ini'),
                os.path.join(exe_dir, '..', 'config.ini'),
                os.path.join(os.getcwd(), 'config.ini')
            ])
        
        # 开发环境路径
        current_dir = os.getcwd()
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        possible_paths.extend([
            os.path.join(current_dir, 'config.ini'),
            os.path.join(project_root, 'config.ini'),
            'config.ini'
        ])
        
        # 查找第一个存在的配置文件
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"✅ 找到配置文件: {path}")
                return path
        
        logger.warning(f"⚠️ 未找到config.ini文件，查找路径: {possible_paths}")
        return None
    
    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = self._get_config_path()
            if not config_path:
                logger.warning("⚠️ 配置文件不存在，将使用默认配置")
                return
            
            self.config = configparser.ConfigParser()
            self.config.read(config_path, encoding='utf-8')
            self.config_loaded = True
            logger.info(f"✅ 配置文件加载成功: {config_path}")
            
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            self.config_loaded = False
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置 - 带缓存优化"""
        import time
        current_time = time.time()
        
        # 检查配置是否需要重新加载
        if (not self.config_loaded or not self.config or 
            current_time - self._config_cache_time > self._config_cache_ttl):
            self._load_config()
            self._config_cache_time = current_time
        
        if not self.config_loaded or not self.config:
            return self._get_default_config()
        
        try:
            if self.config.has_section('DATABASE'):
                db_config = {
                    'host': self.config.get('DATABASE', 'host', fallback='localhost'),
                    'port': self.config.getint('DATABASE', 'port', fallback=3306),
                    'user': self.config.get('DATABASE', 'username', fallback='root'),  # 修复：使用'username'匹配config.ini
                    'password': self.config.get('DATABASE', 'password', fallback=''),
                    'database': self.config.get('DATABASE', 'database', fallback='aps'),
                    'charset': self.config.get('DATABASE', 'charset', fallback='utf8mb4')
                }
                
                # 验证必要的配置项
                if not db_config['password']:
                    logger.error("❌ 数据库密码未配置")
                    return self._get_default_config()
                
                logger.info(f"✅ 数据库配置读取成功: {db_config['host']}:{db_config['port']}/{db_config['database']}")
                return db_config
                
        except Exception as e:
            logger.error(f"❌ 读取数据库配置失败: {e}")
        
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置（仅用于开发环境降级）"""
        if getattr(sys, 'frozen', False):
            # exe环境严格要求配置文件
            raise Exception(
                "❌ 生产环境必须提供config.ini配置文件！\n"
                "请确保config.ini文件与exe文件在同一目录下，并正确配置数据库连接信息。\n"
                "可以运行database_config_wizard.bat配置向导来设置数据库连接。"
            )
        
        # 开发环境提供默认配置
        logger.warning("⚠️ 使用开发环境默认配置 - 仅适用于本地开发")
        return {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
    
    def get_pymysql_config(self, database: Optional[str] = None) -> Dict[str, Any]:
        """获取PyMySQL连接配置"""
        config = self.get_database_config()
        
        pymysql_config = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'charset': config['charset'],
            'autocommit': True
        }
        
        if database:
            pymysql_config['database'] = database
        else:
            pymysql_config['database'] = config['database']
        
        return pymysql_config
    
    def get_sqlalchemy_uri(self) -> str:
        """获取SQLAlchemy数据库URI"""
        config = self.get_database_config()
        return (
            f"mysql+pymysql://{config['user']}:{config['password']}"
            f"@{config['host']}:{config['port']}/{config['database']}"
            f"?charset={config['charset']}"
        )
    
    def validate_connection(self) -> bool:
        """验证数据库连接"""
        try:
            import pymysql
            config = self.get_pymysql_config()
            
            # 尝试连接数据库
            connection = pymysql.connect(**config)
            connection.close()
            logger.info("✅ 数据库连接验证成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接验证失败: {e}")
            return False


# 全局实例
_unified_db_config = None

def get_unified_db_config() -> UnifiedDatabaseConfig:
    """获取统一数据库配置实例（单例模式）"""
    global _unified_db_config
    if _unified_db_config is None:
        _unified_db_config = UnifiedDatabaseConfig()
    return _unified_db_config

def get_database_config() -> Dict[str, Any]:
    """快捷方法：获取数据库配置"""
    return get_unified_db_config().get_database_config()

def get_pymysql_config(database: Optional[str] = None) -> Dict[str, Any]:
    """快捷方法：获取PyMySQL配置"""
    return get_unified_db_config().get_pymysql_config(database)

def get_sqlalchemy_uri() -> str:
    """快捷方法：获取SQLAlchemy URI"""
    return get_unified_db_config().get_sqlalchemy_uri()

def validate_database_connection() -> bool:
    """快捷方法：验证数据库连接"""
    return get_unified_db_config().validate_connection() 