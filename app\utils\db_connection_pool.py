#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS平台 - 高性能数据库连接池管理器
支持100+用户并发，具备动态扩容和智能监控功能
"""

import pymysql
import logging
import threading
import time
import os
from typing import Dict, Any, Optional
from contextlib import contextmanager
from queue import Queue, Empty
from config.aps_config import config

logger = logging.getLogger(__name__)

class DatabaseConnectionPool:
    """高性能数据库连接池管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config = None
        self._config_loaded_time = 0
        self._config_ttl = 300  # 配置缓存5分钟
        
        # 动态连接池配置 - 策略A：服务工具脚本，配置从aps_config统一管理
        from config.aps_config import config
        self._min_pool_size = config.CUSTOM_POOL_MIN_SIZE  # 最小连接数
        self._pool_size = config.CUSTOM_POOL_INIT_SIZE  # 初始连接数  
        self._max_connections = config.CUSTOM_POOL_MAX_SIZE  # 最大连接数
        self._connection_timeout = config.DB_POOL_TIMEOUT  # 连接超时
        self._pool_timeout = 10  # 获取连接超时
        
        # 动态扩容配置
        self._scale_up_threshold = 0.8  # 80%使用率时扩容
        self._scale_down_threshold = 0.3  # 30%使用率时缩容
        self._scale_check_interval = 30  # 30秒检查一次
        self._last_scale_check = 0

        # 连接健康检查配置 - 🔥 用户建议的连接生命周期管理
        self._connection_max_age = config.CONNECTION_MAX_IDLE      # 连接最大存活时间(用户建议：30分钟)
        self._idle_timeout = config.DB_IDLE_TIMEOUT               # 空闲连接超时(用户建议：30分钟)
        self._health_check_interval = 60                          # 健康检查间隔1分钟
        self._last_health_check = 0
        self._session_timeout = config.SESSION_CONNECTION_TIMEOUT  # 会话连接超时(用户建议：1小时)

        # 连接池监控配置
        self._monitor_enabled = True
        self._alert_threshold = 0.9          # 90%使用率告警
        self._connection_creation_times = {} # 连接创建时间记录
        
        # 连接池字典：{database_name: Queue}
        self._pools: Dict[str, Queue] = {}
        self._pool_locks: Dict[str, threading.Lock] = {}
        self._connection_counts: Dict[str, int] = {}
        self._active_connections: Dict[str, int] = {}  # 活跃连接数
        
        # 连接健康检查
        self._health_check_interval = 300  # 5分钟检查一次
        self._last_health_check = 0
        self._unhealthy_connections = set()
        
        # 统计信息
        self._stats = {
            'connections_created': 0,
            'connections_reused': 0,
            'connections_scaled_up': 0,
            'connections_scaled_down': 0,
            'config_cache_hits': 0,
            'config_cache_misses': 0,
            'pool_timeouts': 0,
            'connection_errors': 0,
            'health_checks_performed': 0,
            'unhealthy_connections_removed': 0
        }
        
        # 🔥 批量处理连接池优化 - 用户建议的关键优化
        self._batch_connections: Dict[str, Dict] = {}  # 批量处理专用连接
        self._batch_locks: Dict[str, threading.Lock] = {}  # 批量处理锁
        
        # 🔥 进程连接配额系统 - 防止单进程连接消耗过多
        self._process_connections: Dict[int, int] = {}  # {进程ID: 使用连接数}
        self._process_quotas: Dict[str, int] = {        # 进程类型配额限制
            'main_app': 15,       # 主应用进程配额
            'scheduler': 8,       # 调度器进程配额  
            'tool_script': 5,     # 工具脚本配额
            'monitoring': 3,      # 监控脚本配额
            'test': 10,          # 测试脚本配额
            'default': 3         # 默认进程配额
        }
        self._process_types: Dict[int, str] = {}        # 进程类型标识
        self._process_quota_lock = threading.Lock()     # 配额操作锁
        
        # 启动监控线程
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        
        logger.info(f"🔄 高性能数据库连接池已初始化 - 初始:{self._pool_size}, 最大:{self._max_connections}")
    
    def _monitoring_loop(self):
        """连接池监控循环"""
        while True:
            try:
                current_time = time.time()
                
                # 动态扩缩容检查
                if current_time - self._last_scale_check > self._scale_check_interval:
                    self._check_and_scale_pools()
                    self._last_scale_check = current_time
                
                # 健康检查
                if current_time - self._last_health_check > self._health_check_interval:
                    self._perform_health_check()
                    # 🔥 集成新的连接池健康维护
                    for database in list(self._pools.keys()):
                        self.maintain_pool_health(database)
                    self._last_health_check = current_time
                
                # 🔥 批量处理连接清理 - 用户建议的优化策略
                self.cleanup_idle_batch_connections()
                
                # 🔥 进程连接配额监控
                self._monitor_process_quotas()
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 连接池监控异常: {e}")
                time.sleep(30)  # 异常时等待30秒
    
    def _check_and_scale_pools(self):
        """检查并动态调整连接池大小"""
        for database in list(self._pools.keys()):
            try:
                pool = self._pools[database]
                active = self._active_connections.get(database, 0)
                total = self._connection_counts.get(database, 0)
                
                if total == 0:
                    continue
                
                usage_rate = active / total
                
                # 扩容检查
                if usage_rate > self._scale_up_threshold and total < self._max_connections:
                    scale_up_count = min(10, self._max_connections - total)  # 每次最多扩容10个
                    self._scale_up_pool(database, scale_up_count)
                
                # 缩容检查
                elif usage_rate < self._scale_down_threshold and total > self._min_pool_size:
                    scale_down_count = min(5, total - self._min_pool_size)  # 每次最多缩容5个
                    self._scale_down_pool(database, scale_down_count)
                    
            except Exception as e:
                logger.error(f"❌ 连接池扩缩容检查失败 {database}: {e}")
    
    def _scale_up_pool(self, database: str, count: int):
        """扩容连接池"""
        try:
            with self._pool_locks[database]:
                for _ in range(count):
                    if self._connection_counts[database] >= self._max_connections:
                        break
                    
                    connection = self._create_connection(database)
                    self._pools[database].put_nowait(connection)
                    self._connection_counts[database] += 1
                    self._stats['connections_scaled_up'] += 1
                
                logger.info(f"📈 连接池扩容: {database} +{count} (总计:{self._connection_counts[database]})")
                
        except Exception as e:
            logger.error(f"❌ 连接池扩容失败 {database}: {e}")
    
    def _scale_down_pool(self, database: str, count: int):
        """缩容连接池"""
        try:
            with self._pool_locks[database]:
                removed = 0
                for _ in range(count):
                    try:
                        connection = self._pools[database].get_nowait()
                        connection.close()
                        self._connection_counts[database] -= 1
                        removed += 1
                        self._stats['connections_scaled_down'] += 1
                    except Empty:
                        break
                
                if removed > 0:
                    logger.info(f"📉 连接池缩容: {database} -{removed} (总计:{self._connection_counts[database]})")
                    
        except Exception as e:
            logger.error(f"❌ 连接池缩容失败 {database}: {e}")
    
    def _perform_health_check(self):
        """执行连接健康检查"""
        for database in list(self._pools.keys()):
            try:
                pool = self._pools[database]
                unhealthy_count = 0
                healthy_connections = []
                
                # 检查池中的连接
                temp_connections = []
                while not pool.empty():
                    try:
                        connection = pool.get_nowait()
                        temp_connections.append(connection)
                    except Empty:
                        break
                
                # 验证连接健康状态
                for connection in temp_connections:
                    try:
                        connection.ping(reconnect=False)
                        healthy_connections.append(connection)
                    except:
                        unhealthy_count += 1
                        try:
                            connection.close()
                        except:
                            pass
                        self._connection_counts[database] -= 1
                        self._stats['unhealthy_connections_removed'] += 1
                
                # 将健康连接放回池中
                for connection in healthy_connections:
                    try:
                        pool.put_nowait(connection)
                    except:
                        try:
                            connection.close()
                        except:
                            pass
                        self._connection_counts[database] -= 1
                
                if unhealthy_count > 0:
                    logger.warning(f"🏥 健康检查: {database} 移除{unhealthy_count}个不健康连接")
                
                self._stats['health_checks_performed'] += 1
                
            except Exception as e:
                logger.error(f"❌ 健康检查失败 {database}: {e}")

    def _monitor_process_quotas(self):
        """
        🔥 监控进程连接配额使用情况
        检测配额违规并清理僵尸进程配额
        """
        try:
            quota_stats = self.get_process_quota_stats()
            violations = quota_stats.get('quota_violations', 0)
            
            if violations > 0:
                logger.warning(f"⚠️ 发现 {violations} 个进程超出连接配额")
            
            # 清理僵尸进程配额（进程已不存在但配额仍占用）
            import psutil
            current_pids = set(psutil.pids())
            
            with self._process_quota_lock:
                zombie_pids = []
                for pid in list(self._process_connections.keys()):
                    if pid not in current_pids:
                        zombie_pids.append(pid)
                
                for zombie_pid in zombie_pids:
                    released_connections = self._process_connections.get(zombie_pid, 0)
                    if released_connections > 0:
                        logger.info(f"🧹 清理僵尸进程配额: PID={zombie_pid}, 释放{released_connections}个连接配额")
                    
                    # 清理僵尸进程数据
                    self._process_connections.pop(zombie_pid, None)
                    self._process_types.pop(zombie_pid, None)
                
                if zombie_pids:
                    logger.info(f"🧹 清理了 {len(zombie_pids)} 个僵尸进程的连接配额")
            
        except Exception as e:
            logger.error(f"❌ 进程配额监控失败: {e}")

    def _get_cached_config(self) -> Dict[str, Any]:
        """获取缓存的数据库配置"""
        current_time = time.time()
        
        if (self._config is None or 
            current_time - self._config_loaded_time > self._config_ttl):
            
            # 配置过期或首次加载，重新读取
            try:
                self._config = {
            'host': config.DB_HOST,
            'port': config.DB_PORT,
            'user': config.DB_USER,
            'password': config.DB_PASSWORD,
            'database': config.DB_NAME,
            'charset': config.DB_CHARSET
        }
                self._config_loaded_time = current_time
                self._stats['config_cache_misses'] += 1
                logger.debug("🔄 重新加载数据库配置")
            except Exception as e:
                logger.error(f"❌ 配置加载失败: {e}")
                raise
        else:
            self._stats['config_cache_hits'] += 1
        
        return self._config
    
    def _create_connection(self, database: str = 'aps') -> pymysql.Connection:
        """创建新的数据库连接"""
        config = self._get_cached_config()
        
        conn_params = {
            'host': config['host'],
            'port': config['port'],
            'user': config['user'],
            'password': config['password'],
            'database': database,
            'charset': config['charset'],
            'autocommit': False,
            'connect_timeout': self._connection_timeout,
            'read_timeout': 60,  # 读取超时60秒
            'write_timeout': 60,  # 写入超时60秒
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        try:
            connection = pymysql.connect(**conn_params)
            self._stats['connections_created'] += 1
            logger.debug(f"✅ 新建数据库连接: {config['host']}:{config['port']}/{database}")
            return connection
        except Exception as e:
            self._stats['connection_errors'] += 1
            logger.error(f"❌ 数据库连接创建失败: {e}")
            raise
    
    def _get_pool(self, database: str) -> Queue:
        """获取指定数据库的连接池"""
        if database not in self._pools:
            with self._lock:
                if database not in self._pools:
                    self._pools[database] = Queue(maxsize=self._max_connections)
                    self._pool_locks[database] = threading.Lock()
                    self._connection_counts[database] = 0
                    self._active_connections[database] = 0
                    
                    # 预创建初始连接
                    for _ in range(self._min_pool_size):
                        try:
                            connection = self._create_connection(database)
                            self._pools[database].put_nowait(connection)
                            self._connection_counts[database] += 1
                        except Exception as e:
                            logger.error(f"❌ 预创建连接失败 {database}: {e}")
                            break
                    
                    logger.info(f"🔄 为数据库 '{database}' 创建连接池，预创建 {self._connection_counts[database]} 个连接")
        
        return self._pools[database]

    def get_connection(self, database: str = 'aps') -> pymysql.Connection:
        """从连接池获取数据库连接（高性能版本）"""
        # 执行连接池监控
        self.monitor_connection_pool()
        
        # 🔥 进程配额检查 - 防止单进程连接过多
        import os
        current_pid = os.getpid()
        
        # 自动注册未知进程为工具脚本类型
        if current_pid not in self._process_types:
            self.register_process_type('tool_script')

        pool = self._get_pool(database)
        connection = None
        
        # 尝试从池中获取连接
        try:
            connection = pool.get_nowait()
            
            # 快速健康检查
            try:
                connection.ping(reconnect=False)
                self._active_connections[database] += 1
                self._stats['connections_reused'] += 1
                logger.debug(f"♻️ 复用数据库连接: {database}")
                return connection
            except:
                # 🔥 连接失效，执行智能替换而不是简单关闭
                logger.debug(f"🔧 检测到失效连接，执行获取时替换: {database}")
                self._replace_invalid_connection_during_get(connection, database)
                connection = None  # 标记需要获取新连接
                
        except Empty:
            # 池中没有可用连接
            pass
        
        # 需要创建新连接
        with self._pool_locks[database]:
            # 🔥 进程配额限制检查 - 优先保护系统稳定性
            if not self._check_process_quota(current_pid):
                # 进程配额耗尽，尝试等待现有连接
                try:
                    connection = pool.get(timeout=self._pool_timeout)
                    connection.ping(reconnect=False)
                    self._active_connections[database] += 1
                    self._stats['connections_reused'] += 1
                    return connection
                except (Empty, Exception):
                    process_type = self._process_types.get(current_pid, 'unknown')
                    quota = self._get_process_quota(current_pid)
                    current_usage = self._process_connections.get(current_pid, 0)
                    raise Exception(f"🔥 进程连接配额限制: PID={current_pid}, 类型={process_type}, 配额={current_usage}/{quota}")
            
            if self._connection_counts[database] >= self._max_connections:
                # 等待可用连接
                try:
                    connection = pool.get(timeout=self._pool_timeout)
                    connection.ping(reconnect=False)
                    self._active_connections[database] += 1
                    self._stats['connections_reused'] += 1
                    return connection
                except (Empty, Exception) as e:
                    self._stats['pool_timeouts'] += 1
                    raise Exception(f"数据库连接池已满，无法获取新连接: {database} (活跃:{self._active_connections[database]}, 总数:{self._connection_counts[database]})")
            
            # 创建新连接并分配配额
            connection = self._create_connection(database)
            self._connection_counts[database] += 1
            self._active_connections[database] += 1
            self._allocate_connection_to_process(current_pid)  # 🔥 配额分配
            return connection

    def return_connection(self, connection: pymysql.Connection, database: str = 'aps'):
        """将连接归还到连接池（释放后回收优化版本）"""
        if connection is None:
            return
        
        try:
            # 减少活跃连接计数
            if database in self._active_connections:
                self._active_connections[database] = max(0, self._active_connections[database] - 1)
            
            # 🔥 释放进程连接配额
            import os
            current_pid = os.getpid()
            self._deallocate_connection_from_process(current_pid)
            
            pool = self._get_pool(database)
            
            # 🔥 核心改进：连接状态验证与自动替换
            try:
                # 检查连接是否还有效
                connection.ping(reconnect=False)
                
                # 清理事务状态，确保连接干净可复用
                try:
                    connection.rollback()  # 确保事务清洁
                except:
                    pass
                
                # 立即回收到池中
                try:
                    pool.put_nowait(connection)
                    self._stats['connections_recycled'] = self._stats.get('connections_recycled', 0) + 1
                    logger.debug(f"♻️ 连接已成功回收: {database}")
                    return
                except:
                    # 池已满，智能处理：检查是否需要减少连接数
                    self._handle_pool_overflow(connection, database)
                    return
                    
            except Exception as ping_error:
                # 🔥 连接失效，执行智能替换而不是简单关闭
                logger.debug(f"🔧 连接失效，执行自动替换: {database} - {ping_error}")
                self._replace_invalid_connection(connection, database)
                return
                
        except Exception as e:
            logger.warning(f"⚠️ 连接归还异常，执行应急处理: {database} - {e}")
            self._emergency_connection_cleanup(connection, database)

    def _handle_pool_overflow(self, connection: pymysql.Connection, database: str):
        """处理连接池溢出情况（智能连接数管理）"""
        try:
            # 检查当前连接数是否超过理想范围
            current_count = self._connection_counts.get(database, 0)
            min_size = self._min_pool_size
            
            if current_count > min_size * 1.5:  # 如果连接数超过最小值的1.5倍
                # 销毁多余连接
                connection.close()
                with self._pool_locks[database]:
                    self._connection_counts[database] -= 1
                self._stats['excess_connections_destroyed'] = self._stats.get('excess_connections_destroyed', 0) + 1
                logger.debug(f"🗑️ 销毁多余连接，优化池大小: {database} ({current_count}->{self._connection_counts[database]})")
            else:
                # 连接数合理，但池满了，强制放入（替换最老的连接）
                try:
                    old_connection = pool.get_nowait()  # 取出一个旧连接
                    old_connection.close()  # 关闭旧连接
                    pool.put_nowait(connection)  # 放入新连接
                    logger.debug(f"🔄 替换池中最老连接: {database}")
                except:
                    # 实在无法处理，关闭连接
                    connection.close()
                    with self._pool_locks[database]:
                        self._connection_counts[database] -= 1
        except Exception as e:
            logger.error(f"❌ 处理池溢出失败: {database} - {e}")
            connection.close()
    
    def _replace_invalid_connection(self, invalid_connection: pymysql.Connection, database: str):
        """替换失效连接（核心回收机制）"""
        try:
            # 关闭失效连接
            try:
                invalid_connection.close()
            except:
                pass
            
            pool = self._get_pool(database)
            
            # 🔥 立即创建新连接进行替换，确保池大小不减少
            try:
                new_connection = self._create_connection(database)
                pool.put_nowait(new_connection)
                self._stats['connections_replaced'] = self._stats.get('connections_replaced', 0) + 1
                logger.debug(f"🔧 失效连接已替换为新连接: {database}")
                
            except Exception as create_error:
                # 无法创建新连接，减少计数
                with self._pool_locks[database]:
                    self._connection_counts[database] -= 1
                logger.warning(f"⚠️ 无法替换失效连接: {database} - {create_error}")
                
        except Exception as e:
            logger.error(f"❌ 连接替换失败: {database} - {e}")
    
    def _emergency_connection_cleanup(self, connection: pymysql.Connection, database: str):
        """应急连接清理"""
        try:
            # 强制关闭连接
            try:
                connection.close()
            except:
                pass
            
            # 减少连接计数
            with self._pool_locks[database]:
                if self._connection_counts[database] > 0:
                    self._connection_counts[database] -= 1
            
            # 🔥 释放进程连接配额（应急情况）
            import os
            current_pid = os.getpid()
            self._deallocate_connection_from_process(current_pid)
            
            logger.debug(f"🚨 应急清理完成: {database}")
            
        except Exception as e:
            logger.error(f"❌ 应急清理失败: {database} - {e}")
    
    def _replace_invalid_connection_during_get(self, invalid_connection: pymysql.Connection, database: str):
        """获取连接时替换失效连接"""
        try:
            # 关闭失效连接
            try:
                invalid_connection.close()
            except:
                pass
            
            # 🔥 立即创建新连接并放回池中，维持连接数不变
            try:
                new_connection = self._create_connection(database)
                pool = self._get_pool(database)
                pool.put_nowait(new_connection)
                self._stats['connections_replaced_during_get'] = self._stats.get('connections_replaced_during_get', 0) + 1
                logger.debug(f"🔧 获取时连接替换成功: {database}")
                
            except Exception as create_error:
                # 无法创建新连接，减少计数
                with self._pool_locks[database]:
                    self._connection_counts[database] -= 1
                logger.warning(f"⚠️ 获取时无法替换失效连接: {database} - {create_error}")
                
        except Exception as e:
            logger.error(f"❌ 获取时连接替换失败: {database} - {e}")

    def maintain_pool_health(self, database: str = 'aps'):
        """主动维护连接池健康状态（释放后回收核心算法）"""
        try:
            pool = self._get_pool(database)
            current_count = self._connection_counts.get(database, 0)
            min_size = self._min_pool_size
            
            # 🔥 确保连接池有足够的连接
            if current_count < min_size:
                needed = min_size - current_count
                logger.info(f"🔧 连接池低于最小值，补充{needed}个连接: {database}")
                
                for _ in range(needed):
                    try:
                        new_connection = self._create_connection(database)
                        pool.put_nowait(new_connection)
                        with self._pool_locks[database]:
                            self._connection_counts[database] += 1
                        self._stats['connections_auto_created'] = self._stats.get('connections_auto_created', 0) + 1
                    except Exception as e:
                        logger.warning(f"⚠️ 自动补充连接失败: {database} - {e}")
                        break
            
            # 🔥 检查和清理过期/无效连接
            self._cleanup_stale_connections(database)
            
            logger.debug(f"✅ 连接池健康维护完成: {database} (当前: {self._connection_counts.get(database, 0)})")
            
        except Exception as e:
            logger.error(f"❌ 连接池健康维护失败: {database} - {e}")
    
    def _cleanup_stale_connections(self, database: str):
        """清理陈旧连接（智能空闲连接管理）"""
        try:
            pool = self._get_pool(database)
            temp_connections = []
            cleaned_count = 0
            
            # 取出所有连接进行检查
            while not pool.empty():
                try:
                    connection = pool.get_nowait()
                    temp_connections.append(connection)
                except Empty:
                    break
            
            # 检查每个连接的健康状态
            for connection in temp_connections:
                try:
                    connection.ping(reconnect=False)
                    # 连接健康，放回池中
                    pool.put_nowait(connection)
                except:
                    # 连接无效，执行替换
                    cleaned_count += 1
                    try:
                        connection.close()
                    except:
                        pass
                    
                    # 创建新连接替换
                    try:
                        new_connection = self._create_connection(database)
                        pool.put_nowait(new_connection)
                        self._stats['stale_connections_replaced'] = self._stats.get('stale_connections_replaced', 0) + 1
                    except:
                        # 无法创建新连接，减少计数
                        with self._pool_locks[database]:
                            self._connection_counts[database] -= 1
            
            if cleaned_count > 0:
                logger.info(f"🧹 清理了{cleaned_count}个陈旧连接: {database}")
                
        except Exception as e:
            logger.error(f"❌ 清理陈旧连接失败: {database} - {e}")
    
    @contextmanager
    def get_cursor(self, database: str = 'aps', autocommit: bool = False):
        """获取数据库游标的上下文管理器"""
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection(database)
            if autocommit:
                connection.autocommit(True)
            
            cursor = connection.cursor()
            yield cursor
            
            if not autocommit:
                connection.commit()
                
        except Exception as e:
            if connection and not autocommit:
                try:
                    connection.rollback()
                except:
                    pass
            logger.error(f"❌ 数据库操作失败: {e}")
            raise
            
        finally:
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            
            if connection:
                try:
                    if autocommit:
                        connection.autocommit(False)  # 恢复默认设置
                    self.return_connection(connection, database)
                except:
                    # 如果归还失败，强制关闭
                    try:
                        connection.close()
                    except:
                        pass
    
    @contextmanager 
    def get_connection_context(self, database: str = 'aps'):
        """获取数据库连接的上下文管理器 - 包含事务管理"""
        connection = None
        
        try:
            connection = self.get_connection(database)
            yield connection
        except Exception as e:
            # 发生异常时回滚事务
            if connection:
                try:
                    connection.rollback()
                    logger.debug(f"🔄 连接事务已回滚: {database}")
                except Exception as rollback_error:
                    logger.warning(f"⚠️ 事务回滚失败: {database} - {rollback_error}")
            raise
        finally:
            if connection:
                try:
                    # 确保连接状态正常
                    if hasattr(connection, 'open') and connection.open:
                        # 检查是否有未提交的事务
                        if hasattr(connection, '_mysql') and hasattr(connection._mysql, 'warning_count'):
                            pass  # 连接正常
                    self.return_connection(connection, database)
                except Exception as return_error:
                    logger.warning(f"⚠️ 归还连接失败: {database} - {return_error}")
                    # 即使归还失败，也要尝试关闭连接
                    try:
                        connection.close()
                    except:
                        pass
    
    @contextmanager
    def batch_processing_connection(self, database_name: str = 'aps', batch_id: str = None):
        """
        🔥 批量处理连接管理器 - 用户建议的核心优化
        
        批量处理时保持单个连接，避免为每个操作创建新连接
        循环处理数百个批次时只消耗1个连接，而非数百个连接
        
        Args:
            database_name: 数据库名称  
            batch_id: 批次标识(可选)，用于区分不同的批量操作
            
        Usage:
            with connection_pool.batch_processing_connection('aps', 'scheduling_001') as conn:
                for lot in lots:  # 处理数百个批次
                    cursor = conn.cursor() 
                    cursor.execute("SELECT * FROM table WHERE lot_id = %s", (lot['id'],))
                    # 只使用这一个连接处理所有批次
        """
        batch_key = f"{database_name}_{batch_id or 'default'}"
        
        # 获取或创建批量处理锁
        if batch_key not in self._batch_locks:
            self._batch_locks[batch_key] = threading.Lock()
        
        with self._batch_locks[batch_key]:
            connection = None
            try:
                # 检查是否已有批量连接在使用
                if batch_key in self._batch_connections:
                    existing_conn = self._batch_connections[batch_key]['connection']
                    # 验证连接是否仍然有效
                    try:
                        existing_conn.ping(reconnect=False)
                        logger.debug(f"🔄 复用批量处理连接: {batch_key}")
                        yield existing_conn
                        return
                    except:
                        # 连接无效，清理并创建新连接
                        try:
                            existing_conn.close()
                        except:
                            pass
                        del self._batch_connections[batch_key]
                
                # 创建新的批量处理连接
                connection = self._create_connection(database_name)
                if connection:
                    self._batch_connections[batch_key] = {
                        'connection': connection,
                        'created_at': time.time(),
                        'database': database_name,
                        'batch_id': batch_id
                    }
                    logger.info(f"🔥 创建批量处理连接: {batch_key} (优化策略：循环处理保持连接)")
                    yield connection
                else:
                    raise Exception(f"无法创建批量处理连接: {database_name}")
                    
            except Exception as e:
                logger.error(f"❌ 批量处理连接错误: {batch_key} - {e}")
                raise e
            finally:
                # 批量处理完成，关闭连接
                if batch_key in self._batch_connections:
                    try:
                        self._batch_connections[batch_key]['connection'].close()
                        del self._batch_connections[batch_key]
                        logger.debug(f"🔄 批量处理连接已关闭: {batch_key}")
                    except Exception as e:
                        logger.warning(f"⚠️ 关闭批量处理连接时出错: {batch_key} - {e}")
    
    def close_batch_processing_connection(self, database_name: str, batch_id: str = None):
        """
        手动关闭批量处理连接
        
        Args:
            database_name: 数据库名称
            batch_id: 批次标识
        """
        batch_key = f"{database_name}_{batch_id or 'default'}"
        
        if batch_key in self._batch_connections:
            try:
                connection = self._batch_connections[batch_key]['connection']
                connection.close()
                del self._batch_connections[batch_key]
                logger.info(f"🔄 手动关闭批量处理连接: {batch_key}")
            except Exception as e:
                logger.error(f"❌ 手动关闭批量处理连接失败: {batch_key} - {e}")
    
    def cleanup_idle_batch_connections(self, max_idle_time: int = 1800):
        """
        清理空闲的批量处理连接(默认30分钟)
        """
        current_time = time.time()
        to_remove = []
        
        for batch_key, batch_info in self._batch_connections.items():
            if current_time - batch_info['created_at'] > max_idle_time:
                to_remove.append(batch_key)
        
        for batch_key in to_remove:
            database_name = self._batch_connections[batch_key]['database']
            batch_id = self._batch_connections[batch_key].get('batch_id')
            self.close_batch_processing_connection(database_name, batch_id)
            
        if to_remove:
            logger.info(f"🧹 清理了 {len(to_remove)} 个空闲批量处理连接")
    
    def register_process_type(self, process_type: str):
        """
        🔥 注册当前进程的类型，用于配额管理
        
        Args:
            process_type: 进程类型 ('main_app', 'scheduler', 'tool_script', 'monitoring', 'test')
        """
        import os
        current_pid = os.getpid()
        
        with self._process_quota_lock:
            self._process_types[current_pid] = process_type
            if current_pid not in self._process_connections:
                self._process_connections[current_pid] = 0
        
        quota = self._process_quotas.get(process_type, self._process_quotas['default'])
        logger.info(f"🔥 进程配额注册: PID={current_pid}, 类型={process_type}, 配额={quota}")
    
    def _get_process_quota(self, pid: int) -> int:
        """获取进程的连接配额"""
        process_type = self._process_types.get(pid, 'default')
        return self._process_quotas.get(process_type, self._process_quotas['default'])
    
    def _check_process_quota(self, pid: int) -> bool:
        """
        检查进程是否超出连接配额
        
        Returns:
            True: 可以分配新连接
            False: 已达配额限制
        """
        with self._process_quota_lock:
            current_usage = self._process_connections.get(pid, 0)
            quota = self._get_process_quota(pid)
            
            if current_usage >= quota:
                process_type = self._process_types.get(pid, 'unknown')
                logger.warning(f"⚠️ 进程连接配额耗尽: PID={pid}, 类型={process_type}, 使用={current_usage}/{quota}")
                return False
            
            return True
    
    def _allocate_connection_to_process(self, pid: int):
        """为进程分配连接配额"""
        with self._process_quota_lock:
            if pid not in self._process_connections:
                self._process_connections[pid] = 0
            self._process_connections[pid] += 1
    
    def _deallocate_connection_from_process(self, pid: int):
        """从进程释放连接配额"""
        with self._process_quota_lock:
            if pid in self._process_connections:
                self._process_connections[pid] = max(0, self._process_connections[pid] - 1)
    
    def get_process_quota_stats(self) -> Dict[str, Any]:
        """获取进程连接配额使用统计"""
        with self._process_quota_lock:
            stats = {
                'process_quotas': self._process_quotas.copy(),
                'process_usage': {},
                'quota_violations': 0,
                'total_processes': len(self._process_connections)
            }
            
            for pid, usage in self._process_connections.items():
                process_type = self._process_types.get(pid, 'unknown')
                quota = self._get_process_quota(pid)
                
                stats['process_usage'][f"PID_{pid}"] = {
                    'process_type': process_type,
                    'connections_used': usage,
                    'quota_limit': quota,
                    'usage_rate': usage / quota if quota > 0 else 0,
                    'quota_available': quota - usage
                }
                
                if usage > quota:
                    stats['quota_violations'] += 1
            
            return stats
    
    def close_all_pools(self):
        """关闭所有连接池"""
        logger.info("🔄 关闭所有数据库连接池...")
        
        for database, pool in self._pools.items():
            while not pool.empty():
                try:
                    connection = pool.get_nowait()
                    connection.close()
                except:
                    pass
            
            self._connection_counts[database] = 0
        
        self._pools.clear()
        self._pool_locks.clear()
        self._connection_counts.clear()
        
        logger.info("✅ 所有数据库连接池已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = self._stats.copy()
        stats['pools'] = {}
        
        for database in self._pools:
            stats['pools'][database] = {
                'active_connections': self._connection_counts.get(database, 0),
                'pool_size': self._pools[database].qsize(),
                'max_connections': self._max_connections
            }
        
        return stats

    def monitor_connection_pool(self):
        """监控连接池状态并发出告警"""
        if not self._monitor_enabled:
            return

        current_time = time.time()
        if current_time - self._last_health_check < self._health_check_interval:
            return

        self._last_health_check = current_time

        for database in list(self._pools.keys()):
            try:
                active = self._active_connections.get(database, 0)
                total = self._connection_counts.get(database, 0)

                if total == 0:
                    continue

                usage_rate = active / total

                # 高使用率告警
                if usage_rate >= self._alert_threshold:
                    logger.warning(f"⚠️ 连接池使用率过高: {database} {usage_rate:.1%} ({active}/{total})")

                # 连接池耗尽告警
                if active >= total:
                    logger.error(f"❌ 连接池耗尽: {database} ({active}/{total})")

            except Exception as e:
                logger.error(f"❌ 连接池监控失败 {database}: {e}")

    def print_stats(self):
        """打印连接池统计信息"""
        stats = self.get_stats()

        print("📊 数据库连接池统计:")
        print(f"  新建连接: {stats['connections_created']}")
        print(f"  复用连接: {stats['connections_reused']}")
        print(f"  配置缓存命中: {stats['config_cache_hits']}")
        print(f"  配置缓存未命中: {stats['config_cache_misses']}")

        if stats['pools']:
            print("  连接池状态:")
            for db, pool_stats in stats['pools'].items():
                active = pool_stats['active_connections']
                pool_size = pool_stats['pool_size']
                max_conn = pool_stats['max_connections']
                usage_rate = active / max_conn if max_conn > 0 else 0
                status = "⚠️ 高使用率" if usage_rate >= self._alert_threshold else "✅ 正常"
                print(f"    {db}: 活跃{active}/池大小{pool_size}/最大{max_conn} ({usage_rate:.1%}) {status}")


# 全局连接池实例
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool() -> DatabaseConnectionPool:
    """获取全局连接池实例（单例）"""
    global _connection_pool
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                _connection_pool = DatabaseConnectionPool()
    return _connection_pool

# 便捷方法
def get_connection(database: str = 'aps') -> pymysql.Connection:
    """获取数据库连接（主要入口）"""
    return get_connection_pool().get_connection(database)

def get_db_connection(database: str = 'aps') -> pymysql.Connection:
    """获取数据库连接（便捷方法）"""
    return get_connection_pool().get_connection(database)

def get_db_cursor(database: str = 'aps', autocommit: bool = False):
    """获取数据库游标上下文管理器（便捷方法）"""
    return get_connection_pool().get_cursor(database, autocommit)

def get_db_connection_context(database: str = 'aps'):
    """获取数据库连接上下文管理器（便捷方法）"""
    return get_connection_pool().get_connection_context(database)

def return_db_connection(connection: pymysql.Connection, database: str = 'aps'):
    """归还数据库连接（便捷方法）"""
    return get_connection_pool().return_connection(connection, database)

def print_connection_stats():
    """打印连接池统计信息（便捷方法）"""
    return get_connection_pool().print_stats()

def monitor_connection_pool():
    """监控连接池状态（便捷方法）"""
    return get_connection_pool().monitor_connection_pool()

def batch_processing_connection(database_name: str = 'aps', batch_id: str = None):
    """
    🔥 批量处理连接便捷方法 - 用户建议的核心优化
    
    循环处理时保持连接，单次操作后释放 - 解决连接池耗尽根本问题
    
    Args:
        database_name: 数据库名称
        batch_id: 批次标识，用于区分不同批量操作
        
    Usage:
        # 排产批量处理：268个批次只用1个连接
        with batch_processing_connection('aps', 'scheduling_batch') as conn:
            for lot in lots:  # 处理数百个批次
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM et_wait_lot WHERE LOT_ID = %s", (lot['id'],))
                # 继续处理其他批次...
    """
    return get_connection_pool().batch_processing_connection(database_name, batch_id)

def register_process_type(process_type: str):
    """
    🔥 注册当前进程类型，启用配额管理（便捷方法）
    
    Args:
        process_type: 进程类型 ('main_app', 'scheduler', 'tool_script', 'monitoring', 'test')
    """
    return get_connection_pool().register_process_type(process_type)

def get_process_quota_stats():
    """
    获取进程连接配额使用统计（便捷方法）
    
    Returns:
        进程配额使用详情，包含每个进程的配额使用情况
    """
    return get_connection_pool().get_process_quota_stats()

def get_connection_pool_health():
    """获取连接池健康状态"""
    pool = get_connection_pool()
    stats = pool.get_stats()

    health_info = {
        'status': 'healthy',
        'warnings': [],
        'errors': [],
        'pools': {}
    }

    for db_name, pool_stats in stats.get('pools', {}).items():
        active = pool_stats.get('active_connections', 0)
        total = pool_stats.get('total_connections', 0)
        usage_rate = active / total if total > 0 else 0

        pool_health = {
            'active_connections': active,
            'total_connections': total,
            'usage_rate': usage_rate,
            'status': 'healthy'
        }

        if usage_rate >= 0.9:
            pool_health['status'] = 'critical'
            health_info['errors'].append(f"连接池 {db_name} 使用率过高: {usage_rate:.1%}")
            health_info['status'] = 'critical'
        elif usage_rate >= 0.7:
            pool_health['status'] = 'warning'
            health_info['warnings'].append(f"连接池 {db_name} 使用率偏高: {usage_rate:.1%}")
            if health_info['status'] == 'healthy':
                health_info['status'] = 'warning'

        health_info['pools'][db_name] = pool_health

    return health_info

def get_detailed_connection_metrics():
    """📊 获取详细的连接池指标和监控数据"""
    pool = get_connection_pool()
    stats = pool.get_stats()
    
    detailed_metrics = {
        'timestamp': time.time(),
        'overview': {
            'total_connections_created': stats.get('connections_created', 0),
            'total_connections_reused': stats.get('connections_reused', 0),
            'config_cache_hits': stats.get('config_cache_hits', 0),
            'config_cache_misses': stats.get('config_cache_misses', 0),
            'pool_timeouts': stats.get('pool_timeouts', 0),
            'connection_errors': stats.get('connection_errors', 0),
            'health_checks_performed': stats.get('health_checks_performed', 0),
            'unhealthy_connections_removed': stats.get('unhealthy_connections_removed', 0)
        },
        'pools': {},
        'recommendations': []
    }
    
    # 计算缓存命中率
    cache_total = stats.get('config_cache_hits', 0) + stats.get('config_cache_misses', 0)
    cache_hit_rate = stats.get('config_cache_hits', 0) / cache_total if cache_total > 0 else 0
    detailed_metrics['overview']['cache_hit_rate'] = cache_hit_rate
    
    # 计算连接复用率
    conn_total = stats.get('connections_created', 0) + stats.get('connections_reused', 0)
    reuse_rate = stats.get('connections_reused', 0) / conn_total if conn_total > 0 else 0
    detailed_metrics['overview']['connection_reuse_rate'] = reuse_rate
    
    # 分析各连接池状态
    for db_name, pool_stats in stats.get('pools', {}).items():
        active = pool_stats.get('active_connections', 0)
        pool_size = pool_stats.get('pool_size', 0)
        max_conn = pool_stats.get('max_connections', 120)
        
        pool_metrics = {
            'active_connections': active,
            'available_connections': pool_size,
            'max_connections': max_conn,
            'usage_rate': active / max_conn if max_conn > 0 else 0,
            'availability_rate': pool_size / max_conn if max_conn > 0 else 0,
            'status': 'healthy'
        }
        
        # 状态评估和建议
        usage_rate = pool_metrics['usage_rate']
        if usage_rate >= 0.95:
            pool_metrics['status'] = 'critical'
            detailed_metrics['recommendations'].append(f"🚨 {db_name}: 连接池接近耗尽({usage_rate:.1%})，建议立即扩容")
        elif usage_rate >= 0.85:
            pool_metrics['status'] = 'warning'
            detailed_metrics['recommendations'].append(f"⚠️ {db_name}: 连接池使用率高({usage_rate:.1%})，建议优化查询或扩容")
        elif usage_rate >= 0.7:
            pool_metrics['status'] = 'caution'
            detailed_metrics['recommendations'].append(f"💡 {db_name}: 连接池使用率偏高({usage_rate:.1%})，建议关注")
        
        detailed_metrics['pools'][db_name] = pool_metrics
    
    # 全局建议
    if stats.get('pool_timeouts', 0) > 0:
        detailed_metrics['recommendations'].append(f"⏰ 发生了{stats['pool_timeouts']}次连接池超时，建议检查连接泄漏")
    
    if cache_hit_rate < 0.8:
        detailed_metrics['recommendations'].append(f"📈 配置缓存命中率较低({cache_hit_rate:.1%})，建议优化缓存策略")
    
    if reuse_rate < 0.7:
        detailed_metrics['recommendations'].append(f"♻️ 连接复用率较低({reuse_rate:.1%})，建议检查连接池配置")
    
    return detailed_metrics

def log_connection_pool_status():
    """📊 记录连接池状态到日志，用于监控"""
    try:
        metrics = get_detailed_connection_metrics()
        overview = metrics['overview']
        
        logger.info("📊 连接池状态报告:")
        logger.info(f"  总连接创建: {overview['total_connections_created']}")
        logger.info(f"  连接复用: {overview['total_connections_reused']}")
        logger.info(f"  连接复用率: {overview['connection_reuse_rate']:.1%}")
        logger.info(f"  配置缓存命中率: {overview['cache_hit_rate']:.1%}")
        
        if overview.get('pool_timeouts', 0) > 0:
            logger.warning(f"⚠️ 连接池超时: {overview['pool_timeouts']} 次")
        
        if overview.get('connection_errors', 0) > 0:
            logger.warning(f"❌ 连接错误: {overview['connection_errors']} 次")
        
        for db_name, pool_info in metrics['pools'].items():
            status_emoji = {'healthy': '✅', 'caution': '💡', 'warning': '⚠️', 'critical': '🚨'}
            emoji = status_emoji.get(pool_info['status'], '❓')
            logger.info(f"  {emoji} {db_name}: {pool_info['active_connections']}/{pool_info['max_connections']} ({pool_info['usage_rate']:.1%})")
        
        for recommendation in metrics['recommendations']:
            logger.warning(f"💡 建议: {recommendation}")
            
    except Exception as e:
        logger.error(f"❌ 记录连接池状态失败: {e}")