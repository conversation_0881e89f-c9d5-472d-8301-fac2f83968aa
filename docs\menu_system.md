# 菜单系统说明文档

## 概述

本系统使用基于配置文件的菜单系统，不再依赖数据库中的`menu_settings`表。这种设计有以下优点：

1. 更易于修改 - 直接编辑配置文件就可以添加或修改菜单项
2. 版本控制 - 菜单结构随代码一起版本控制
3. 更好的开发体验 - 无需操作数据库就能修改菜单
4. 系统更加稳定 - 避免数据库菜单表被误操作导致系统问题

## 菜单配置文件

菜单系统的核心配置文件是 `app/config/menu_config.py`，它包含所有菜单项的定义。

### 关键组件

1. `MENU_ID_MAP` - 定义菜单代码到菜单ID的映射，用于权限管理
2. `MENU_CONFIG` - 定义菜单的树形结构，包含所有菜单项的详细信息
3. 辅助函数 - 提供访问菜单项的工具函数

## 如何添加新菜单项

### 步骤1: 在MENU_ID_MAP中添加映射

在`MENU_ID_MAP`字典中为新菜单项添加一个唯一ID：

```python
MENU_ID_MAP = {
    # 已有菜单项...
    
    # 新增菜单项
    'new_menu_code': 25,  # 使用一个未使用的ID
}
```

### 步骤2: 在MENU_CONFIG中添加菜单项定义

在`MENU_CONFIG`列表中添加新菜单项。如果是顶级菜单：

```python
MENU_CONFIG = [
    # 已有菜单项...
    
    # 新增顶级菜单
    {
        'code': 'new_menu_code',
        'name': '新菜单名称',
        'icon': 'fas fa-icon-name',  # 使用FontAwesome图标
        'route': None,  # 如果有直接链接则填写路由，否则为None
        'order': 7,  # 显示顺序
        'children': [
            # 子菜单项...
        ]
    }
]
```

如果是子菜单，则添加到对应父菜单的`children`列表中：

```python
'children': [
    # 已有子菜单...
    
    # 新增子菜单
    {
        'code': 'new_submenu_code',
        'name': '新子菜单名称',
        'icon': 'fas fa-icon-name',
        'route': '/new/route',  # 子菜单通常有具体路由
        'order': 1  # 子菜单显示顺序
    }
]
```

## 如何修改菜单项

直接在`app/config/menu_config.py`文件中修改对应菜单项的属性即可：

1. 修改名称 - 更改`name`属性
2. 修改图标 - 更改`icon`属性
3. 修改链接 - 更改`route`属性
4. 调整顺序 - 更改`order`属性

## 注意事项

1. 菜单ID必须保持唯一，不要重复使用已有ID
2. 不要轻易改变已有菜单的ID，这会影响用户权限
3. 菜单代码(`code`)应该使用有意义的名称，便于识别
4. 添加新菜单后，需要更新相关路由和页面

## 权限管理

系统仍然保留了基于菜单项的权限管理功能。管理员可以为用户分配菜单权限，控制用户可以访问的功能。

对管理员来说:
- 管理员用户自动拥有所有菜单权限
- 权限管理页面显示所有可用菜单项

对普通用户来说:
- 只能看到被分配了权限的菜单项
- 未授权的菜单项不会显示在侧边栏中

## 从旧系统迁移

如果你是从旧的基于数据库的菜单系统升级，可以使用迁移工具：

```bash
python menu_migrate.py
```

迁移工具会自动将用户权限从旧系统迁移到新系统，并保持现有权限不变。