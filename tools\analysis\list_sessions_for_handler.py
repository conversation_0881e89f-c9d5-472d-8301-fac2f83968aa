#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import argparse
import pymysql

def db_query(sql: str, params=None):
    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
    try:
        with conn.cursor() as cur:
            cur.execute(sql, params or ())
            cols = [c[0] for c in cur.description]
            return [dict(zip(cols, row)) for row in cur.fetchall()]
    finally:
        conn.close()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--handler-id', required=True)
    args = parser.parse_args()
    rows = db_query("""
        SELECT SESSION_ID, COUNT(*) AS cnt, MIN(PRIORITY) AS min_p, MAX(PRIORITY) AS max_p,
               MAX(id) AS max_id
        FROM lotprioritydone WHERE HANDLER_ID=%s
        GROUP BY SESSION_ID ORDER BY max_id DESC LIMIT 20
    """, (args.handler_id,))
    if not rows:
        print("[EMPTY] 该设备没有lotprioritydone记录", args.handler_id)
    else:
        for r in rows:
            print(f"SESSION={r['SESSION_ID']}, cnt={r['cnt']}, minP={r['min_p']}, maxP={r['max_p']}, max_id={r['max_id']}")

