#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产批次数据服务
统一管理done-lots页面的所有数据操作，使用现有连接池
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy import text
from app import db
from app.utils.db_connection_pool import get_db_connection_context

logger = logging.getLogger(__name__)

class DoneLotsDataService:
    """已排产批次数据服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = logger
        self.logger.info("🔧 DoneLotsDataService 初始化")
    
    def get_done_lots_data(self, 
                          filters: Optional[Dict] = None, 
                          sort: Optional[Dict] = None, 
                          pagination: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取已排产批次数据
        
        Args:
            filters: 筛选条件
            sort: 排序条件
            pagination: 分页参数
            
        Returns:
            包含数据和统计信息的字典
        """
        try:
            self.logger.info(f"📊 获取已排产数据，筛选条件: {filters}, 排序: {sort}, 分页: {pagination}")
            
            # 使用现有连接池获取连接
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions, query_params = self._build_where_conditions(filters or {})
                
                # 构建排序条件
                order_clause = self._build_order_clause(sort or {})
                
                # 查询总数
                count_query = f"SELECT COUNT(*) as total FROM lotprioritydone {where_conditions}"
                cursor.execute(count_query, query_params)
                total_result = cursor.fetchone()
                total_count = total_result['total'] if isinstance(total_result, dict) else total_result[0]
                
                # 构建数据查询
                data_query = f"""
                    SELECT 
                        id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                        PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, STEP,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        RELEASE_TIME, FAC_ID, CREATE_TIME,
                        comprehensive_score, processing_time, changeover_time,
                        algorithm_version, match_type, priority_score
                    FROM lotprioritydone 
                    {where_conditions}
                    {order_clause}
                """
                
                # 应用分页
                if pagination and 'page' in pagination and 'size' in pagination:
                    page = max(1, pagination['page'])
                    size = max(1, min(1000, pagination['size']))  # 限制最大1000条
                    offset = (page - 1) * size
                    data_query += f" LIMIT {size} OFFSET {offset}"
                
                cursor.execute(data_query, query_params)
                raw_data = cursor.fetchall()
                
                # 处理数据格式
                data = []
                for row in raw_data:
                    if isinstance(row, dict):
                        data.append(row)
                    else:
                        # 如果是tuple，转换为dict
                        data.append({
                            'id': row[0],
                            'PRIORITY': row[1] or '',
                            'HANDLER_ID': row[2] or '',
                            'LOT_ID': row[3] or '',
                            'LOT_TYPE': row[4] or '',
                            'GOOD_QTY': row[5] or 0,
                            'PROD_ID': row[6] or '',
                            'DEVICE': row[7] or '',
                            'CHIP_ID': row[8] or '',
                            'PKG_PN': row[9] or '',
                            'PO_ID': row[10] or '',
                            'STAGE': row[11] or '',
                            'STEP': row[12] or '',
                            'WIP_STATE': row[13] or '',
                            'PROC_STATE': row[14] or '',
                            'HOLD_STATE': row[15] or 0,
                            'FLOW_ID': row[16] or '',
                            'FLOW_VER': row[17] or '',
                            'RELEASE_TIME': row[18] or '',
                            'FAC_ID': row[19] or '',
                            'CREATE_TIME': row[20] or '',
                            'comprehensive_score': row[21] or 0.0,
                            'processing_time': row[22] or 0.0,
                            'changeover_time': row[23] or 0.0,
                            'algorithm_version': row[24] or '',
                            'match_type': row[25] or '',
                            'priority_score': row[26] or 0.0
                        })
                
                cursor.close()
                
                # 计算分页信息
                page_info = {}
                if pagination:
                    page = pagination.get('page', 1)
                    size = pagination.get('size', 50)
                    total_pages = (total_count + size - 1) // size
                    page_info = {
                        'current_page': page,
                        'page_size': size,
                        'total_pages': total_pages,
                        'total': total_count,  # 修复：添加total字段
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                
                result = {
                    'success': True,
                    'data': data,
                    'total_count': total_count,
                    'filtered_count': len(data),
                    'pagination': page_info,
                    'filters_applied': len(filters or {}),
                    'query_time': datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ 成功获取已排产数据: {len(data)}条记录，总数: {total_count}")
                return result
                
        except Exception as e:
            self.logger.error(f"❌ 获取已排产数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total_count': 0
            }
    
    def get_failed_lots_data(self, filters: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取失败批次数据
        
        Args:
            filters: 筛选条件
            
        Returns:
            失败批次数据
        """
        try:
            self.logger.info(f"📊 获取失败批次数据，筛选条件: {filters}")
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查失败批次表是否存在
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
                """
                cursor.execute(check_table_sql)
                table_exists_result = cursor.fetchone()
                table_exists = (table_exists_result['COUNT(*)'] if isinstance(table_exists_result, dict) 
                               else table_exists_result[0]) > 0
                
                if not table_exists:
                    self.logger.warning("⚠️ scheduling_failed_lots表不存在，返回空数据")
                    return {
                        'success': True,
                        'data': [],
                        'total_count': 0,
                        'message': '失败批次表不存在'
                    }
                
                # 构建失败批次查询条件
                where_conditions = "WHERE 1=1"
                query_params = {}
                
                if filters:
                    # 时间范围筛选
                    if 'hours_limit' in filters and filters['hours_limit'] is not None:
                        hours = int(filters['hours_limit'])
                        where_conditions += " AND timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR)"
                        query_params['hours'] = hours
                    
                    # 其他筛选条件
                    if 'device' in filters:
                        where_conditions += " AND device LIKE %s"
                        query_params['device'] = f"%{filters['device']}%"
                    
                    if 'stage' in filters:
                        where_conditions += " AND stage LIKE %s" 
                        query_params['stage'] = f"%{filters['stage']}%"
                
                # 查询失败批次数据
                failed_query = f"""
                    SELECT
                        sfl.lot_id,
                        sfl.device,
                        sfl.stage,
                        sfl.good_qty,
                        sfl.failure_reason,
                        sfl.failure_details,
                        sfl.suggestion,
                        sfl.session_id,
                        sfl.timestamp,
                        ewl.LOT_TYPE,
                        ewl.PKG_PN
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                    {where_conditions}
                    ORDER BY sfl.timestamp DESC
                    LIMIT 1000
                """
                
                cursor.execute(failed_query, list(query_params.values()))
                failed_results = cursor.fetchall()
                
                # 处理失败批次数据
                failed_lots = []
                for row in failed_results:
                    if isinstance(row, dict):
                        lot_data = row
                    else:
                        lot_data = {
                            'lot_id': row[0],
                            'device': row[1] or '',
                            'stage': row[2] or '',
                            'good_qty': row[3] or 0,
                            'failure_reason': row[4] or '未知原因',
                            'failure_details': row[5] or '',
                            'suggestion': row[6] or '',
                            'session_id': row[7] or '',
                            'timestamp': row[8],
                            'LOT_TYPE': row[9] or '',
                            'PKG_PN': row[10] or ''
                        }
                    
                    # 格式化为统一结构
                    failed_lot = {
                        'LOT_ID': lot_data['lot_id'],
                        'DEVICE': lot_data['device'],
                        'STAGE': lot_data['stage'],
                        'LOT_TYPE': lot_data['LOT_TYPE'],
                        'PKG_PN': lot_data['PKG_PN'],
                        'GOOD_QTY': lot_data['good_qty'],
                        'failure_reason': lot_data['failure_reason'],
                        'suggestion': lot_data['suggestion'],
                        'timestamp': lot_data['timestamp'].isoformat() if lot_data['timestamp'] else datetime.now().isoformat(),
                        'session_id': lot_data['session_id'],
                        '_isFailedLot': True,
                        '_source': 'failed_lots',
                        'PRIORITY': 'FAILED',
                        'comprehensive_score': 0
                    }
                    failed_lots.append(failed_lot)
                
                cursor.close()
                
                result = {
                    'success': True,
                    'data': failed_lots,
                    'total_count': len(failed_lots),
                    'data_source': 'database',
                    'query_time': datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ 成功获取失败批次数据: {len(failed_lots)}条记录")
                return result
                
        except Exception as e:
            self.logger.error(f"❌ 获取失败批次数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total_count': 0
            }
    
    def get_final_result_data(self, 
                             filters: Optional[Dict] = None, 
                             sort: Optional[Dict] = None, 
                             pagination: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取最终结果数据
        
        Args:
            filters: 筛选条件
            sort: 排序条件
            pagination: 分页参数
            
        Returns:
            最终结果数据
        """
        try:
            self.logger.info(f"📊 获取最终结果数据，筛选条件: {filters}")
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查最终结果表是否存在
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'final_scheduling_result'
                """
                cursor.execute(check_table_sql)
                table_exists_result = cursor.fetchone()
                table_exists = (table_exists_result['COUNT(*)'] if isinstance(table_exists_result, dict) 
                               else table_exists_result[0]) > 0
                
                if not table_exists:
                    self.logger.warning("⚠️ final_scheduling_result表不存在，返回空数据")
                    # 计算分页信息（即使是空数据）
                    page_info = {}
                    if pagination:
                        page_info = {
                            'current_page': pagination.get('page', 1),
                            'page_size': pagination.get('size', 50),
                            'total_pages': 0,
                            'total': 0,
                            'has_next': False,
                            'has_prev': False
                        }
                    return {
                        'success': True,
                        'data': [],
                        'total_count': 0,
                        'pagination': page_info,
                        'message': '最终结果表不存在'
                    }
                
                # 构建查询条件（简化版，后续可扩展）
                where_conditions = "WHERE 1=1"
                query_params = {}
                
                if filters:
                    if 'session_id' in filters:
                        where_conditions += " AND session_id = %s"
                        query_params['session_id'] = filters['session_id']
                    
                    if 'handler_id' in filters:
                        where_conditions += " AND handler_id = %s"
                        query_params['handler_id'] = filters['handler_id']
                
                # 查询最终结果数据（使用更通用的排序字段）
                final_query = f"""
                    SELECT * FROM final_scheduling_result 
                    {where_conditions}
                    ORDER BY id DESC
                """
                
                # 应用分页
                if pagination and 'page' in pagination and 'size' in pagination:
                    page = max(1, pagination['page'])
                    size = max(1, min(1000, pagination['size']))
                    offset = (page - 1) * size
                    final_query += f" LIMIT {size} OFFSET {offset}"
                
                cursor.execute(final_query, list(query_params.values()))
                results = cursor.fetchall()
                
                # 查询总数
                count_query = f"SELECT COUNT(*) as total FROM final_scheduling_result {where_conditions}"
                cursor.execute(count_query, list(query_params.values()))
                total_result = cursor.fetchone()
                total_count = total_result['total'] if isinstance(total_result, dict) else total_result[0]
                
                cursor.close()
                
                # 处理数据格式
                data = []
                for row in results:
                    if isinstance(row, dict):
                        data.append(row)
                    else:
                        # 根据实际表结构调整字段映射
                        data.append(dict(zip([col[0] for col in cursor.description], row)))
                
                # 计算分页信息
                page_info = {}
                if pagination:
                    page = pagination.get('page', 1)
                    size = pagination.get('size', 50)
                    total_pages = (total_count + size - 1) // size
                    page_info = {
                        'current_page': page,
                        'page_size': size,
                        'total_pages': total_pages,
                        'total': total_count,  # 添加total字段
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                
                result = {
                    'success': True,
                    'data': data,
                    'total_count': total_count,
                    'pagination': page_info,
                    'query_time': datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ 成功获取最终结果数据: {len(data)}条记录")
                return result
                
        except Exception as e:
            self.logger.error(f"❌ 获取最终结果数据失败: {e}")
            # 即使失败也要返回分页信息
            page_info = {}
            if pagination:
                page_info = {
                    'current_page': pagination.get('page', 1),
                    'page_size': pagination.get('size', 50),
                    'total_pages': 0,
                    'total': 0,
                    'has_next': False,
                    'has_prev': False
                }
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total_count': 0,
                'pagination': page_info
            }
    
    def get_data_statistics(self, filters: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            filters: 筛选条件
            
        Returns:
            统计信息
        """
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 构建筛选条件
                where_conditions, query_params = self._build_where_conditions(filters or {})
                
                # 统计查询
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_lots,
                        SUM(GOOD_QTY) as total_quantity,
                        AVG(comprehensive_score) as avg_score,
                        COUNT(DISTINCT HANDLER_ID) as unique_handlers,
                        COUNT(DISTINCT DEVICE) as unique_devices
                    FROM lotprioritydone 
                    {where_conditions}
                """
                
                cursor.execute(stats_query, query_params)
                stats_result = cursor.fetchone()
                
                if isinstance(stats_result, dict):
                    stats = stats_result
                else:
                    stats = {
                        'total_lots': stats_result[0] or 0,
                        'total_quantity': stats_result[1] or 0,
                        'avg_score': stats_result[2] or 0.0,
                        'unique_handlers': stats_result[3] or 0,
                        'unique_devices': stats_result[4] or 0
                    }
                
                cursor.close()
                
                return {
                    'success': True,
                    'statistics': stats,
                    'total_records': stats.get('total_lots', 0),  # 添加兼容字段
                    'query_time': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"❌ 获取统计信息失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'statistics': {}
            }
    
    def export_data(self, 
                   export_type: str = 'filtered',
                   filters: Optional[Dict] = None,
                   format: str = 'excel') -> Dict[str, Any]:
        """
        导出数据
        
        Args:
            export_type: 导出类型 ('current_page', 'filtered', 'all')
            filters: 筛选条件
            format: 导出格式 ('excel', 'csv')
            
        Returns:
            导出结果信息
        """
        try:
            self.logger.info(f"📤 开始导出数据，类型: {export_type}, 格式: {format}")
            
            # 根据导出类型获取相应数据
            if export_type == 'all':
                # 导出全部数据，不应用筛选
                data_result = self.get_done_lots_data(filters=None, pagination=None)
            else:
                # 导出筛选结果，不分页
                data_result = self.get_done_lots_data(filters=filters, pagination=None)
            
            if not data_result['success']:
                return {
                    'success': False,
                    'error': '获取导出数据失败',
                    'details': data_result.get('error', '')
                }
            
            export_data = data_result['data']
            
            # 这里可以添加实际的导出逻辑（Excel/CSV生成）
            # 暂时返回数据信息，实际导出逻辑在后续阶段实现
            
            return {
                'success': True,
                'export_type': export_type,
                'format': format,
                'record_count': len(export_data),
                'total_available': data_result['total_count'],
                'data': export_data,  # 实际实现中可能不返回完整数据，而是文件路径
                'export_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 导出数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_where_conditions(self, filters: Dict) -> Tuple[str, Dict]:
        """
        构建WHERE条件和查询参数
        
        Args:
            filters: 筛选条件
            
        Returns:
            (where_clause, query_params)
        """
        conditions = []
        params = {}
        
        if not filters:
            return "", {}
        
        # 批次号筛选
        if 'lot_id' in filters and filters['lot_id']:
            conditions.append("LOT_ID LIKE %(lot_id)s")
            params['lot_id'] = f"%{filters['lot_id']}%"
        
        # 产品名称筛选
        if 'device' in filters and filters['device']:
            if isinstance(filters['device'], list):
                device_conditions = []
                for i, device in enumerate(filters['device']):
                    param_name = f'device_{i}'
                    device_conditions.append(f"DEVICE = %({param_name})s")
                    params[param_name] = device
                conditions.append(f"({' OR '.join(device_conditions)})")
            else:
                conditions.append("DEVICE LIKE %(device)s")
                params['device'] = f"%{filters['device']}%"
        
        # 设备筛选
        if 'handler_id' in filters and filters['handler_id']:
            conditions.append("HANDLER_ID LIKE %(handler_id)s")
            params['handler_id'] = f"%{filters['handler_id']}%"
        
        # 工序筛选
        if 'stage' in filters and filters['stage']:
            conditions.append("STAGE LIKE %(stage)s")
            params['stage'] = f"%{filters['stage']}%"
        
        # 优先级范围筛选
        if 'priority_range' in filters and filters['priority_range']:
            priority_range = filters['priority_range']
            if len(priority_range) == 2:
                conditions.append("PRIORITY BETWEEN %(priority_min)s AND %(priority_max)s")
                params['priority_min'] = priority_range[0]
                params['priority_max'] = priority_range[1]
        
        # 日期范围筛选
        if 'date_range' in filters and filters['date_range']:
            date_range = filters['date_range']
            if len(date_range) == 2:
                conditions.append("DATE(CREATE_TIME) BETWEEN %(date_start)s AND %(date_end)s")
                params['date_start'] = date_range[0]
                params['date_end'] = date_range[1]
        
        # 状态筛选
        if 'status' in filters and filters['status']:
            if isinstance(filters['status'], list):
                status_conditions = []
                for i, status in enumerate(filters['status']):
                    param_name = f'status_{i}'
                    status_conditions.append(f"WIP_STATE = %({param_name})s")
                    params[param_name] = status
                conditions.append(f"({' OR '.join(status_conditions)})")
            else:
                conditions.append("WIP_STATE = %(status)s")
                params['status'] = filters['status']
        
        # 全局搜索
        if 'global_search' in filters and filters['global_search']:
            search_term = filters['global_search']
            global_conditions = [
                "LOT_ID LIKE %(global_search)s",
                "DEVICE LIKE %(global_search)s",
                "CHIP_ID LIKE %(global_search)s",
                "HANDLER_ID LIKE %(global_search)s",
                "PO_ID LIKE %(global_search)s"
            ]
            conditions.append(f"({' OR '.join(global_conditions)})")
            params['global_search'] = f"%{search_term}%"
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        return where_clause, params
    
    def _build_order_clause(self, sort: Dict) -> str:
        """
        构建ORDER BY子句
        
        Args:
            sort: 排序条件
            
        Returns:
            ORDER BY子句
        """
        if not sort or 'field' not in sort:
            return "ORDER BY PRIORITY ASC, CREATE_TIME DESC"
        
        field = sort['field']
        order = sort.get('order', 'ASC').upper()
        
        # 验证排序字段
        valid_fields = [
            'PRIORITY', 'CREATE_TIME', 'LOT_ID', 'DEVICE', 'HANDLER_ID', 
            'GOOD_QTY', 'STAGE', 'STEP', 'comprehensive_score'
        ]
        
        if field not in valid_fields:
            field = 'PRIORITY'
        
        if order not in ['ASC', 'DESC']:
            order = 'ASC'
        
        # 特殊处理：按优先级排序时添加时间作为次要排序
        if field == 'PRIORITY':
            return f"ORDER BY {field} {order}, CREATE_TIME DESC"
        else:
            return f"ORDER BY {field} {order}"