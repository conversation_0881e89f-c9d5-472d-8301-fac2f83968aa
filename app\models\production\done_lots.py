"""
已排产批次数据模型
对应lotprioritydone.xlsx文件
"""
from app import db
from datetime import datetime

class DoneLot(db.Model):
    """已排产批次模型 - 对应lotprioritydone表，使用实际数据库字段"""
    __tablename__ = 'lotprioritydone'
    __table_args__ = {'extend_existing': True}
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    
    # 实际数据库字段
    PRIORITY = db.Column(db.Integer, nullable=True, comment='优先级')
    HANDLER_ID = db.Column(db.String(50), nullable=True, comment='分选机编号')
    LOT_ID = db.Column(db.String(50), nullable=False, comment='内部工单号')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='批次类型')
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='良品数量')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品名称')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片名称')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装料号')
    PO_ID = db.Column(db.String(50), nullable=True, comment='订单号')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    STEP = db.Column(db.String(50), nullable=True, comment='工步')  # 重要：STEP字段
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='WIP状态')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='流程状态')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='扣留状态')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='流程ID')
    FLOW_VER = db.Column(db.String(50), nullable=True, comment='流程版本')
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='解除时间')
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    CREATE_TIME = db.Column(db.String(50), nullable=True, comment='创建时间')
    
    # SQLAlchemy生成的字段
    created_at = db.Column(db.DateTime, nullable=True, comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=True, comment='更新时间')
    
    # 添加属性访问器，方便代码兼容
    @property
    def lot_id(self):
        return self.LOT_ID
    
    @lot_id.setter
    def lot_id(self, value):
        self.LOT_ID = value
        
    @property
    def device(self):
        return self.DEVICE
    
    @device.setter  
    def device(self, value):
        self.DEVICE = value
        
    @property
    def stage(self):
        return self.STAGE
    
    @stage.setter
    def stage(self, value):
        self.STAGE = value
        
    @property
    def step(self):
        return self.STEP
    
    @step.setter
    def step(self, value):
        self.STEP = value
        
    @property
    def quantity(self):
        return self.GOOD_QTY
    
    @quantity.setter
    def quantity(self, value):
        self.GOOD_QTY = value
        
    @property
    def pkg_pn(self):
        return self.PKG_PN
    
    @pkg_pn.setter
    def pkg_pn(self, value):
        self.PKG_PN = value
        
    @property
    def chip_id(self):
        return self.CHIP_ID
    
    @chip_id.setter
    def chip_id(self, value):
        self.CHIP_ID = value
        
    @property
    def priority(self):
        return self.PRIORITY
    
    @priority.setter
    def priority(self, value):
        self.PRIORITY = value
        
    @property
    def equipment_id(self):
        return self.HANDLER_ID
    
    @equipment_id.setter
    def equipment_id(self, value):
        self.HANDLER_ID = value
        
    @property
    def status(self):
        return self.WIP_STATE
    
    @status.setter
    def status(self, value):
        self.WIP_STATE = value
        
    # 其他属性
    @property
    def completion_rate(self):
        return getattr(self, '_completion_rate', None)
    
    @completion_rate.setter
    def completion_rate(self, value):
        self._completion_rate = value
        
    @property
    def user(self):
        return getattr(self, '_user', None)
    
    @user.setter
    def user(self, value):
        self._user = value
        
    @property
    def scheduled_start_time(self):
        return getattr(self, '_scheduled_start_time', None)
    
    @scheduled_start_time.setter
    def scheduled_start_time(self, value):
        self._scheduled_start_time = value
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'LOT_ID': self.LOT_ID,
            'lot_id': self.LOT_ID,  # 兼容性字段
            'DEVICE': self.DEVICE,
            'device': self.DEVICE,  # 兼容性字段
            'STAGE': self.STAGE,
            'stage': self.STAGE,  # 兼容性字段
            'STEP': self.STEP,
            'step': self.STEP,  # 兼容性字段
            'GOOD_QTY': self.GOOD_QTY,
            'quantity': self.GOOD_QTY,  # 兼容性字段
            'PKG_PN': self.PKG_PN,
            'pkg_pn': self.PKG_PN,  # 兼容性字段
            'CHIP_ID': self.CHIP_ID,
            'chip_id': self.CHIP_ID,  # 兼容性字段
            'PRIORITY': self.PRIORITY,
            'priority': self.PRIORITY,  # 兼容性字段
            'HANDLER_ID': self.HANDLER_ID,
            'equipment_id': self.HANDLER_ID,  # 兼容性字段
            'LOT_TYPE': self.LOT_TYPE,
            'PROD_ID': self.PROD_ID,
            'PO_ID': self.PO_ID,
            'WIP_STATE': self.WIP_STATE,
            'status': self.WIP_STATE,  # 兼容性字段
            'PROC_STATE': self.PROC_STATE,
            'HOLD_STATE': self.HOLD_STATE,
            'FLOW_ID': self.FLOW_ID,
            'FLOW_VER': self.FLOW_VER,
            'RELEASE_TIME': self.RELEASE_TIME,
            'FAC_ID': self.FAC_ID,
            'CREATE_TIME': self.CREATE_TIME,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<DoneLot {self.LOT_ID}>' 