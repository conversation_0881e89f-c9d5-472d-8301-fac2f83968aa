#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify代理API模块
实现与Dify平台的通信和代理功能
"""

from flask import Blueprint, request, jsonify, Response, current_app
from flask_login import login_required, current_user
import requests
import json
import logging
from app.models import SystemSetting
from app import db

# 创建蓝图
dify_proxy_bp = Blueprint('dify_proxy', __name__, url_prefix='/api/dify-proxy')

logger = logging.getLogger(__name__)

def get_dify_config():
    """获取Dify配置信息"""
    try:
        # 从系统设置中获取Dify配置
        chatbot_token = SystemSetting.query.filter_by(key='chatbot_token').first()
        chatbot_server = SystemSetting.query.filter_by(key='chatbot_server').first()
        enable_chatbot = SystemSetting.query.filter_by(key='enable_chatbot').first()
        
        config = {
            'token': chatbot_token.value if chatbot_token else 'uV72gGRdNz0eP7ac',
            'server': chatbot_server.value if chatbot_server else 'http://localhost:3000',
            'enabled': enable_chatbot.value.lower() == 'true' if enable_chatbot else True
        }
        
        return config
    except Exception as e:
        logger.error(f"获取Dify配置失败: {e}")
        # 返回默认配置
        return {
            'token': 'uV72gGRdNz0eP7ac',
            'server': 'http://************',
            'enabled': True
        }

@dify_proxy_bp.route('/embed.min.js')
def get_embed_script():
    """代理Dify嵌入脚本"""
    try:
        config = get_dify_config()
        
        if not config['enabled']:
            return jsonify({'error': 'Dify聊天机器人未启用'}), 503
        
        # 构建Dify服务器的脚本URL
        script_url = f"{config['server']}/embed.min.js"
        
        # 请求Dify服务器的脚本
        response = requests.get(script_url, timeout=10)
        
        if response.status_code == 200:
            # 修改脚本中的配置，使其指向我们的代理API
            script_content = response.text
            
            # 替换基础URL为我们的代理路径
            script_content = script_content.replace(
                config['server'], 
                '/api/dify-proxy'
            )
            
            return Response(
                script_content,
                mimetype='application/javascript',
                headers={
                    'Cache-Control': 'public, max-age=3600',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            logger.error(f"从Dify服务器获取脚本失败: {response.status_code}")
            return jsonify({'error': '无法获取Dify脚本'}), 502
            
    except requests.exceptions.RequestException as e:
        logger.error(f"请求Dify服务器失败: {e}")
        return jsonify({'error': '无法连接到Dify服务器'}), 502
    except Exception as e:
        logger.error(f"获取Dify脚本失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@dify_proxy_bp.route('/v1/chat-messages', methods=['POST'])
@login_required
def chat_messages():
    """代理聊天消息API"""
    try:
        config = get_dify_config()
        
        if not config['enabled']:
            return jsonify({'error': 'Dify聊天机器人未启用'}), 503
        
        # 获取请求数据
        data = request.get_json()
        
        # 添加用户上下文信息
        if current_user and current_user.is_authenticated:
            data['user'] = {
                'id': current_user.username,
                'name': current_user.username,
                'role': getattr(current_user, 'role', 'user')
            }
        
        # 构建Dify API URL
        api_url = f"{config['server']}/v1/chat-messages"
        
        # 准备请求头
        headers = {
            'Authorization': f'Bearer {config["token"]}',
            'Content-Type': 'application/json'
        }
        
        # 转发请求到Dify服务器
        response = requests.post(
            api_url,
            json=data,
            headers=headers,
            timeout=30
        )
        
        # 返回Dify的响应
        return Response(
            response.content,
            status=response.status_code,
            headers={
                'Content-Type': response.headers.get('Content-Type', 'application/json'),
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except requests.exceptions.RequestException as e:
        logger.error(f"转发请求到Dify失败: {e}")
        return jsonify({'error': '无法连接到Dify服务器'}), 502
    except Exception as e:
        logger.error(f"处理聊天消息失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@dify_proxy_bp.route('/v1/conversations', methods=['GET'])
@login_required
def get_conversations():
    """获取对话列表"""
    try:
        config = get_dify_config()
        
        if not config['enabled']:
            return jsonify({'error': 'Dify聊天机器人未启用'}), 503
        
        # 构建API URL
        api_url = f"{config['server']}/v1/conversations"
        
        # 准备请求头
        headers = {
            'Authorization': f'Bearer {config["token"]}',
            'Content-Type': 'application/json'
        }
        
        # 添加查询参数
        params = dict(request.args)
        if current_user and current_user.is_authenticated:
            params['user'] = current_user.username
        
        # 转发请求
        response = requests.get(
            api_url,
            params=params,
            headers=headers,
            timeout=10
        )
        
        return Response(
            response.content,
            status=response.status_code,
            headers={
                'Content-Type': response.headers.get('Content-Type', 'application/json'),
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except requests.exceptions.RequestException as e:
        logger.error(f"获取对话列表失败: {e}")
        return jsonify({'error': '无法连接到Dify服务器'}), 502
    except Exception as e:
        logger.error(f"获取对话列表失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@dify_proxy_bp.route('/v1/conversations/<conversation_id>/messages', methods=['GET'])
@login_required
def get_conversation_messages(conversation_id):
    """获取对话消息"""
    try:
        config = get_dify_config()
        
        if not config['enabled']:
            return jsonify({'error': 'Dify聊天机器人未启用'}), 503
        
        # 构建API URL
        api_url = f"{config['server']}/v1/conversations/{conversation_id}/messages"
        
        # 准备请求头
        headers = {
            'Authorization': f'Bearer {config["token"]}',
            'Content-Type': 'application/json'
        }
        
        # 添加查询参数
        params = dict(request.args)
        if current_user and current_user.is_authenticated:
            params['user'] = current_user.username
        
        # 转发请求
        response = requests.get(
            api_url,
            params=params,
            headers=headers,
            timeout=10
        )
        
        return Response(
            response.content,
            status=response.status_code,
            headers={
                'Content-Type': response.headers.get('Content-Type', 'application/json'),
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except requests.exceptions.RequestException as e:
        logger.error(f"获取对话消息失败: {e}")
        return jsonify({'error': '无法连接到Dify服务器'}), 502
    except Exception as e:
        logger.error(f"获取对话消息失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@dify_proxy_bp.route('/config')
@login_required
def get_config():
    """获取Dify配置信息"""
    try:
        config = get_dify_config()
        
        # 只返回前端需要的配置信息，不暴露敏感信息
        return jsonify({
            'enabled': config['enabled'],
            'token': config['token'],  # 前端需要token来初始化聊天组件
            'server_status': 'connected' if config['enabled'] else 'disabled'
        })
        
    except Exception as e:
        logger.error(f"获取Dify配置失败: {e}")
        return jsonify({'error': '获取配置失败'}), 500

@dify_proxy_bp.route('/test-connection', methods=['POST'])
@login_required
def test_connection():
    """测试Dify连接"""
    try:
        config = get_dify_config()
        
        # 测试连接到Dify服务器
        test_url = f"{config['server']}/health"
        
        response = requests.get(test_url, timeout=5)
        
        if response.status_code == 200:
            return jsonify({
                'status': 'success',
                'message': '连接Dify服务器成功',
                'server': config['server']
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Dify服务器响应错误: {response.status_code}'
            }), 502
            
    except requests.exceptions.RequestException as e:
        logger.error(f"测试Dify连接失败: {e}")
        return jsonify({
            'status': 'error',
            'message': f'无法连接到Dify服务器: {str(e)}'
        }), 502
    except Exception as e:
        logger.error(f"测试Dify连接失败: {e}")
        return jsonify({
            'status': 'error',
            'message': '服务器内部错误'
        }), 500

# 处理CORS预检请求
@dify_proxy_bp.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = Response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response

@dify_proxy_bp.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response 