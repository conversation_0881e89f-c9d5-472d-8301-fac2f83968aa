app:
  description: YXKJ-产品进度查询AI助手1.0
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: YXKJ-产品进度查询AI助手1.0
  use_icon_as_answer_icon: true
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.4@03ff7db65ec6cebcb6065a12b41751f4dbb55c0480a5eb65dbb14e4b9bfe302a
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: jaguarliuu/rookie_text2data:0.3.0@c7fdd3a69ec976a7054fdbb6d159a201060f82ee49881d531c875dc2da0d3d91
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: bowenliang123/md_exporter:0.5.0@7e9d1dd9aaac0e3206d44a90f5b1f993f27f0e6c4e6c61f0d937d28e386616a7
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.18@ca40ec06ff35ca611fa5fdf99a15eeb007a9fe3bd725c9ff6d0436469ab0edc9
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 您好！我是YXKJ-AI助手，可以帮助您解答关于生产排程、订单管理、资源分配等方面的问题。请问有什么可以帮助您的吗？
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我们一共有多少台handler?每种handler有多少台？生成饼图
    - 5103这颗产品WIP中量产批次有多少个批次？每个批次有多少颗？
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: tool
        targetType: if-else
      id: 1742881171324-source-1743045229587-target
      selected: false
      source: '1742881171324'
      sourceHandle: source
      target: '1743045229587'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1743045229587-694b1796-23de-4abe-b11f-64fc355718c8-1743224205931-target
      selected: false
      source: '1743045229587'
      sourceHandle: 694b1796-23de-4abe-b11f-64fc355718c8
      target: '1743224205931'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1743224205931-source-1743224889674-target
      selected: false
      source: '1743224205931'
      sourceHandle: source
      target: '1743224889674'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: answer
      id: 1742881171324-fail-branch-1743663218558-target
      selected: false
      source: '1742881171324'
      sourceHandle: fail-branch
      target: '1743663218558'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: tool
      id: 1743662929844-source-1742881171324-target
      source: '1743662929844'
      sourceHandle: source
      target: '1742881171324'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1743045229587-false-1746607115852-target
      source: '1743045229587'
      sourceHandle: 'false'
      target: '1746607115852'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1746607115852-source-answer-target
      source: '1746607115852'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1743224889674-source-1747280804133-target
      source: '1743224889674'
      sourceHandle: source
      target: '1747280804133'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1747280804133-source-1743224945651-target
      source: '1747280804133'
      sourceHandle: source
      target: '1743224945651'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1743146693834-source-1743047486499-target
      source: '1743146693834'
      sourceHandle: source
      target: '1743047486499'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1743045229587-true-1747992329329-target
      source: '1743045229587'
      sourceHandle: 'true'
      target: '1747992329329'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1747992329329-source-1743146693834-target
      source: '1747992329329'
      sourceHandle: source
      target: '1743146693834'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1742881140864-source-1743664802540-target
      source: '1742881140864'
      sourceHandle: source
      target: '1743664802540'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1743664802540-source-1749543280670-target
      source: '1743664802540'
      sourceHandle: source
      target: '1749543280670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: template-transform
      id: 1749543280670-source-1749612952956-target
      source: '1749543280670'
      sourceHandle: source
      target: '1749612952956'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: tool
      id: 1749612952956-source-1743662929844-target
      source: '1749612952956'
      sourceHandle: source
      target: '1743662929844'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1742881140864'
      position:
        x: 417.18795931626187
        y: 424.9748489006279
      positionAbsolute:
        x: 417.18795931626187
        y: 424.9748489006279
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1746607115852.text#}}'
        desc: ''
        selected: false
        title: 回复数据
        type: answer
        variables: []
      height: 105
      id: answer
      position:
        x: 1283.6491101150077
        y: 1263.092374374328
      positionAbsolute:
        x: 1283.6491101150077
        y: 1263.092374374328
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_strategy: fail-branch
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          label:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          llm_description: Database type
          max: null
          min: null
          name: db_type
          options:
          - label:
              en_US: MySQL
              ja_JP: MySQL
              pt_BR: MySQL
              zh_Hans: MySQL
            value: mysql
          - label:
              en_US: PostgreSQL
              ja_JP: PostgreSQL
              pt_BR: PostgreSQL
              zh_Hans: PostgreSQL
            value: postgresql
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          label:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          llm_description: Database ip/host
          max: null
          min: null
          name: host
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          label:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          llm_description: Database port
          max: 65535
          min: 1
          name: port
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          label:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          llm_description: Database name
          max: null
          min: null
          name: db_name
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          label:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          llm_description: Username
          max: null
          min: null
          name: username
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          label:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          llm_description: Password
          max: null
          min: null
          name: password
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: secret-input
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Fetching data from the database using natural language.
            ja_JP: Fetching data from the database using natural language.
            pt_BR: Fetching data from the database using natural language.
            zh_Hans: Fetching data from the database using natural language.
          label:
            en_US: SQL string
            ja_JP: SQL string
            pt_BR: SQL string
            zh_Hans: 待执行的 SQL 语句
          llm_description: Fetching data from the database using natural language.
          max: null
          min: null
          name: sql
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          db_name: ''
          db_type: ''
          host: ''
          password: ''
          port: ''
          sql: ''
          username: ''
        provider_id: jaguarliuu/rookie_text2data/rookie_text2data
        provider_name: jaguarliuu/rookie_text2data/rookie_text2data
        provider_type: builtin
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: true
        title: rookie_excute_sql
        tool_configurations:
          db_name: test
          db_type: mysql
          host: ************
          password: Pw.250416
          port: 3306
          result_format: text
          username: user01
        tool_label: rookie_excute_sql
        tool_name: rookie_excute_sql
        tool_parameters:
          schema:
            type: mixed
            value: ''
          sql:
            type: mixed
            value: '{{#1743662929844.text#}}'
        type: tool
      height: 308
      id: '1742881171324'
      position:
        x: 1585.4516477675174
        y: 398.81229211385426
      positionAbsolute:
        x: 1585.4516477675174
        y: 398.81229211385426
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: 461235d3-716d-4dfa-9b4c-77dbbe7912a4
            value: 图
            varType: string
            variable_selector:
            - sys
            - query
          id: 'true'
          logical_operator: and
        - case_id: 694b1796-23de-4abe-b11f-64fc355718c8
          conditions:
          - comparison_operator: contains
            id: 49a094e6-c2be-4044-bfa7-aa30b60ea97a
            value: excel
            varType: string
            variable_selector:
            - sys
            - query
          - comparison_operator: contains
            id: 97320255-d2c5-4095-857d-3c4586e977d9
            value: Excel
            varType: string
            variable_selector:
            - sys
            - query
          - comparison_operator: contains
            id: 1fcb3ff4-ec52-4df0-b9c9-5ea896ea18af
            value: EXCEL
            varType: string
            variable_selector:
            - sys
            - query
          id: 694b1796-23de-4abe-b11f-64fc355718c8
          logical_operator: or
        desc: ''
        selected: false
        title: 条件分支
        type: if-else
      height: 226
      id: '1743045229587'
      position:
        x: 537.3234497346841
        y: 846.8046505626968
      positionAbsolute:
        x: 537.3234497346841
        y: 846.8046505626968
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1747992329329.text#}}

          {{#1743146693834.text#}}'
        desc: ''
        selected: false
        title: 输出图表
        type: answer
        variables: []
      height: 124
      id: '1743047486499'
      position:
        x: 1890.549890295225
        y: 879.3210225190487
      positionAbsolute:
        x: 1890.549890295225
        y: 879.3210225190487
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 7ee0c95d-88ef-4e24-852a-84638779b6c3
          role: system
          text: '你是Echarts可视化专家，收到结构化数据后，请严格按照一下要求生成图表option:

            【输入要求】

            输入内容为：{{#context#}}{{#sys.query#}}

            只有当输入中包含可用于绘制图表的结构化数据时，才需要相应，否则请直接返回空字符串。

            【处理要求】

            先判断数据类型（如数值型，分类型，时间序列等），再自动选择最合适的EChats图表类型（如bar,line,pie等）。

            option配置需完整，包含tile,tooltip,legend,series等常用字段，series.type与数据类型匹配。

            title.text请用简洁中文自动生成，准确反映数据内容。响应式布局布局为默认设置。

            series.data中必须包含所有原始数据点，不允许遗漏，采样或合并。

            【输出要求】

            输出内容必须包含字符串，且以```echats开头，以```结尾，中间为完整的echarts option JSON。

            只输出option配置本身，不允许有任何注释，说明，前后缀或多余内容。

            若输入不满足绘图条件，直接输出空字符串。'
        - id: 877920da-3208-49b1-8d05-fbcae2fe7d7a
          role: user
          text: '用户需求：{{#1743664802540.text#}}

            数据内容：{{#1742881171324.text#}}'
        selected: false
        title: 图表生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743146693834'
      position:
        x: 1526.3030158541394
        y: 879.3210225190487
      positionAbsolute:
        x: 1526.3030158541394
        y: 879.3210225190487
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: a66a786f-2e55-4f8a-bb4a-317d0da0330b
          role: system
          text: '# 任务

            把数据库数据转化为表格类型的markdown文本内容，把表头翻译为中文，

            # 输出

            只输出转化后的表格类型的markdown，不要’‘''markdown\n''''''等语言标签，只输出标准的markdwon表格，输出的表格名称需要符合用户的问题

            注意：

            数据包含竖线 |​​：需要转义为 \|

            ​​中文字符问题​​：确保数据库连接使用UTF-8编码

            ​​空结果处理​​：添加空值占位符'
        - id: 4d824ec5-a785-4465-b6ac-762f2ee016cd
          role: user
          text: '用户需求：{{#sys.query#}}

            数据内容：{{#1742881171324.text#}}

            '
        selected: false
        title: markdown转换
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743224205931'
      position:
        x: 931.8795098790367
        y: 1021.592243823186
      positionAbsolute:
        x: 931.8795098790367
        y: 1021.592243823186
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Markdown table text
            ja_JP: Markdown table text
            pt_BR: Markdown table text
            zh_Hans: Markdown格式文本，必须为Markdown表格格式，可包含多个表格
          label:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          llm_description: ''
          max: null
          min: null
          name: md_text
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          md_text: ''
        provider_id: bowenliang123/md_exporter/md_exporter
        provider_name: bowenliang123/md_exporter/md_exporter
        provider_type: builtin
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: Markdown转XLSX文件
        tool_configurations: {}
        tool_label: Markdown转XLSX文件
        tool_name: md_to_xlsx
        tool_parameters:
          md_text:
            type: mixed
            value: '{{#1743224205931.text#}}'
        type: tool
      height: 80
      id: '1743224889674'
      position:
        x: 1260.229950542449
        y: 1021.592243823186
      positionAbsolute:
        x: 1260.229950542449
        y: 1021.592243823186
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1747280804133.text#}}{{#1743224889674.files#}}'
        desc: 输出excel文件
        selected: false
        title: 生成excel
        type: answer
        variables: []
      height: 152
      id: '1743224945651'
      position:
        x: 1885.4516477675174
        y: 1021.592243823186
      positionAbsolute:
        x: 1885.4516477675174
        y: 1021.592243823186
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          label:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          llm_description: Database type
          max: null
          min: null
          name: db_type
          options:
          - label:
              en_US: MySQL
              ja_JP: MySQL
              pt_BR: MySQL
              zh_Hans: MySQL
            value: mysql
          - label:
              en_US: PostgreSQL
              ja_JP: PostgreSQL
              pt_BR: PostgreSQL
              zh_Hans: PostgreSQL
            value: postgresql
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: limit
            ja_JP: limit
            pt_BR: limit
            zh_Hans: SQL返回数据量限制
          label:
            en_US: limit
            ja_JP: limit
            pt_BR: limit
            zh_Hans: SQL返回数据量限制
          llm_description: limit
          max: 1000
          min: 1
          name: limit
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: json
          form: form
          human_description:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          label:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          llm_description: result_format
          max: null
          min: null
          name: result_format
          options:
          - label:
              en_US: JSON
              ja_JP: JSON
              pt_BR: JSON
              zh_Hans: JSON
            value: json
          - label:
              en_US: TEXT
              ja_JP: TEXT
              pt_BR: TEXT
              zh_Hans: TEXT
            value: text
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          label:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          llm_description: Database ip/host
          max: null
          min: null
          name: host
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          label:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          llm_description: Database port
          max: 65535
          min: 1
          name: port
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          label:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          llm_description: Database name
          max: null
          min: null
          name: db_name
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: table_names
            ja_JP: table_names
            pt_BR: table_names
            zh_Hans: 数据表名称
          label:
            en_US: table_names
            ja_JP: table_names
            pt_BR: table_names
            zh_Hans: 数据表名称
          llm_description: table_names
          max: null
          min: null
          name: table_names
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          label:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          llm_description: Username
          max: null
          min: null
          name: username
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          label:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          llm_description: Password
          max: null
          min: null
          name: password
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: secret-input
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: LLM model for text2data.
            ja_JP: LLM model for text2data.
            pt_BR: LLM model for text2data.
            zh_Hans: LLM model for text2data.
          label:
            en_US: Model
            ja_JP: Model
            pt_BR: Model
            zh_Hans: 模型
          llm_description: LLM model for text2data.
          max: null
          min: null
          name: model
          options: []
          placeholder: null
          precision: null
          required: true
          scope: llm
          template: null
          type: model-selector
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Fetching data from the database using natural language.
            ja_JP: Fetching data from the database using natural language.
            pt_BR: Fetching data from the database using natural language.
            zh_Hans: Fetching data from the database using natural language.
          label:
            en_US: Query string
            ja_JP: Query string
            pt_BR: Query string
            zh_Hans: 查询语句
          llm_description: Fetching data from the database using natural language.
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: custom_prompt
            ja_JP: custom_prompt
            pt_BR: custom_prompt
            zh_Hans: 自定义提示
          label:
            en_US: custom_prompt
            ja_JP: custom_prompt
            pt_BR: custom_prompt
            zh_Hans: 自定义提示
          llm_description: custom_prompt
          max: null
          min: null
          name: custom_prompt
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: with_comment
            ja_JP: with_comment
            pt_BR: with_comment
            zh_Hans: 是否包含注释
          label:
            en_US: with_comment
            ja_JP: with_comment
            pt_BR: with_comment
            zh_Hans: 是否包含注释
          llm_description: with_comment
          max: null
          min: null
          name: with_comment
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        params:
          custom_prompt: ''
          db_name: ''
          db_type: ''
          host: ''
          limit: ''
          model: ''
          password: ''
          port: ''
          query: ''
          result_format: ''
          table_names: ''
          username: ''
          with_comment: ''
        provider_id: jaguarliuu/rookie_text2data/rookie_text2data
        provider_name: jaguarliuu/rookie_text2data/rookie_text2data
        provider_type: builtin
        selected: false
        title: rookie_text2data
        tool_configurations:
          db_name: test
          db_type: mysql
          host: ************
          limit: 999
          model:
            completion_params: {}
            mode: chat
            model: deepseek-v3
            model_type: llm
            provider: langgenius/tongyi/tongyi
            type: model-selector
          password: Pw.250416
          port: 3306
          result_format: text
          username: user01
          with_comment: 0
        tool_label: rookie_text2data
        tool_name: rookie_text2data
        tool_parameters:
          custom_prompt:
            type: mixed
            value: '# 数据表结构说明

              {{#1749612952956.output#}}

              # 注意事项

              1. 如果没有说明，查不到信息，不要乱编！

              2. 如果要计算开机率，则利用EQP_STATUS表里的机器状态来计算，除了IDLE的机台，其他都可以i计入已经开机，然后利用已开机的机台总数除以总机台数得到最终的开机率百分比

              3.关于机台分类，请直接按照EQP_STATUS表里的机台类型(HANDLER_TYPE)进行分类

              4. 三温的意思是温度能力(TEMPERATURE_CAPACITY)的信息里是“常高低温”的三个温度

              5.当用户需要查询某个产品的进度时，直接查询WIP_LOT表，DEVICE的字段查询时不需要精准匹配，只需要包含问题中的产品名称字段。LOT_TYPE的字段有两个选项，分别是”量产批“和“工程批”。WIP_STATE里的Released状态表示已投产，PROC_STATE里的Wait表示待测试，Process表示已上机，HOLD_STATE里NotHold和Hold表示是否扣留。

              6.输出时不要使用中文，应该使用表头对应的英文名称'
          query:
            type: mixed
            value: '{{#1743664802540.text#}}'
          schema_name:
            type: mixed
            value: ''
          table_names:
            type: mixed
            value: ''
        type: tool
      height: 324
      id: '1743662929844'
      position:
        x: 1205.0159446872472
        y: 398.81229211385426
      positionAbsolute:
        x: 1205.0159446872472
        y: 398.81229211385426
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: SQL执行失败，请重试！
        desc: ''
        selected: false
        title: SQL执行失败
        type: answer
        variables: []
      height: 102
      id: '1743663218558'
      position:
        x: 1958.8158392463833
        y: 625.6450599041566
      positionAbsolute:
        x: 1958.8158392463833
        y: 625.6450599041566
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - sys
          - query
        desc: ''
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: true
            size: 25
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: e5d4196f-30cd-4763-bbf9-52b24ac837bb
          role: system
          text: '# 角色

            你是文字提炼专家

            # 任务

            负责把用户的查询数据需求根据{{#context#}}的结果，进行提炼，总结，抓取关键的数据查询文字，去除掉和数据查询的无关内容，比如生成图表，生成Excel，生成表单等和数据查询无关的内容，只保留提炼查询数据的文字

            # 输出

            只输出你提炼后的结果，无需其他言语

            #注意

            当用户在询问你的问题里，包含产品或者批次信息时，需要抓取对应产品和批次信息关联的封装类型和工序信息'
        - id: 28fe0013-8977-4c4c-bf7f-b0fc59c96cba
          role: user
          text: 用户的查询数据需求：{{#sys.query#}}
        selected: false
        title: 需求提炼器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743664802540'
      position:
        x: 756.3899007108101
        y: 317.9462075001902
      positionAbsolute:
        x: 756.3899007108101
        y: 317.9462075001902
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1742881171324'
          - text
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: true
            size: 25
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 089c9451-a4e2-46fc-ad22-6cc62ed80b8c
          role: system
          text: '#角色

            你是数据分析大师和总结者

            # 任务

            你可以结合用户的问题和从数据库查询到的结果{{#context#}}，整合提炼后用精准合适的自然语言回复用户，不要编造假数据

            #输出

            1.若用户只查询数据，则提炼后回复总结后的信息和数据计算过程和明细的数据

            2.若用户需要你分析数据，则你可以i结合数据给出专业的分析内容

            '
        - id: 19a7b350-b8a2-4eaa-875b-c2f856ce1e80
          role: user
          text: '用户问题：{{#sys.query#}}

            数据库数据：{{#1742881171324.text#}}'
        selected: false
        title: 总结回复1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1746607115852'
      position:
        x: 931.8795098790367
        y: 1263.092374374328
      positionAbsolute:
        x: 931.8795098790367
        y: 1263.092374374328
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: qvq-max-latest
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: ff6b7089-104b-4c09-be98-24ef26f0ff28
          role: system
          text: '#角色

            你是数据分析大师和总结者

            # 任务

            你可以结合用户的问题和从数据库查询到的结果，整合提炼后用精准合适的自然语言回复用户，不要编造假数据

            #输出

            1.若用户只查询数据，则提炼后回复总结后的信息和数据计算过程和明细的数据

            2.若用户需要你分析数据，则你可以i结合数据给出专业的分析内容'
        - id: edf6c860-5047-4af3-937a-cbbbc3b311b3
          role: user
          text: '用户问题：{{#sys.query#}}

            数据库数据：{{#1742881171324.text#}}

            '
        selected: false
        title: 总结回复2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1747280804133'
      position:
        x: 1585.4516477675174
        y: 1021.592243823186
      positionAbsolute:
        x: 1585.4516477675174
        y: 1021.592243823186
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: d4a6e9ed-dfc4-42bd-b49d-f7884a9dbebf
          role: system
          text: '请根据用户问题和查询结果，用简介的中文自然语言回答。并给出分析意见。

            用户问题：{{#1742881171324.text#}}

            查询结果：{{#context#}}

            '
        selected: false
        title: 结果汇总
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1747992329329'
      position:
        x: 1083.4364549621855
        y: 879.3210225190487
      positionAbsolute:
        x: 1083.4364549621855
        y: 879.3210225190487
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - b20b9083-1a79-43cc-9427-474f3d32cf96
        desc: ''
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: weighted_score
          reranking_model:
            model: gte-rerank-v2
            provider: langgenius/tongyi/tongyi
          score_threshold: 0.3
          top_k: 8
          weights:
            keyword_setting:
              keyword_weight: 0
            vector_setting:
              embedding_model_name: bge-m3
              embedding_provider_name: langgenius/ollama/ollama
              vector_weight: 1
        query_variable_selector:
        - '1743664802540'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 92
      id: '1749543280670'
      position:
        x: 756.3899007108101
        y: 460.6510627007739
      positionAbsolute:
        x: 756.3899007108101
        y: 460.6510627007739
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{% for item in arg1 %}\r\n{{ item.content }}\r\n{# 添加分隔线区分不同条目\
          \ #}\r\n{% if not loop.last %}----------------------------------------{%\
          \ endif %}\r\n{% endfor %}"
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1749543280670'
          - result
          variable: arg1
      height: 54
      id: '1749612952956'
      position:
        x: 680.9215128991617
        y: 639.0321317015034
      positionAbsolute:
        x: 680.9215128991617
        y: 639.0321317015034
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -144.88376392909902
      y: 41.61077200227476
      zoom: 0.4829681597234135
