"""Dify配置API接口"""
import json
from flask import Blueprint, request, jsonify, current_app
from flask_login import current_user
from app import db
from app.models import DifyConfig
from app.decorators import login_required

dify_settings_bp = Blueprint('dify_settings', __name__)

@dify_settings_bp.route('/dify-settings', methods=['GET'])
@login_required
def get_dify_settings():
    """获取Dify配置"""
    try:
        # 获取所有配置
        
        configs = DifyConfig.query.all()
        
        # 转换为字典格式，兼容前端
        settings_data = {
            'training_ai': {
                'enabled': False,
                'server_url': 'http://************',
                'api_key': '',
                'app_id': '',
                'chatbot_url': ''
            },
            'progress_ai': {
                'enabled': False,
                'server_url': 'http://************',
                'api_key': '',
                'app_id': '',
                'chatbot_url': ''
            },
            'chatbot': {
                'enabled': True,
                'server_url': 'http://************',
                'token': 'uV72gGRdNz0eP7ac',
                'color': '#b72424',
                'integration_type': 'script'
            }
        }
        
        # 填充实际配置数据
        for config in configs:
            config_dict = config.to_dict(include_sensitive=False)
            
            if config.config_type == 'training_ai':
                settings_data['training_ai'] = {
                    'enabled': config.enabled,
                    'server_url': config.server_url,
                    'api_key': config.get_api_key() or '',
                    'app_id': config.app_id or '',
                    'chatbot_url': config.chatbot_url or ''
                }
            elif config.config_type == 'progress_ai':
                settings_data['progress_ai'] = {
                    'enabled': config.enabled,
                    'server_url': config.server_url,
                    'api_key': config.get_api_key() or '',
                    'app_id': config.app_id or '',
                    'chatbot_url': config.chatbot_url or ''
                }
            elif config.config_type == 'chatbot':
                settings_data['chatbot'] = {
                    'enabled': config.enabled,
                    'server_url': config.server_url,
                    'token': config.chatbot_token or 'uV72gGRdNz0eP7ac',
                    'color': config.chatbot_color or '#b72424',
                    'integration_type': config.integration_type or 'script'
                }
        
        return jsonify(settings_data)
        
    except Exception as e:
        current_app.logger.error(f"获取Dify设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dify_settings_bp.route('/dify-settings', methods=['POST'])
@login_required
def save_dify_settings():
    """保存Dify配置"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': '数据格式错误'
            }), 400
        
        username = current_user.username if current_user.is_authenticated else 'system'
        
        # 处理新员工培训AI助教设置
        if 'training_ai' in data:
            training_config = data['training_ai']
            config = DifyConfig.get_config_by_type('training_ai')
            
            if not config:
                config = DifyConfig(
                    config_name='新员工培训AI助教',
                    config_type='training_ai',
                    description='新员工培训AI助教配置',
                    created_by=username
                )
                db.session.add(config)
            
            # 更新配置
            config.enabled = training_config.get('enabled', False)
            config.server_url = training_config.get('server_url', 'http://************')
            config.app_id = training_config.get('app_id', '')
            config.chatbot_url = training_config.get('chatbot_url', '')
            config.updated_by = username
            
            # 处理API密钥
            api_key = training_config.get('api_key', '')
            if api_key:
                config.set_api_key(api_key)
        
        # 处理产品进度查询AI助手设置
        if 'progress_ai' in data:
            progress_config = data['progress_ai']
            config = DifyConfig.get_config_by_type('progress_ai')
            
            if not config:
                config = DifyConfig(
                    config_name='产品进度查询AI助手',
                    config_type='progress_ai',
                    description='产品进度查询AI助手配置',
                    created_by=username
                )
                db.session.add(config)
            
            # 更新配置
            config.enabled = progress_config.get('enabled', False)
            config.server_url = progress_config.get('server_url', 'http://************')
            config.app_id = progress_config.get('app_id', '')
            config.chatbot_url = progress_config.get('chatbot_url', '')
            config.updated_by = username
            
            # 处理API密钥
            api_key = progress_config.get('api_key', '')
            if api_key:
                config.set_api_key(api_key)
        
        # 处理聊天机器人设置
        if 'chatbot' in data:
            chatbot_config = data['chatbot']
            config = DifyConfig.get_config_by_type('chatbot')
            
            if not config:
                config = DifyConfig(
                    config_name='Dify聊天机器人',
                    config_type='chatbot',
                    description='Dify聊天机器人配置',
                    created_by=username
                )
                db.session.add(config)
            
            # 更新配置
            config.enabled = chatbot_config.get('enabled', True)
            config.server_url = chatbot_config.get('server_url', 'http://************')
            config.chatbot_token = chatbot_config.get('token', 'uV72gGRdNz0eP7ac')
            config.chatbot_color = chatbot_config.get('color', '#b72424')
            config.integration_type = chatbot_config.get('integration_type', 'script')
            config.updated_by = username
        
        # 提交更改
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Dify设置保存成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存Dify设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dify_settings_bp.route('/dify-settings/test', methods=['POST'])
@login_required
def test_dify_connection():
    """测试Dify连接"""
    try:
        data = request.get_json()
        config_type = data.get('type')  # 'training_ai' 或 'progress_ai'
        
        if config_type not in ['training_ai', 'progress_ai']:
            return jsonify({
                'success': False,
                'error': '无效的配置类型'
            }), 400
        
        server_url = data.get('server_url', '').strip()
        api_key = data.get('api_key', '').strip()
        app_id = data.get('app_id', '').strip()
        
        if not all([server_url, api_key, app_id]):
            return jsonify({
                'success': False,
                'error': '请填写完整的连接信息'
            }), 400
        
        # 创建临时配置对象进行测试
        temp_config = DifyConfig(
            config_type=config_type,
            server_url=server_url,
            app_id=app_id
        )
        temp_config.set_api_key(api_key)
        
        # 测试连接
        success, message = temp_config.test_connection()
        
        if success:
            # 如果存在真实配置，更新测试状态
            real_config = DifyConfig.get_config_by_type(config_type)
            if real_config:
                real_config.last_test_at = temp_config.last_test_at
                real_config.last_test_status = temp_config.last_test_status
                real_config.last_test_message = temp_config.last_test_message
                db.session.commit()
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        current_app.logger.error(f"测试Dify连接失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'测试失败: {str(e)}'
        }), 500

@dify_settings_bp.route('/dify-settings/init', methods=['POST'])
@login_required
def init_default_configs():
    """初始化默认配置"""
    try:
        username = current_user.username if current_user.is_authenticated else 'system'
        
        # 创建默认配置
        default_configs = DifyConfig.create_default_configs()
        
        for config in default_configs:
            config.created_by = username
            config.updated_by = username
            db.session.add(config)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'成功初始化{len(default_configs)}个默认配置',
            'created_count': len(default_configs)
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"初始化默认配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dify_settings_bp.route('/dify-settings/configs', methods=['GET'])
@login_required
def get_all_configs():
    """获取所有配置（管理接口）"""
    try:
        configs = DifyConfig.query.all()
        configs_data = [config.to_dict() for config in configs]
        
        return jsonify({
            'success': True,
            'configs': configs_data
        })
        
    except Exception as e:
        current_app.logger.error(f"获取配置列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 