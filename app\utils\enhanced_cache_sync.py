#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 Phase 7: 强化缓存同步机制
解决数据一致性问题，提供精确的缓存管理策略

核心特性：
1. 细粒度缓存失效和更新
2. 数据一致性实时监控
3. 智能缓存预热和刷新
4. 多层缓存协调管理
5. 事件驱动的缓存同步
"""

import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable, Set, Tuple
from datetime import datetime, timedelta
from contextlib import contextmanager
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class CacheEventType(Enum):
    """缓存事件类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    BULK_OPERATION = "bulk_operation"
    DATA_INCONSISTENCY = "data_inconsistency"
    CACHE_MISS = "cache_miss"
    MANUAL_REFRESH = "manual_refresh"

class CacheSyncStrategy(Enum):
    """缓存同步策略"""
    IMMEDIATE = "immediate"          # 立即同步
    DELAYED = "delayed"              # 延迟同步  
    BATCH = "batch"                  # 批量同步
    SMART_REFRESH = "smart_refresh"  # 智能刷新
    PRIORITY_BASED = "priority_based" # 基于优先级

@dataclass
class CacheEvent:
    """缓存事件"""
    event_type: CacheEventType
    table_name: str
    affected_data_types: List[str]
    record_ids: List[Any]
    timestamp: datetime
    metadata: Dict[str, Any] = None

@dataclass 
class CacheSyncRule:
    """缓存同步规则"""
    data_type: str
    dependencies: List[str]  # 依赖的数据类型
    sync_strategy: CacheSyncStrategy
    priority: int  # 1-10，数字越小优先级越高
    batch_window_seconds: int = 5  # 批量同步窗口
    consistency_check_interval: int = 60  # 一致性检查间隔(秒)

class EnhancedCacheSync:
    """
    🔥 强化缓存同步机制
    
    设计理念：
    - 精确控制缓存生命周期
    - 主动数据一致性保障  
    - 智能预测性缓存刷新
    - 事件驱动的同步机制
    """
    
    def __init__(self):
        """初始化强化缓存同步管理器"""
        
        # 🔒 线程安全
        self._lock = threading.RLock()
        self._event_lock = threading.Lock()
        
        # 📊 事件队列和统计
        self._pending_events: List[CacheEvent] = []
        self._processed_events: List[CacheEvent] = []
        self._sync_stats = defaultdict(int)
        
        # 🎯 同步规则配置
        self._sync_rules: Dict[str, CacheSyncRule] = {}
        self._table_to_data_types: Dict[str, List[str]] = {}
        self._dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        
        # ⏰ 定时器和批量处理
        self._batch_timers: Dict[str, threading.Timer] = {}
        self._consistency_check_timers: Dict[str, threading.Timer] = {}
        
        # 🏃 工作线程
        self._sync_thread: Optional[threading.Thread] = None
        self._is_running = False
        
        # 📈 性能监控
        self._performance_metrics = {
            'sync_operations': 0,
            'consistency_checks': 0,
            'data_inconsistencies_detected': 0,
            'auto_fixes_applied': 0,
            'cache_hit_improvements': 0
        }
        
        # 初始化同步规则
        self._init_sync_rules()
        self._start_sync_worker()
        
        logger.info("🔥 强化缓存同步机制初始化完成")
    
    def _init_sync_rules(self):
        """初始化缓存同步规则"""
        
        # 🏃 高优先级关键数据 - 立即同步
        critical_rules = [
            CacheSyncRule(
                data_type='wait_lot_data',
                dependencies=['wip_lots', 'equipment_status_data'],
                sync_strategy=CacheSyncStrategy.IMMEDIATE,
                priority=1,
                consistency_check_interval=30
            ),
            CacheSyncRule(
                data_type='equipment_status_data', 
                dependencies=['wip_lots'],
                sync_strategy=CacheSyncStrategy.IMMEDIATE,
                priority=1,
                consistency_check_interval=60
            ),
            CacheSyncRule(
                data_type='wip_lots',
                dependencies=[],
                sync_strategy=CacheSyncStrategy.IMMEDIATE,
                priority=2,
                consistency_check_interval=60
            )
        ]
        
        # 📊 中优先级业务数据 - 智能刷新
        business_rules = [
            CacheSyncRule(
                data_type='priority_config',
                dependencies=['wait_lot_data'],
                sync_strategy=CacheSyncStrategy.SMART_REFRESH,
                priority=3,
                batch_window_seconds=10,
                consistency_check_interval=300
            ),
            CacheSyncRule(
                data_type='device_priority_config',
                dependencies=['equipment_status_data'],
                sync_strategy=CacheSyncStrategy.SMART_REFRESH,
                priority=3,
                batch_window_seconds=10,
                consistency_check_interval=300
            )
        ]
        
        # 🔧 低优先级配置数据 - 批量同步
        config_rules = [
            CacheSyncRule(
                data_type='test_specs',
                dependencies=[],
                sync_strategy=CacheSyncStrategy.BATCH,
                priority=5,
                batch_window_seconds=30,
                consistency_check_interval=1800
            ),
            CacheSyncRule(
                data_type='uph_data',
                dependencies=[],
                sync_strategy=CacheSyncStrategy.BATCH,
                priority=5,
                batch_window_seconds=30,
                consistency_check_interval=1800
            )
        ]
        
        # 注册所有规则
        all_rules = critical_rules + business_rules + config_rules
        for rule in all_rules:
            self._sync_rules[rule.data_type] = rule
            
        # 🗺️ 构建表名到数据类型的映射
        self._table_to_data_types.update({
            'et_wait_lot': ['wait_lot_data'],
            'ET_WAIT_LOT': ['wait_lot_data'],
            'eqp_status': ['equipment_status_data'],
            'EQP_STATUS': ['equipment_status_data'],
            'wip_lot': ['wip_lots'],
            'WIP_LOT': ['wip_lots'],
            'et_ft_test_spec': ['test_specs'],
            'ET_FT_TEST_SPEC': ['test_specs'],
            'et_uph_eqp': ['uph_data'],
            'ET_UPH_EQP': ['uph_data'],
            'priority_config': ['priority_config'],
            'device_priority_config': ['device_priority_config']
        })
        
        # 🕸️ 构建依赖关系图
        for rule in all_rules:
            for dep in rule.dependencies:
                self._dependency_graph[dep].add(rule.data_type)
        
        logger.info(f"📋 已初始化 {len(all_rules)} 个缓存同步规则")
    
    def _start_sync_worker(self):
        """启动同步工作线程"""
        self._is_running = True
        self._sync_thread = threading.Thread(target=self._sync_worker_loop, daemon=True)
        self._sync_thread.start()
        logger.info("🏃 缓存同步工作线程已启动")
    
    def _sync_worker_loop(self):
        """同步工作线程主循环"""
        while self._is_running:
            try:
                # 处理待处理事件
                self._process_pending_events()
                
                # 定期一致性检查
                self._perform_scheduled_consistency_checks()
                
                # 清理过期事件
                self._cleanup_processed_events()
                
                time.sleep(1)  # 1秒检查间隔
                
            except Exception as e:
                logger.error(f"❌ 缓存同步工作线程异常: {e}")
                time.sleep(5)  # 异常时等待5秒再继续
    
    def on_data_change(self, table_name: str, event_type: CacheEventType, 
                      record_ids: List[Any] = None, metadata: Dict[str, Any] = None):
        """
        🔥 数据变更事件处理
        
        Args:
            table_name: 变更的表名
            event_type: 事件类型
            record_ids: 影响的记录ID列表
            metadata: 事件元数据
        """
        
        affected_data_types = self._table_to_data_types.get(table_name, [])
        if not affected_data_types:
            logger.debug(f"⏭️ 表 {table_name} 未配置缓存同步规则，跳过处理")
            return
        
        # 创建事件
        event = CacheEvent(
            event_type=event_type,
            table_name=table_name,
            affected_data_types=affected_data_types,
            record_ids=record_ids or [],
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        # 添加到待处理队列
        with self._event_lock:
            self._pending_events.append(event)
            
        logger.info(f"📢 记录数据变更事件: {table_name}.{event_type.value}, 影响数据类型: {affected_data_types}")
        
        # 高优先级事件立即处理
        critical_data_types = ['wait_lot_data', 'equipment_status_data', 'wip_lots']
        if any(dt in critical_data_types for dt in affected_data_types):
            self._process_event_immediately(event)
    
    def _process_pending_events(self):
        """处理待处理事件队列"""
        events_to_process = []
        
        with self._event_lock:
            events_to_process = self._pending_events.copy()
            self._pending_events.clear()
        
        for event in events_to_process:
            try:
                self._process_single_event(event)
                self._processed_events.append(event)
                self._sync_stats['events_processed'] += 1
                
            except Exception as e:
                logger.error(f"❌ 处理缓存事件失败: {event.table_name}.{event.event_type.value}, 错误: {e}")
                self._sync_stats['events_failed'] += 1
    
    def _process_single_event(self, event: CacheEvent):
        """处理单个缓存事件"""
        for data_type in event.affected_data_types:
            rule = self._sync_rules.get(data_type)
            if not rule:
                continue
                
            if rule.sync_strategy == CacheSyncStrategy.IMMEDIATE:
                self._sync_immediately(data_type, event)
            elif rule.sync_strategy == CacheSyncStrategy.SMART_REFRESH:
                self._sync_with_smart_refresh(data_type, event)
            elif rule.sync_strategy == CacheSyncStrategy.BATCH:
                self._schedule_batch_sync(data_type, event)
            elif rule.sync_strategy == CacheSyncStrategy.PRIORITY_BASED:
                self._sync_priority_based(data_type, event)
    
    def _process_event_immediately(self, event: CacheEvent):
        """立即处理高优先级事件"""
        logger.info(f"⚡ 立即处理高优先级事件: {event.table_name}.{event.event_type.value}")
        self._process_single_event(event)
        
        # 移动到已处理队列
        with self._event_lock:
            if event in self._pending_events:
                self._pending_events.remove(event)
            self._processed_events.append(event)
    
    def _sync_immediately(self, data_type: str, event: CacheEvent):
        """立即同步缓存"""
        try:
            # 清理指定数据类型的缓存
            self._clear_cache_by_type(data_type)
            
            # 触发依赖数据类型的同步
            self._cascade_sync_dependencies(data_type)
            
            logger.info(f"⚡ 立即同步完成: {data_type}")
            self._sync_stats['immediate_syncs'] += 1
            
        except Exception as e:
            logger.error(f"❌ 立即同步失败: {data_type}, 错误: {e}")
            self._sync_stats['sync_failures'] += 1
    
    def _sync_with_smart_refresh(self, data_type: str, event: CacheEvent):
        """智能刷新缓存"""
        try:
            # 检查缓存年龄和使用频率
            cache_age = self._get_cache_age(data_type)
            usage_frequency = self._get_cache_usage_frequency(data_type)
            
            # 基于年龄和使用频率决定是否刷新
            should_refresh = (
                cache_age > 300 or  # 超过5分钟
                usage_frequency > 10 or  # 高频使用
                event.event_type in [CacheEventType.CREATE, CacheEventType.DELETE]  # 关键变更
            )
            
            if should_refresh:
                self._clear_cache_by_type(data_type)
                # 可选择预热缓存
                if usage_frequency > 5:  # 中高频数据预热
                    self._preheat_cache(data_type)
                
                logger.info(f"🧠 智能刷新完成: {data_type} (年龄:{cache_age}s, 频率:{usage_frequency})")
                self._sync_stats['smart_refreshes'] += 1
            else:
                logger.debug(f"🧠 智能刷新跳过: {data_type} (不满足刷新条件)")
                
        except Exception as e:
            logger.error(f"❌ 智能刷新失败: {data_type}, 错误: {e}")
    
    def _schedule_batch_sync(self, data_type: str, event: CacheEvent):
        """安排批量同步"""
        rule = self._sync_rules.get(data_type)
        if not rule:
            return
            
        # 取消现有定时器
        if data_type in self._batch_timers:
            self._batch_timers[data_type].cancel()
        
        # 创建新的延迟同步定时器
        timer = threading.Timer(
            rule.batch_window_seconds,
            self._execute_batch_sync,
            args=[data_type, event]
        )
        timer.start()
        self._batch_timers[data_type] = timer
        
        logger.debug(f"⏰ 已安排批量同步: {data_type}, 延迟 {rule.batch_window_seconds}s")
    
    def _execute_batch_sync(self, data_type: str, event: CacheEvent):
        """执行批量同步"""
        try:
            self._clear_cache_by_type(data_type)
            
            # 清理定时器引用
            if data_type in self._batch_timers:
                del self._batch_timers[data_type]
            
            logger.info(f"📦 批量同步完成: {data_type}")
            self._sync_stats['batch_syncs'] += 1
            
        except Exception as e:
            logger.error(f"❌ 批量同步失败: {data_type}, 错误: {e}")
    
    def _cascade_sync_dependencies(self, data_type: str):
        """级联同步依赖数据类型"""
        dependent_types = self._dependency_graph.get(data_type, set())
        
        for dep_type in dependent_types:
            try:
                # 创建依赖同步事件
                dep_event = CacheEvent(
                    event_type=CacheEventType.UPDATE,
                    table_name=f"dependency_of_{data_type}",
                    affected_data_types=[dep_type],
                    record_ids=[],
                    timestamp=datetime.now(),
                    metadata={'cascade_from': data_type}
                )
                
                # 根据依赖类型的规则处理
                self._process_single_event(dep_event)
                
            except Exception as e:
                logger.error(f"❌ 级联同步失败: {data_type} -> {dep_type}, 错误: {e}")
        
        if dependent_types:
            logger.info(f"🔗 级联同步完成: {data_type} -> {list(dependent_types)}")
    
    def _clear_cache_by_type(self, data_type: str):
        """按数据类型清理缓存"""
        try:
            # 使用简化缓存系统的分类清理功能
            from app.utils.simple_cache import cache_clear_category
            cache_clear_category(data_type)
            
            logger.debug(f"🧹 已清理缓存分类: {data_type}")
            
        except Exception as e:
            logger.error(f"❌ 清理缓存失败: {data_type}, 错误: {e}")
    
    def _preheat_cache(self, data_type: str):
        """预热缓存"""
        try:
            # 根据数据类型预热相应数据
            if data_type == 'wait_lot_data':
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                manager.get_wait_lot_data()  # 触发数据加载和缓存
                
            elif data_type == 'equipment_status_data':
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                manager.get_equipment_status_data()
                
            logger.info(f"🔥 缓存预热完成: {data_type}")
            self._sync_stats['cache_preheats'] += 1
            
        except Exception as e:
            logger.error(f"❌ 缓存预热失败: {data_type}, 错误: {e}")
    
    def _get_cache_age(self, data_type: str) -> int:
        """获取缓存年龄（秒）"""
        try:
            from app.utils.simple_cache import get_simple_cache
            cache = get_simple_cache()
            # 简化实现：返回估算年龄
            return 0  # 实际实现需要访问缓存的创建时间
        except:
            return 0
    
    def _get_cache_usage_frequency(self, data_type: str) -> int:
        """获取缓存使用频率"""
        # 简化实现：基于统计返回频率
        return self._sync_stats.get(f'usage_{data_type}', 0)
    
    def _perform_scheduled_consistency_checks(self):
        """执行定期一致性检查"""
        current_time = time.time()
        
        for data_type, rule in self._sync_rules.items():
            last_check_key = f'last_consistency_check_{data_type}'
            last_check = self._sync_stats.get(last_check_key, 0)
            
            if current_time - last_check >= rule.consistency_check_interval:
                self._perform_consistency_check(data_type)
                self._sync_stats[last_check_key] = current_time
    
    def _perform_consistency_check(self, data_type: str):
        """执行数据一致性检查"""
        try:
            inconsistencies = self._check_data_consistency(data_type)
            
            if inconsistencies:
                logger.warning(f"⚠️ 发现数据不一致: {data_type}, 问题数: {len(inconsistencies)}")
                
                # 自动修复
                self._auto_fix_inconsistencies(data_type, inconsistencies)
                self._sync_stats['data_inconsistencies_detected'] += len(inconsistencies)
            else:
                logger.debug(f"✅ 数据一致性检查通过: {data_type}")
                
            self._sync_stats['consistency_checks'] += 1
            
        except Exception as e:
            logger.error(f"❌ 一致性检查失败: {data_type}, 错误: {e}")
    
    def _check_data_consistency(self, data_type: str) -> List[Dict[str, Any]]:
        """检查数据一致性"""
        inconsistencies = []
        
        try:
            if data_type == 'wait_lot_data':
                inconsistencies.extend(self._check_wait_lot_consistency())
            elif data_type == 'equipment_status_data':
                inconsistencies.extend(self._check_equipment_consistency())
                
        except Exception as e:
            logger.error(f"❌ {data_type} 一致性检查异常: {e}")
            
        return inconsistencies
    
    def _check_wait_lot_consistency(self) -> List[Dict[str, Any]]:
        """检查待排产批次数据一致性"""
        inconsistencies = []
        
        try:
            # 检查Flask应用上下文是否可用
            from flask import has_app_context
            if not has_app_context():
                logger.debug("⏭️ 跳过待排产批次一致性检查：Flask应用上下文未就绪")
                return []
                
            from app.services.data_source_manager import DataSourceManager
            from app.utils.unified_connection_manager import get_unified_read_connection
            
            manager = DataSourceManager()
            
            # 获取缓存数据
            cached_data = manager._get_cached_data('wait_lot_data', lambda: [])
            cached_count = len(cached_data) if cached_data else 0
            
            # 获取数据库实际数据
            with get_unified_read_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
                actual_count = cursor.fetchone()[0]
            
            # 检查数量不一致
            if cached_count != actual_count:
                inconsistencies.append({
                    'type': 'count_mismatch',
                    'cached_count': cached_count,
                    'actual_count': actual_count,
                    'data_type': 'wait_lot_data'
                })
            
        except Exception as e:
            logger.error(f"❌ 待排产批次一致性检查失败: {e}")
            
        return inconsistencies
    
    def _check_equipment_consistency(self) -> List[Dict[str, Any]]:
        """检查设备状态数据一致性"""
        inconsistencies = []
        
        try:
            # 检查Flask应用上下文是否可用
            from flask import has_app_context
            if not has_app_context():
                logger.debug("⏭️ 跳过设备状态一致性检查：Flask应用上下文未就绪")
                return []
                
            from app.services.data_source_manager import DataSourceManager
            from app.utils.unified_connection_manager import get_unified_read_connection
            
            manager = DataSourceManager()
            
            # 获取缓存数据
            cached_data = manager._get_cached_data('equipment_status_data', lambda: [])
            cached_count = len(cached_data) if cached_data else 0
            
            # 获取数据库实际数据
            with get_unified_read_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM eqp_status")
                actual_count = cursor.fetchone()[0]
            
            # 检查数量不一致  
            if cached_count != actual_count:
                inconsistencies.append({
                    'type': 'count_mismatch',
                    'cached_count': cached_count,
                    'actual_count': actual_count,
                    'data_type': 'equipment_status_data'
                })
                
        except Exception as e:
            logger.error(f"❌ 设备状态一致性检查失败: {e}")
            
        return inconsistencies
    
    def _auto_fix_inconsistencies(self, data_type: str, inconsistencies: List[Dict[str, Any]]):
        """自动修复数据不一致问题"""
        for issue in inconsistencies:
            try:
                if issue['type'] == 'count_mismatch':
                    # 清理缓存，强制重新加载
                    self._clear_cache_by_type(data_type)
                    logger.info(f"🔧 已修复数据不一致: {data_type} - 清理缓存强制重新加载")
                    self._sync_stats['auto_fixes_applied'] += 1
                    
            except Exception as e:
                logger.error(f"❌ 自动修复失败: {data_type}, 错误: {e}")
    
    def _cleanup_processed_events(self):
        """清理过期的已处理事件"""
        cutoff_time = datetime.now() - timedelta(hours=24)  # 保留24小时
        
        with self._event_lock:
            before_count = len(self._processed_events)
            self._processed_events = [
                event for event in self._processed_events 
                if event.timestamp > cutoff_time
            ]
            after_count = len(self._processed_events)
            
            if before_count > after_count:
                logger.debug(f"🧹 清理过期事件: {before_count - after_count} 个")
    
    @contextmanager
    def batch_sync_context(self, data_types: List[str]):
        """批量同步上下文管理器"""
        logger.info(f"📦 开始批量同步上下文: {data_types}")
        
        try:
            # 暂停自动同步
            original_running = self._is_running
            self._is_running = False
            
            yield self
            
        finally:
            # 恢复自动同步并处理积累的事件
            self._is_running = original_running
            
            # 批量清理指定的数据类型
            for data_type in data_types:
                self._clear_cache_by_type(data_type)
            
            logger.info(f"📦 批量同步上下文完成: {data_types}")
    
    def manual_refresh(self, data_types: List[str] = None, force_preheat: bool = False):
        """手动刷新缓存"""
        if data_types is None:
            data_types = list(self._sync_rules.keys())
        
        logger.info(f"🔄 手动刷新缓存: {data_types}")
        
        for data_type in data_types:
            try:
                self._clear_cache_by_type(data_type)
                
                if force_preheat:
                    self._preheat_cache(data_type)
                
                # 记录手动刷新事件
                event = CacheEvent(
                    event_type=CacheEventType.MANUAL_REFRESH,
                    table_name='manual',
                    affected_data_types=[data_type],
                    record_ids=[],
                    timestamp=datetime.now(),
                    metadata={'force_preheat': force_preheat}
                )
                
                with self._event_lock:
                    self._processed_events.append(event)
                
            except Exception as e:
                logger.error(f"❌ 手动刷新失败: {data_type}, 错误: {e}")
        
        self._sync_stats['manual_refreshes'] += len(data_types)
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取缓存同步状态"""
        with self._lock:
            status = {
                'is_running': self._is_running,
                'sync_rules_count': len(self._sync_rules),
                'pending_events': len(self._pending_events),
                'processed_events_24h': len(self._processed_events),
                'active_batch_timers': len(self._batch_timers),
                'performance_metrics': self._performance_metrics.copy(),
                'sync_statistics': dict(self._sync_stats),
                'data_types_managed': list(self._sync_rules.keys())
            }
        
        return status
    
    def shutdown(self):
        """关闭缓存同步机制"""
        logger.info("🛑 正在关闭强化缓存同步机制...")
        
        self._is_running = False
        
        # 取消所有定时器
        for timer in self._batch_timers.values():
            timer.cancel()
        self._batch_timers.clear()
        
        for timer in self._consistency_check_timers.values():
            timer.cancel()
        self._consistency_check_timers.clear()
        
        # 等待工作线程结束
        if self._sync_thread and self._sync_thread.is_alive():
            self._sync_thread.join(timeout=5)
        
        logger.info("✅ 强化缓存同步机制已关闭")

# 🌐 全局实例
_enhanced_cache_sync: Optional[EnhancedCacheSync] = None
_sync_lock = threading.Lock()

def get_enhanced_cache_sync() -> EnhancedCacheSync:
    """获取强化缓存同步实例（单例）"""
    global _enhanced_cache_sync
    if _enhanced_cache_sync is None:
        with _sync_lock:
            if _enhanced_cache_sync is None:
                _enhanced_cache_sync = EnhancedCacheSync()
    return _enhanced_cache_sync

def on_data_change_event(table_name: str, operation: str, record_ids: List[Any] = None):
    """便捷函数：触发数据变更事件"""
    event_type_map = {
        'INSERT': CacheEventType.CREATE,
        'UPDATE': CacheEventType.UPDATE, 
        'DELETE': CacheEventType.DELETE,
        'BULK': CacheEventType.BULK_OPERATION
    }
    
    event_type = event_type_map.get(operation.upper(), CacheEventType.UPDATE)
    sync_manager = get_enhanced_cache_sync()
    sync_manager.on_data_change(table_name, event_type, record_ids)

def manual_cache_refresh(data_types: List[str] = None, force_preheat: bool = False):
    """便捷函数：手动刷新缓存"""
    sync_manager = get_enhanced_cache_sync()
    sync_manager.manual_refresh(data_types, force_preheat)

def get_cache_sync_status() -> Dict[str, Any]:
    """便捷函数：获取缓存同步状态"""
    sync_manager = get_enhanced_cache_sync()
    return sync_manager.get_sync_status()