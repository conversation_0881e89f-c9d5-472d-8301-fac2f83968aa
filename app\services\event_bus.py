#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件总线服务
支持Redis事件发布订阅和WebSocket实时推送
"""

import json
import logging
try:
    import redis
except ImportError:
    logger.warning("Redis模块未安装，使用模拟客户端")
    redis = None
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from flask import current_app
from flask_socketio import SocketIO, emit

logger = logging.getLogger(__name__)

class EventBus:
    """事件总线 - 支持Redis和WebSocket的事件发布订阅系统"""
    
    def __init__(self, redis_url: str = None, socketio: SocketIO = None):
        """初始化事件总线
        
        Args:
            redis_url: Redis连接URL，默认使用localhost
            socketio: Flask-SocketIO实例
        """
        self.redis_url = redis_url or 'redis://localhost:6379/0'
        self.socketio = socketio
        self._redis_client = None
        self._pubsub = None
        self._subscribers = {}
        self._running = False
        self._subscriber_thread = None
        
        # 事件类型定义
        self.EVENT_TYPES = {
            'ATTACHMENT_DOWNLOADED': 'attachment_downloaded',
            'FILE_SCANNED': 'file_scanned', 
            'PARSING_STARTED': 'parsing_started',
            'PARSING_PROGRESS': 'parsing_progress',
            'PARSING_COMPLETE': 'parsing_complete',
            'CLASSIFICATION_UPDATED': 'classification_updated',
            'DATA_STORED': 'data_stored',
            'PROCESS_ERROR': 'process_error',
            'TASK_CREATED': 'task_created',
            'TASK_UPDATED': 'task_updated',
            'TASK_COMPLETED': 'task_completed'
        }
    
    @property
    def redis_client(self):
        """获取Redis客户端"""
        if self._redis_client is None:
            try:
                if redis:
                    self._redis_client = redis.from_url(self.redis_url, decode_responses=True)
                else:
                    self._redis_client = MockRedisClient()
                # 测试连接
                self._redis_client.ping()
                logger.info("Redis连接成功")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                # 返回模拟客户端以避免程序崩溃
                self._redis_client = MockRedisClient()
        return self._redis_client
    
    def publish_event(self, event_type: str, data: Dict[str, Any], 
                     user_id: str = None, room: str = None):
        """发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            user_id: 目标用户ID（用于WebSocket）
            room: 目标房间（用于WebSocket）
        """
        try:
            # 构建事件消息
            event_message = {
                'event_type': event_type,
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'room': room
            }
            
            # 发布到Redis
            channel = f"events:{event_type}"
            self.redis_client.publish(channel, json.dumps(event_message))
            
            # 通过WebSocket推送（如果可用）
            if self.socketio:
                self._emit_websocket_event(event_type, event_message, user_id, room)
                
            logger.debug(f"事件已发布: {event_type}")
            
        except Exception as e:
            logger.error(f"发布事件失败 {event_type}: {e}")
    
    def subscribe_events(self, event_types: list, callback: Callable):
        """订阅事件
        
        Args:
            event_types: 要订阅的事件类型列表
            callback: 事件回调函数
        """
        for event_type in event_types:
            if event_type not in self._subscribers:
                self._subscribers[event_type] = []
            self._subscribers[event_type].append(callback)
        
        # 启动订阅者线程
        if not self._running:
            self._start_subscriber_thread()
    
    def unsubscribe_events(self, event_types: list, callback: Callable):
        """取消订阅事件"""
        for event_type in event_types:
            if event_type in self._subscribers and callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)
    
    def _emit_websocket_event(self, event_type: str, event_message: Dict[str, Any],
                            user_id: str = None, room: str = None):
        """通过WebSocket发送事件"""
        try:
            if room:
                self.socketio.emit(event_type, event_message, room=room)
            elif user_id:
                self.socketio.emit(event_type, event_message, room=f"user_{user_id}")
            else:
                self.socketio.emit(event_type, event_message)
        except Exception as e:
            logger.error(f"WebSocket事件发送失败: {e}")
    
    def _start_subscriber_thread(self):
        """启动Redis订阅者线程"""
        if self._subscriber_thread and self._subscriber_thread.is_alive():
            return
            
        self._running = True
        self._subscriber_thread = threading.Thread(target=self._subscriber_worker)
        self._subscriber_thread.daemon = True
        self._subscriber_thread.start()
        logger.info("事件订阅者线程已启动")
    
    def _subscriber_worker(self):
        """订阅者工作线程"""
        try:
            if self._pubsub is None:
                self._pubsub = self.redis_client.pubsub()
            
            # 订阅所有事件频道
            for event_type in self._subscribers.keys():
                channel = f"events:{event_type}"
                self._pubsub.subscribe(channel)
            
            # 监听消息
            while self._running:
                try:
                    message = self._pubsub.get_message(timeout=1)
                    if message and message['type'] == 'message':
                        self._handle_redis_message(message)
                except Exception as e:
                    logger.error(f"处理Redis消息失败: {e}")
                    time.sleep(1)
                    
        except Exception as e:
            logger.error(f"订阅者线程错误: {e}")
        finally:
            if self._pubsub:
                self._pubsub.close()
    
    def _handle_redis_message(self, message):
        """处理Redis消息"""
        try:
            channel = message['channel']
            event_type = channel.replace('events:', '')
            event_data = json.loads(message['data'])
            
            # 调用所有订阅者回调
            if event_type in self._subscribers:
                for callback in self._subscribers[event_type]:
                    try:
                        callback(event_type, event_data)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")
                        
        except Exception as e:
            logger.error(f"处理Redis消息失败: {e}")
    
    def is_healthy(self) -> bool:
        """检查事件总线健康状态
        
        Returns:
            bool: 事件总线是否健康
        """
        try:
            # 检查基本状态
            if not self._running and self._subscribers:
                return False
            
            # 检查Redis连接
            if self._redis_client and not isinstance(self._redis_client, MockRedisClient):
                try:
                    self._redis_client.ping()
                except Exception:
                    return False
            
            # 检查订阅者线程是否正常运行（如果有订阅者）
            if self._subscribers and self._subscriber_thread:
                if not self._subscriber_thread.is_alive():
                    return False
            
            return True
        except Exception as e:
            logger.error(f"检查事件总线健康状态时出错: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取事件总线状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        redis_connected = False
        redis_type = 'mock'
        
        try:
            if self._redis_client:
                if isinstance(self._redis_client, MockRedisClient):
                    redis_type = 'mock'
                    redis_connected = True
                else:
                    self._redis_client.ping()
                    redis_type = 'real'
                    redis_connected = True
        except Exception:
            redis_connected = False
        
        return {
            'healthy': self.is_healthy(),
            'running': self._running,
            'redis_connected': redis_connected,
            'redis_type': redis_type,
            'subscriber_count': len(self._subscribers),
            'subscriber_thread_alive': self._subscriber_thread and self._subscriber_thread.is_alive(),
            'websocket_available': self.socketio is not None
        }

    def stop(self):
        """停止事件总线"""
        self._running = False
        if self._subscriber_thread:
            self._subscriber_thread.join(timeout=5)
        if self._pubsub:
            self._pubsub.close()
        if self._redis_client:
            self._redis_client.close()
        logger.info("事件总线已停止")


class MockRedisClient:
    """模拟Redis客户端 - 用于Redis不可用时的降级处理"""
    
    def __init__(self):
        self._data = {}
    
    def publish(self, channel, message):
        """模拟发布消息"""
        logger.warning(f"Redis不可用，事件未发布: {channel}")
        return 0
    
    def ping(self):
        """模拟ping"""
        return True
    
    def pubsub(self):
        """模拟pubsub"""
        return MockPubSub()
    
    def close(self):
        """模拟关闭"""
        pass


class MockPubSub:
    """模拟Redis PubSub"""
    
    def subscribe(self, channel):
        pass
    
    def get_message(self, timeout=1):
        time.sleep(timeout)
        return None
    
    def close(self):
        pass


# 全局事件总线实例
_event_bus = None

def get_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus

def init_event_bus(app, socketio=None):
    """初始化事件总线"""
    global _event_bus
    redis_url = app.config.get('REDIS_URL', 'redis://localhost:6379/0')
    _event_bus = EventBus(redis_url=redis_url, socketio=socketio)
    return _event_bus 