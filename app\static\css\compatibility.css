/*
 * 兼容性样式表
 * 用于解决跨浏览器兼容性问题和针对高对比度模式的样式调整
 */

/* 修复高对比度模式下的可见性问题 */
@media (forced-colors: active) {
  /* 确保按钮在高对比度模式下可见 */
  .btn {
    forced-color-adjust: none;
    border: 1px solid currentColor;
  }
  
  /* 确保图标在高对比度模式下可见 */
  .fas, .far, .fab {
    forced-color-adjust: none;
    color: currentColor;
  }
  
  /* 确保进度条在高对比度模式下可见 */
  .progress {
    forced-color-adjust: none;
    border: 1px solid currentColor;
  }
  
  .progress-bar {
    forced-color-adjust: none;
    background-color: currentColor;
  }
}

/* 修复Safari浏览器下的样式问题 */
@supports (-webkit-appearance: none) {
  /* Safari特定的样式修复 */
  .form-control {
    -webkit-appearance: none;
  }
  
  .btn {
    -webkit-appearance: none;
  }
}

/* 修复IE/Edge浏览器兼容性问题 */
@supports (-ms-ime-align: auto) {
  /* Edge特定的样式修复 */
  .table {
    border-collapse: separate;
    border-spacing: 0;
  }
}

/* 通用样式修复 */
* {
  box-sizing: border-box;
}

/* 确保图片响应式 */
img {
  max-width: 100%;
  height: auto;
}

/* 修复按钮样式 */
.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.5);
}

/* 修复表格样式 */
.table {
  border-collapse: collapse;
  width: 100%;
}

.table th,
.table td {
  border: 1px solid #dee2e6;
  padding: 0.75rem;
  vertical-align: top;
}

/* 修复模态框样式 */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 修复下拉菜单样式 */
.dropdown-menu {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 修复导航栏样式 */
.navbar {
  padding: 0.5rem 1rem;
}

.navbar-brand {
  font-size: 1.25rem;
  font-weight: bold;
}

/* 修复分页样式 */
.pagination {
  margin-bottom: 0;
}

.page-link {
  border: 1px solid #dee2e6;
  color: #0d6efd;
}

.page-link:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
  color: #0a58ca;
}

.page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
}

/* 修复卡片样式 */
.card {
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.375rem;
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 0.75rem 1.25rem;
}

.card-body {
  padding: 1.25rem;
}
