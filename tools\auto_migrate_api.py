#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动API迁移工具

此工具用于批量处理API接口迁移，包括：
1. 自动将旧API路由标记为废弃
2. 根据模板创建新的API接口
3. 添加迁移映射关系
"""

import os
import sys
import json
import argparse
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api_migration_helper import APIMigrationHelper

class AutoAPIImporter:
    def __init__(self):
        self.helper = APIMigrationHelper()
        self.api_v2_modules = {
            'production': 'app/api_v2/production',
            'orders': 'app/api_v2/orders',
            'resources': 'app/api_v2/resources',
            'system': 'app/api_v2/system',
            'auth': 'app/api_v2/auth'
        }
        
        # 确保目录存在
        for module_dir in self.api_v2_modules.values():
            os.makedirs(module_dir, exist_ok=True)
    
    def _get_module_for_endpoint(self, endpoint):
        """根据端点确定应该放在哪个模块"""
        if '/production/' in endpoint:
            return 'production'
        elif '/orders/' in endpoint:
            return 'orders'
        elif '/resources/' in endpoint:
            return 'resources'
        elif '/system/' in endpoint or '/database' in endpoint:
            return 'system'
        elif '/auth/' in endpoint:
            return 'auth'
        elif '/email' in endpoint or '/order_data' in endpoint:
            return 'orders'  # 邮件附件和订单相关接口放到orders模块
        else:
            return 'production'  # 默认放到生产模块
    
    def _get_new_endpoint_path(self, old_endpoint):
        """根据旧端点生成新端点路径"""
        # 特殊情况处理
        special_cases = {
            '/api/production/batch/upload': '/api/v2/production/lots/upload',
            '/api/production/import-from-directory': '/api/v2/production/files/import',
            '/api/production/import-progress': '/api/v2/production/files/progress',
            '/api/production/delete-file': '/api/v2/production/files/delete',
            '/api/production/file-data': '/api/v2/production/files/data',
            '/api/production/export-file': '/api/v2/production/files/export',
            '/api/production/imported-files': '/api/v2/production/files/list',
            '/api/test-database-connection': '/api/v2/system/database/test',
            '/api/database-status': '/api/v2/system/database/status'
        }
        
        if old_endpoint in special_cases:
            return special_cases[old_endpoint]
        
        # 通用处理
        parts = old_endpoint.split('/')
        
        # 移除空字符串
        parts = [p for p in parts if p]
        
        # 确定模块
        module = self._get_module_for_endpoint(old_endpoint)
        
        # 构建新路径
        new_parts = ['api', 'v2', module]
        
        # 添加剩余的路径部分，跳过api和模块名
        skip_next = False
        for i, part in enumerate(parts):
            if part == 'api' or skip_next:
                skip_next = False
                continue
                
            if part in ['production', 'orders', 'resources', 'system', 'auth']:
                skip_next = True
                continue
                
            new_parts.append(part)
        
        # 构建最终路径
        return '/' + '/'.join(new_parts)
    
    def migrate_endpoint(self, old_endpoint, new_endpoint=None, create_template=True, dry_run=False):
        """迁移单个端点"""
        if not new_endpoint:
            new_endpoint = self._get_new_endpoint_path(old_endpoint)
        
        print(f"准备迁移: {old_endpoint} -> {new_endpoint}")
        
        # 查找包含旧端点的文件
        file_path = self.helper.find_endpoint_file(old_endpoint)
        if not file_path:
            print(f"❌ 未找到端点: {old_endpoint}")
            return False
        
        # 分析旧端点
        endpoint_info = self.helper.analyze_endpoint(file_path, old_endpoint)
        if not endpoint_info:
            print(f"❌ 无法分析端点: {old_endpoint}")
            return False
        
        # 生成迁移模板
        if create_template:
            template = self.helper.generate_migration_template(endpoint_info, new_endpoint)
            
            # 确定模板应该保存的位置
            module = self._get_module_for_endpoint(new_endpoint)
            module_dir = self.api_v2_modules[module]
            template_file = os.path.join(module_dir, f"generated_{datetime.now().strftime('%Y%m%d%H%M%S')}.py")
            
            if not dry_run:
                with open(template_file, 'w', encoding='utf-8') as f:
                    f.write(f"""
# -*- coding: utf-8 -*-
\"\"\"
为旧API {old_endpoint} 自动迁移生成的新API模板
\"\"\"

from flask import Blueprint, request, jsonify
from app import db

{template}
""")
                print(f"✅ 已生成模板: {template_file}")
            else:
                print(f"✅ 将生成模板: {template_file}")
                print(template)
        
        # 标记旧端点为废弃
        if not dry_run:
            if self.helper.mark_endpoint_deprecated(file_path, old_endpoint, new_endpoint):
                self.helper.add_migration_mapping(old_endpoint, new_endpoint)
                print(f"✅ 已成功标记端点 {old_endpoint} 为废弃并指向 {new_endpoint}")
                return True
            else:
                print(f"❌ 标记端点 {old_endpoint} 为废弃失败")
                return False
        else:
            print(f"✅ 将标记端点 {old_endpoint} 为废弃并指向 {new_endpoint}")
            return True
            
    def batch_migrate(self, endpoints_file=None, module=None, limit=None, dry_run=False):
        """批量迁移多个端点"""
        endpoints_to_migrate = []
        
        if endpoints_file:
            # 从文件加载端点列表
            try:
                with open(endpoints_file, 'r', encoding='utf-8') as f:
                    endpoints_data = json.load(f)
                    
                for item in endpoints_data:
                    old_endpoint = item['old_endpoint']
                    new_endpoint = item.get('new_endpoint')
                    endpoints_to_migrate.append((old_endpoint, new_endpoint))
            except Exception as e:
                print(f"❌ 无法从文件加载端点列表: {e}")
                return
        else:
            # 从旧API目录扫描端点
            old_endpoints = []
            for root, _, files in os.walk(self.helper.source_api_dir):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        
                        # 如果指定了模块，只处理该模块的文件
                        if module:
                            if module not in file_path:
                                continue
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                            # 提取端点
                            matches = self.helper.endpoint_pattern.finditer(content)
                            for match in matches:
                                endpoint = match.group(1)
                                
                                # 确保是绝对路径
                                if not endpoint.startswith('/'):
                                    # 提取蓝图前缀
                                    bp_match = re.search(r'Blueprint\([^,]+,\s*[^,]+,\s*url_prefix=[\'"]([^\'"]*)[\'"]', content)
                                    if bp_match:
                                        blueprint_prefix = bp_match.group(1)
                                        if blueprint_prefix.endswith('/') and endpoint.startswith('/'):
                                            endpoint = f"{blueprint_prefix}{endpoint[1:]}"
                                        else:
                                            endpoint = f"{blueprint_prefix}/{endpoint}"
                                
                                # 过滤已迁移的端点
                                if "映射关系" in self.helper.deprecated_map and endpoint in self.helper.deprecated_map["映射关系"]:
                                    continue
                                    
                                # 生成新端点
                                new_endpoint = self._get_new_endpoint_path(endpoint)
                                old_endpoints.append((endpoint, new_endpoint))
                        except Exception as e:
                            print(f"分析文件 {file_path} 失败: {e}")
            
            # 按模块分组，优先处理常规API
            production_endpoints = [e for e in old_endpoints if '/production/' in e[0]]
            orders_endpoints = [e for e in old_endpoints if '/orders/' in e[0]]
            system_endpoints = [e for e in old_endpoints if '/system/' in e[0] or '/database' in e[0]]
            other_endpoints = [e for e in old_endpoints if e not in production_endpoints and e not in orders_endpoints and e not in system_endpoints]
            
            # 组合按优先级排序的端点
            endpoints_to_migrate = production_endpoints + orders_endpoints + system_endpoints + other_endpoints
            
            # 限制处理的端点数
            if limit and isinstance(limit, int) and limit > 0:
                endpoints_to_migrate = endpoints_to_migrate[:limit]
        
        # 批量迁移
        success_count = 0
        total_count = len(endpoints_to_migrate)
        
        for i, (old_endpoint, new_endpoint) in enumerate(endpoints_to_migrate):
            print(f"\n处理 {i+1}/{total_count}: {old_endpoint}")
            if self.migrate_endpoint(old_endpoint, new_endpoint, True, dry_run):
                success_count += 1
        
        print(f"\n✅ 批量迁移完成: 成功 {success_count}/{total_count}")
        
        # 生成迁移报告
        report_file = self.helper.generate_migration_report()
        print(f"✅ 已生成最新迁移报告: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='自动API迁移工具')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 单个迁移命令
    migrate_parser = subparsers.add_parser('migrate', help='迁移单个API端点')
    migrate_parser.add_argument('old_endpoint', help='旧端点路径')
    migrate_parser.add_argument('--new-endpoint', help='新端点路径', default=None)
    migrate_parser.add_argument('--no-template', help='不创建模板', action='store_true')
    migrate_parser.add_argument('--dry-run', help='仅打印不执行', action='store_true')
    
    # 批量迁移命令
    batch_parser = subparsers.add_parser('batch', help='批量迁移多个API端点')
    batch_parser.add_argument('--file', help='包含待迁移端点的JSON文件路径')
    batch_parser.add_argument('--module', help='要处理的模块', choices=['production', 'orders', 'resources', 'system', 'auth'])
    batch_parser.add_argument('--limit', help='限制处理的端点数量', type=int)
    batch_parser.add_argument('--dry-run', help='仅打印不执行', action='store_true')
    
    args = parser.parse_args()
    
    importer = AutoAPIImporter()
    
    if args.command == 'migrate':
        importer.migrate_endpoint(args.old_endpoint, args.new_endpoint, not args.no_template, args.dry_run)
    
    elif args.command == 'batch':
        importer.batch_migrate(args.file, args.module, args.limit, args.dry_run)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    import re  # 添加缺失的导入
    main() 