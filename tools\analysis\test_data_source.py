#!/usr/bin/env python3
"""
测试数据源管理器的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_source_manager import DataSourceManager
from collections import Counter
from app import create_app

def test_data_source():
    """测试数据源管理器"""
    print("🔍 测试数据源管理器...")

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        try:
        # 创建数据源管理器
        data_manager = DataSourceManager()
        
        # 清理缓存
        print("🧹 清理测试规范缓存...")
        data_manager.clear_cache('test_spec_data')
        
        # 获取测试规范数据
        print("📊 获取测试规范数据...")
        test_specs_dict, source = data_manager.get_test_spec_data()
        
        print(f"\n📋 数据源: {source}")
        print(f"📋 字典格式数据总数: {len(test_specs_dict)}")
        
        if test_specs_dict:
            # 转换为列表格式
            test_specs_list = list(test_specs_dict.values())
            print(f"📋 列表格式数据总数: {len(test_specs_list)}")
            
            # 统计DEVICE分布
            devices = [spec.get('DEVICE', 'N/A') for spec in test_specs_list]
            device_counts = Counter(devices)
            
            print(f"\n📊 DEVICE分布（前10）:")
            for device, count in device_counts.most_common(10):
                print(f"  - {device}: {count} 条记录")
            
            # 显示前5条记录
            print(f"\n📋 前5条记录详情:")
            for i, spec in enumerate(test_specs_list[:5], 1):
                device = spec.get('DEVICE', 'N/A')
                stage = spec.get('STAGE', 'N/A')
                approval = spec.get('APPROVAL_STATE', 'N/A')
                tester = spec.get('TESTER', 'N/A')
                print(f"  {i}. DEVICE='{device}' STAGE='{stage}' TESTER='{tester}' APPROVAL='{approval}'")
            
            # 检查是否只有一个DEVICE
            unique_devices = set(devices)
            print(f"\n🔍 唯一DEVICE数量: {len(unique_devices)}")
            if len(unique_devices) == 1:
                print(f"⚠️ 警告：所有记录都是同一个DEVICE: {list(unique_devices)[0]}")
            else:
                print(f"✅ 正常：包含多个不同的DEVICE")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_source()
