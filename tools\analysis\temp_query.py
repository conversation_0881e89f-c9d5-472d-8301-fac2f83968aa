import pymysql
lots=['YX0125HB0456','YX0125HB0455','YX0125HB0454']
conn=pymysql.connect(host='localhost',user='root',password='WWWwww123!',database='aps',charset='utf8mb4')
cur=conn.cursor()
lot_list=",".join(["'%s'"%x for x in lots])
sql=(f"SELECT LOT_ID,HANDLER_ID,match_type FROM lotprioritydone "
     f"WHERE SESSION_ID='session_20250115_120000_b8f33d1a' AND LOT_ID IN ({lot_list}) ORDER BY LOT_ID")
cur.execute(sql)
for row in cur.fetchall():
    print(row)
cur.close(); conn.close()

