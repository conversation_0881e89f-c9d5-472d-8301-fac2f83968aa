test_task_2_1_intelligent_algorithm_selection.py

algorithm_selector.py
def record_algorithm_performance(self, algorithm, lot_count, equipment_count, 
                               execution_time, success_rate, accuracy_score):
    # 🧠 按规模分组学习：scale_key = "400-65" (408批次→400组, 67设备→65组)
    scale_key = f"{lot_count//10*10}-{equipment_count//5*5}"
    
    # 📊 性能数据累积
    performance_data = {
        'execution_times': [1.75],  # 本次执行时间
        'success_rates': [0.1495],  # 本次成功率  
        'accuracy_scores': []       # 精度评分
    }
    
    # 🔄 滑动窗口管理（保持最近100次记录）
    if len(performance_data['execution_times']) > 100:
        performance_data['execution_times'] = performance_data['execution_times'][-100:]

# 系统自动识别了408批次场景为EXTREME复杂度
complexity_score = (
    408 * 0.3 +      # 批次数量贡献：122.4
    67 * 0.2 +       # 设备数量贡献：13.4  
    51.5 * 0.15 +    # 多样性贡献：7.7
    34.0 * 0.25 +    # 约束复杂度贡献：8.5
    100.0 * 0.1      # 交期压力贡献：10.0
) = 162.0 → EXTREME级别