#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析最新排产验收结果
"""

import pandas as pd
import sys

def main():
    try:
        # 读取最新验收结果
        df = pd.read_csv('reports/latest_scheduling_check.csv')
        
        print("=== 最新排产验收结果汇总 ===")
        print(df['check_type'].value_counts())
        
        print("\n=== 各类问题统计 ===")
        for check_type in df['check_type'].unique():
            count = len(df[df['check_type'] == check_type])
            print(f"{check_type}: {count} 个问题项")
        
        print("\n=== J类问题详情（解析失败但可续排）===")
        j_issues = df[df['check_type'] == 'J_解析失败但可续排']
        if len(j_issues) > 0:
            print(j_issues[['LOT_ID', 'issue_desc']].to_string(index=False))
        else:
            print("无J类问题")
            
        print("\n=== F类问题详情（跨工序合法性）===")
        f_issues = df[df['check_type'] == 'F_跨工序合法性']
        if len(f_issues) > 0:
            print(f"发现 {len(f_issues)} 个跨工序违规问题")
            print(f_issues[['LOT_ID', 'HANDLER_ID', 'issue_desc']].head(10).to_string(index=False))
        else:
            print("无F类问题")
            
        print("\n=== 总体评估 ===")
        total_issues = len(df)
        critical_issues = len(df[df['check_type'].isin(['F_跨工序合法性', 'G_更优未选', 'H_跨工序A优先性', 'I_设备内优先级'])])
        print(f"总问题项: {total_issues}")
        print(f"关键问题项: {critical_issues}")
        print(f"问题严重程度: {'高' if critical_issues > 50 else '中' if critical_issues > 10 else '低'}")
        
    except Exception as e:
        print(f"分析失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
