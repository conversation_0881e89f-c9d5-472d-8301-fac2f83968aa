#!/usr/bin/env python3
"""
初始化最终排产调整结果数据库表
"""

import mysql.connector
from datetime import datetime

def init_final_scheduling_tables():
    """初始化最终排产调整结果相关表"""
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("=== 开始初始化最终排产调整结果表 ===")
        
        # 1. 备份现有数据
        print("1. 备份现有数据...")
        try:
            cursor.execute("CREATE TABLE final_scheduling_result_backup AS SELECT * FROM final_scheduling_result")
            print("✅ 现有数据已备份")
        except Exception as e:
            print(f"⚠️ 备份跳过: {e}")
        
        # 2. 删除现有表
        print("2. 删除现有表...")
        cursor.execute("DROP TABLE IF EXISTS final_scheduling_result")
        cursor.execute("DROP TABLE IF EXISTS adjustment_operations")
        print("✅ 现有表已删除")
        
        # 3. 创建final_scheduling_result表
        print("3. 创建final_scheduling_result表...")
        create_final_table_sql = """
        CREATE TABLE final_scheduling_result (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            session_id VARCHAR(100) NOT NULL COMMENT '会话ID，用于区分不同的调整会话',
            session_name VARCHAR(200) DEFAULT NULL COMMENT '会话名称，用户自定义的调整会话名称',
            
            -- 批次基本信息
            lot_id VARCHAR(50) NOT NULL COMMENT '内部工单号',
            lot_type VARCHAR(20) DEFAULT NULL COMMENT '批次类型',
            good_qty INT DEFAULT 0 COMMENT '良品数量',
            prod_id VARCHAR(50) DEFAULT NULL COMMENT '产品ID',
            device VARCHAR(100) DEFAULT NULL COMMENT '产品名称',
            chip_id VARCHAR(100) DEFAULT NULL COMMENT '芯片名称',
            pkg_pn VARCHAR(100) DEFAULT NULL COMMENT '封装料号',
            po_id VARCHAR(50) DEFAULT NULL COMMENT '订单号',
            stage VARCHAR(20) DEFAULT NULL COMMENT '工序',
            wip_state VARCHAR(20) DEFAULT 'SCHEDULED' COMMENT 'WIP状态',
            proc_state VARCHAR(20) DEFAULT 'READY' COMMENT '流程状态',
            hold_state VARCHAR(20) DEFAULT NULL COMMENT '扣留状态',
            flow_id VARCHAR(50) DEFAULT NULL COMMENT '流程ID',
            flow_ver VARCHAR(20) DEFAULT NULL COMMENT '流程版本',
            release_time DATETIME DEFAULT NULL COMMENT '释放时间',
            fac_id VARCHAR(20) DEFAULT NULL COMMENT '工厂ID',
            create_time DATETIME DEFAULT NULL COMMENT '批次创建时间',
            
            -- 调整结果字段
            final_priority INT NOT NULL COMMENT '最终优先级（调整后）',
            final_handler_id VARCHAR(50) NOT NULL COMMENT '最终分选机ID（调整后）',
            
            -- 原始数据字段
            original_priority INT DEFAULT NULL COMMENT '原始优先级（调整前）',
            original_handler_id VARCHAR(50) DEFAULT NULL COMMENT '原始分选机ID（调整前）',
            
            -- 数据来源和类型
            source_type ENUM('success', 'failed', 'manual') NOT NULL DEFAULT 'success' COMMENT '数据来源类型',
            failure_reason TEXT DEFAULT NULL COMMENT '失败原因（仅失败批次有效）',
            
            -- 调整信息
            adjustment_type ENUM('priority_only', 'handler_only', 'both', 'recovery', 'none') NOT NULL DEFAULT 'none' COMMENT '调整类型',
            adjustment_reason TEXT DEFAULT NULL COMMENT '调整原因说明',
            
            -- 算法评分信息
            comprehensive_score DECIMAL(5,2) DEFAULT NULL COMMENT '综合评分',
            processing_time DECIMAL(8,2) DEFAULT NULL COMMENT '预计处理时间（小时）',
            changeover_time DECIMAL(8,2) DEFAULT NULL COMMENT '换机时间（分钟）',
            match_type VARCHAR(50) DEFAULT NULL COMMENT '匹配类型',
            algorithm_version VARCHAR(50) DEFAULT NULL COMMENT '算法版本',
            
            -- 会话管理字段
            status ENUM('draft', 'published', 'executing', 'completed', 'cancelled') NOT NULL DEFAULT 'draft' COMMENT '状态',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活（用于软删除）',
            
            -- 审计字段
            adjusted_by VARCHAR(50) NOT NULL COMMENT '调整人',
            adjusted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
            published_by VARCHAR(50) DEFAULT NULL COMMENT '发布人',
            published_at DATETIME DEFAULT NULL COMMENT '发布时间',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
            
            -- 扩展字段
            metadata JSON DEFAULT NULL COMMENT '元数据（JSON格式）',
            tags VARCHAR(500) DEFAULT NULL COMMENT '标签（逗号分隔）',
            
            -- 索引
            INDEX idx_session_id (session_id),
            INDEX idx_lot_id (lot_id),
            INDEX idx_final_handler_id (final_handler_id),
            INDEX idx_source_type (source_type),
            INDEX idx_status (status),
            INDEX idx_adjusted_by (adjusted_by),
            INDEX idx_adjusted_at (adjusted_at),
            INDEX idx_session_status (session_id, status),
            INDEX idx_handler_priority (final_handler_id, final_priority),
            
            -- 唯一约束
            UNIQUE KEY uk_session_lot (session_id, lot_id)
            
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='最终排产调整结果表'
        """
        
        cursor.execute(create_final_table_sql)
        print("✅ final_scheduling_result表创建成功")
        
        # 4. 创建scheduling_sessions表
        print("4. 创建scheduling_sessions表...")
        cursor.execute("DROP TABLE IF EXISTS scheduling_sessions")
        create_sessions_table_sql = """
        CREATE TABLE scheduling_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            session_id VARCHAR(100) NOT NULL UNIQUE COMMENT '会话ID',
            session_name VARCHAR(200) NOT NULL COMMENT '会话名称',
            description TEXT DEFAULT NULL COMMENT '会话描述',
            base_data_source VARCHAR(100) DEFAULT 'lotprioritydone' COMMENT '基础数据源表名',
            total_lots INT DEFAULT 0 COMMENT '总批次数',
            success_lots INT DEFAULT 0 COMMENT '成功批次数',
            failed_lots INT DEFAULT 0 COMMENT '失败批次数',
            adjusted_lots INT DEFAULT 0 COMMENT '调整批次数',
            status ENUM('draft', 'published', 'executing', 'completed', 'cancelled') NOT NULL DEFAULT 'draft' COMMENT '会话状态',
            created_by VARCHAR(50) NOT NULL COMMENT '创建人',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_by VARCHAR(50) DEFAULT NULL COMMENT '更新人',
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            published_by VARCHAR(50) DEFAULT NULL COMMENT '发布人',
            published_at DATETIME DEFAULT NULL COMMENT '发布时间',
            
            -- 索引
            INDEX idx_session_id (session_id),
            INDEX idx_status (status),
            INDEX idx_created_by (created_by),
            INDEX idx_created_at (created_at)
            
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排产调整会话管理表'
        """
        
        cursor.execute(create_sessions_table_sql)
        print("✅ scheduling_sessions表创建成功")
        
        # 5. 创建adjustment_operations表
        print("5. 创建adjustment_operations表...")
        create_operations_table_sql = """
        CREATE TABLE adjustment_operations (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
            operation_type ENUM('drag_move', 'drag_reorder', 'batch_move', 'batch_priority', 'priority_change', 'handler_change', 'recovery') NOT NULL COMMENT '操作类型',
            lot_id VARCHAR(50) NOT NULL COMMENT '内部工单号',
            old_priority INT DEFAULT NULL COMMENT '原优先级',
            new_priority INT DEFAULT NULL COMMENT '新优先级',
            old_handler_id VARCHAR(50) DEFAULT NULL COMMENT '原分选机ID',
            new_handler_id VARCHAR(50) DEFAULT NULL COMMENT '新分选机ID',
            operation_description TEXT DEFAULT NULL COMMENT '操作描述',
            operation_data JSON DEFAULT NULL COMMENT '操作数据（JSON格式）',
            operated_by VARCHAR(50) NOT NULL COMMENT '操作人',
            operated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
            
            -- 索引
            INDEX idx_session_id (session_id),
            INDEX idx_lot_id (lot_id),
            INDEX idx_operation_type (operation_type),
            INDEX idx_operated_by (operated_by),
            INDEX idx_operated_at (operated_at)
            
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调整操作历史表'
        """
        
        cursor.execute(create_operations_table_sql)
        print("✅ adjustment_operations表创建成功")
        
        # 6. 创建存储过程 - 发布调整结果
        print("6. 创建存储过程...")
        cursor.execute("DROP PROCEDURE IF EXISTS PublishAdjustmentResults")
        
        create_procedure_sql = """
        CREATE PROCEDURE PublishAdjustmentResults(IN p_session_id VARCHAR(100), IN p_published_by VARCHAR(50))
        BEGIN
            DECLARE v_count INT DEFAULT 0;
            DECLARE v_affected_rows INT DEFAULT 0;
            
            START TRANSACTION;
            
            -- 检查会话是否存在
            SELECT COUNT(*) INTO v_count FROM scheduling_sessions WHERE session_id = p_session_id;
            IF v_count = 0 THEN
                ROLLBACK;
                SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '会话不存在';
            END IF;
            
            -- 清空生产表
            DELETE FROM lotprioritydone;
            
            -- 插入调整结果到生产表
            INSERT INTO lotprioritydone (
                HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID,
                PKG_PN, PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID,
                FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME, PRIORITY,
                comprehensive_score, processing_time, changeover_time, algorithm_version, match_type
            )
            SELECT 
                final_handler_id, lot_id, lot_type, good_qty, prod_id, device, chip_id,
                pkg_pn, po_id, stage, wip_state, proc_state, hold_state, flow_id,
                flow_ver, release_time, fac_id, create_time, final_priority,
                comprehensive_score, processing_time, changeover_time, algorithm_version, match_type
            FROM final_scheduling_result
            WHERE session_id = p_session_id 
            AND is_active = TRUE
            AND source_type != 'failed';
            
            SET v_affected_rows = ROW_COUNT();
            
            -- 更新会话状态
            UPDATE scheduling_sessions 
            SET status = 'published', published_by = p_published_by, published_at = NOW()
            WHERE session_id = p_session_id;
            
            -- 更新调整结果状态
            UPDATE final_scheduling_result 
            SET status = 'published', published_by = p_published_by, published_at = NOW()
            WHERE session_id = p_session_id AND is_active = TRUE;
            
            COMMIT;
            
            SELECT v_affected_rows as affected_rows;
        END
        """
        
        cursor.execute(create_procedure_sql)
        print("✅ PublishAdjustmentResults存储过程创建成功")
        
        # 7. 创建视图
        print("7. 创建视图...")
        cursor.execute("DROP VIEW IF EXISTS v_latest_adjustment_results")
        
        create_view_sql = """
        CREATE VIEW v_latest_adjustment_results AS
        SELECT 
            fsr.*,
            ss.session_name as session_display_name,
            ss.description as session_description,
            ss.created_by as session_creator,
            ss.published_at as session_published_at
        FROM final_scheduling_result fsr
        LEFT JOIN scheduling_sessions ss ON fsr.session_id = ss.session_id
        WHERE fsr.is_active = TRUE
        ORDER BY fsr.session_id, fsr.final_priority ASC
        """
        
        cursor.execute(create_view_sql)
        print("✅ v_latest_adjustment_results视图创建成功")
        
        # 8. 恢复备份数据（如果存在）
        print("8. 恢复备份数据...")
        try:
            cursor.execute("""
                INSERT INTO final_scheduling_result (
                    session_id, session_name, lot_id, lot_type, good_qty, prod_id, device, chip_id,
                    pkg_pn, po_id, stage, wip_state, proc_state, hold_state, flow_id, flow_ver,
                    release_time, fac_id, create_time, final_priority, final_handler_id,
                    original_priority, original_handler_id, source_type, failure_reason,
                    adjustment_type, adjustment_reason, comprehensive_score, processing_time,
                    changeover_time, match_type, algorithm_version, status, adjusted_by,
                    adjusted_at, published_by, published_at, created_at, updated_at
                )
                SELECT 
                    session_id, session_name, lot_id, lot_type, good_qty, prod_id, device, chip_id,
                    pkg_pn, po_id, stage, wip_state, proc_state, hold_state, flow_id, flow_ver,
                    release_time, fac_id, create_time, final_priority, final_handler_id,
                    original_priority, original_handler_id, source_type, failure_reason,
                    adjustment_type, adjustment_reason, comprehensive_score, processing_time,
                    changeover_time, match_type, algorithm_version, status, adjusted_by,
                    adjusted_at, published_by, published_at, created_at, updated_at
                FROM final_scheduling_result_backup
            """)
            restored_count = cursor.rowcount
            print(f"✅ 恢复备份数据: {restored_count} 条记录")
        except Exception as e:
            print(f"⚠️ 备份恢复跳过: {e}")
        
        # 9. 删除备份表
        try:
            cursor.execute("DROP TABLE final_scheduling_result_backup")
            print("✅ 备份表已清理")
        except Exception as e:
            print(f"⚠️ 备份表清理跳过: {e}")
        
        # 提交所有更改
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n" + "="*50)
        print("🎉 最终排产调整结果表初始化完成！")
        print("="*50)
        print("✅ final_scheduling_result - 最终排产调整结果表")
        print("✅ scheduling_sessions - 排产调整会话管理表")
        print("✅ adjustment_operations - 调整操作历史表")
        print("✅ PublishAdjustmentResults - 发布调整结果存储过程")
        print("✅ v_latest_adjustment_results - 最新调整结果视图")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库表初始化失败: {e}")
        return False

if __name__ == "__main__":
    init_final_scheduling_tables() 