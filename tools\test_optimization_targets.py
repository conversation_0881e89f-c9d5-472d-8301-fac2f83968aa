import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import json
import logging

from app import create_app
from app.services.real_scheduling_service import RealSchedulingService

logging.basicConfig(level=logging.INFO)

# 创建并推入Flask应用上下文
app, _ = create_app()

results_summary = {}
with app.app_context():
    rs = RealSchedulingService()

    for target in ["balanced", "makespan", "efficiency"]:
        logging.info(f"\n🚀 开始测试优化目标: {target}")
        result = rs.execute_real_scheduling(
            algorithm="intelligent",  # 固定策略，改变优化目标
            optimization_target=target,
            user_id="test_script"
        )

        # 结果兼容处理
        if isinstance(result, dict) and "schedule" in result:
            schedule = result.get("schedule", [])
            metrics = result.get("metrics", {})
        else:
            schedule = result if isinstance(result, list) else []
            metrics = {
                "total_batches": len(schedule),
                "scheduled_batches": len(schedule),
                "failed_batches": 0,
                "success_rate": "100%" if schedule else "0%",
            }

        # 简化统计：机台分配 & 平均综合得分
        handler_distribution = {}
        avg_score = 0.0
        if schedule:
            total_score = 0.0
            for rec in schedule:
                hid = rec.get("HANDLER_ID", "UNKNOWN")
                handler_distribution[hid] = handler_distribution.get(hid, 0) + 1
                total_score += rec.get("COMPREHENSIVE_SCORE", 0)
            avg_score = total_score / len(schedule)

        results_summary[target] = {
            "metrics": metrics,
            "handler_distribution": handler_distribution,
            "average_comprehensive_score": round(avg_score, 2),
        }

# 输出对比结果
print("\n===== 对比结果 =====")
print(json.dumps(results_summary, ensure_ascii=False, indent=2)) 