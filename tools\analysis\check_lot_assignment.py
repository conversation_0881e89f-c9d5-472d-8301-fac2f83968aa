#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对特定批次，核查排产分配与匹配类型的合理性。
- 批次: YX0125IA0029
输出：
1) lotprioritydone 实际分配记录
2) 候选设备 HCHC-C-013-6800 与 HCHC-O-011-6800 的设备配置
3) 基于 et_recipe_file 的 A/B类跨工序以及小改机/换测试机小改机匹配判定
"""
import pymysql

def fetch_one(cur, sql, args=None):
    cur.execute(sql, args or ())
    return cur.fetchone()

def fetch_all(cur, sql, args=None):
    cur.execute(sql, args or ())
    return cur.fetchall()

def main():
    LOT_ID = 'YX0125IA0029'
    target_handlers = ['HCHC-C-013-6800', 'HCHC-O-011-6800']
    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
    try:
        cur = conn.cursor(pymysql.cursors.DictCursor)
        print(f"\n🔎 检查批次: {LOT_ID}")
        lot = fetch_one(cur, """
            SELECT LOT_ID, DEVICE, STAGE, HANDLER_ID, match_type, changeover_time
            FROM lotprioritydone WHERE LOT_ID=%s
        """, (LOT_ID,))
        if not lot:
            print("❌ lotprioritydone 中未找到该批次记录")
            return
        print("\n[1] 实际分配记录")
        print(lot)

        print("\n[2] 设备配置 (eqp_status)")
        eqp_map = {}
        for hid in target_handlers:
            eqp = fetch_one(cur, """
                SELECT HANDLER_ID, DEVICE, STAGE, HANDLER_CONFIG, KIT_PN, TESTER
                FROM eqp_status WHERE HANDLER_ID=%s
            """, (hid,))
            eqp_map[hid] = eqp
            print(hid, '=>', eqp)

        print("\n[3] 配方匹配验证 (et_recipe_file)")
        # 判定条件：
        # - 小改机：DEVICE+STAGE一致 且 HC+KIT+TESTER一致 (HB/TB可不同)
        # - 换测试机小改机：DEVICE+STAGE一致 且 HC+KIT一致 但 TESTER不同
        # - 跨工序A：DEVICE或STAGE不一致 且 HC+KIT一致
        # - 跨工序B：DEVICE或STAGE不一致 且 仅HC一致 (KIT不一致)
        req_device = lot['DEVICE']
        req_stage = lot['STAGE']
        for hid, eqp in eqp_map.items():
            if not eqp:
                print(f"- {hid}: 无设备记录")
                continue
            print(f"\n→ 验证针对设备 {hid}")
            eqp_dev = eqp['DEVICE'] or ''
            eqp_stg = eqp['STAGE'] or ''
            eqp_hc  = eqp['HANDLER_CONFIG'] or ''
            eqp_kit = eqp['KIT_PN'] or ''
            eqp_tst = eqp['TESTER'] or ''
            same_stage = (eqp_dev == req_device and (eqp_stg or '').upper() == (req_stage or '').upper())
            cross_stage = not same_stage
            print(f"   - same_stage={same_stage}, cross_stage={cross_stage}")

            # COUNT for A类: HC+KIT一致（在lot的DEVICE+STAGE下是否存在这样的配方）
            a_cnt = fetch_one(cur, """
                SELECT COUNT(*) AS c FROM et_recipe_file r
                 WHERE r.DEVICE=%s AND UPPER(r.STAGE)=UPPER(%s)
                   AND r.HANDLER_CONFIG=%s AND r.KIT_PN=%s
            """, (req_device, req_stage, eqp_hc, eqp_kit))['c']

            # COUNT for B类: 仅HC一致（但没有A类的HC+KIT完全一致）
            b_cnt_hc = fetch_one(cur, """
                SELECT COUNT(*) AS c FROM et_recipe_file r
                 WHERE r.DEVICE=%s AND UPPER(r.STAGE)=UPPER(%s)
                   AND r.HANDLER_CONFIG=%s
            """, (req_device, req_stage, eqp_hc))['c']

            print(f"   - 配方计数: A类条件(=HC+KIT)={a_cnt}, HC一致总数={b_cnt_hc}")

            # 判定期望分类
            expected = None
            if same_stage:
                # 先看是否存在HC+KIT+TESTER一致
                same_tester_cnt = 1 if eqp_tst else 0
                # 无法直接从配方查TESTER，使用设备tester与lot需求tester集合匹配较复杂，这里先按HC+KIT判定换测试机可能性
                if a_cnt > 0:
                    # HC+KIT一致；是否tester一致无法从配方直接验证，这里给出两种可能
                    expected = '小改机/换测试机小改机(取决TESTER是否一致)'
                else:
                    expected = '大改机(仅HC一致需看KIT是否不一致)或不匹配'
            else:
                if a_cnt > 0:
                    expected = '跨工序A类匹配(60)'
                elif b_cnt_hc > 0:
                    expected = '跨工序B类匹配(90)'
                else:
                    expected = '跨工序无匹配'
            print(f"   - 期望分类: {expected}")

        # 对比实际与期望
        print("\n[4] 综合判断与结论")
        actual_hid = lot['HANDLER_ID']
        actual_type = lot['match_type']
        print(f"   实际分配设备: {actual_hid}, 匹配类型: {actual_type}, 改机: {lot['changeover_time']}分钟")
        cand = eqp_map.get('HCHC-C-013-6800')
        if cand:
            same_stage_cand = (cand['DEVICE'] == req_device and (cand['STAGE'] or '').upper() == (req_stage or '').upper())
            print(f"   候选设备 HCHC-C-013-6800 与批次 same_stage={same_stage_cand}")
        print("\n说明: 若 HCHC-C-013-6800 满足 same_stage 且 HC+KIT 一致，应优先在同工序选择（小改机/换测试机小改机）而非跨工序设备。若算法仍选择 HCHC-O-011-6800，需检查候选池评分/优先级排序是否导致跨工序得分更高。")
    finally:
        conn.close()

if __name__ == '__main__':
    main()

