"""AI助手设置API接口 - 重定向到Dify设置"""
import json
import requests
from flask import Blueprint, request, jsonify, current_app, redirect, url_for
from app import db
from app.models import DifyConfig
from app.decorators import login_required

ai_settings_bp = Blueprint('ai_settings', __name__)

# 为了向后兼容，重定向到新的dify-settings接口
@ai_settings_bp.route('/ai-settings', methods=['GET'])
@login_required
def get_ai_settings():
    """获取AI助手设置 - 重定向到Dify设置"""
    try:
        # 直接调用dify_settings的逻辑
        from app.api_v2.system.dify_settings import get_dify_settings
        return get_dify_settings()
    except Exception as e:
        current_app.logger.error(f"获取AI设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_settings_bp.route('/ai-settings', methods=['POST'])
@login_required
def save_ai_settings():
    """保存AI助手设置 - 重定向到Dify设置"""
    try:
        # 直接调用dify_settings的逻辑
        from app.api_v2.system.dify_settings import save_dify_settings
        return save_dify_settings()
    except Exception as e:
        current_app.logger.error(f"保存AI设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_settings_bp.route('/test-ai', methods=['POST'])
@login_required
def test_ai_connection():
    """测试AI连接 - 重定向到Dify设置"""
    try:
        # 直接调用dify_settings的逻辑
        from app.api_v2.system.dify_settings import test_dify_connection
        return test_dify_connection()
    except Exception as e:
        current_app.logger.error(f"测试AI连接失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def call_dify_api(ai_type, message, user_id='anonymous'):
    """调用Dify API"""
    try:
        # 获取对应类型的配置
        config = DifyConfig.get_config_by_type(ai_type)
        
        if not config or not config.enabled:
            return {
                'success': False,
                'error': f'{ai_type} AI助手未启用或配置不存在'
            }
        
        api_key = config.get_api_key()
        if not api_key:
            return {
                'success': False,
                'error': 'API密钥未配置'
            }
        
        # 构造API URL
        server_url = config.server_url.rstrip('/')
        api_url = f"{server_url}/v1/chat-messages"
        
        # 构造请求
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'inputs': {},
            'query': message,
            'response_mode': 'blocking',
            'conversation_id': '',
            'user': user_id
        }
        
        # 发送请求
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return {
                'success': True,
                'response': result.get('answer', ''),
                'conversation_id': result.get('conversation_id', '')
            }
        else:
            return {
                'success': False,
                'error': f'API调用失败: HTTP {response.status_code}',
                'details': response.text[:200]
            }
            
    except requests.exceptions.Timeout:
        return {
            'success': False,
            'error': '请求超时'
        }
    except requests.exceptions.ConnectionError:
        return {
            'success': False,
            'error': '连接错误'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'调用失败: {str(e)}'
        } 