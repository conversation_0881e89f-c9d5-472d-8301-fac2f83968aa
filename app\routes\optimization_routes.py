
"""
优化监控仪表板路由
"""
from flask import Blueprint, render_template, jsonify
from flask_login import login_required

optimization_bp = Blueprint('optimization', __name__, url_prefix='/system')

# 🔧 修复：性能监控路由已移至 app/main/routes.py，避免路由冲突
# @optimization_bp.route('/performance')
# @login_required
# def performance_dashboard():
#     """性能监控仪表板"""
#     return render_template('system/performance_dashboard.html')

@optimization_bp.route('/optimization')
@login_required  
def optimization_dashboard():
    """优化监控仪表板"""
    return render_template('system/optimization_dashboard.html')
