#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 多级缓存API - Task 2.2 API接口
提供多级缓存系统的监控、管理和优化接口

功能：
1. 缓存状态监控
2. 性能统计查看
3. 缓存清理管理
4. 优化建议获取
"""

import logging
from flask import Blueprint, jsonify, request
from app.services.multilevel_cache_manager import multilevel_cache
from app.services.intelligent_cache_adapter import IntelligentCacheAdapter

logger = logging.getLogger(__name__)

multilevel_cache_bp = Blueprint('multilevel_cache', __name__)

# 初始化缓存适配器
cache_adapter = IntelligentCacheAdapter(multilevel_cache)

@multilevel_cache_bp.route('/multilevel-cache/status', methods=['GET'])
def get_multilevel_cache_status():
    """获取多级缓存状态"""
    try:
        cache_stats = multilevel_cache.get_cache_stats()
        
        return jsonify({
            'success': True,
            'data': {
                'cache_stats': cache_stats,
                'system_info': {
                    'l1_memory_available': True,
                    'l2_redis_available': cache_stats['redis_available'],
                    'total_cache_levels': 2 if cache_stats['redis_available'] else 1
                }
            },
            'message': '多级缓存状态获取成功'
        })
    except Exception as e:
        logger.error(f"获取多级缓存状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/performance', methods=['GET'])
def get_cache_performance():
    """获取缓存性能分析"""
    try:
        performance_data = cache_adapter.analyze_cache_performance()
        
        return jsonify({
            'success': True,
            'data': performance_data,
            'message': '缓存性能分析获取成功'
        })
    except Exception as e:
        logger.error(f"获取缓存性能分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/clear', methods=['POST'])
def clear_multilevel_cache():
    """清空多级缓存"""
    try:
        data = request.get_json() or {}
        cache_level = data.get('level', 'all')  # all, l1, l2
        pattern = data.get('pattern')  # 可选的清理模式
        
        if pattern:
            # 按模式清理
            multilevel_cache.invalidate_by_pattern(pattern)
            message = f"已按模式 '{pattern}' 清理缓存"
        elif cache_level == 'all':
            # 清理所有缓存
            multilevel_cache.clear_all_caches()
            message = "已清理所有级别缓存"
        else:
            message = f"缓存级别 '{cache_level}' 清理功能待实现"
        
        return jsonify({
            'success': True,
            'message': message
        })
    except Exception as e:
        logger.error(f"清理多级缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/recommendations', methods=['GET'])
def get_optimization_recommendations():
    """获取缓存优化建议"""
    try:
        recommendations = cache_adapter.get_optimization_recommendations()
        
        return jsonify({
            'success': True,
            'data': {
                'recommendations': recommendations,
                'total_count': len(recommendations),
                'high_priority_count': len([r for r in recommendations if r.get('priority') == 'high']),
                'medium_priority_count': len([r for r in recommendations if r.get('priority') == 'medium'])
            },
            'message': '缓存优化建议获取成功'
        })
    except Exception as e:
        logger.error(f"获取缓存优化建议失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/preload', methods=['POST'])
def preload_algorithm_data():
    """预加载算法数据"""
    try:
        data = request.get_json() or {}
        algorithm_type = data.get('algorithm_type', 'hybrid')
        
        # 模拟数据上下文（实际应用中应该从数据管理器获取）
        data_context = {
            'device_priority': [],
            'lot_priority': [],
            'test_specs': [],
            'equipment_status': [],
            'uph_data': {},
            'recipe_files': []
        }
        
        # 尝试从缓存适配器预加载
        try:
            from app.services.algorithm_selector import AlgorithmType
            algo_enum = AlgorithmType(algorithm_type)
            
            preparation_result = cache_adapter.prepare_for_algorithm(
                algorithm_type=algo_enum,
                data_context=data_context
            )
            
            return jsonify({
                'success': True,
                'data': preparation_result,
                'message': f'算法 {algorithm_type} 数据预加载完成'
            })
            
        except ValueError:
            return jsonify({
                'success': False,
                'error': f'不支持的算法类型: {algorithm_type}'
            }), 400
            
    except Exception as e:
        logger.error(f"预加载算法数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/hot-keys', methods=['GET'])
def get_hot_keys():
    """获取热点数据键列表"""
    try:
        cache_stats = multilevel_cache.get_cache_stats()
        
        # 获取热点键信息（简化实现）
        hot_keys_info = {
            'count': cache_stats['hot_keys_count'],
            'description': '访问频率超过阈值的数据键',
            'optimization_tips': [
                '热点数据已自动延长TTL',
                '考虑将热点数据迁移到L1缓存',
                '监控热点数据的访问模式'
            ]
        }
        
        return jsonify({
            'success': True,
            'data': hot_keys_info,
            'message': '热点数据信息获取成功'
        })
    except Exception as e:
        logger.error(f"获取热点数据信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/algorithm-stats', methods=['GET'])
def get_algorithm_cache_stats():
    """获取各算法的缓存统计"""
    try:
        performance_data = cache_adapter.analyze_cache_performance()
        algorithm_performance = performance_data.get('algorithm_performance', {})
        
        # 格式化算法统计数据
        formatted_stats = []
        for algo, stats in algorithm_performance.items():
            formatted_stats.append({
                'algorithm': algo,
                'avg_execution_time': stats['avg_execution_time'],
                'avg_cache_hit_rate': f"{stats['avg_cache_hit_rate']:.1%}",
                'execution_count': stats['execution_count'],
                'performance_grade': _get_performance_grade(stats['avg_cache_hit_rate'], stats['avg_execution_time'])
            })
        
        return jsonify({
            'success': True,
            'data': {
                'algorithm_stats': formatted_stats,
                'total_algorithms': len(formatted_stats),
                'prediction_accuracy': performance_data.get('prediction_accuracy', 0),
                'patterns_learned': performance_data.get('total_patterns_learned', 0)
            },
            'message': '算法缓存统计获取成功'
        })
    except Exception as e:
        logger.error(f"获取算法缓存统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/learning-data/clear', methods=['POST'])
def clear_learning_data():
    """清空缓存学习数据"""
    try:
        cache_adapter.clear_learning_data()
        
        return jsonify({
            'success': True,
            'message': '缓存学习数据已清空'
        })
    except Exception as e:
        logger.error(f"清空缓存学习数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@multilevel_cache_bp.route('/multilevel-cache/benchmark', methods=['POST'])
def run_cache_benchmark():
    """运行缓存性能基准测试"""
    try:
        data = request.get_json() or {}
        test_iterations = data.get('iterations', 100)
        
        # 简化的基准测试
        import time
        import random
        
        start_time = time.time()
        cache_hits = 0
        cache_misses = 0
        
        for i in range(test_iterations):
            test_key = f"benchmark_test_{random.randint(1, 50)}"
            test_value = f"test_data_{i}"
            
            # 设置缓存
            multilevel_cache.set(test_key, test_value)
            
            # 获取缓存
            result = multilevel_cache.get(test_key)
            if result is not None:
                cache_hits += 1
            else:
                cache_misses += 1
        
        total_time = time.time() - start_time
        hit_rate = (cache_hits / test_iterations) * 100 if test_iterations > 0 else 0
        
        benchmark_result = {
            'test_iterations': test_iterations,
            'total_time': round(total_time, 3),
            'avg_time_per_operation': round(total_time / test_iterations, 6),
            'cache_hit_rate': round(hit_rate, 2),
            'cache_hits': cache_hits,
            'cache_misses': cache_misses,
            'operations_per_second': round(test_iterations / total_time, 2)
        }
        
        return jsonify({
            'success': True,
            'data': benchmark_result,
            'message': '缓存性能基准测试完成'
        })
    except Exception as e:
        logger.error(f"缓存性能基准测试失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _get_performance_grade(hit_rate: float, execution_time: float) -> str:
    """根据缓存命中率和执行时间评估性能等级"""
    if hit_rate >= 0.9 and execution_time <= 1.0:
        return "A+"
    elif hit_rate >= 0.8 and execution_time <= 2.0:
        return "A"
    elif hit_rate >= 0.7 and execution_time <= 3.0:
        return "B"
    elif hit_rate >= 0.6 and execution_time <= 5.0:
        return "C"
    else:
        return "D" 