#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出任务管理器
负责大数据量导出的后台任务处理和进度跟踪

功能：
- 异步导出任务管理
- 进度跟踪和状态更新
- 大数据分批处理
- 文件缓存和清理
- 任务队列管理

作者：Claude Code Assistant
版本：阶段3.2
"""

import logging
import os
import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from threading import Thread
import time

# 导入必要的模块
from app.utils.db_connection_pool import get_db_connection_context
from app.services.done_lots_data_service import DoneLotsDataService

logger = logging.getLogger(__name__)

class ExportTaskManager:
    """导出任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Dict] = {}
        self.max_concurrent_tasks = 3
        self.running_tasks = 0
        self.export_dir = os.path.join(os.getcwd(), 'exports')
        self.cleanup_interval = 3600  # 1小时清理一次
        self.file_retention_hours = 24  # 文件保留24小时
        
        # 确保导出目录存在
        os.makedirs(self.export_dir, exist_ok=True)
        
        # 启动清理任务
        self._start_cleanup_thread()
        
        logger.info("📤 ExportTaskManager 初始化完成")
    
    def create_export_task(self, export_config: Dict) -> str:
        """
        创建导出任务
        
        Args:
            export_config: 导出配置
                - mode: 'view', 'adjust', 'final_result'
                - export_type: 'filtered', 'all'
                - format: 'excel', 'csv'
                - filters: 筛选条件
                - user_id: 用户ID (可选)
        
        Returns:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        task_info = {
            'task_id': task_id,
            'status': 'pending',  # pending, running, completed, failed
            'progress': 0,
            'message': '任务已创建，等待执行...',
            'config': export_config,
            'created_at': datetime.now(),
            'started_at': None,
            'completed_at': None,
            'file_path': None,
            'file_size': None,
            'records_count': None,
            'error_message': None,
            'estimated_duration': None,
            'processing_stats': {
                'batches_processed': 0,
                'total_batches': 0,
                'current_batch_size': 0
            }
        }
        
        self.tasks[task_id] = task_info
        
        logger.info(f"📝 创建导出任务: {task_id} - {export_config.get('mode', 'unknown')}模式")
        
        # 如果当前运行任务数未达到限制，立即开始执行
        if self.running_tasks < self.max_concurrent_tasks:
            self._start_task_execution(task_id)
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        # 返回任务状态的副本，避免外部修改
        status = {
            'task_id': task['task_id'],
            'status': task['status'],
            'progress': task['progress'],
            'message': task['message'],
            'created_at': task['created_at'].isoformat(),
            'file_path': task['file_path'],
            'file_size': task['file_size'],
            'records_count': task['records_count'],
            'error_message': task['error_message'],
            'estimated_duration': task['estimated_duration'],
            'processing_stats': task['processing_stats'].copy()
        }
        
        if task['started_at']:
            status['started_at'] = task['started_at'].isoformat()
        if task['completed_at']:
            status['completed_at'] = task['completed_at'].isoformat()
            # 计算实际耗时
            duration = (task['completed_at'] - task['started_at']).total_seconds()
            status['actual_duration'] = duration
        
        return status
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        if task['status'] in ['completed', 'failed']:
            return False
        
        task['status'] = 'cancelled'
        task['message'] = '任务已取消'
        task['completed_at'] = datetime.now()
        
        # 删除部分生成的文件
        if task['file_path'] and os.path.exists(task['file_path']):
            try:
                os.remove(task['file_path'])
            except Exception as e:
                logger.warning(f"删除取消任务的文件失败: {e}")
        
        logger.info(f"❌ 导出任务已取消: {task_id}")
        return True
    
    def _start_task_execution(self, task_id: str):
        """开始执行任务"""
        task = self.tasks.get(task_id)
        if not task or task['status'] != 'pending':
            return
        
        # 更新任务状态
        task['status'] = 'running'
        task['started_at'] = datetime.now()
        task['message'] = '正在执行导出...'
        self.running_tasks += 1
        
        # 在后台线程中执行任务
        thread = Thread(target=self._execute_task, args=(task_id,))
        thread.daemon = True
        thread.start()
    
    def _execute_task(self, task_id: str):
        """执行导出任务"""
        task = self.tasks.get(task_id)
        if not task:
            return
        
        try:
            logger.info(f"🚀 开始执行导出任务: {task_id}")
            
            # 估算任务时间和数据量
            self._estimate_task_requirements(task)
            
            # 根据数据量选择处理策略
            if task['processing_stats']['total_batches'] > 5:
                # 大数据量：分批处理
                self._execute_large_export(task)
            else:
                # 小数据量：直接处理
                self._execute_direct_export(task)
            
            # 任务完成
            task['status'] = 'completed'
            task['progress'] = 100
            task['message'] = f'导出完成，共 {task["records_count"]} 条记录'
            task['completed_at'] = datetime.now()
            
            logger.info(f"✅ 导出任务完成: {task_id} - {task['records_count']}条记录")
            
        except Exception as e:
            # 任务失败
            task['status'] = 'failed'
            task['error_message'] = str(e)
            task['message'] = f'导出失败: {str(e)}'
            task['completed_at'] = datetime.now()
            
            logger.error(f"❌ 导出任务失败: {task_id} - {e}")
            
        finally:
            self.running_tasks -= 1
            # 检查是否有待执行的任务
            self._check_pending_tasks()
    
    def _estimate_task_requirements(self, task: Dict):
        """估算任务需求"""
        try:
            config = task['config']
            data_service = DoneLotsDataService()
            
            # 获取数据统计信息
            stats = data_service.get_data_statistics(filters=config.get('filters', {}))
            
            if stats.get('success'):
                total_records = stats['statistics'].get('total_lots', 0)
                
                # 根据数据量计算分批大小
                if total_records <= 1000:
                    batch_size = total_records  # 小数据量直接处理
                    total_batches = 1
                elif total_records <= 10000:
                    batch_size = 2000  # 中等数据量
                    total_batches = (total_records + batch_size - 1) // batch_size
                else:
                    batch_size = 5000  # 大数据量
                    total_batches = (total_records + batch_size - 1) // batch_size
                
                # 估算处理时间（每1000条记录约需1-2秒）
                estimated_seconds = (total_records / 1000) * 1.5
                
                task['records_count'] = total_records
                task['estimated_duration'] = estimated_seconds
                task['processing_stats'].update({
                    'total_batches': total_batches,
                    'current_batch_size': batch_size
                })
                
                task['message'] = f'预计处理 {total_records} 条记录，分 {total_batches} 批次'
                
                logger.info(f"📊 任务估算完成: {total_records}条记录, {total_batches}批次, 预计{estimated_seconds:.1f}秒")
            
        except Exception as e:
            logger.warning(f"任务估算失败: {e}")
            # 使用默认值
            task['processing_stats'].update({
                'total_batches': 1,
                'current_batch_size': 1000
            })
    
    def _execute_direct_export(self, task: Dict):
        """直接导出（小数据量）"""
        config = task['config']
        data_service = DoneLotsDataService()
        
        task['progress'] = 10
        task['message'] = '正在获取数据...'
        
        # 获取数据
        if config['mode'] == 'view':
            data = data_service.get_done_lots_data(
                filters=config.get('filters', {}),
                pagination={'enabled': False}
            )
        elif config['mode'] == 'adjust':
            success_data = data_service.get_done_lots_data(
                filters=config.get('filters', {}),
                pagination={'enabled': False}
            )
            failed_data = data_service.get_failed_lots_data(
                filters=config.get('filters', {})
            )
            data = {
                'data': success_data.get('data', []) + failed_data.get('data', []),
                'total': success_data.get('total', 0) + failed_data.get('total', 0)
            }
        elif config['mode'] == 'final_result':
            data = data_service.get_final_result_data(
                filters=config.get('filters', {}),
                pagination={'enabled': False}
            )
        
        task['progress'] = 60
        task['message'] = '正在生成文件...'
        
        # 生成文件
        file_path = self._generate_export_file(data['data'], config, task['task_id'])
        
        task['file_path'] = file_path
        task['file_size'] = os.path.getsize(file_path)
        task['records_count'] = len(data['data'])
        task['progress'] = 90
        task['message'] = '文件生成完成'
    
    def _execute_large_export(self, task: Dict):
        """分批导出（大数据量）"""
        config = task['config']
        data_service = DoneLotsDataService()
        
        batch_size = task['processing_stats']['current_batch_size']
        total_batches = task['processing_stats']['total_batches']
        all_data = []
        
        # 分批获取数据
        for batch_num in range(total_batches):
            if task['status'] == 'cancelled':
                return
            
            offset = batch_num * batch_size
            task['progress'] = 10 + (batch_num / total_batches) * 70  # 10-80%用于数据获取
            task['message'] = f'正在处理第 {batch_num + 1}/{total_batches} 批数据...'
            task['processing_stats']['batches_processed'] = batch_num + 1
            
            # 获取批次数据
            batch_filters = config.get('filters', {}).copy()
            
            if config['mode'] == 'view':
                batch_data = data_service.get_done_lots_data(
                    filters=batch_filters,
                    pagination={'page': batch_num + 1, 'size': batch_size}
                )
                all_data.extend(batch_data.get('data', []))
            
            elif config['mode'] == 'adjust':
                # 调整模式需要特殊处理分批逻辑
                success_data = data_service.get_done_lots_data(
                    filters=batch_filters,
                    pagination={'page': batch_num + 1, 'size': batch_size}
                )
                all_data.extend(success_data.get('data', []))
                
                # 只在第一批添加失败数据
                if batch_num == 0:
                    failed_data = data_service.get_failed_lots_data(filters=batch_filters)
                    all_data.extend(failed_data.get('data', []))
            
            elif config['mode'] == 'final_result':
                # 最终结果模式暂时使用简化处理
                if batch_num == 0:
                    batch_data = data_service.get_final_result_data(
                        filters=batch_filters,
                        pagination={'enabled': False}
                    )
                    all_data.extend(batch_data.get('data', []))
                    break
            
            # 添加小延时，避免数据库压力过大
            time.sleep(0.1)
        
        task['progress'] = 80
        task['message'] = '正在生成导出文件...'
        
        # 生成文件
        file_path = self._generate_export_file(all_data, config, task['task_id'])
        
        task['file_path'] = file_path
        task['file_size'] = os.path.getsize(file_path)
        task['records_count'] = len(all_data)
        task['progress'] = 95
        task['message'] = '文件生成完成'
    
    def _generate_export_file(self, data: List[Dict], config: Dict, task_id: str) -> str:
        """生成导出文件"""
        import pandas as pd
        
        # 准备导出数据
        export_data = []
        for item in data:
            row_data = {
                '优先级': item.get('priority', ''),
                '内部工单号': item.get('lot_id', ''),
                '产品名称': item.get('device', '') or item.get('product_name', ''),
                '芯片名称': item.get('chip_id', ''),
                '分选机ID': item.get('handler_id', ''),
                '数量': item.get('quantity', 0) or item.get('good_qty', 0),
                '综合评分': item.get('score', 0.0) or item.get('comprehensive_score', 0.0),
                '工序': item.get('stage', ''),
                '工步': item.get('step', ''),
                '状态': item.get('status', '') or item.get('wip_state', ''),
                '创建时间': item.get('create_time', '') or item.get('CREATE_TIME', '')
            }
            
            # 根据模式添加特有字段
            if config['mode'] == 'adjust':
                row_data['失败原因'] = item.get('failure_reason', '')
                row_data['错误信息'] = item.get('error_message', '')
            elif config['mode'] == 'final_result':
                row_data['发布时间'] = item.get('published_time', '')
                row_data['调整次数'] = item.get('adjustment_count', 0)
            
            export_data.append(row_data)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        mode_name = {'view': '查看模式', 'adjust': '调整模式', 'final_result': '最终结果'}[config['mode']]
        type_name = {'filtered': '筛选结果', 'all': '全部数据'}[config.get('export_type', 'filtered')]
        
        if config.get('format', 'excel') == 'csv':
            filename = f"已排产批次_{mode_name}_{type_name}_{timestamp}_{task_id[:8]}.csv"
            file_path = os.path.join(self.export_dir, filename)
            
            # 生成CSV文件
            df = pd.DataFrame(export_data)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        
        else:
            filename = f"已排产批次_{mode_name}_{type_name}_{timestamp}_{task_id[:8]}.xlsx"
            file_path = os.path.join(self.export_dir, filename)
            
            # 生成Excel文件
            df = pd.DataFrame(export_data)
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=mode_name, index=False)
                
                # 调整列宽
                worksheet = writer.sheets[mode_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            cell_length = len(str(cell.value))
                            if cell_length > max_length:
                                max_length = cell_length
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"📁 导出文件生成: {file_path} ({os.path.getsize(file_path)} bytes)")
        return file_path
    
    def _check_pending_tasks(self):
        """检查并启动待执行的任务"""
        if self.running_tasks >= self.max_concurrent_tasks:
            return
        
        # 查找待执行的任务
        for task_id, task in self.tasks.items():
            if task['status'] == 'pending' and self.running_tasks < self.max_concurrent_tasks:
                self._start_task_execution(task_id)
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_loop():
            while True:
                try:
                    self._cleanup_old_files()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"清理任务执行失败: {e}")
                    time.sleep(300)  # 出错后5分钟再试
        
        thread = Thread(target=cleanup_loop)
        thread.daemon = True
        thread.start()
        
        logger.info("🧹 文件清理线程已启动")
    
    def _cleanup_old_files(self):
        """清理过期文件"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.file_retention_hours)
            cleaned_count = 0
            
            # 清理任务记录中的过期文件
            expired_tasks = []
            for task_id, task in self.tasks.items():
                if (task.get('completed_at') and 
                    task['completed_at'] < cutoff_time and 
                    task['status'] in ['completed', 'failed', 'cancelled']):
                    
                    # 删除文件
                    if task.get('file_path') and os.path.exists(task['file_path']):
                        try:
                            os.remove(task['file_path'])
                            cleaned_count += 1
                        except Exception as e:
                            logger.warning(f"删除过期文件失败: {task['file_path']} - {e}")
                    
                    expired_tasks.append(task_id)
            
            # 删除过期任务记录
            for task_id in expired_tasks:
                del self.tasks[task_id]
            
            # 清理导出目录中的孤立文件
            if os.path.exists(self.export_dir):
                for filename in os.listdir(self.export_dir):
                    file_path = os.path.join(self.export_dir, filename)
                    if os.path.isfile(file_path):
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_mtime < cutoff_time:
                            try:
                                os.remove(file_path)
                                cleaned_count += 1
                            except Exception as e:
                                logger.warning(f"删除孤立文件失败: {file_path} - {e}")
            
            if cleaned_count > 0:
                logger.info(f"🧹 清理完成: 删除了 {cleaned_count} 个过期文件，清理了 {len(expired_tasks)} 个过期任务")
        
        except Exception as e:
            logger.error(f"文件清理过程出错: {e}")
    
    def get_all_tasks(self, limit: int = 50) -> List[Dict]:
        """获取所有任务状态"""
        tasks = sorted(
            [self.get_task_status(task_id) for task_id in self.tasks.keys()],
            key=lambda x: x['created_at'],
            reverse=True
        )
        return tasks[:limit]
    
    def get_running_tasks(self) -> List[Dict]:
        """获取正在运行的任务"""
        return [
            self.get_task_status(task_id)
            for task_id, task in self.tasks.items()
            if task['status'] == 'running'
        ]

# 全局实例
export_task_manager = ExportTaskManager()

logger.info("📤 ExportTaskManager 全局实例已创建")