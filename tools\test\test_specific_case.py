#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定匹配案例
验证JWH6396-XXXXWQFNWK_TA0-Hot的匹配情况
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.services.real_scheduling_service import RealSchedulingService, normalize_pn, pn_equal
from app.utils.db_connection_pool import get_db_connection

def test_specific_case():
    """测试特定的匹配案例"""
    print("=== 测试特定匹配案例: JWH6396-XXXXWQFNWK_TA0-Hot ===")
    
    try:
        # 初始化服务
        service = RealSchedulingService()
        
        # 构造测试数据（基于真实数据）
        lot_requirements = {
            'DEVICE': 'JWH6396-XXXXWQFNWK_TA0',
            'STAGE': 'Hot'
        }
        
        # 模拟预加载数据（基于真实数据库查询结果）
        preloaded_data = {
            'test_specs': [
                {
                    'DEVICE': 'JWH6396-XXXXWQFNWK_TA0',
                    'STAGE': 'Hot',
                    'TESTER': 'STS8300',
                    'HB_PN': '20230144_A',
                    'TB_PN': '20230144_A'
                }
            ],
            'recipe_files': [
                {
                    'DEVICE': 'JWH6396-XXXXWQFNWK_TA0',
                    'STAGE': 'Hot',
                    'HANDLER_CONFIG': 'C6800H',
                    'KIT_PN': 'CKC-QFN6X6-0.85-1X4'
                }
            ]
        }
        
        # 测试设备（基于真实数据库查询结果）
        equipment = {
            'HANDLER_ID': 'HCHC-O-005-6800',
            'DEVICE': 'JWH6396-XXXXWQFNWK_TA0',
            'STAGE': 'Hot',
            'HANDLER_CONFIG': 'C6800H',
            'KIT_PN': 'CKC-QFN6X6-0.85-1X4',
            'TESTER': 'STS8300',
            'HB_PN': '',  # 设备当前没有HB_PN
            'TB_PN': '',  # 设备当前没有TB_PN
            'STATUS': 'IDLE'
        }
        
        print("📋 测试数据:")
        print(f"   批次需求: {lot_requirements['DEVICE']}-{lot_requirements['STAGE']}")
        print(f"   规格配置: TESTER=STS8300, HB_PN=20230144_A, TB_PN=20230144_A")
        print(f"   配方配置: HANDLER_CONFIG=C6800H, KIT_PN=CKC-QFN6X6-0.85-1X4")
        print(f"   设备配置: HANDLER_CONFIG=C6800H, KIT_PN=CKC-QFN6X6-0.85-1X4, TESTER=STS8300")
        print(f"   设备HB/TB: HB_PN='', TB_PN=''")
        
        # 测试匹配逻辑
        print("\n🔍 开始匹配测试:")
        
        # 1. 测试资源获取
        req_tester = service._get_required_tester('JWH6396-XXXXWQFNWK_TA0', 'Hot', preloaded_data)
        req_hb = service._get_required_hb_pn('JWH6396-XXXXWQFNWK_TA0', 'Hot', preloaded_data)
        req_tb = service._get_required_tb_pn('JWH6396-XXXXWQFNWK_TA0', 'Hot', preloaded_data)
        req_handler_config = service._get_required_handler_config('JWH6396-XXXXWQFNWK_TA0', 'Hot', preloaded_data)
        req_kit = service._get_required_kit_pn('JWH6396-XXXXWQFNWK_TA0', 'Hot', preloaded_data)
        
        print(f"   获取的需求资源:")
        print(f"     TESTER: '{req_tester}'")
        print(f"     HB_PN: '{req_hb}'")
        print(f"     TB_PN: '{req_tb}'")
        print(f"     HANDLER_CONFIG: '{req_handler_config}'")
        print(f"     KIT_PN: '{req_kit}'")
        
        # 2. 检查是否缺失资源信息
        is_missing = service._is_missing_resource_info(req_handler_config, req_kit, req_tester, req_hb, req_tb)
        print(f"   是否缺失资源信息: {is_missing}")
        
        # 3. 测试字段比较
        eqp_handler_config = equipment.get('HANDLER_CONFIG', '').strip()
        eqp_kit = equipment.get('KIT_PN', '').strip()
        eqp_tester = equipment.get('TESTER', '').strip()
        eqp_hb = equipment.get('HB_PN', '').strip()
        eqp_tb = equipment.get('TB_PN', '').strip()
        
        print(f"   字段比较:")
        print(f"     HANDLER_CONFIG: '{req_handler_config}' vs '{eqp_handler_config}' = {service._field_equal(req_handler_config, eqp_handler_config)}")
        print(f"     KIT_PN: '{req_kit}' vs '{eqp_kit}' = {service._field_equal(req_kit, eqp_kit)}")
        print(f"     TESTER: '{req_tester}' vs '{eqp_tester}' = {service._field_equal(req_tester, eqp_tester)}")
        print(f"     HB_PN: '{req_hb}' vs '{eqp_hb}' = {pn_equal(req_hb, eqp_hb)}")
        print(f"     TB_PN: '{req_tb}' vs '{eqp_tb}' = {pn_equal(req_tb, eqp_tb)}")
        
        # 4. 执行完整匹配评分
        score, match_type, changeover_time = service.calculate_equipment_match_score_optimized(
            lot_requirements, equipment, preloaded_data
        )
        
        print(f"\n🎯 匹配结果:")
        print(f"   匹配类型: {match_type}")
        print(f"   评分: {score}")
        print(f"   改机时间: {changeover_time}分钟")
        
        # 5. 分析预期结果
        print(f"\n📊 结果分析:")
        if is_missing:
            print(f"   ✅ 预期: 同产品续排 (因为资源信息缺失)")
        elif (service._field_equal(req_handler_config, eqp_handler_config) and
              service._field_equal(req_kit, eqp_kit) and
              service._field_equal(req_tester, eqp_tester) and
              pn_equal(req_hb, eqp_hb) and
              pn_equal(req_tb, eqp_tb)):
            print(f"   ✅ 预期: 同配置匹配")
        else:
            print(f"   ⚠️ 需要进一步分析匹配类型")
        
        # 6. 测试另一种情况：设备有HB/TB配置
        print(f"\n🔄 测试设备有HB/TB配置的情况:")
        equipment_with_hb_tb = equipment.copy()
        equipment_with_hb_tb['HB_PN'] = '20230144_A'
        equipment_with_hb_tb['TB_PN'] = '20230144_A'
        
        score2, match_type2, changeover_time2 = service.calculate_equipment_match_score_optimized(
            lot_requirements, equipment_with_hb_tb, preloaded_data
        )
        
        print(f"   设备HB/TB配置: HB_PN=20230144_A, TB_PN=20230144_A")
        print(f"   匹配结果: {match_type2}({score2}分, {changeover_time2}分钟)")
        
        print("\n✅ 特定案例测试完成")
        
    except Exception as e:
        print(f"✗ 特定案例测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_real_database_case():
    """测试真实数据库案例"""
    print("\n=== 测试真实数据库案例 ===")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取一个具体的批次
        cursor.execute("""
            SELECT * FROM et_wait_lot 
            WHERE DEVICE = 'JWH6396-XXXXWQFNWK_TA0' AND STAGE = 'Hot' 
            LIMIT 1
        """)
        lot = cursor.fetchone()
        
        if not lot:
            print("   ⚠️ 没有找到对应的批次")
            return
        
        print(f"   测试批次: {lot['LOT_ID']}")
        
        # 获取对应的规格和配方
        cursor.execute("""
            SELECT * FROM et_ft_test_spec 
            WHERE DEVICE = %s AND STAGE = %s
        """, (lot['DEVICE'], lot['STAGE']))
        test_spec = cursor.fetchone()
        
        cursor.execute("""
            SELECT * FROM et_recipe_file 
            WHERE DEVICE = %s AND STAGE = %s
        """, (lot['DEVICE'], lot['STAGE']))
        recipe = cursor.fetchone()
        
        cursor.execute("""
            SELECT * FROM eqp_status 
            WHERE DEVICE = %s AND STAGE = %s
        """, (lot['DEVICE'], lot['STAGE']))
        equipment = cursor.fetchone()
        
        if not test_spec:
            print(f"   ❌ 没有找到测试规格")
            return
        if not recipe:
            print(f"   ❌ 没有找到配方")
            return
        if not equipment:
            print(f"   ❌ 没有找到设备")
            return
        
        print(f"   规格: TESTER={test_spec.get('TESTER')}, HB_PN={test_spec.get('HB_PN')}, TB_PN={test_spec.get('TB_PN')}")
        print(f"   配方: HANDLER_CONFIG={recipe.get('HANDLER_CONFIG')}, KIT_PN={recipe.get('KIT_PN')}")
        print(f"   设备: {equipment['HANDLER_ID']}, CONFIG={equipment.get('HANDLER_CONFIG')}, KIT={equipment.get('KIT_PN')}, TESTER={equipment.get('TESTER')}")
        
        # 初始化服务并测试
        service = RealSchedulingService()
        
        # 构造预加载数据
        preloaded_data = {
            'test_specs': [test_spec],
            'recipe_files': [recipe]
        }
        
        lot_requirements = {
            'DEVICE': lot['DEVICE'],
            'STAGE': lot['STAGE']
        }
        
        score, match_type, changeover_time = service.calculate_equipment_match_score_optimized(
            lot_requirements, equipment, preloaded_data
        )
        
        print(f"   匹配结果: {match_type}({score}分, {changeover_time}分钟)")
        
        cursor.close()
        conn.close()
        
        print("✅ 真实数据库案例测试完成")
        
    except Exception as e:
        print(f"✗ 真实数据库案例测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 特定案例匹配测试开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_specific_case()
    test_real_database_case()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")
