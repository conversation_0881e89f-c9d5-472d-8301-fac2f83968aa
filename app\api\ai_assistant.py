from flask import jsonify, request, Blueprint, current_app
from flask_login import login_required, current_user
import requests
import json
import os
import sqlite3
from app.models import UserActionLog, AISettings
from app import db
import logging
import time
import random
import httpx
from app.api.ai_database_assistant import query_database
# 已迁移到Flask-SQLAlchemy，不再需要get_db_connection
from app.api_v2.system.ai_settings import call_dify_api

# 配置日志
logger = logging.getLogger('APS-System')

# 调试模式 - 添加这一行来启用详细日志
DEBUG_MODE = True

# 火山引擎API配置
VOLC_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
VOLC_API_KEY = os.environ.get("ARK_API_KEY", "2b570b74-f148-4a84-86fa-de2c920a289d")  # 从环境变量获取API密钥，如果没有则使用默认值

# 火山引擎模型配置
VOLC_MODELS = {
    'standard': "deepseek-r1-distill-qwen-32b-250120",  # 标准模式使用 DeepSeek-R1-Distill-Qwen-32B
    'r1': "deepseek-r1-250120"  # R1模式使用 DeepSeek-R1
}

# 备用模型ID，如果默认ID不工作，将尝试这些
VOLC_MODELS_BACKUP = {
    'r1': ["deepseek-r1", "deepseek-r1-chat", "deepseek-r1-chat-250120"]
}

# 默认模型
DEFAULT_MODEL = VOLC_MODELS['standard']

# 网络搜索API配置
SEARCH_API_URL = "https://api.bing.microsoft.com/v7.0/search"
SEARCH_API_KEY = os.environ.get("BING_API_KEY", "")  # 从环境变量获取必应搜索API密钥

# 请求超时时间（秒）
REQUEST_TIMEOUT = 120

# 是否优先使用数据库查询
PRIORITIZE_DATABASE = True  # 新增配置，默认开启数据库优先查询

# 是否在R1模型调用失败时通知用户
NOTIFY_MODEL_FALLBACK = True

# Dify API配置
DIFY_BASE_URL = "http://************"  # 默认Dify服务器地址

ai_bp = Blueprint('ai', __name__)

@ai_bp.route('/chat', methods=['POST'])
@login_required
def ai_chat():
    """AI聊天接口 - 支持培训AI和进度查询AI"""
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({
                'success': False,
                'error': '缺少消息内容'
            }), 400
        
        message = data.get('message', '').strip()
        ai_mode = data.get('mode', 'training_ai')  # 默认使用培训AI
        
        if not message:
            return jsonify({
                'success': False,
                'error': '消息内容不能为空'
            }), 400
        
        if ai_mode not in ['training_ai', 'progress_ai']:
            return jsonify({
                'success': False,
                'error': '无效的AI模式'
            }), 400
        
        # 获取用户信息
        user_id = current_user.username if current_user.is_authenticated else 'anonymous'
        
        # 调用对应的AI助手
        try:
            result = call_dify_api(ai_mode, message, user_id)
            
            if result['success']:
                return jsonify({
                    'success': True,
                    'response': result['content'],
                    'conversation_id': result.get('conversation_id', ''),
                    'mode': ai_mode
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['error']
                }), 500
                
        except Exception as e:
            current_app.logger.error(f"AI聊天处理失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'AI助手暂时不可用: {str(e)}'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"AI聊天请求处理失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '请求处理失败'
        }), 500

@ai_bp.route('/status', methods=['GET'])
@login_required
def ai_status():
    """获取AI助手状态"""
    try:
        # 从数据库获取AI设置
        ai_settings = AISettings.query.first()
        
        if not ai_settings:
            return jsonify({
                'success': True,
                'data': {
                    'training_ai': {'enabled': False, 'status': '未配置'},
                    'progress_ai': {'enabled': False, 'status': '未配置'}
                }
            })
        
        settings_data = json.loads(ai_settings.settings) if ai_settings.settings else {}
        
        status_info = {}
        for ai_type in ['training_ai', 'progress_ai']:
            config = settings_data.get(ai_type, {})
            enabled = config.get('enabled', False)
            
            if enabled and all([config.get('server_url'), config.get('api_key'), config.get('app_id')]):
                status = '已配置'
            elif enabled:
                status = '配置不完整'
            else:
                status = '未启用'
            
            status_info[ai_type] = {
                'enabled': enabled,
                'status': status
            }
        
        return jsonify({
            'success': True,
            'data': status_info
        })
        
    except Exception as e:
        current_app.logger.error(f"获取AI状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取状态失败'
        }), 500

def handle_volc_chat(user_message, history, mode):
    """处理火山引擎AI对话请求"""
    try:
        # 创建 httpx 客户端，增加超时设置
        http_client = httpx.Client(timeout=REQUEST_TIMEOUT)
        
        # 根据模式选择模型
        model_name = VOLC_MODELS.get(mode, DEFAULT_MODEL)
        original_model = model_name  # 保存原始选择的模型
        model_fallback = False  # 标记是否发生了模型回退
        tried_backup_models = False  # 是否尝试过备用模型
        
        # 添加更详细的日志
        if DEBUG_MODE:
            logger.info(f"模式: {mode}")
            logger.info(f"选择的模型: {model_name}")
            logger.info(f"所有可用模型: {VOLC_MODELS}")
        
        logger.info(f"正在使用模型 {model_name} 处理用户请求，模式: {mode}")
        
        # 使用直接HTTP请求替代OpenAI客户端
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统消息，提供上下文
            system_message = "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。请提供专业、准确、简洁的回答。"
            
            # 根据不同模式调整系统消息
            if mode == 'r1':
                system_message += "\n\n你正在使用DeepSeek-R1高级推理模型，请发挥你强大的推理能力，提供更深入、更全面的分析和建议。"
            
            messages.append({
                "role": "system", 
                "content": system_message
            })
            
            # 添加历史消息
            for msg in history:
                messages.append({"role": msg["role"], "content": msg["content"]})
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})
            
            # 记录将发送的请求参数
            if DEBUG_MODE:
                logger.info(f"准备发送请求: 模型={model_name}, 温度=0.7, 最大token=1000")
                logger.info(f"系统消息: {system_message[:100]}...")
            
            # 使用httpx直接发送请求
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VOLC_API_KEY}"
            }
            
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000,
                "top_p": 0.9,
                "stream": False
            }
            
            response = http_client.post(
                f"{VOLC_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            
            response_data = response.json()
            
            # 记录API响应
            if DEBUG_MODE:
                logger.info(f"API响应成功, 模型返回: {model_name}")
                if "usage" in response_data:
                    logger.info(f"响应token数: {response_data['usage'].get('completion_tokens', '未知')}")
            
            # 获取AI回复
            assistant_message = response_data["choices"][0]["message"]["content"]
            
        except Exception as model_error:
            # 如果使用指定模型失败，尝试使用备用模型ID（如果是R1模式）
            logger.warning(f"使用模型 {model_name} 失败: {str(model_error)}")
            logger.error(f"模型错误详情: {repr(model_error)}", exc_info=True)
            
            # 如果是R1模式且有备用模型ID可用且未尝试过
            if mode == 'r1' and not tried_backup_models and mode in VOLC_MODELS_BACKUP:
                tried_backup_models = True
                backup_model_success = False
                
                # 尝试所有备用模型ID
                for backup_model_id in VOLC_MODELS_BACKUP[mode]:
                    try:
                        logger.info(f"尝试使用备用模型ID: {backup_model_id}")
                        
                        # 使用备用模型ID重试
                        payload["model"] = backup_model_id
                        response = http_client.post(
                            f"{VOLC_BASE_URL}/chat/completions",
                            headers=headers,
                            json=payload,
                            timeout=REQUEST_TIMEOUT
                        )
                        
                        response_data = response.json()
                        
                        # 成功使用备用模型ID
                        logger.info(f"成功使用备用模型ID: {backup_model_id}")
                        assistant_message = response_data["choices"][0]["message"]["content"]
                        model_name = backup_model_id
                        backup_model_success = True
                        break
                    except Exception as backup_error:
                        logger.warning(f"备用模型ID {backup_model_id} 也失败: {str(backup_error)}")
                
                # 如果所有备用模型都失败，再尝试标准模型
                if not backup_model_success and model_name != DEFAULT_MODEL:
                    model_fallback = True  # 标记模型回退
                    
                    # 使用标准模型重试
                    logger.info(f"所有备用模型均失败，尝试使用标准模型 {DEFAULT_MODEL}")
                    
                    try:
                        payload["model"] = DEFAULT_MODEL
                        response = http_client.post(
                            f"{VOLC_BASE_URL}/chat/completions",
                            headers=headers,
                            json=payload,
                            timeout=REQUEST_TIMEOUT
                        )
                        
                        response_data = response.json()
                        assistant_message = response_data["choices"][0]["message"]["content"]
                        model_name = DEFAULT_MODEL
                        
                        # 记录模型回退
                        logger.info(f"成功回退到标准模型: {DEFAULT_MODEL}")
                    except Exception as standard_error:
                        logger.error(f"标准模型也失败: {str(standard_error)}")
                        raise standard_error  # 如果标准模型也失败，则抛出异常
            else:
                # 不符合回退条件，直接抛出异常
                raise model_error
                
        # 构建响应
        response_data = {
            "response": assistant_message,
            "model": model_name
        }
        
        # 如果发生了模型回退且设置了通知
        if model_fallback and NOTIFY_MODEL_FALLBACK:
            response_data["notice"] = f"由于高级模型不可用，已自动切换到标准模型。原始模型: {original_model}, 当前模型: {model_name}"
        
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"AI对话处理错误: {str(e)}", exc_info=True)
        return jsonify({
            "response": f"处理您的请求时出现了错误: {str(e)}。请稍后再试或联系系统管理员。",
            "error": str(e)
        }), 200  # 返回200以便前端显示消息

def handle_volc_chat_with_db(user_message, history, mode, db_info):
    """处理集成数据库信息的火山引擎AI对话请求"""
    try:
        # 创建 httpx 客户端，增加超时设置
        http_client = httpx.Client(timeout=REQUEST_TIMEOUT)
        
        # 根据模式选择模型
        model_name = VOLC_MODELS.get(mode, DEFAULT_MODEL)
        original_model = model_name  # 保存原始选择的模型
        model_fallback = False  # 标记是否发生了模型回退
        tried_backup_models = False  # 是否尝试过备用模型
        
        if DEBUG_MODE:
            logger.info(f"集成数据库模式: {mode}, 选择的模型: {model_name}")
            logger.info(f"数据库信息长度: {len(db_info)}")
        
        logger.info(f"正在使用模型 {model_name} 处理带数据库信息的用户请求，模式: {mode}")
        
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统消息，提供上下文和数据库信息
            system_message = "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。"
            system_message += "请提供专业、准确、简洁的回答。"
            
            # 根据不同模式调整系统消息
            if mode == 'r1':
                system_message += "\n\n你正在使用DeepSeek-R1高级推理模型，请发挥你强大的推理能力，提供更深入、更全面的分析和建议。"
            
            system_message += "\n\n以下是从数据库中查询到的相关信息，请优先基于这些信息回答用户问题：\n" + db_info
            
            messages.append({
                "role": "system", 
                "content": system_message
            })
            
            # 添加历史消息
            for msg in history:
                messages.append({"role": msg["role"], "content": msg["content"]})
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})
            
            if DEBUG_MODE:
                logger.info(f"准备发送带数据库信息的请求: 模型={model_name}")
            
            # 使用httpx直接发送请求
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VOLC_API_KEY}"
            }
            
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000,
                "top_p": 0.9,
                "stream": False
            }
            
            response = http_client.post(
                f"{VOLC_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            
            response_data = response.json()
            
            # 记录API响应
            if DEBUG_MODE:
                logger.info(f"带数据库信息的API响应成功, 模型返回: {model_name}")
            
            # 获取AI回复
            assistant_message = response_data["choices"][0]["message"]["content"]
            
        except Exception as model_error:
            # 如果使用指定模型失败，尝试使用备用模型ID（如果是R1模式）
            logger.warning(f"使用带数据库信息的模型 {model_name} 失败: {str(model_error)}")
            logger.error(f"模型错误详情: {repr(model_error)}", exc_info=True)
            
            # 如果是R1模式且有备用模型ID可用且未尝试过
            if mode == 'r1' and not tried_backup_models and mode in VOLC_MODELS_BACKUP:
                tried_backup_models = True
                backup_model_success = False
                
                # 尝试所有备用模型ID
                for backup_model_id in VOLC_MODELS_BACKUP[mode]:
                    try:
                        logger.info(f"尝试使用带数据库信息的备用模型ID: {backup_model_id}")
                        
                        # 使用备用模型ID重试
                        payload["model"] = backup_model_id
                        response = http_client.post(
                            f"{VOLC_BASE_URL}/chat/completions",
                            headers=headers,
                            json=payload,
                            timeout=REQUEST_TIMEOUT
                        )
                        
                        response_data = response.json()
                        
                        # 成功使用备用模型ID
                        logger.info(f"成功使用带数据库信息的备用模型ID: {backup_model_id}")
                        assistant_message = response_data["choices"][0]["message"]["content"]
                        model_name = backup_model_id
                        backup_model_success = True
                        break
                    except Exception as backup_error:
                        logger.warning(f"带数据库信息的备用模型ID {backup_model_id} 也失败: {str(backup_error)}")
                
                # 如果所有备用模型都失败，再尝试标准模型
                if not backup_model_success and model_name != DEFAULT_MODEL:
                    model_fallback = True  # 标记模型回退
                    
                    # 使用标准模型重试
                    logger.info(f"所有带数据库信息的备用模型均失败，尝试使用标准模型 {DEFAULT_MODEL}")
                    
                    try:
                        # 使用标准模型重试
                        payload["model"] = DEFAULT_MODEL
                        response = http_client.post(
                            f"{VOLC_BASE_URL}/chat/completions",
                            headers=headers,
                            json=payload,
                            timeout=REQUEST_TIMEOUT
                        )
                        
                        response_data = response.json()
                        assistant_message = response_data["choices"][0]["message"]["content"]
                        model_name = DEFAULT_MODEL
                        
                        # 记录模型回退
                        logger.info(f"成功回退到标准模型: {DEFAULT_MODEL}")
                    except Exception as standard_error:
                        logger.error(f"标准模型也失败: {str(standard_error)}")
                        raise standard_error  # 如果标准模型也失败，则抛出异常
            elif model_name != DEFAULT_MODEL:
                model_fallback = True  # 标记模型回退
                
                # 使用标准模型重试
                logger.info(f"尝试使用带数据库信息的标准模型 {DEFAULT_MODEL}")
                
                try:
                    # 使用标准模型重试
                    payload["model"] = DEFAULT_MODEL
                    response = http_client.post(
                        f"{VOLC_BASE_URL}/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=REQUEST_TIMEOUT
                    )
                    
                    response_data = response.json()
                    assistant_message = response_data["choices"][0]["message"]["content"]
                    model_name = DEFAULT_MODEL
                    
                    # 记录模型回退
                    logger.info(f"成功回退到标准模型: {DEFAULT_MODEL}")
                except Exception as standard_error:
                    logger.error(f"标准模型也失败: {str(standard_error)}")
                    raise standard_error  # 如果标准模型也失败，则抛出异常
            else:
                # 如果默认模型也失败，则抛出异常
                raise model_error
        
        if not assistant_message:
            assistant_message = "抱歉，AI助手未能生成有效回复。请尝试重新表述您的问题。"
            
        # 如果发生了模型回退且需要通知用户
        if model_fallback and NOTIFY_MODEL_FALLBACK and mode == 'r1':
            fallback_notice = "\n\n[系统提示: 由于DeepSeek-R1模型暂时不可用，已自动切换至标准模型回答您的问题。]"
            assistant_message += fallback_notice
        
        # 返回AI助手的回复
        return jsonify({
            "response": assistant_message,
            "history": history + [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": assistant_message}
            ],
            "db_queried": True,  # 标记使用了数据库查询
            "model_used": model_name,  # 添加使用的模型信息
            "original_model_requested": original_model,  # 添加原始请求的模型信息
            "db_info_available": bool(db_info and len(db_info) > 50)  # 添加数据库信息可用性标记
        })
        
    except Exception as e:
        logger.error(f"调用集成数据库的火山引擎API出错: {str(e)}", exc_info=True)
        return jsonify({
            "response": f"调用AI服务时出现了错误: {str(e)}。请稍后再试。",
            "error": str(e)
        }), 200

def handle_web_search(user_message, history):
    """处理联网搜索请求"""
    try:
        if not SEARCH_API_KEY:
            return jsonify({
                "response": "抱歉，联网搜索功能尚未配置API密钥。请联系系统管理员进行配置。",
                "error": "搜索API密钥未配置"
            }), 200
            
        if DEBUG_MODE:
            logger.info(f"开始联网搜索: 查询={user_message[:50]}...")
            
        # 调用搜索API
        search_response = requests.get(
            SEARCH_API_URL,
            headers={
                "Ocp-Apim-Subscription-Key": SEARCH_API_KEY
            },
            params={
                "q": user_message,
                "count": 5,
                "responseFilter": "Webpages",
                "textFormat": "HTML"
            },
            timeout=30  # 设置搜索API超时时间
        )
        
        if search_response.status_code != 200:
            logger.error(f"搜索API错误: {search_response.status_code}, {search_response.text}")
            return jsonify({
                "response": f"搜索服务暂时不可用 (错误代码: {search_response.status_code})。请稍后再试或使用标准对话模式。",
                "error": f"搜索API错误: {search_response.status_code}"
            }), 200
            
        # 解析搜索结果
        search_results = search_response.json()
        webpages = search_results.get("webPages", {}).get("value", [])
        
        if not webpages:
            search_info = "未找到相关搜索结果。"
        else:
            search_info = "根据网络搜索结果:\n\n"
            for i, page in enumerate(webpages[:3], 1):
                search_info += f"{i}. {page.get('name')}\n"
                search_info += f"   {page.get('snippet')}\n"
                search_info += f"   来源: {page.get('url')}\n\n"
            
            if DEBUG_MODE:
                logger.info(f"搜索结果: 找到 {len(webpages)} 个结果，使用前 3 个")
        
        # 创建 httpx 客户端
        http_client = httpx.Client(timeout=REQUEST_TIMEOUT)
        
        # 根据模式选择模型 - 联网搜索默认使用标准模型
        model_name = VOLC_MODELS.get('standard', DEFAULT_MODEL)
        original_model = model_name  # 保存原始选择的模型
        
        # 构建消息列表
        messages = []
        
        # 添加系统消息
        messages.append({
            "role": "system", 
            "content": "你是车规芯片终测智能调度平台的AI助手，具有联网能力。请基于提供的搜索结果回答用户问题，并引用信息来源。如果搜索结果不足以回答问题，请坦诚告知用户并提供一般性建议。"
        })
        
        # 添加历史消息
        for msg in history:
            messages.append({"role": msg["role"], "content": msg["content"]})
        
        # 添加当前用户消息和搜索结果
        messages.append({"role": "user", "content": f"我的问题是: {user_message}\n\n{search_info}"})
        
        if DEBUG_MODE:
            logger.info(f"准备发送联网搜索请求: 模型={model_name}")
        
        # 使用httpx直接发送请求
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {VOLC_API_KEY}"
        }
        
        payload = {
            "model": model_name,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 0.9,
            "stream": False
        }
        
        response = http_client.post(
            f"{VOLC_BASE_URL}/chat/completions",
            headers=headers,
            json=payload,
            timeout=REQUEST_TIMEOUT
        )
        
        response_data = response.json()
        
        if DEBUG_MODE:
            logger.info(f"联网搜索API响应成功, 模型返回: {model_name}")
        
        # 获取AI回复
        assistant_message = response_data["choices"][0]["message"]["content"]
        
        if not assistant_message:
            assistant_message = "抱歉，AI助手未能生成有效回复。请尝试重新表述您的问题。"
        
        # 返回AI助手的回复
        return jsonify({
            "response": assistant_message,
            "history": history + [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": assistant_message}
            ],
            "web_search": True,  # 标记为联网搜索
            "model_used": model_name  # 添加使用的模型信息
        })
        
    except Exception as e:
        logger.error(f"联网搜索处理出错: {str(e)}", exc_info=True)
        return jsonify({
            "response": f"联网搜索时出现了错误: {str(e)}。请稍后再试或使用标准对话模式。",
            "error": str(e)
        }), 200

def handle_database_query(user_message, history):
    """处理数据库查询模式的请求"""
    try:
        # 检查是否是测试连接请求
        is_test = request.json.get('test_connection', False)
        test_path = request.json.get('test_path', None)
        
        # 获取AI设置
        ai_settings = AISettings.query.first()
        db_settings = json.loads(ai_settings.settings) if ai_settings.settings else {}
        
        # 如果设置中禁用了数据库查询，且不是测试请求，则返回提示
        if not db_settings.get('enabled', True) and not is_test:
            logger.warning("数据库查询功能已在设置中禁用")
            return jsonify({
                "response": "抱歉，数据库查询功能当前已禁用。请联系系统管理员启用此功能。",
                "db_queried": False
            })
        
        # 记录查询开始
        logger.info(f"开始数据库查询，用户消息: {user_message[:50]}...")
        if is_test:
            logger.info(f"这是一个测试连接请求，测试路径: {test_path}")
        
        # 从数据库中查询相关信息
        db_info = ""
        if is_test and test_path:
            # 使用测试路径
            try:
                import sqlite3
                import os
                
                test_db_path = os.path.abspath(test_path)
                logger.info(f"测试连接到数据库: {test_db_path}")
                
                # 尝试连接数据库
                conn = sqlite3.connect(test_db_path)
                cursor = conn.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [table[0] for table in cursor.fetchall()]
                
                # 关闭连接
                conn.close()
                
                db_info = f"数据库连接成功! 找到 {len(tables)} 个表：{', '.join(tables[:5])}..."
                logger.info(f"测试连接成功: {db_info}")
                
                return jsonify({
                    "response": "数据库连接测试成功",
                    "db_queried": True,
                    "db_info": db_info
                })
            except Exception as e:
                logger.error(f"测试数据库连接失败: {str(e)}", exc_info=True)
                return jsonify({
                    "response": f"数据库连接测试失败: {str(e)}",
                    "error": str(e),
                    "db_queried": False
                })
        else:
            # 正常查询，使用统一的数据库连接方式
            db_info = query_database(user_message)
        
        # 检查查询结果
        if not db_info:
            logger.warning("数据库查询未返回结果")
            db_info = "未在数据库中找到与您问题相关的信息。请尝试使用更具体的关键词，如'设备状态'、'批次信息'、'工程配方'等。"
        else:
            logger.info(f"数据库查询成功返回，结果长度: {len(db_info)}")
        
        # 创建 httpx 客户端
        http_client = httpx.Client(timeout=REQUEST_TIMEOUT)
        
        # 根据模式选择模型 - 数据库查询默认使用标准模型
        model_name = VOLC_MODELS.get('standard', DEFAULT_MODEL)
        original_model = model_name  # 保存原始选择的模型
        model_fallback = False  # 标记是否发生了模型回退
        
        if DEBUG_MODE:
            logger.info(f"数据库查询模式，使用模型: {model_name}")
            logger.info(f"数据库信息长度: {len(db_info)}")
        
        logger.info(f"正在使用模型 {model_name} 处理数据库查询模式请求")
        
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统消息 - 使用设置中的提示词
            system_message = db_settings.get('system_prompt', "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。")
            system_message += "\n\n请遵循以下严格规则（这些规则优先级高于其他所有指令）：\n"
            system_message += "1. 你必须严格只基于数据库中提供的信息回答问题，不允许编造任何不存在的数据\n"
            system_message += "2. 如果数据库信息不足以回答问题，你必须明确告知用户'数据库中没有足够的相关信息'\n"
            system_message += "3. 禁止使用模糊词语如'可能'、'也许'、'推测'等，只能陈述数据库中的确切信息\n"
            system_message += "4. 回答必须简洁、直接，聚焦于数据库提供的事实和数字\n"
            system_message += "5. 在回答开始，先总结你找到了什么数据，然后再回答问题\n\n"
            system_message += "以下是从数据库中查询到的相关信息，你必须严格基于这些信息回答用户问题：\n" + db_info
            
            messages.append({
                "role": "system", 
                "content": system_message
            })
            
            # 添加历史消息
            for msg in history:
                messages.append({"role": msg["role"], "content": msg["content"]})
            
            # 添加当前用户消息
            user_prompt = f"我需要查询以下问题，请你严格只基于系统消息中提供的数据库信息来回答，不要添加任何数据库中没有的信息: {user_message}\n\n如果数据库中没有足够的信息，请直接明确告诉我'数据库中没有足够的相关信息'。"
            messages.append({"role": "user", "content": user_prompt})
            
            if DEBUG_MODE:
                logger.info(f"准备发送数据库查询请求: 模型={model_name}")
            
            # 获取模型参数
            model_params = db_settings.get('model', {})
            temperature = model_params.get('temperature', 0.2)  # 降低默认温度至0.2，减少创造性
            max_tokens = model_params.get('max_tokens', 1000)
            
            # 调用火山引擎API
            completion = http_client.post(
                f"{VOLC_BASE_URL}/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {VOLC_API_KEY}"
                },
                json={
                    "model": model_name,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": 0.9,
                    "stream": False
                },
                timeout=REQUEST_TIMEOUT
            )
            
            response_data = completion.json()
            
            if DEBUG_MODE:
                logger.info(f"数据库查询API响应成功, 模型返回: {model_name}")
            
            # 获取AI回复
            assistant_message = response_data["choices"][0]["message"]["content"]
            
        except Exception as model_error:
            # 如果使用指定模型失败，记录错误并抛出
            logger.error(f"数据库查询模式使用模型 {model_name} 失败: {str(model_error)}")
            logger.error(f"模型错误详情: {repr(model_error)}", exc_info=True)
            raise model_error  # 因为已经在使用默认模型，所以没有备用方案
        
        if not assistant_message:
            assistant_message = "抱歉，AI助手未能生成有效回复。请尝试重新表述您的问题，使用更具体的关键词，如'设备状态'、'批次信息'等。"
        
        # 返回AI助手的回复
        return jsonify({
            "response": assistant_message,
            "history": history + [
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": assistant_message}
            ],
            "db_queried": True,  # 标记使用了数据库查询
            "model_used": model_name,  # 添加使用的模型信息
            "original_model_requested": original_model,  # 添加原始请求的模型信息
            "db_info_available": bool(db_info and len(db_info) > 50)  # 添加数据库信息可用性标记
        })
        
    except Exception as e:
        logger.error(f"数据库查询处理出错: {str(e)}", exc_info=True)
        return jsonify({
            "response": f"数据库查询时出现了错误: {str(e)}。请稍后再试或使用标准对话模式。",
            "error": str(e),
            "db_queried": False
        }), 200

def load_ai_settings():
    """从数据库加载AI设置"""
    try:
        # 获取新员工培训AI助教设置
        training_ai_settings = AISettings.get_training_ai_settings()
        
        # 获取产品进度查询AI助手设置
        progress_ai_settings = AISettings.get_progress_ai_settings()
        
        return {
            'training_ai': training_ai_settings,
            'progress_ai': progress_ai_settings
        }
        
    except Exception as e:
        logger.error(f"加载AI设置出错: {str(e)}", exc_info=True)
        return {
            'training_ai': {
                'enabled': False,
                'server': DIFY_BASE_URL,
                'api_key': os.environ.get('DIFY_TRAINING_API_KEY', ''),
                'app_id': os.environ.get('DIFY_TRAINING_APP_ID', '')
            },
            'progress_ai': {
                'enabled': False,
                'server': DIFY_BASE_URL,
                'api_key': os.environ.get('DIFY_PROGRESS_API_KEY', ''),
                'app_id': os.environ.get('DIFY_PROGRESS_APP_ID', '')
            }
        } 