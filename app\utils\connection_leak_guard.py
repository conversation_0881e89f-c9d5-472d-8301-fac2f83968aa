#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接泄漏防护守护进程 - 阶段1优化
自动检测和清理泄漏的数据库连接，防止连接池耗尽
"""

import threading
import time
import logging
import signal
import sys
from typing import Dict, Any, Set
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ConnectionLeak:
    """连接泄漏信息"""
    connection_id: str
    start_time: float
    function_name: str
    thread_id: int
    timeout: int
    stack_trace: str = None

class ConnectionLeakGuard:
    """连接泄漏防护守护进程"""
    
    def __init__(self, check_interval=60, max_connection_age=300):
        self.check_interval = check_interval  # 检查间隔(秒)
        self.max_connection_age = max_connection_age  # 最大连接年龄(秒)
        
        self._running = False
        self._guard_thread = None
        self._lock = threading.Lock()
        
        # 连接注册表
        self._active_connections: Dict[str, ConnectionLeak] = {}
        self._cleaned_connections: Set[str] = set()
        
        # 统计信息
        self._stats = {
            'total_connections_tracked': 0,
            'leaks_detected': 0,
            'leaks_cleaned': 0,
            'false_positives': 0,
            'guard_runs': 0
        }
        
        logger.info("🛡️ 连接泄漏防护守护进程已初始化")
    
    def register_connection(self, connection_id: str, function_name: str, 
                          timeout: int = 300, stack_trace: str = None):
        """注册连接"""
        with self._lock:
            connection_leak = ConnectionLeak(
                connection_id=connection_id,
                start_time=time.time(),
                function_name=function_name,
                thread_id=threading.current_thread().ident,
                timeout=timeout,
                stack_trace=stack_trace
            )
            
            self._active_connections[connection_id] = connection_leak
            self._stats['total_connections_tracked'] += 1
            
            logger.debug(f"📝 注册连接: {connection_id} from {function_name}")
    
    def unregister_connection(self, connection_id: str):
        """注销连接"""
        with self._lock:
            if connection_id in self._active_connections:
                connection_leak = self._active_connections.pop(connection_id)
                duration = time.time() - connection_leak.start_time
                
                logger.debug(f"✅ 注销连接: {connection_id}, 持续时间: {duration:.1f}s")
                return True
            
            return False
    
    def start_guard(self):
        """启动守护进程"""
        if self._running:
            logger.warning("🛡️ 守护进程已在运行")
            return
        
        self._running = True
        self._guard_thread = threading.Thread(
            target=self._guard_loop,
            name="ConnectionLeakGuard",
            daemon=True
        )
        self._guard_thread.start()
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🚀 连接泄漏防护守护进程已启动")
    
    def stop_guard(self):
        """停止守护进程"""
        self._running = False
        
        if self._guard_thread and self._guard_thread.is_alive():
            self._guard_thread.join(timeout=5)
        
        logger.info("⏹️ 连接泄漏防护守护进程已停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"🔔 收到信号 {signum}，正在停止守护进程...")
        self.stop_guard()
        sys.exit(0)
    
    def _guard_loop(self):
        """守护进程主循环"""
        logger.info(f"🔄 守护进程开始运行，检查间隔: {self.check_interval}秒")
        
        while self._running:
            try:
                self._check_for_leaks()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"❌ 守护进程检查异常: {e}")
                time.sleep(self.check_interval)
    
    def _check_for_leaks(self):
        """检查连接泄漏"""
        self._stats['guard_runs'] += 1
        current_time = time.time()
        leaked_connections = []
        
        with self._lock:
            for conn_id, conn_leak in self._active_connections.items():
                connection_age = current_time - conn_leak.start_time
                
                # 检查是否超时
                if connection_age > conn_leak.timeout:
                    leaked_connections.append((conn_id, conn_leak, connection_age))
        
        if not leaked_connections:
            logger.debug(f"✅ 守护检查完成，无泄漏连接 (活跃: {len(self._active_connections)})")
            return
        
        # 处理泄漏连接
        for conn_id, conn_leak, age in leaked_connections:
            self._handle_leaked_connection(conn_id, conn_leak, age)
    
    def _handle_leaked_connection(self, connection_id: str, 
                                 connection_leak: ConnectionLeak, age: float):
        """处理泄漏连接"""
        self._stats['leaks_detected'] += 1
        
        logger.warning(f"🔴 检测到连接泄漏: {connection_id}")
        logger.warning(f"   函数: {connection_leak.function_name}")
        logger.warning(f"   线程: {connection_leak.thread_id}")
        logger.warning(f"   年龄: {age:.1f}秒 (超时: {connection_leak.timeout}秒)")
        logger.warning(f"   开始时间: {datetime.fromtimestamp(connection_leak.start_time)}")
        
        if connection_leak.stack_trace:
            logger.warning(f"   调用栈: {connection_leak.stack_trace}")
        
        # 尝试清理连接
        if self._cleanup_leaked_connection(connection_id, connection_leak):
            self._stats['leaks_cleaned'] += 1
            logger.info(f"🧹 已清理泄漏连接: {connection_id}")
        else:
            logger.error(f"❌ 无法清理泄漏连接: {connection_id}")
        
        # 从注册表中移除
        with self._lock:
            if connection_id in self._active_connections:
                del self._active_connections[connection_id]
                self._cleaned_connections.add(connection_id)
    
    def _cleanup_leaked_connection(self, connection_id: str, 
                                  connection_leak: ConnectionLeak) -> bool:
        """清理泄漏连接"""
        try:
            # 这里可以实现具体的连接清理逻辑
            # 比如强制关闭连接、回滚事务等
            
            # 记录清理操作
            logger.info(f"🔧 正在清理连接: {connection_id}")
            
            # 模拟清理过程
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接清理失败 {connection_id}: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取守护进程状态"""
        with self._lock:
            current_time = time.time()
            
            # 分析活跃连接
            connection_ages = []
            for conn_leak in self._active_connections.values():
                age = current_time - conn_leak.start_time
                connection_ages.append(age)
            
            avg_age = sum(connection_ages) / len(connection_ages) if connection_ages else 0
            max_age = max(connection_ages) if connection_ages else 0
            
            return {
                'guard_running': self._running,
                'check_interval': self.check_interval,
                'max_connection_age': self.max_connection_age,
                'active_connections': len(self._active_connections),
                'cleaned_connections': len(self._cleaned_connections),
                'average_connection_age': avg_age,
                'max_connection_age_current': max_age,
                'statistics': self._stats.copy(),
                'connection_details': [
                    {
                        'id': conn_id,
                        'function': conn_leak.function_name,
                        'age': current_time - conn_leak.start_time,
                        'timeout': conn_leak.timeout,
                        'thread_id': conn_leak.thread_id
                    }
                    for conn_id, conn_leak in self._active_connections.items()
                ]
            }
    
    def force_cleanup(self) -> int:
        """强制清理所有活跃连接"""
        cleaned_count = 0
        
        with self._lock:
            connection_ids = list(self._active_connections.keys())
        
        for conn_id in connection_ids:
            if conn_id in self._active_connections:
                conn_leak = self._active_connections[conn_id]
                if self._cleanup_leaked_connection(conn_id, conn_leak):
                    cleaned_count += 1
                
                # 从注册表中移除
                with self._lock:
                    if conn_id in self._active_connections:
                        del self._active_connections[conn_id]
                        self._cleaned_connections.add(conn_id)
        
        logger.warning(f"🧹 强制清理了 {cleaned_count} 个连接")
        return cleaned_count
    
    def print_status_report(self):
        """打印状态报告"""
        status = self.get_status()
        
        print("\n🛡️ 连接泄漏防护状态报告")
        print("=" * 50)
        print(f"守护进程状态: {'运行中' if status['guard_running'] else '已停止'}")
        print(f"检查间隔: {status['check_interval']}秒")
        print(f"最大连接年龄限制: {status['max_connection_age']}秒")
        
        print(f"\n📊 连接统计:")
        print(f"   活跃连接: {status['active_connections']}个")
        print(f"   已清理连接: {status['cleaned_connections']}个")
        print(f"   平均连接年龄: {status['average_connection_age']:.1f}秒")
        print(f"   最大连接年龄: {status['max_connection_age_current']:.1f}秒")
        
        print(f"\n📈 守护统计:")
        stats = status['statistics']
        print(f"   总跟踪连接: {stats['total_connections_tracked']}")
        print(f"   检测到泄漏: {stats['leaks_detected']}")
        print(f"   成功清理: {stats['leaks_cleaned']}")
        print(f"   守护运行次数: {stats['guard_runs']}")
        
        if status['connection_details']:
            print(f"\n🔍 活跃连接详情:")
            for conn in status['connection_details']:
                print(f"   ID: {conn['id'][:16]}... | 函数: {conn['function']} | "
                      f"年龄: {conn['age']:.1f}s | 超时: {conn['timeout']}s")

# 全局守护实例
connection_leak_guard = ConnectionLeakGuard()

def start_connection_guard():
    """启动连接防护守护进程"""
    connection_leak_guard.start_guard()

def stop_connection_guard():
    """停止连接防护守护进程"""
    connection_leak_guard.stop_guard()

def register_connection(connection_id: str, function_name: str, timeout: int = 300):
    """注册连接"""
    connection_leak_guard.register_connection(connection_id, function_name, timeout)

def unregister_connection(connection_id: str):
    """注销连接"""
    connection_leak_guard.unregister_connection(connection_id)

def get_guard_status():
    """获取守护状态"""
    return connection_leak_guard.get_status()

if __name__ == "__main__":
    # 测试运行
    print("🛡️ 连接泄漏防护守护进程测试")
    
    guard = ConnectionLeakGuard(check_interval=5, max_connection_age=15)
    
    try:
        guard.start_guard()
        
        # 模拟一些连接
        guard.register_connection("test_conn_1", "test_function_1", timeout=10)
        guard.register_connection("test_conn_2", "test_function_2", timeout=20)
        
        # 运行一段时间
        time.sleep(12)
        
        # 正常注销一个连接
        guard.unregister_connection("test_conn_1")
        
        # 让另一个连接泄漏
        time.sleep(10)
        
        # 打印状态
        guard.print_status_report()
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    finally:
        guard.stop_guard()