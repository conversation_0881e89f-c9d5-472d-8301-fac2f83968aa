{% extends "base.html" %}

{% block title %}系统设置{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card settings-card">
                <div class="card-body">
                    <h4 class="settings-header">
                        <i class="fas fa-cog me-2"></i>AI助手设置
                    </h4>
                    
                    <!-- 设置导航标签 -->
                    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="training-ai-tab" data-bs-toggle="tab" data-bs-target="#training-ai" 
                                    type="button" role="tab" aria-controls="training-ai" aria-selected="true">
                                <i class="fas fa-graduation-cap me-1"></i>新员工培训AI助教
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="progress-ai-tab" data-bs-toggle="tab" data-bs-target="#progress-ai" 
                                    type="button" role="tab" aria-controls="progress-ai" aria-selected="false">
                                <i class="fas fa-chart-line me-1"></i>产品进度查询AI助手
                            </button>
                        </li>

                    </ul>
                    
                    <!-- 设置内容 -->
                    <div class="tab-content" id="settingsTabContent">
                        <!-- 新员工培训AI助教设置 -->
                        <div class="tab-pane fade show active" id="training-ai" role="tabpanel" aria-labelledby="training-ai-tab">
                            <div class="settings-section">
                                <h5><i class="fas fa-graduation-cap me-2"></i>新员工培训AI助教配置</h5>
                                <hr>
                                
                                <form id="trainingAIForm">
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableTrainingAI" name="enable_training_ai" 
                                                   data-bs-toggle="collapse" data-bs-target="#trainingAISettings">
                                            <label class="form-check-label" for="enableTrainingAI">启用新员工培训AI助教</label>
                                            <span class="status-badge" id="trainingAIStatus">未启用</span>
                                        </div>
                                        <p class="setting-description">为新员工提供专业的培训指导，帮助新员工快速了解车规芯片终测智能调度平台的操作流程和业务知识。</p>
                                    </div>
                                    
                                    <div class="collapse" id="trainingAISettings">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="trainingAIServer" class="form-label">
                                                                <i class="fas fa-server me-1"></i>Dify API服务器
                                                            </label>
                                                            <input type="text" class="form-control" id="trainingAIServer" name="training_ai_server" 
                                                                   value="http://10.16.10.103" placeholder="http://your-dify-server.com">
                                                            <div class="form-text">新员工培训AI助教的Dify API服务器地址。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="trainingAIKey" class="form-label">
                                                                <i class="fas fa-key me-1"></i>API密钥
                                                            </label>
                                                            <input type="text" class="form-control" id="trainingAIKey" name="training_ai_key" 
                                                                   placeholder="输入Dify API密钥">
                                                            <div class="form-text">新员工培训AI助教的Dify API密钥。</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="trainingAIModel" class="form-label">
                                                                <i class="fas fa-cog me-1"></i>AI模型/应用ID
                                                            </label>
                                                            <input type="text" class="form-control" id="trainingAIModel" name="training_ai_model" 
                                                                   placeholder="输入Dify应用ID或模型标识">
                                                            <div class="form-text">新员工培训AI助教对应的Dify应用ID。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="trainingAIChatbotUrl" class="form-label">
                                                                <i class="fas fa-link me-1"></i>聊天窗口URL
                                                            </label>
                                                            <input type="text" class="form-control" id="trainingAIChatbotUrl" name="training_ai_chatbot_url" 
                                                                   placeholder="https://udify.app/chatbot/xxxxx">
                                                            <div class="form-text">Dify聊天窗口的嵌入URL（从Dify应用中获取）。</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 连接测试 -->
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-plug me-2"></i>连接测试</h6>
                                                    <div class="alert alert-light border">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>测试新员工培训AI助教的连接状态</span>
                                                            <button type="button" class="btn btn-sm btn-outline-primary" id="testTrainingAIBtn">
                                                                <i class="fas fa-play me-1"></i>测试连接
                                                            </button>
                                                        </div>
                                                        <div id="trainingAITestResult" class="mt-2" style="display: none;">
                                                            <hr>
                                                            <div class="text-muted">测试结果将显示在这里...</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-primary" id="saveTrainingAIBtn">
                                            <i class="fas fa-save me-1"></i>保存新员工培训AI助教设置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 产品进度查询AI助手设置 -->
                        <div class="tab-pane fade" id="progress-ai" role="tabpanel" aria-labelledby="progress-ai-tab">
                            <div class="settings-section">
                                <h5><i class="fas fa-chart-line me-2"></i>产品进度查询AI助手配置</h5>
                                <hr>
                                
                                <form id="progressAIForm">
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableProgressAI" name="enable_progress_ai" 
                                                   data-bs-toggle="collapse" data-bs-target="#progressAISettings">
                                            <label class="form-check-label" for="enableProgressAI">启用产品进度查询AI助手</label>
                                            <span class="status-badge" id="progressAIStatus">未启用</span>
                                        </div>
                                        <p class="setting-description">为用户提供实时的产品进度查询服务，支持查询订单状态、生产进度、设备状态等信息。</p>
                                    </div>
                                    
                                    <div class="collapse" id="progressAISettings">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="progressAIServer" class="form-label">
                                                                <i class="fas fa-server me-1"></i>Dify API服务器
                                                            </label>
                                                            <input type="text" class="form-control" id="progressAIServer" name="progress_ai_server" 
                                                                   value="http://10.16.10.103" placeholder="http://your-dify-server.com">
                                                            <div class="form-text">产品进度查询AI助手的Dify API服务器地址。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="progressAIKey" class="form-label">
                                                                <i class="fas fa-key me-1"></i>API密钥
                                                            </label>
                                                            <input type="text" class="form-control" id="progressAIKey" name="progress_ai_key" 
                                                                   placeholder="输入Dify API密钥">
                                                            <div class="form-text">产品进度查询AI助手的Dify API密钥。</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="progressAIModel" class="form-label">
                                                                <i class="fas fa-cog me-1"></i>AI模型/应用ID
                                                            </label>
                                                            <input type="text" class="form-control" id="progressAIModel" name="progress_ai_model" 
                                                                   placeholder="输入Dify应用ID或模型标识">
                                                            <div class="form-text">产品进度查询AI助手对应的Dify应用ID。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="progressAIChatbotUrl" class="form-label">
                                                                <i class="fas fa-link me-1"></i>聊天窗口URL
                                                            </label>
                                                            <input type="text" class="form-control" id="progressAIChatbotUrl" name="progress_ai_chatbot_url" 
                                                                   placeholder="https://udify.app/chatbot/xxxxx">
                                                            <div class="form-text">Dify聊天窗口的嵌入URL（从Dify应用中获取）。</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 连接测试 -->
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-plug me-2"></i>连接测试</h6>
                                                    <div class="alert alert-light border">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>测试产品进度查询AI助手的连接状态</span>
                                                            <button type="button" class="btn btn-sm btn-outline-primary" id="testProgressAIBtn">
                                                                <i class="fas fa-play me-1"></i>测试连接
                                                            </button>
                                                        </div>
                                                        <div id="progressAITestResult" class="mt-2" style="display: none;">
                                                            <hr>
                                                            <div class="text-muted">测试结果将显示在这里...</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-primary" id="saveProgressAIBtn">
                                            <i class="fas fa-save me-1"></i>保存产品进度查询AI助手设置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS样式 -->
<style>
.settings-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.settings-header {
    color: #dc3545;
    border-bottom: 2px solid #dc3545;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.settings-section {
    padding: 20px 0;
}

.setting-description {
    color: #6c757d;
    font-size: 0.9em;
    margin-top: 5px;
}

.status-badge {
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 12px;
}

.status-badge.bg-success {
    background-color: #28a745 !important;
    color: white;
}

.status-badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

.status-badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link.active {
    color: #dc3545;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom: 2px solid #dc3545;
}

.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-primary {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-primary:hover {
    background-color: #c82333;
    border-color: #bd2130;
}
</style>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===============================
    // AI助手配置功能
    // ===============================
    
    // 获取设置表单元素
    const enableTrainingAI = document.getElementById('enableTrainingAI');
    const enableProgressAI = document.getElementById('enableProgressAI');
    const saveTrainingAIBtn = document.getElementById('saveTrainingAIBtn');
    const saveProgressAIBtn = document.getElementById('saveProgressAIBtn');
    const testTrainingAIBtn = document.getElementById('testTrainingAIBtn');
    const testProgressAIBtn = document.getElementById('testProgressAIBtn');
    
    // 工具函数：更新状态徽章
    function updateStatusBadge(elementId, type, text) {
        const badge = document.getElementById(elementId);
        if (badge) {
            badge.className = `status-badge bg-${type}`;
            badge.textContent = text;
        }
    }
    
    // 工具函数：显示成功提示
    function showSuccessToast(message) {
        // 创建简单的提示框
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
    
    // 工具函数：显示错误提示
    function showErrorAlert(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
    
    // 加载AI助手设置
    function loadAISettings() {
        fetch('/api/v2/system/ai-settings')
            .then(response => response.json())
            .then(data => {
                console.log('AI设置加载成功:', data);
                
                // 更新新员工培训AI助教设置
                if (data.training_ai) {
                    const trainingConfig = data.training_ai;
                    document.getElementById('enableTrainingAI').checked = trainingConfig.enabled || false;
                    document.getElementById('trainingAIServer').value = trainingConfig.server_url || 'http://10.16.10.103';
                    document.getElementById('trainingAIKey').value = trainingConfig.api_key || '';
                    document.getElementById('trainingAIModel').value = trainingConfig.app_id || '';
                    document.getElementById('trainingAIChatbotUrl').value = trainingConfig.chatbot_url || '';
                    
                    updateStatusBadge('trainingAIStatus', 
                        trainingConfig.enabled ? 'success' : 'secondary', 
                        trainingConfig.enabled ? '已启用' : '未启用');
                        
                    if (trainingConfig.enabled) {
                        document.getElementById('trainingAISettings').classList.add('show');
                    }
                }
                
                // 更新产品进度查询AI助手设置
                if (data.progress_ai) {
                    const progressConfig = data.progress_ai;
                    document.getElementById('enableProgressAI').checked = progressConfig.enabled || false;
                    document.getElementById('progressAIServer').value = progressConfig.server_url || 'http://10.16.10.103';
                    document.getElementById('progressAIKey').value = progressConfig.api_key || '';
                    document.getElementById('progressAIModel').value = progressConfig.app_id || '';
                    document.getElementById('progressAIChatbotUrl').value = progressConfig.chatbot_url || '';
                    
                    updateStatusBadge('progressAIStatus', 
                        progressConfig.enabled ? 'success' : 'secondary', 
                        progressConfig.enabled ? '已启用' : '未启用');
                        
                    if (progressConfig.enabled) {
                        document.getElementById('progressAISettings').classList.add('show');
                    }
                }
            })
            .catch(error => {
                console.error('加载AI设置失败:', error);
                showErrorAlert('加载AI设置失败: ' + error.message);
            });
    }
    
    // 保存新员工培训AI助教设置
    function saveTrainingAISettings() {
        const config = {
            training_ai: {
                enabled: document.getElementById('enableTrainingAI').checked,
                server_url: document.getElementById('trainingAIServer').value,
                api_key: document.getElementById('trainingAIKey').value,
                app_id: document.getElementById('trainingAIModel').value,
                chatbot_url: document.getElementById('trainingAIChatbotUrl').value
            }
        };
        
        fetch('/api/v2/system/ai-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessToast('新员工培训AI助教设置已保存！');
                updateStatusBadge('trainingAIStatus', 
                    config.training_ai.enabled ? 'success' : 'secondary', 
                    config.training_ai.enabled ? '已启用' : '未启用');
            } else {
                showErrorAlert(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存新员工培训AI助教设置失败:', error);
            showErrorAlert('保存失败: ' + error.message);
        });
    }
    
    // 保存产品进度查询AI助手设置
    function saveProgressAISettings() {
        const config = {
            progress_ai: {
                enabled: document.getElementById('enableProgressAI').checked,
                server_url: document.getElementById('progressAIServer').value,
                api_key: document.getElementById('progressAIKey').value,
                app_id: document.getElementById('progressAIModel').value,
                chatbot_url: document.getElementById('progressAIChatbotUrl').value
            }
        };
        
        fetch('/api/v2/system/ai-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessToast('产品进度查询AI助手设置已保存！');
                updateStatusBadge('progressAIStatus', 
                    config.progress_ai.enabled ? 'success' : 'secondary', 
                    config.progress_ai.enabled ? '已启用' : '未启用');
            } else {
                showErrorAlert(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存产品进度查询AI助手设置失败:', error);
            showErrorAlert('保存失败: ' + error.message);
        });
    }
    
    // 测试新员工培训AI助教连接
    function testTrainingAIConnection() {
        const resultDiv = document.getElementById('trainingAITestResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在测试连接...</div>';
        
        const testData = {
            type: 'training_ai',
            server_url: document.getElementById('trainingAIServer').value,
            api_key: document.getElementById('trainingAIKey').value,
            app_id: document.getElementById('trainingAIModel').value
        };
        
        if (!testData.server_url || !testData.api_key || !testData.app_id) {
            resultDiv.innerHTML = '<div class="text-danger"><i class="fas fa-times me-2"></i>请先填写完整的连接信息</div>';
            return;
        }
        
        fetch('/api/v2/system/dify-settings/test', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="text-success">
                        <i class="fas fa-check me-2"></i>${data.message}
                        <br><small>服务器: ${testData.server_url}</small>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-times me-2"></i>${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('测试连接失败:', error);
            resultDiv.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-times me-2"></i>测试失败: ${error.message}
                </div>
            `;
        });
    }
    
    // 测试产品进度查询AI助手连接
    function testProgressAIConnection() {
        const resultDiv = document.getElementById('progressAITestResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在测试连接...</div>';
        
        const testData = {
            type: 'progress_ai',
            server_url: document.getElementById('progressAIServer').value,
            api_key: document.getElementById('progressAIKey').value,
            app_id: document.getElementById('progressAIModel').value
        };
        
        if (!testData.server_url || !testData.api_key || !testData.app_id) {
            resultDiv.innerHTML = '<div class="text-danger"><i class="fas fa-times me-2"></i>请先填写完整的连接信息</div>';
            return;
        }
        
        fetch('/api/v2/system/dify-settings/test', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="text-success">
                        <i class="fas fa-check me-2"></i>${data.message}
                        <br><small>服务器: ${testData.server_url}</small>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-times me-2"></i>${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('测试连接失败:', error);
            resultDiv.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-times me-2"></i>测试失败: ${error.message}
                </div>
            `;
        });
    }
    
    // 绑定事件监听器
    if (enableTrainingAI) {
        enableTrainingAI.addEventListener('change', function() {
            updateStatusBadge('trainingAIStatus', 
                this.checked ? 'success' : 'secondary', 
                this.checked ? '已启用' : '未启用');
        });
    }
    
    if (enableProgressAI) {
        enableProgressAI.addEventListener('change', function() {
            updateStatusBadge('progressAIStatus', 
                this.checked ? 'success' : 'secondary', 
                this.checked ? '已启用' : '未启用');
        });
    }
    
    if (saveTrainingAIBtn) saveTrainingAIBtn.addEventListener('click', saveTrainingAISettings);
    if (saveProgressAIBtn) saveProgressAIBtn.addEventListener('click', saveProgressAISettings);
    if (testTrainingAIBtn) testTrainingAIBtn.addEventListener('click', testTrainingAIConnection);
    if (testProgressAIBtn) testProgressAIBtn.addEventListener('click', testProgressAIConnection);
    
    // 页面加载时加载设置
    loadAISettings();
});
</script>

{% block extra_js %}
<!-- 引入API客户端 -->
<script src="{{ url_for('static', filename='js/base/api-client-v2.js') }}"></script>

<!-- 引入Toast管理器 -->
<script src="{{ url_for('static', filename='js/base/toast-manager.js') }}"></script>

<!-- 引入系统设置JavaScript -->
<script src="{{ url_for('static', filename='js/system_settings.js') }}"></script>
{% endblock %}
{% endblock %} 