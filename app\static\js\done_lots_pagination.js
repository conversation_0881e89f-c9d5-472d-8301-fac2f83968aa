/**
 * 已排产表分页组件
 * 
 * 功能：
 * - 智能分页控制器
 * - 支持跳转到指定页面
 * - 动态页面大小调整
 * - 分页信息显示
 * - 快速导航功能
 * - 键盘导航支持
 * 
 * 重构原因：
 * - 提供更好的分页体验
 * - 支持大数据量分页
 * - 统一三种模式的分页逻辑
 * - 改善分页性能和响应速度
 */

class DoneLotsPagination {
    constructor(containerId, dataManager) {
        this.containerId = containerId;
        this.dataManager = dataManager;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            throw new Error(`找不到容器元素: ${containerId}`);
        }
        
        if (!this.dataManager) {
            throw new Error('需要提供 DoneLotsDataManager 实例');
        }
        
        // 分页配置
        this.config = {
            pageSizeOptions: [10, 25, 50, 100, 200],
            defaultPageSize: 50,
            maxVisiblePages: 7, // 最大显示页码数量
            showQuickJump: true, // 显示快速跳转
            showPageSizeSelector: true, // 显示页面大小选择器
            showTotalInfo: true, // 显示总数信息
            keyboardNavigation: true // 键盘导航
        };
        
        // 当前状态
        this.currentState = {
            page: 1,
            size: this.config.defaultPageSize,
            total: 0,
            total_pages: 0,
            loading: false
        };
        
        // 初始化组件
        this.init();
        
        console.log('📄 DoneLotsPagination 初始化完成');
    }
    
    /**
     * 初始化组件
     */
    init() {
        this.createPaginationUI();
        this.bindEvents();
        this.updateFromDataManager();
    }
    
    /**
     * 创建分页UI
     */
    createPaginationUI() {
        this.container.innerHTML = `
            <div class="pagination-container">
                <!-- 总数信息 -->
                <div class="pagination-info">
                    <div class="total-info" id="totalInfo">
                        <span class="info-label">📊 总计</span>
                        <span class="info-value" id="totalCount">0</span>
                        <span class="info-unit">条记录</span>
                    </div>
                    <div class="current-info" id="currentInfo">
                        第 <span id="currentPage">1</span> 页 / 共 <span id="totalPages">0</span> 页
                    </div>
                </div>
                
                <!-- 页面大小选择器 -->
                <div class="page-size-selector">
                    <label class="size-label">📋 每页显示</label>
                    <select id="pageSizeSelect" class="size-select">
                        ${this.config.pageSizeOptions.map(size => 
                            `<option value="${size}" ${size === this.config.defaultPageSize ? 'selected' : ''}>${size} 条</option>`
                        ).join('')}
                    </select>
                </div>
                
                <!-- 分页导航 -->
                <div class="pagination-nav">
                    <!-- 快速导航按钮 -->
                    <div class="nav-buttons">
                        <button class="nav-btn first-btn" id="firstBtn" title="首页 (Ctrl+Home)">
                            <span class="btn-icon">⏮️</span>
                            <span class="btn-text">首页</span>
                        </button>
                        <button class="nav-btn prev-btn" id="prevBtn" title="上一页 (←)">
                            <span class="btn-icon">◀️</span>
                            <span class="btn-text">上一页</span>
                        </button>
                    </div>
                    
                    <!-- 页码列表 -->
                    <div class="page-numbers" id="pageNumbers">
                        <!-- 动态生成页码按钮 -->
                    </div>
                    
                    <!-- 快速导航按钮 -->
                    <div class="nav-buttons">
                        <button class="nav-btn next-btn" id="nextBtn" title="下一页 (→)">
                            <span class="btn-text">下一页</span>
                            <span class="btn-icon">▶️</span>
                        </button>
                        <button class="nav-btn last-btn" id="lastBtn" title="末页 (Ctrl+End)">
                            <span class="btn-text">末页</span>
                            <span class="btn-icon">⏭️</span>
                        </button>
                    </div>
                </div>
                
                <!-- 快速跳转 -->
                <div class="quick-jump">
                    <label class="jump-label">🎯 跳转到</label>
                    <input type="number" 
                           id="jumpPageInput" 
                           class="jump-input" 
                           min="1" 
                           max="${this.currentState.total_pages || 1}"
                           placeholder="页码">
                    <button class="jump-btn" id="jumpBtn">跳转</button>
                </div>
                
                <!-- 加载指示器 -->
                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                    <div class="loading-spinner">⏳ 加载中...</div>
                </div>
            </div>
        `;
        
        this.addPaginationStyles();
    }
    
    /**
     * 添加样式
     */
    addPaginationStyles() {
        const existingStyle = document.getElementById('done-lots-pagination-styles');
        if (existingStyle) return;
        
        const style = document.createElement('style');
        style.id = 'done-lots-pagination-styles';
        style.textContent = `
            .pagination-container {
                position: relative;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: space-between;
                gap: 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .pagination-info {
                display: flex;
                flex-direction: column;
                gap: 5px;
                min-width: 200px;
            }
            
            .total-info {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                color: #495057;
            }
            
            .info-label {
                font-size: 14px;
            }
            
            .info-value {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 16px;
                font-weight: bold;
                box-shadow: 0 2px 4px rgba(0,123,255,0.3);
            }
            
            .info-unit {
                font-size: 14px;
                color: #6c757d;
            }
            
            .current-info {
                font-size: 13px;
                color: #6c757d;
                margin-left: 22px;
            }
            
            .page-size-selector {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .size-label {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
            }
            
            .size-select {
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 20px;
                background: white;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                outline: none;
            }
            
            .size-select:hover {
                border-color: #007bff;
            }
            
            .size-select:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
            }
            
            .pagination-nav {
                display: flex;
                align-items: center;
                gap: 15px;
                flex-wrap: wrap;
            }
            
            .nav-buttons {
                display: flex;
                gap: 8px;
            }
            
            .nav-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 10px 16px;
                border: 2px solid #e9ecef;
                background: white;
                border-radius: 25px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
                font-weight: 500;
                color: #495057;
                outline: none;
            }
            
            .nav-btn:hover:not(:disabled) {
                background: #007bff;
                color: white;
                border-color: #007bff;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,123,255,0.3);
            }
            
            .nav-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            
            .btn-icon {
                font-size: 12px;
            }
            
            .btn-text {
                font-size: 13px;
            }
            
            .page-numbers {
                display: flex;
                gap: 5px;
                align-items: center;
                flex-wrap: wrap;
            }
            
            .page-btn {
                min-width: 40px;
                height: 40px;
                border: 2px solid #e9ecef;
                background: white;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
                font-weight: 600;
                color: #495057;
                display: flex;
                align-items: center;
                justify-content: center;
                outline: none;
            }
            
            .page-btn:hover:not(.current):not(:disabled) {
                background: #f8f9fa;
                border-color: #007bff;
                color: #007bff;
                transform: scale(1.1);
            }
            
            .page-btn.current {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-color: #007bff;
                box-shadow: 0 4px 8px rgba(0,123,255,0.3);
                transform: scale(1.1);
            }
            
            .page-ellipsis {
                padding: 10px 5px;
                color: #6c757d;
                font-weight: bold;
                cursor: default;
            }
            
            .quick-jump {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .jump-label {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
            }
            
            .jump-input {
                width: 80px;
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 20px;
                text-align: center;
                font-size: 14px;
                outline: none;
                transition: all 0.3s ease;
            }
            
            .jump-input:hover {
                border-color: #007bff;
            }
            
            .jump-input:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
            }
            
            .jump-btn {
                padding: 8px 16px;
                background: linear-gradient(135deg, #28a745, #1e7e34);
                color: white;
                border: none;
                border-radius: 20px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.3s ease;
                outline: none;
            }
            
            .jump-btn:hover {
                background: linear-gradient(135deg, #218838, #1c7430);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(40,167,69,0.3);
            }
            
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }
            
            .loading-spinner {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(0,123,255,0.3);
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .pagination-container {
                    flex-direction: column;
                    gap: 15px;
                    padding: 15px;
                }
                
                .pagination-info {
                    text-align: center;
                    min-width: auto;
                }
                
                .pagination-nav {
                    flex-direction: column;
                    gap: 10px;
                }
                
                .nav-buttons {
                    justify-content: center;
                }
                
                .page-numbers {
                    justify-content: center;
                }
                
                .quick-jump {
                    justify-content: center;
                    flex-wrap: wrap;
                }
                
                .btn-text {
                    display: none;
                }
                
                .nav-btn {
                    min-width: 45px;
                    justify-content: center;
                }
            }
            
            @media (max-width: 480px) {
                .page-btn {
                    min-width: 35px;
                    height: 35px;
                    font-size: 12px;
                }
                
                .nav-btn {
                    padding: 8px 12px;
                    font-size: 12px;
                }
                
                .config.maxVisiblePages = 5; /* 移动端显示更少页码 */
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 页面大小选择器
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.changePageSize(parseInt(e.target.value));
            });
        }
        
        // 导航按钮
        const firstBtn = document.getElementById('firstBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const lastBtn = document.getElementById('lastBtn');
        
        if (firstBtn) firstBtn.addEventListener('click', () => this.goToPage(1));
        if (prevBtn) prevBtn.addEventListener('click', () => this.goToPrevPage());
        if (nextBtn) nextBtn.addEventListener('click', () => this.goToNextPage());
        if (lastBtn) lastBtn.addEventListener('click', () => this.goToPage(this.currentState.total_pages));
        
        // 快速跳转
        const jumpInput = document.getElementById('jumpPageInput');
        const jumpBtn = document.getElementById('jumpBtn');
        
        if (jumpInput) {
            jumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.quickJump();
                }
            });
        }
        
        if (jumpBtn) {
            jumpBtn.addEventListener('click', () => this.quickJump());
        }
        
        // 键盘导航
        if (this.config.keyboardNavigation) {
            document.addEventListener('keydown', (e) => this.handleKeyNavigation(e));
        }
        
        // 监听数据管理器事件
        this.dataManager.on('dataLoaded', (data) => {
            this.updateState(data.pagination);
            this.hideLoading();
        });
        
        this.dataManager.on('loadingChanged', (data) => {
            if (data.loading) {
                this.showLoading();
            } else {
                this.hideLoading();
            }
        });
        
        this.dataManager.on('paginationChanged', (data) => {
            this.updateState(data.pagination);
        });
    }
    
    /**
     * 更新状态
     */
    updateState(pagination) {
        if (!pagination) return;
        
        const oldState = { ...this.currentState };
        this.currentState = { ...this.currentState, ...pagination };
        
        // 更新UI
        this.updatePaginationInfo();
        this.updatePageNumbers();
        this.updateNavigationButtons();
        this.updateJumpInput();
        
        console.log('📄 分页状态更新:', this.currentState);
        
        // 触发状态变化事件
        this.emit('stateChanged', { oldState, newState: this.currentState });
    }
    
    /**
     * 从数据管理器更新状态
     */
    updateFromDataManager() {
        const currentData = this.dataManager.getCurrentData();
        if (currentData.pagination) {
            this.updateState(currentData.pagination);
        }
    }
    
    /**
     * 更新分页信息显示
     */
    updatePaginationInfo() {
        const totalCount = document.getElementById('totalCount');
        const currentPage = document.getElementById('currentPage');
        const totalPages = document.getElementById('totalPages');
        
        if (totalCount) totalCount.textContent = this.currentState.total.toLocaleString();
        if (currentPage) currentPage.textContent = this.currentState.page.toString();
        if (totalPages) totalPages.textContent = this.currentState.total_pages.toString();
    }
    
    /**
     * 更新页码按钮
     */
    updatePageNumbers() {
        const container = document.getElementById('pageNumbers');
        if (!container) return;
        
        const { page, total_pages } = this.currentState;
        const maxVisible = this.config.maxVisiblePages;
        
        let html = '';
        
        if (total_pages <= maxVisible) {
            // 总页数较少，显示所有页码
            for (let i = 1; i <= total_pages; i++) {
                html += this.createPageButton(i, i === page);
            }
        } else {
            // 总页数较多，使用智能分页显示
            const startPage = Math.max(1, page - Math.floor(maxVisible / 2));
            const endPage = Math.min(total_pages, startPage + maxVisible - 1);
            
            // 显示首页
            if (startPage > 1) {
                html += this.createPageButton(1, false);
                if (startPage > 2) {
                    html += '<span class="page-ellipsis">...</span>';
                }
            }
            
            // 显示中间页码
            for (let i = startPage; i <= endPage; i++) {
                html += this.createPageButton(i, i === page);
            }
            
            // 显示末页
            if (endPage < total_pages) {
                if (endPage < total_pages - 1) {
                    html += '<span class="page-ellipsis">...</span>';
                }
                html += this.createPageButton(total_pages, false);
            }
        }
        
        container.innerHTML = html;
        
        // 绑定页码按钮事件
        container.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetPage = parseInt(e.target.dataset.page);
                if (targetPage && targetPage !== page) {
                    this.goToPage(targetPage);
                }
            });
        });
    }
    
    /**
     * 创建页码按钮HTML
     */
    createPageButton(pageNum, isCurrent) {
        return `
            <button class="page-btn ${isCurrent ? 'current' : ''}" 
                    data-page="${pageNum}"
                    title="第 ${pageNum} 页">
                ${pageNum}
            </button>
        `;
    }
    
    /**
     * 更新导航按钮状态
     */
    updateNavigationButtons() {
        const { page, total_pages } = this.currentState;
        
        const firstBtn = document.getElementById('firstBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const lastBtn = document.getElementById('lastBtn');
        
        if (firstBtn) firstBtn.disabled = page <= 1;
        if (prevBtn) prevBtn.disabled = page <= 1;
        if (nextBtn) nextBtn.disabled = page >= total_pages;
        if (lastBtn) lastBtn.disabled = page >= total_pages;
    }
    
    /**
     * 更新跳转输入框
     */
    updateJumpInput() {
        const jumpInput = document.getElementById('jumpPageInput');
        if (jumpInput) {
            jumpInput.max = this.currentState.total_pages.toString();
            jumpInput.placeholder = `1-${this.currentState.total_pages}`;
        }
    }
    
    /**
     * 跳转到指定页面
     */
    goToPage(pageNum) {
        const targetPage = Math.max(1, Math.min(pageNum, this.currentState.total_pages));
        
        if (targetPage !== this.currentState.page) {
            console.log(`📄 跳转到第 ${targetPage} 页`);
            this.showLoading();
            this.dataManager.goToPage(targetPage);
        }
    }
    
    /**
     * 上一页
     */
    goToPrevPage() {
        if (this.currentState.page > 1) {
            this.goToPage(this.currentState.page - 1);
        }
    }
    
    /**
     * 下一页
     */
    goToNextPage() {
        if (this.currentState.page < this.currentState.total_pages) {
            this.goToPage(this.currentState.page + 1);
        }
    }
    
    /**
     * 改变页面大小
     */
    changePageSize(newSize) {
        if (newSize !== this.currentState.size) {
            console.log(`📄 改变页面大小: ${this.currentState.size} → ${newSize}`);
            this.showLoading();
            this.dataManager.changePageSize(newSize);
        }
    }
    
    /**
     * 快速跳转
     */
    quickJump() {
        const jumpInput = document.getElementById('jumpPageInput');
        if (!jumpInput) return;
        
        const targetPage = parseInt(jumpInput.value);
        if (isNaN(targetPage) || targetPage < 1 || targetPage > this.currentState.total_pages) {
            alert(`请输入有效的页码 (1-${this.currentState.total_pages})`);
            jumpInput.focus();
            return;
        }
        
        jumpInput.value = ''; // 清空输入框
        this.goToPage(targetPage);
    }
    
    /**
     * 处理键盘导航
     */
    handleKeyNavigation(e) {
        // 避免在输入框中触发
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        const { ctrlKey, key } = e;
        
        switch (key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.goToPrevPage();
                break;
            
            case 'ArrowRight':
                e.preventDefault();
                this.goToNextPage();
                break;
            
            case 'Home':
                if (ctrlKey) {
                    e.preventDefault();
                    this.goToPage(1);
                }
                break;
            
            case 'End':
                if (ctrlKey) {
                    e.preventDefault();
                    this.goToPage(this.currentState.total_pages);
                }
                break;
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    /**
     * 获取当前状态
     */
    getCurrentState() {
        return { ...this.currentState };
    }
    
    /**
     * 重置分页状态
     */
    reset() {
        this.currentState = {
            page: 1,
            size: this.config.defaultPageSize,
            total: 0,
            total_pages: 0,
            loading: false
        };
        
        this.updatePaginationInfo();
        this.updatePageNumbers();
        this.updateNavigationButtons();
        this.updateJumpInput();
        
        console.log('📄 分页状态已重置');
    }
    
    /**
     * 事件处理
     */
    on(eventName, callback) {
        if (!this.eventListeners) {
            this.eventListeners = {};
        }
        
        if (!this.eventListeners[eventName]) {
            this.eventListeners[eventName] = [];
        }
        
        this.eventListeners[eventName].push(callback);
        return this;
    }
    
    off(eventName, callback) {
        if (this.eventListeners && this.eventListeners[eventName]) {
            const index = this.eventListeners[eventName].indexOf(callback);
            if (index > -1) {
                this.eventListeners[eventName].splice(index, 1);
            }
        }
        return this;
    }
    
    emit(eventName, data) {
        if (this.eventListeners && this.eventListeners[eventName]) {
            this.eventListeners[eventName].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`分页事件回调执行失败 [${eventName}]:`, error);
                }
            });
        }
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        
        if (this.config.keyboardNavigation) {
            document.removeEventListener('keydown', this.handleKeyNavigation);
        }
        
        this.eventListeners = {};
        console.log('🗑️ DoneLotsPagination 已销毁');
    }
}

// 导出到全局
window.DoneLotsPagination = DoneLotsPagination;

console.log('📦 DoneLotsPagination 模块已加载');