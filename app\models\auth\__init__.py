"""
认证相关模型 - 模块化结构

包含用户、权限、操作日志等认证相关的数据模型
"""

# 从原始models.py导入认证相关模型
try:
    from app.models import (
        User,
        UserPermission, 
        UserActionLog,
        MenuSetting  # 修正名称
    )
    
    # 导出认证相关模型
    __all__ = [
        'User',
        'UserPermission',
        'UserActionLog', 
        'MenuSetting'  # 修正名称
    ]
    
except ImportError as e:
    # 如果导入失败，记录错误
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"认证模型导入失败: {e}")
    
    __all__ = [] 