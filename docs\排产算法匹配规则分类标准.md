# AEC-FT 排产算法匹配规则分类标准

## 📋 文档信息

**版本**: v3.0-enhanced  
**创建时间**: 2025-01-15  
**适用范围**: AEC-FT车规芯片终测智能调度平台  
**更新说明**: 新增跨工序匹配分类，优化小改机匹配逻辑

---

## 🎯 核心原则

### 1. 匹配优先级原则
- **严格匹配优先**: DEVICE+STAGE完全一致的设备优先考虑
- **配置兼容性**: 按照HANDLER_CONFIG → KIT_PN → TESTER → HB_PN/TB_PN的层次匹配
- **改机时间最小化**: 在满足技术要求的前提下，选择改机时间最短的方案
- **跨工序灵活性**: 当严格匹配无法满足时，允许跨工序匹配提供生产灵活性

### 2. 集合化匹配（EXISTS语义）
- **任一满足原则**: 设备匹配批次需求时，只要存在任一组合(配方×测试规范)满足条件即可
- **多配置支持**: 一个DEVICE+STAGE可能对应多种配置选择，算法支持灵活匹配
- **容错处理**: 支持不同命名规范和版本兼容性

---

## 🔧 七类匹配规则详解

### 1️⃣ 同产品续排 (0分钟)
**匹配条件**:
- ✅ DEVICE完全一致
- ✅ STAGE完全一致
- ✅ 设备状态为Run

**业务含义**: 同一产品在同一工序的连续生产，无需任何改机操作

**评分**: 95分  
**改机时间**: 0分钟  
**适用场景**: 批量生产中的连续排产

---

### 2️⃣ 同配置匹配 (0分钟)
**匹配条件**:
- ✅ DEVICE完全一致
- ✅ STAGE完全一致  
- ✅ HANDLER_CONFIG一致
- ✅ KIT_PN一致
- ✅ TESTER一致
- ✅ HB_PN基础PN一致或设备为空
- ✅ TB_PN基础PN一致或设备为空

**业务含义**: 设备当前配置与批次需求完全匹配，无需改机

**评分**: 100分  
**改机时间**: 0分钟  
**适用场景**: 理想的完全匹配情况

---

### 3️⃣ 小改机匹配 (45分钟)
**匹配条件**:
- ✅ DEVICE完全一致
- ✅ STAGE完全一致
- ✅ HANDLER_CONFIG一致
- ✅ KIT_PN一致
- ✅ TESTER一致
- ❌ HB_PN或TB_PN基础PN不一致

**业务含义**: 仅需更换HB/TB组件，保持主要配置不变

**评分**: 80分  
**改机时间**: 45分钟  
**适用场景**: 同一产品不同测试条件或封装变体

---

### 4️⃣ 换测试机小改机匹配 (55分钟)
**匹配条件**:
- ✅ DEVICE完全一致
- ✅ STAGE完全一致
- ✅ HANDLER_CONFIG一致
- ✅ KIT_PN一致
- ❌ TESTER不一致

**业务含义**: 需要更换测试机和相关HB/TB组件

**评分**: 75分  
**改机时间**: 55分钟 (小改机45分钟 + 换测试机10分钟)  
**适用场景**: 测试设备资源调配或测试条件变更

---

### 5️⃣ 大改机匹配 (120分钟)
**匹配条件**:
- ✅ DEVICE完全一致
- ✅ STAGE完全一致
- ✅ HANDLER_CONFIG一致
- ❌ KIT_PN不一致

**业务含义**: 需要更换KIT、TESTER和HB/TB组件，配置变更较大

**评分**: 60分  
**改机时间**: 120分钟  
**适用场景**: 不同封装类型或测试方案的产品

---

### 6️⃣ 跨工序A类匹配 (60分钟)
**匹配条件**:
- ❌ DEVICE不一致 或 STAGE不一致
- ✅ HANDLER_CONFIG一致
- ✅ KIT_PN一致

**业务含义**: 跨工序生产，但核心配置兼容，改机成本相对较低

**评分**: 70分  
**改机时间**: 60分钟  
**适用场景**: 工序间设备资源调配，优先级高于B类跨工序

---

### 7️⃣ 跨工序B类匹配 (90分钟)
**匹配条件**:
- ❌ DEVICE不一致 或 STAGE不一致
- ✅ HANDLER_CONFIG一致
- ❌ KIT_PN不一致

**业务含义**: 跨工序生产，仅HANDLER_CONFIG兼容，改机成本较高

**评分**: 50分  
**改机时间**: 90分钟  
**适用场景**: 设备资源紧张时的备选方案，优先级低于A类跨工序

---

### ❌ 无法上机
**匹配条件**:
- ❌ 不满足以上任一条件

**业务含义**: 设备无法处理该批次，需要人工干预或等待合适设备

**评分**: 0分  
**改机时间**: 9999分钟 (表示无法改机)  
**适用场景**: 技术不兼容或设备能力限制

---

## 🔍 匹配逻辑执行顺序

### 1. 候选设备筛选
```
严格池: DEVICE+STAGE完全一致 (优先级最高)
  ↓
空闲池: DEVICE/STAGE为空的设备 (次优先级)
  ↓  
跨工序池: DEVICE或STAGE不一致但配置兼容 (备选)
  ├── A类: HANDLER_CONFIG+KIT_PN一致
  └── B类: 仅HANDLER_CONFIG一致
```

### 2. 匹配规则判定
```
同产品续排 (DEVICE+STAGE一致) → 评分95
  ↓
同配置匹配 (全配置一致) → 评分100
  ↓
小改机匹配 (DEVICE+STAGE一致，HB/TB不同) → 评分80
  ↓
换测试机小改机匹配 (DEVICE+STAGE一致，TESTER不同) → 评分75
  ↓
大改机匹配 (DEVICE+STAGE一致，KIT不同) → 评分60
  ↓
跨工序A类匹配 (跨工序，HC+KIT一致) → 评分70
  ↓
跨工序B类匹配 (跨工序，仅HC一致) → 评分50
  ↓
无法上机 → 评分0
```

### 3. 综合评分排序
```
最终评分 = 技术匹配评分(25%) + 负载均衡评分(20%) + 交期紧迫评分(25%) + 产值效率评分(20%) + 业务优先级评分(10%)
```

---

## 📊 匹配类型统计示例

基于最新排产结果的分布情况：

| 匹配类型 | 批次数量 | 改机时间 | 占比 |
|---------|---------|---------|------|
| 跨工序B类匹配 | 84 | 90分钟 | 29.3% |
| 换测试机小改机匹配 | 84 | 55分钟 | 29.3% |
| 跨工序A类匹配 | 70 | 60分钟 | 24.4% |
| 同产品续排 | 49 | 0分钟 | 17.1% |

**总计**: 287个成功排产批次，排产成功率90.8%

---

## 🎯 业务价值

### 1. 生产灵活性
- **跨工序匹配**: 154个批次通过跨工序匹配获得生产机会
- **资源利用率**: 提高设备利用率，减少生产瓶颈

### 2. 改机成本控制
- **分级改机时间**: 从0分钟到120分钟的精确分级
- **成本可预测**: 每种匹配类型的改机成本明确

### 3. 算法透明度
- **可追溯性**: 每个批次的匹配依据清晰可查
- **决策支持**: 为生产调度提供科学依据

---

## 🔧 技术实现要点

### 1. 集合化匹配实现
```python
def _exists_same_config_match(self, req_handler_configs, req_kit_pns, req_testers, 
                             req_hb_pns, req_tb_pns, eqp_handler_config, eqp_kit, 
                             eqp_tester, eqp_hb, eqp_tb, eqp_stage, req_stage_u,
                             eqp_device, req_device):
    """检查是否存在同配置匹配（集合化匹配：任一满足）"""
    # 同配置匹配必须DEVICE和STAGE都一致
    if eqp_device != req_device or eqp_stage != req_stage_u:
        return False
    # ... 其他匹配逻辑
```

### 2. 跨工序匹配判定
```python
def _exists_cross_stage_match(self, req_handler_configs, req_kit_pns,
                             eqp_handler_config, eqp_kit, eqp_device, req_device,
                             eqp_stage, req_stage):
    """检查是否存在跨工序匹配（集合化匹配：任一满足）"""
    # 跨工序匹配：DEVICE或STAGE不一致
    if eqp_device == req_device and eqp_stage.upper() == req_stage.upper():
        return False  # 不是跨工序
    # ... 配置兼容性检查
```

---

## 📝 更新日志

### v3.0-enhanced (2025-01-15)
- ✅ 新增跨工序A类和B类匹配规则
- ✅ 修复小改机匹配DEVICE+STAGE检查逻辑
- ✅ 实现集合化匹配（EXISTS语义）
- ✅ 完善数据持久化，支持所有排产算法字段
- ✅ 优化候选设备筛选逻辑，支持三层候选池

### v2.x (历史版本)
- 六类匹配规则基础实现
- 基础的设备匹配逻辑
- 简单的改机时间计算

---

**文档维护**: 算法团队  
**审核**: 生产调度部门  
**生效日期**: 2025-01-15
