#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络地址检测助手
智能检测本机的所有网络接口和IP地址
"""

import socket
import ipaddress
import logging
from typing import List, Dict, Optional, Tuple
import platform

logger = logging.getLogger(__name__)

class NetworkHelper:
    """网络地址检测助手"""
    
    @staticmethod
    def get_all_local_ips() -> List[Dict[str, str]]:
        """
        获取本机所有网络接口的IP地址
        
        Returns:
            List[Dict]: 包含接口信息的列表
            [
                {
                    'ip': '*************',
                    'type': 'private',
                    'interface': 'eth0',
                    'description': '局域网地址'
                },
                ...
            ]
        """
        interfaces = []
        
        try:
            # 方法1: 使用socket获取连接到外网的IP
            try:
                # 连接到一个外部地址来获取本机IP（不会实际发送数据）
                with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                    s.connect(("*******", 80))
                    primary_ip = s.getsockname()[0]
                    
                    ip_info = NetworkHelper._analyze_ip(primary_ip)
                    if ip_info:
                        interfaces.append({
                            'ip': primary_ip,
                            'type': ip_info['type'],
                            'interface': 'primary',
                            'description': ip_info['description'],
                            'is_primary': True
                        })
            except Exception as e:
                logger.debug(f"无法获取主要IP地址: {e}")
            
            # 方法2: 使用socket.getaddrinfo获取主机名对应的所有IP
            try:
                hostname = socket.gethostname()
                addr_infos = socket.getaddrinfo(hostname, None)
                
                seen_ips = set()
                for addr_info in addr_infos:
                    ip = addr_info[4][0]
                    if ip not in seen_ips and not ip.startswith('127.'):
                        seen_ips.add(ip)
                        ip_info = NetworkHelper._analyze_ip(ip)
                        if ip_info:
                            # 检查是否已经添加过这个IP
                            existing = any(iface['ip'] == ip for iface in interfaces)
                            if not existing:
                                interfaces.append({
                                    'ip': ip,
                                    'type': ip_info['type'],
                                    'interface': 'hostname',
                                    'description': ip_info['description'],
                                    'is_primary': False
                                })
            except Exception as e:
                logger.debug(f"无法通过主机名获取IP地址: {e}")
            
            # 方法3: 尝试使用netifaces库（如果可用）
            try:
                import netifaces
                for interface in netifaces.interfaces():
                    try:
                        addresses = netifaces.ifaddresses(interface)
                        if netifaces.AF_INET in addresses:
                            for addr in addresses[netifaces.AF_INET]:
                                ip = addr['addr']
                                if not ip.startswith('127.'):
                                    ip_info = NetworkHelper._analyze_ip(ip)
                                    if ip_info:
                                        # 检查是否已经添加过这个IP
                                        existing = any(iface['ip'] == ip for iface in interfaces)
                                        if not existing:
                                            interfaces.append({
                                                'ip': ip,
                                                'type': ip_info['type'],
                                                'interface': interface,
                                                'description': ip_info['description'],
                                                'is_primary': False
                                            })
                    except Exception:
                        continue
            except ImportError:
                logger.debug("netifaces库不可用，跳过接口枚举")
            
        except Exception as e:
            logger.error(f"获取网络接口失败: {e}")
        
        # 如果没有找到任何IP，尝试最基本的方法
        if not interfaces:
            try:
                basic_ip = socket.gethostbyname(socket.gethostname())
                if basic_ip and not basic_ip.startswith('127.'):
                    ip_info = NetworkHelper._analyze_ip(basic_ip)
                    if ip_info:
                        interfaces.append({
                            'ip': basic_ip,
                            'type': ip_info['type'],
                            'interface': 'fallback',
                            'description': ip_info['description'],
                            'is_primary': True
                        })
            except Exception as e:
                logger.debug(f"基本IP获取失败: {e}")
        
        return interfaces
    
    @staticmethod
    def _analyze_ip(ip_str: str) -> Optional[Dict[str, str]]:
        """
        分析IP地址类型
        
        Args:
            ip_str: IP地址字符串
            
        Returns:
            Dict: IP地址信息，如果无效则返回None
        """
        try:
            ip = ipaddress.ip_address(ip_str)
            
            if ip.is_loopback:
                return None  # 跳过回环地址
            
            if ip.is_private:
                # 私有地址（局域网）
                if ip_str.startswith('192.168.'):
                    description = f"局域网地址 (192.168.x.x)"
                elif ip_str.startswith('10.'):
                    description = f"局域网地址 (10.x.x.x)"
                elif ip_str.startswith('172.'):
                    # ********** 到 **************
                    second_octet = int(ip_str.split('.')[1])
                    if 16 <= second_octet <= 31:
                        description = f"局域网地址 (172.{second_octet}.x.x)"
                    else:
                        description = f"私有地址 ({ip_str})"
                else:
                    description = f"私有地址 ({ip_str})"
                    
                return {
                    'type': 'private',
                    'description': description
                }
            
            elif ip.is_global:
                # 公网地址
                return {
                    'type': 'public',
                    'description': f"公网地址 ({ip_str})"
                }
            
            else:
                # 其他类型（链路本地等）
                return {
                    'type': 'other',
                    'description': f"其他地址 ({ip_str})"
                }
                
        except ValueError:
            # 无效的IP地址
            return None
    
    @staticmethod
    def get_primary_lan_ip() -> Optional[str]:
        """
        获取主要的局域网IP地址
        
        Returns:
            str: 主要的局域网IP地址，如果没有则返回None
        """
        interfaces = NetworkHelper.get_all_local_ips()
        
        # 优先返回标记为primary的私有地址
        for interface in interfaces:
            if interface.get('is_primary') and interface['type'] == 'private':
                return interface['ip']
        
        # 如果没有primary，返回第一个私有地址
        for interface in interfaces:
            if interface['type'] == 'private':
                return interface['ip']
        
        return None
    
    @staticmethod
    def get_all_access_urls(host: str, port: int) -> List[Dict[str, str]]:
        """
        获取所有可能的访问URL
        
        Args:
            host: 绑定的主机地址
            port: 端口号
            
        Returns:
            List[Dict]: 访问URL列表
        """
        urls = []
        
        if host == '0.0.0.0':
            # 如果绑定到0.0.0.0，获取所有可能的访问地址
            
            # 添加localhost
            urls.append({
                'url': f"http://localhost:{port}",
                'type': 'local',
                'description': '本机访问'
            })
            
            # 添加127.0.0.1
            urls.append({
                'url': f"http://127.0.0.1:{port}",
                'type': 'local',
                'description': '本机访问'
            })
            
            # 添加所有网络接口IP
            interfaces = NetworkHelper.get_all_local_ips()
            for interface in interfaces:
                ip = interface['ip']
                urls.append({
                    'url': f"http://{ip}:{port}",
                    'type': interface['type'],
                    'description': f"{interface['description']} ({interface['interface']})"
                })
        
        else:
            # 如果绑定到特定地址
            urls.append({
                'url': f"http://{host}:{port}",
                'type': 'specific',
                'description': f'绑定地址访问'
            })
        
        return urls
    
    @staticmethod
    def print_network_info(host: str, port: int) -> None:
        """
        打印网络访问信息
        
        Args:
            host: 绑定的主机地址  
            port: 端口号
        """
        print("✅ 系统启动成功！")
        print("📡 网络访问信息:")
        print("-" * 50)
        
        urls = NetworkHelper.get_all_access_urls(host, port)
        
        # 按类型分组显示
        local_urls = [u for u in urls if u['type'] == 'local']
        private_urls = [u for u in urls if u['type'] == 'private']
        public_urls = [u for u in urls if u['type'] == 'public']
        other_urls = [u for u in urls if u['type'] not in ['local', 'private', 'public']]
        
        if local_urls:
            print("🏠 本机访问:")
            for url_info in local_urls:
                print(f"   {url_info['url']}")
        
        if private_urls:
            print("🌐 局域网访问:")
            for url_info in private_urls:
                print(f"   {url_info['url']} - {url_info['description']}")
        
        if public_urls:
            print("🌍 公网访问:")
            for url_info in public_urls:
                print(f"   {url_info['url']} - {url_info['description']}")
        
        if other_urls:
            print("🔗 其他地址:")
            for url_info in other_urls:
                print(f"   {url_info['url']} - {url_info['description']}")
        
        print("-" * 50)
        print("👤 默认账户: admin / admin")
        print("📱 支持浏览器: Chrome, Firefox, Edge")
        print("⏹️  按 Ctrl+C 停止服务")
        print("=" * 50)

# 便捷函数
def get_lan_ip() -> Optional[str]:
    """获取局域网IP地址的便捷函数"""
    return NetworkHelper.get_primary_lan_ip()

def print_access_info(host: str, port: int) -> None:
    """打印访问信息的便捷函数"""
    NetworkHelper.print_network_info(host, port) 