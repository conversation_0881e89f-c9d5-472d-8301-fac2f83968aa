# Bootstrap 主题自定义说明

## 概述
APS系统支持多种方式自定义Bootstrap默认样式，以实现符合企业品牌的红色主题。

## 方法对比

### 1. CSS覆盖（当前使用）⭐
**文件**: `app/static/css/custom/theme.css`
- ✅ **优点**: 简单直接，无需编译工具
- ✅ **优点**: 即时生效，便于调试
- ✅ **优点**: 使用CSS变量，易于维护
- ❌ **缺点**: 需要了解Bootstrap的CSS选择器优先级

### 2. SCSS变量重定义（推荐用于大型项目）
**文件**: `app/static/css/custom/bootstrap-custom.scss`
- ✅ **优点**: 从源头修改，样式一致性最好
- ✅ **优点**: 支持Bootstrap所有变量
- ✅ **优点**: 生成的CSS文件更小
- ❌ **缺点**: 需要Node.js和Sass编译器

### 3. CDN替换
- ✅ **优点**: 无需本地文件
- ❌ **缺点**: 依赖网络，自定义程度有限

## 当前实现

### 主题色定义
```css
:root {
    --aps-primary: #b72424;        /* 主要色 */
    --aps-primary-dark: #a01e1e;   /* 深色变体 */
    --aps-primary-light: #d73027;  /* 浅色变体 */
    --aps-primary-lighter: #f8e6e6; /* 极浅背景色 */
}
```

### 覆盖的组件
- 分页组件 (pagination)
- 按钮 (btn-primary)
- 链接 (a)
- 导航栏 (navbar)
- 卡片头部 (card-header)

## 使用SCSS方法（可选）

### 安装依赖
```powershell
npm install
```

### 编译样式
```powershell
# 一次性编译
npm run build-css

# 监听文件变化
npm run watch-css
```

### 在模板中引用
```html
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/bootstrap-custom.css') }}">
```

## 扩展主题

### 添加新组件样式
在 `theme.css` 中添加：
```css
/* 自定义组件样式 */
.my-component {
    background-color: var(--aps-primary);
    color: white;
}
```

### 支持暗色模式
```css
@media (prefers-color-scheme: dark) {
    :root {
        --aps-primary: #ff6b6b;
    }
}
```

## 最佳实践

1. **使用CSS变量**: 便于全局修改和维护
2. **保持一致性**: 所有相关组件使用相同的色彩变量
3. **测试兼容性**: 确保在不同浏览器中效果一致
4. **性能考虑**: 避免过度使用 `!important`
5. **文档记录**: 记录自定义的组件和变量

## 故障排除

### 样式不生效
1. 检查CSS文件路径是否正确
2. 确认浏览器缓存已清理
3. 检查CSS选择器优先级

### 编译失败
1. 确认Node.js版本 >= 14
2. 检查SCSS语法错误
3. 验证Bootstrap源文件路径

## 相关文件
- `app/static/css/custom/theme.css` - 主题样式文件
- `app/static/css/custom/bootstrap-custom.scss` - SCSS源文件
- `package.json` - 前端依赖配置
- `app/templates/resources/base_resource.html` - 使用示例 