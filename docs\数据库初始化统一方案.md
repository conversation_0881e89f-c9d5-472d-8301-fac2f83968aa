# APS 数据库初始化统一方案

## 📋 概述

本文档详细说明APS项目的数据库初始化策略，统一不同类型数据的处理方式，解决当前两套并行初始化系统的问题。

## 🗂️ 数据分类体系

### 1. 表结构数据 - **必须初始化**
> 📊 数据库表的DDL定义，系统运行的基础

#### 业务表（38个）
```sql
-- 核心业务表
ct, wip_lot, ET_WAIT_LOT, ET_UPH_EQP, eqp_status, et_ft_test_spec, 
et_recipe_file, TCC_INV, LotPriorityDone, order_data, cp_order_data,
ft_order_summary, cp_order_summary, products, production_orders, 
production_schedules, customer_orders, order_items, resources, 
test_specs, maintenance_records, resource_usage_logs, wip_records, 
lot_type_classification_rules

-- 配置管理表
devicepriorityconfig, lotpriorityconfig, product_priority_config, 
stage_mapping_config
```

#### 系统表（12个）
```sql
-- 用户权限系统
users, user_permissions, user_action_logs, menu_permissions

-- 系统配置
system_settings, ai_settings, email_configs, email_attachments, 
excel_mappings, user_filter_presets, settings

-- 调度系统
scheduling_tasks, scheduling_config, scheduler_jobs, scheduler_job_logs, 
scheduler_config, migration_log

-- 数据源管理
database_configs, database_mappings, dify_configs, database_info
```

**初始化策略**: 
- ✅ **强制创建** - 系统启动前必须存在
- ✅ **版本控制** - 支持表结构升级迁移
- ✅ **完整性检查** - 验证表结构正确性

---

### 2. 系统配置数据 - **必须初始化**
> ⚙️ 系统正常运行所需的基础配置

#### 用户权限数据
```python
# 默认管理员账户
admin_user = {
    'username': 'admin',
    'password': 'admin123',  # 首次登录后强制修改
    'role': 'admin',
    'email': '<EMAIL>'
}

# 基础菜单权限（17个模块）
menu_permissions = [
    '系统管理', '用户管理', '资源管理', '生产预览', 
    '优先级设置', '智能排产', '测试订单', '在制品跟踪'
]
```

#### 系统设置
```python
system_settings = {
    'system_version': '2.0',
    'database_type': 'mysql',
    'global_scheduler_enabled': False,
    'mysql_migration_completed': True,
    'resource_management_fixed': True,
    'priority_tables_migrated': True
}
```

#### AI助手配置
```json
{
    "database": {
        "enabled": true,
        "type": "mysql",
        "prioritize_database": true
    },
    "features": {
        "intelligent_scheduling": true,
        "resource_management": true,
        "priority_optimization": true
    }
}
```

**初始化策略**:
- ✅ **必须存在** - 系统无法正常启动否则
- ✅ **幂等操作** - 重复执行不会产生副作用
- ✅ **配置覆盖** - 支持配置更新和回滚

---

### 3. 业务配置数据 - **建议初始化示例**
> 📋 业务运行的参考配置，帮助用户理解系统功能

#### 设备优先级示例
```python
device_priority_examples = [
    ('MT8768', 1, '高优先级芯片'),
    ('MT6765', 2, '标准优先级芯片'),
    ('MT6762', 3, '低优先级芯片')
]
```

#### 批次优先级示例
```python
lot_priority_examples = [
    ('MT8768', 'FT', 1, '终测优先'),
    ('MT8768', 'CP', 2, 'CP测试次优先')
]
```

#### STAGE映射示例
```python
stage_mapping_examples = [
    ('FT1', 'FT', 'exact', '终测第一站'),
    ('CP测试', 'CP', 'fuzzy', 'CP测试站')
]
```

#### 设备状态模板
```python
equipment_templates = [
    ('TESTER_01', 'HANDLER_01', 'idle', 'FT_TESTER', 'LINE_A'),
    ('TESTER_02', 'HANDLER_02', 'idle', 'FT_TESTER', 'LINE_A')
]
```

**初始化策略**:
- 🔄 **可选初始化** - 用户可选择是否创建示例
- 📝 **教学目的** - 帮助用户理解配置方法
- 🗑️ **易于清理** - 用户可以安全删除示例数据
- 📚 **文档说明** - 提供详细的配置说明

---

### 4. 生产业务数据 - **禁止初始化**
> 🏭 实际的生产运营数据，由业务操作产生

#### 实时订单数据
```sql
-- 禁止初始化的表数据
- customer_orders (客户订单)
- order_items (订单明细)
- production_orders (生产任务)
- production_schedules (生产计划)
```

#### 设备实时状态
```sql
-- 禁止初始化的动态数据
- 当前正在运行的LOT_ID
- 设备实时运行状态
- 实时良率数据
- 当前批次进度
```

#### 历史生产数据
```sql
-- 禁止初始化的历史数据
- ct (生产周期历史)
- wip_records (在制品历史)
- maintenance_records (维护记录)
- resource_usage_logs (资源使用日志)
```

**禁止原因**:
- 🚫 **数据污染** - 会影响真实的生产分析
- 🚫 **业务混乱** - 假数据会误导生产决策
- 🚫 **安全风险** - 可能覆盖重要的生产数据
- 🚫 **合规问题** - 生产数据需要完整的审计跟踪

---

### 5. Excel导入数据 - **禁止初始化**
> 📁 从外部系统导入的动态数据

#### 邮件附件数据
```sql
-- 禁止初始化
- email_attachments (邮件附件记录)
- 解析后的订单数据
- 客户提供的生产计划
```

#### 临时导入数据
```sql
-- 禁止初始化
- Excel文件解析结果
- 批量导入的临时数据
- 数据清洗的中间结果
```

**禁止原因**:
- 📂 **外部依赖** - 数据来自外部系统
- 🔄 **动态变化** - 数据频繁更新
- 🧹 **临时性质** - 多为临时处理数据

---

## 🔧 统一初始化方案

### 初始化层级设计

```python
class DatabaseInitializationLevels:
    """数据库初始化层级"""
    
    LEVEL_1_STRUCTURE = "表结构创建"      # 必须
    LEVEL_2_SYSTEM = "系统配置初始化"     # 必须  
    LEVEL_3_EXAMPLES = "示例数据创建"     # 可选
    LEVEL_4_VALIDATION = "数据完整性验证"  # 必须
```

### 初始化模式

```python
class InitializationModes:
    """初始化模式定义"""
    
    MINIMAL = "minimal"        # 仅表结构 + 系统配置
    STANDARD = "standard"      # 包含示例数据
    FULL = "full"             # 完整初始化 + 验证
    REPAIR = "repair"         # 修复模式，仅创建缺失项
```

### 统一初始化入口

```python
def unified_database_init(mode="standard", force=False):
    """
    统一数据库初始化入口
    
    Args:
        mode: 初始化模式 (minimal/standard/full/repair)
        force: 是否强制重建已存在的结构
    
    Returns:
        InitializationResult: 初始化结果
    """
    pass
```

## 📋 实施建议

### 1. 立即实施（高优先级）
- [ ] 统一 `init_db.py` 和 `DatabaseInitializationService`
- [ ] 实现层级化初始化策略
- [ ] 添加初始化模式选择
- [ ] 完善数据完整性验证

### 2. 近期优化（中优先级）
- [ ] 创建配置模板和示例数据库
- [ ] 实现增量更新和回滚机制
- [ ] 添加初始化进度显示
- [ ] 完善错误处理和恢复

### 3. 长期规划（低优先级）
- [ ] 实现多环境配置支持
- [ ] 添加数据库性能优化
- [ ] 集成备份和恢复功能
- [ ] 实现自动化测试覆盖

## 🎯 预期效果

1. **🏗️ 架构统一** - 消除双重初始化系统
2. **📋 操作简化** - 用户一键完成所有初始化
3. **🛡️ 数据安全** - 严格区分可初始化和禁止初始化的数据
4. **📚 易于维护** - 清晰的分层架构，便于扩展和维护
5. **🔧 灵活配置** - 支持不同场景的初始化需求

---

*文档版本: v1.0*  
*创建时间: 2025-01-14*  
*维护人员: AI Assistant* 