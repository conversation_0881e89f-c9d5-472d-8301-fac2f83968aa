---
alwaysApply: false
---
# AEC-FT 快速开发检查清单

## 🚀 开发前必检 (5分钟快速检查)

### ✅ 技术栈检查
- [ ] 使用 Flask 2.3.4 + Flask-SocketIO 5.3.5
- [ ] 数据库: MySQL 8.0+ (主) + Redis 4.0+ (缓存)
- [ ] 前端: Bootstrap 5 + UnifiedAPIClient
- [ ] ❌ 禁止: SQLite业务数据库、外部CDN、硬编码配置

### ✅ API设计检查
- [ ] 新API必须使用 `/api/v2/module/action` 格式
- [ ] 响应格式: `{"status": "success/error", "message": "描述", "data": {...}}`
- [ ] 实现适当的错误处理和缓存控制

### ✅ 配置管理检查
- [ ] 使用统一配置系统 (config.ini > 环境变量 > 默认值)
- [ ] 避免硬编码，使用 `config.aps_config`
- [ ] 生产环境必须使用 config.ini

### ✅ 安全性检查
- [ ] 实现RBAC权限控制
- [ ] 防止SQL注入 (使用ORM或参数化查询)
- [ ] 验证所有用户输入

### ✅ 性能检查
- [ ] 使用缓存策略 (Redis/内存缓存)
- [ ] 数据库连接池管理
- [ ] API响应时间 < 2秒 (正常负载)

---

## 🔧 新增页面/菜单快速流程

### 1. 前端开发
```bash
# 在 app/templates/ 创建模板
# 遵循现有结构和Bootstrap 5规范
```

### 2. 后端开发  
```bash
# 在 app/api_v2/ 开发API (优先v2)
# 使用蓝图组织: auth/production/orders/resources/system
```

### 3. 菜单配置
```python
# 更新 app/config/menu_config.py
# 1. 添加菜单ID到 MENU_ID_MAP
# 2. 在 MENU_CONFIG 定义菜单结构
```

### 4. 权限配置
```sql
# 在数据库 aps.user_permissions 配置权限
# 确保权限ID与菜单ID映射一致
```

### 5. 缓存清理
```bash
# 调用 /clear_cache 清理缓存
# 验证菜单更新生效
```

---

## 🧪 测试快速检查 (6维度)

### 1. 基础功能测试
- [ ] 单元测试: 核心函数验证
- [ ] 集成测试: 模块间交互
- [ ] API测试: 输入输出验证

### 2. 数据库连接池测试
- [ ] 并发连接测试 (100个并发)
- [ ] 连接泄漏检测
- [ ] 异常恢复测试

### 3. 真实场景测试
- [ ] 真实数据量测试 (1000-50000条)
- [ ] 用户行为模拟
- [ ] 网络异常场景

### 4. 性能测试
- [ ] API响应 < 2秒
- [ ] 页面加载 < 3秒
- [ ] 数据库查询 < 500ms
- [ ] 系统资源 < 80% CPU, < 1GB内存

### 5. 故障恢复测试
- [ ] 服务重启恢复
- [ ] 数据一致性验证
- [ ] 自动恢复机制

### 6. 历史问题回归
- [ ] 双圈圈加载问题
- [ ] 数据库字段缺失
- [ ] 路由冲突问题
- [ ] 排产重复记录

---

## 🎯 排产算法快速规范

### 算法架构
- **主服务**: `RealSchedulingService`
- **评分维度**: 技术匹配(25%) + 负载均衡(20%) + 交期紧迫(25%) + 产值效率(20%) + 业务优先级(10%)

### 性能要求
- [ ] 1000批次处理 < 10秒
- [ ] 匹配成功率 > 95%
- [ ] 内存使用 < 1GB

### 关键特性
- [ ] 同产品续排优化
- [ ] 智能STAGE匹配
- [ ] 自动DUE_DATE生成
- [ ] 实时负载追踪

---

## ⚠️ 常见违规行为

### ❌ 禁止行为
- 使用SQLite作为业务数据库
- 硬编码配置信息
- 使用外部CDN依赖
- 跳过权限验证
- 忽略错误处理

### ❌ 性能杀手
- 不使用缓存
- N+1查询问题
- 不使用连接池
- 同步阻塞操作
- 内存泄漏

### ❌ 安全漏洞
- SQL注入风险
- 未验证用户输入
- 权限控制缺失
- 敏感信息泄露
- 会话管理不当

---

## 🚨 紧急问题处理

### 数据库连接问题
```bash
# 1. 检查配置文件 config.ini
# 2. 验证数据库服务状态
# 3. 检查连接池健康状态
# 4. 查看错误日志 logs/app.log
```

### 性能问题
```bash
# 1. 检查缓存状态
# 2. 监控数据库查询
# 3. 检查系统资源使用
# 4. 分析慢查询日志
```

### 权限问题
```bash
# 1. 检查用户权限配置
# 2. 验证菜单配置
# 3. 清理权限缓存
# 4. 重启应用服务
```

---

## 📞 快速参考

### 关键文件位置
- 配置: `config/aps_config.py`
- 菜单: `app/config/menu_config.py`
- API: `app/api_v2/`
- 服务: `app/services/`
- 模板: `app/templates/`

### 常用命令
```bash
# 启动应用
python run.py

# 初始化数据库
python run.py init-db

# 数据库迁移
python run.py migrate

# 清理缓存
curl http://localhost:5000/clear_cache
```

### 性能基准
- API响应: 正常<2秒, 峰值<5秒
- 页面加载: 首次<3秒, 缓存<1秒
- 数据库查询: 简单<500ms, 复杂<2秒
- 系统资源: CPU<80%, 内存<1GB

---

## ✅ 部署前最终检查

- [ ] 所有测试通过 (6维度测试框架)
- [ ] 性能基准满足要求
- [ ] 安全检查通过
- [ ] 配置文件正确
- [ ] 权限配置完整
- [ ] 文档已更新
- [ ] 缓存已清理

---

*快速检查清单 - 确保每次开发都符合项目规范*
