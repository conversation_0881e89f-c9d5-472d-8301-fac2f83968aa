"""
性能监控API端点
"""
from flask import Blueprint, jsonify
from flask_login import login_required
from app.utils.performance_metrics import performance_metrics

monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/monitoring')

@monitoring_bp.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'performance_monitoring',
        'timestamp': performance_metrics.get_current_stats()['timestamp']
    })

@monitoring_bp.route('/metrics')
@login_required
def get_metrics():
    """获取当前性能指标"""
    return jsonify(performance_metrics.get_current_stats())

@monitoring_bp.route('/metrics/history')
@login_required
def get_historical_metrics():
    """获取历史性能数据"""
    from flask import request
    hours = int(request.args.get('hours', 1))
    return jsonify(performance_metrics.get_historical_data(hours))

@monitoring_bp.route('/metrics/clear')
@login_required
def clear_metrics():
    """清空性能指标"""
    performance_metrics.metrics.clear()
    return jsonify({'status': 'cleared'})
