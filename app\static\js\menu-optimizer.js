/**
 * 菜单性能优化工具
 * 提供高效的菜单管理和交互功能
 */

class MenuOptimizer {
    constructor() {
        this.menuCache = new Map();
        this.activeMenus = new Set();
        this.menuContainer = null;
        this.initialized = false;
        this.debounceTimers = new Map();
        
        // 绑定上下文
        this.handleMenuClick = this.handleMenuClick.bind(this);
        this.handleMenuHover = this.handleMenuHover.bind(this);
    }
    
    /**
     * 初始化菜单优化器
     */
    init() {
        if (this.initialized) return;
        
        this.menuContainer = document.getElementById('sidebar-menu');
        if (!this.menuContainer) {
            console.warn('菜单容器未找到');
            return;
        }
        
        // 使用事件委托优化性能
        this.menuContainer.addEventListener('click', this.handleMenuClick, { passive: false });
        
        // 添加预加载hover效果
        this.menuContainer.addEventListener('mouseenter', this.handleMenuHover, { passive: true });
        
        this.initialized = true;
        console.log('菜单优化器已初始化');
    }
    
    /**
     * 处理菜单点击事件（事件委托）
     */
    handleMenuClick(event) {
        const target = event.target.closest('.nav-link');
        if (!target) return;
        
        // 防抖处理，避免快速连击
        const menuId = target.dataset.route || target.textContent.trim();
        if (this.debounceTimers.has(menuId)) {
            clearTimeout(this.debounceTimers.get(menuId));
        }
        
        this.debounceTimers.set(menuId, setTimeout(() => {
            this.processMenuClick(target, event);
            this.debounceTimers.delete(menuId);
        }, 50));
    }
    
    /**
     * 处理实际的菜单点击逻辑
     */
    processMenuClick(target, event) {
        const href = target.getAttribute('href');
        const isTopLevel = target.dataset.isTopLevel === 'true';
        const submenu = target.nextElementSibling;
        
        // 如果是有子菜单的父菜单
        if (href === '#' && submenu && submenu.classList.contains('submenu')) {
            event.preventDefault();
            this.toggleSubmenu(target, submenu, isTopLevel);
            return;
        }
        
        // 如果是有效链接，标记为活动菜单
        if (href && href !== '#') {
            this.setActiveMenu(target);
        }
    }
    
    /**
     * 切换子菜单显示状态
     */
    toggleSubmenu(menuLink, submenu, isTopLevel) {
        const isExpanded = menuLink.classList.contains('expanded');
        
        // 使用requestAnimationFrame优化动画性能
        requestAnimationFrame(() => {
            if (isTopLevel && !isExpanded) {
                // 关闭其他顶级菜单
                this.closeOtherTopLevelMenus(menuLink);
            }
            
            // 切换当前菜单状态
            menuLink.classList.toggle('expanded');
            submenu.classList.toggle('show');
            
            // 更新活动菜单记录
            if (submenu.classList.contains('show')) {
                this.activeMenus.add(menuLink);
            } else {
                this.activeMenus.delete(menuLink);
            }
        });
    }
    
    /**
     * 关闭其他顶级菜单
     */
    closeOtherTopLevelMenus(exceptMenu) {
        const topLevelMenus = this.menuContainer.querySelectorAll('.nav-link[data-is-top-level="true"]');
        
        // 批量处理DOM更新
        const updates = [];
        
        topLevelMenus.forEach(menu => {
            if (menu !== exceptMenu && menu.classList.contains('expanded')) {
                updates.push(() => {
                    menu.classList.remove('expanded');
                    const submenu = menu.nextElementSibling;
                    if (submenu && submenu.classList.contains('submenu')) {
                        submenu.classList.remove('show');
                    }
                    this.activeMenus.delete(menu);
                });
            }
        });
        
        // 批量执行更新
        if (updates.length > 0) {
            requestAnimationFrame(() => {
                updates.forEach(update => update());
            });
        }
    }
    
    /**
     * 设置活动菜单
     */
    setActiveMenu(menuLink) {
        // 移除所有活动状态
        const allLinks = this.menuContainer.querySelectorAll('.nav-link');
        allLinks.forEach(link => link.classList.remove('active'));
        
        // 设置当前菜单为活动状态
        menuLink.classList.add('active');
        
        // 展开父菜单路径
        this.expandParentMenus(menuLink);
    }
    
    /**
     * 展开包含活动菜单的所有父菜单
     */
    expandParentMenus(menuLink) {
        let parentSubmenu = menuLink.closest('.submenu');
        const toExpand = [];
        
        // 收集需要展开的菜单
        while (parentSubmenu) {
            const parentLink = parentSubmenu.previousElementSibling;
            if (parentLink && parentLink.classList.contains('nav-link')) {
                toExpand.push({ link: parentLink, submenu: parentSubmenu });
                
                if (parentLink.dataset.isTopLevel === 'true') {
                    // 找到顶级菜单，关闭其他顶级菜单
                    this.closeOtherTopLevelMenus(parentLink);
                    break;
                }
            }
            parentSubmenu = parentSubmenu.parentElement.closest('.submenu');
        }
        
        // 批量展开菜单
        if (toExpand.length > 0) {
            requestAnimationFrame(() => {
                toExpand.forEach(({ link, submenu }) => {
                    link.classList.add('expanded');
                    submenu.classList.add('show');
                    this.activeMenus.add(link);
                });
            });
        }
    }
    
    /**
     * 处理菜单悬停（预加载效果）
     */
    handleMenuHover(event) {
        if (event.target.matches('.nav-link')) {
            // 预热样式计算
            event.target.getBoundingClientRect();
        }
    }
    
    /**
     * 根据当前路径设置活动菜单
     */
    setActiveMenuByPath(path = window.location.pathname) {
        const allLinks = this.menuContainer.querySelectorAll('.nav-link');
        let bestMatch = null;
        let bestMatchLength = 0;
        
        // 查找最佳匹配
        allLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href !== '#' && path.startsWith(href) && href.length > bestMatchLength) {
                bestMatch = link;
                bestMatchLength = href.length;
            }
        });
        
        if (bestMatch) {
            this.setActiveMenu(bestMatch);
        }
    }
    
    /**
     * 清理资源
     */
    destroy() {
        if (!this.initialized) return;
        
        // 清理事件监听器
        if (this.menuContainer) {
            this.menuContainer.removeEventListener('click', this.handleMenuClick);
            this.menuContainer.removeEventListener('mouseenter', this.handleMenuHover);
        }
        
        // 清理定时器
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();
        
        // 重置状态
        this.activeMenus.clear();
        this.menuCache.clear();
        this.initialized = false;
        
        console.log('菜单优化器已清理');
    }
    
    /**
     * 获取性能统计
     */
    getStats() {
        return {
            cacheSize: this.menuCache.size,
            activeMenusCount: this.activeMenus.size,
            pendingTimers: this.debounceTimers.size,
            initialized: this.initialized
        };
    }
}

// 创建全局实例
window.MenuOptimizer = new MenuOptimizer();

// 导出为模块（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuOptimizer;
} 