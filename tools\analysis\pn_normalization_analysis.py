#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HB_PN/TB_PN 数据勘测脚本
用于分析et_ft_test_spec和eqp_status表中的PN字段模式，确定正规化规则
"""

import sys
import os
import json
import re
from collections import defaultdict, Counter

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.utils.db_connection_pool import get_db_connection

def analyze_pn_patterns():
    """分析HB_PN和TB_PN的数据模式"""
    
    print("=== HB_PN/TB_PN 数据模式分析 ===\n")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()  # 已配置DictCursor，无需额外参数
        
        # 1. 分析et_ft_test_spec表
        print("1. 分析 et_ft_test_spec 表中的 HB_PN, TB_PN 模式:")
        cursor.execute("""
            SELECT DISTINCT HB_PN, TB_PN, DEVICE, STAGE 
            FROM et_ft_test_spec 
            WHERE HB_PN IS NOT NULL OR TB_PN IS NOT NULL
            ORDER BY DEVICE, STAGE, HB_PN, TB_PN
            LIMIT 100
        """)
        
        spec_data = cursor.fetchall()
        print(f"   找到 {len(spec_data)} 条记录")
        
        hb_patterns = []
        tb_patterns = []
        
        for row in spec_data:
            if row['HB_PN']:
                hb_patterns.append(row['HB_PN'])
            if row['TB_PN']:
                tb_patterns.append(row['TB_PN'])
        
        print(f"   HB_PN 样例 (前20个): {hb_patterns[:20]}")
        print(f"   TB_PN 样例 (前20个): {tb_patterns[:20]}")
        
        # 2. 分析eqp_status表
        print("\n2. 分析 eqp_status 表中的 HB_PN, TB_PN 模式:")
        cursor.execute("""
            SELECT DISTINCT HB_PN, TB_PN, DEVICE, STAGE 
            FROM eqp_status 
            WHERE HB_PN IS NOT NULL OR TB_PN IS NOT NULL
            ORDER BY DEVICE, STAGE, HB_PN, TB_PN
            LIMIT 100
        """)
        
        eqp_data = cursor.fetchall()
        print(f"   找到 {len(eqp_data)} 条记录")
        
        eqp_hb_patterns = []
        eqp_tb_patterns = []
        
        for row in eqp_data:
            if row['HB_PN']:
                eqp_hb_patterns.append(row['HB_PN'])
            if row['TB_PN']:
                eqp_tb_patterns.append(row['TB_PN'])
        
        print(f"   HB_PN 样例 (前20个): {eqp_hb_patterns[:20]}")
        print(f"   TB_PN 样例 (前20个): {eqp_tb_patterns[:20]}")
        
        # 3. 分析后缀模式
        print("\n3. 分析后缀模式:")
        all_pns = hb_patterns + tb_patterns + eqp_hb_patterns + eqp_tb_patterns
        
        # 统计常见后缀
        suffix_patterns = defaultdict(list)
        
        for pn in all_pns:
            if not pn:
                continue
                
            # 检查各种后缀模式
            if re.search(r'_[A-Za-z0-9]+$', pn):
                match = re.search(r'_([A-Za-z0-9]+)$', pn)
                if match:
                    suffix_patterns['_suffix'].append(match.group(1))
            
            if re.search(r'-SN\d+$', pn):
                match = re.search(r'-SN(\d+)$', pn)
                if match:
                    suffix_patterns['-SN_suffix'].append(match.group(1))
            
            if re.search(r'_\d+$', pn):
                match = re.search(r'_(\d+)$', pn)
                if match:
                    suffix_patterns['_number_suffix'].append(match.group(1))
        
        for pattern_type, suffixes in suffix_patterns.items():
            counter = Counter(suffixes)
            print(f"   {pattern_type}: {counter.most_common(10)}")
        
        # 4. 测试正规化规则
        print("\n4. 测试正规化规则:")
        
        def normalize_pn_v1(pn):
            """第一版正规化规则"""
            if not pn:
                return ''
            s = pn.strip()
            # 去除-SN数字后缀
            s = re.sub(r'-SN\d+$', '', s)
            # 去除_字母数字后缀
            s = re.sub(r'_[A-Za-z0-9]+$', '', s)
            return s
        
        # 测试一些样例
        test_cases = [
            ("20210009_C_04", "20210009_C"),
            ("TEST_PN_SN123", "TEST_PN"),
            ("ABC-SN001", "ABC"),
            ("XYZ_123", "XYZ"),
        ]
        
        print("   正规化测试:")
        for original, expected in test_cases:
            normalized = normalize_pn_v1(original)
            status = "✓" if normalized == expected else "✗"
            print(f"   {status} {original} -> {normalized} (期望: {expected})")
        
        # 5. 实际数据正规化测试
        print("\n5. 实际数据正规化效果:")
        unique_pns = list(set(all_pns))[:20]
        for pn in unique_pns:
            if pn:
                normalized = normalize_pn_v1(pn)
                if normalized != pn:
                    print(f"   {pn} -> {normalized}")
        
        cursor.close()
        conn.close()
        
        print("\n=== 分析完成 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_pn_patterns()
