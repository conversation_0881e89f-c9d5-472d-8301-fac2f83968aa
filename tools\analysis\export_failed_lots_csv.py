#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出最新一次排产会话的失败批次详单 CSV，以及含跨工序候选的样本清单。
- 输出目录: reports/
- 文件: failed_lots_latest_session.csv, cross_stage_candidates_latest_session.csv
"""
import os
import csv
import json
from datetime import datetime

# 复用项目内的高性能连接池（无需额外依赖）
from app.utils.db_connection_pool import get_db_cursor, register_process_type

register_process_type('tool_script')

OUTPUT_DIR = os.path.join('reports')
FAILED_CSV = os.path.join(OUTPUT_DIR, 'failed_lots_latest_session.csv')
CROSS_CSV = os.path.join(OUTPUT_DIR, 'cross_stage_candidates_latest_session.csv')

SQL_LATEST_SESSION = """
SELECT SESSION_ID
FROM scheduling_failed_lots
WHERE SESSION_ID IS NOT NULL AND SESSION_ID <> ''
ORDER BY `timestamp` DESC
LIMIT 1
"""

SQL_FAILED_LATEST = """
SELECT sfl.lot_id, sfl.device, sfl.stage, sfl.failure_reason, sfl.failure_details, sfl.timestamp, sfl.SESSION_ID
FROM scheduling_failed_lots sfl
WHERE sfl.SESSION_ID = %s
ORDER BY sfl.timestamp DESC
"""

os.makedirs(OUTPUT_DIR, exist_ok=True)


def _parse_details(raw):
    counts = {
        'strict_device_stage_match_count': None,
        'idle_config_match_count': None,
        'cross_stage_config_match_count': None,
    }
    if not raw:
        return counts
    try:
        if isinstance(raw, (bytes, bytearray)):
            raw = raw.decode('utf-8', errors='ignore')
        if isinstance(raw, str):
            data = json.loads(raw)
        else:
            data = raw
        for k in counts.keys():
            v = data.get(k)
            if isinstance(v, (int, float)):
                counts[k] = int(v)
    except Exception:
        # 保留默认None，避免导出中断
        pass
    return counts


def main():
    # 1) 获取最新会话ID
    with get_db_cursor('aps', autocommit=True) as cur:
        cur.execute(SQL_LATEST_SESSION)
        row = cur.fetchone()
        if not row or not row.get('SESSION_ID'):
            print('[WARN] 未找到最新会话ID，可能尚未执行过排产。')
            return
        session_id = row['SESSION_ID']
        print(f'[INFO] 最新会话: {session_id}')

        # 2) 查询该会话失败批次
        cur.execute(SQL_FAILED_LATEST, (session_id,))
        results = cur.fetchall() or []
        print(f'[INFO] 失败批次: {len(results)} 条')

    # 3) 导出CSV
    failed_rows = []
    cross_rows = []

    for r in results:
        counts = _parse_details(r.get('failure_details'))
        failed_row = {
            'SESSION_ID': r.get('SESSION_ID'),
            'lot_id': r.get('lot_id'),
            'device': r.get('device'),
            'stage': r.get('stage'),
            'failure_reason': r.get('failure_reason'),
            'strict_device_stage_match_count': counts['strict_device_stage_match_count'],
            'idle_config_match_count': counts['idle_config_match_count'],
            'cross_stage_config_match_count': counts['cross_stage_config_match_count'],
            'timestamp': r.get('timestamp').strftime('%Y-%m-%d %H:%M:%S') if isinstance(r.get('timestamp'), datetime) else r.get('timestamp')
        }
        failed_rows.append(failed_row)
        if (counts['cross_stage_config_match_count'] or 0) > 0:
            cross_rows.append(failed_row)

    headers = ['SESSION_ID','lot_id','device','stage','failure_reason',
               'strict_device_stage_match_count','idle_config_match_count','cross_stage_config_match_count','timestamp']

    with open(FAILED_CSV, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(failed_rows)
    with open(CROSS_CSV, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(cross_rows)

    print(f'[OK] 导出完成: {FAILED_CSV} ({len(failed_rows)} 行)')
    print(f'[OK] 导出完成: {CROSS_CSV} ({len(cross_rows)} 行)')


if __name__ == '__main__':
    main()

