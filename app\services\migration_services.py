#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据迁移服务模块
负责将传统的批次管理表和测试规范表数据迁移到统一模型中
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from app.models import db
from app.models.unified.unified_lot_model import UnifiedLotModel
from app.models.unified.unified_test_spec import UnifiedTestSpec

# 延迟导入避免循环导入
def get_traditional_models():
    """延迟导入传统模型"""
    try:
        from app.models import ET_WAIT_LOT, LOT_WIP, WIP_LOT, Test_Spec
        return ET_WAIT_LOT, LOT_WIP, WIP_LOT, Test_Spec
    except ImportError as e:
        logging.warning(f"传统模型导入失败: {e}")
        return None, None, None, None

logger = logging.getLogger(__name__)

class LotDataMigrationService:
    """批次数据迁移服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def migrate_from_et_wait_lot(self, batch_size: int = 1000) -> Tuple[int, int]:
        """从ET_WAIT_LOT迁移数据"""
        self.logger.info("开始从ET_WAIT_LOT迁移数据...")
        
        ET_WAIT_LOT, _, _, _ = get_traditional_models()
        if not ET_WAIT_LOT:
            self.logger.error("无法导入ET_WAIT_LOT模型")
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            # 分批处理数据
            offset = 0
            while True:
                records = ET_WAIT_LOT.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 检查是否已存在
                        existing = UnifiedLotModel.query.filter_by(LOT_ID=record.LOT_ID).first()
                        if existing:
                            self.logger.debug(f"批次 {record.LOT_ID} 已存在，跳过")
                            continue
                        
                        # 创建统一记录
                        unified_record = UnifiedLotModel(
                            LOT_ID=record.LOT_ID,
                            DEVICE=getattr(record, 'DEVICE', None),
                            CHIP_ID=getattr(record, 'CHIP_ID', None),
                            PKG_PN=getattr(record, 'PKG_PN', None),
                            PROD_ID=getattr(record, 'PROD_ID', None),
                            STAGE=getattr(record, 'STAGE', None),
                            GOOD_QTY=getattr(record, 'GOOD_QTY', None),
                            LOT_QTY=getattr(record, 'LOT_QTY', None),
                            WIP_STATE=getattr(record, 'WIP_STATE', None),
                            PROC_STATE=getattr(record, 'PROC_STATE', None),
                            HOLD_STATE=getattr(record, 'HOLD_STATE', None),
                            PRIORITY_LEVEL='medium',
                            PRIORITY_ORDER=999,
                            source_table='v_et_wait_lot_unified',
                            migration_status='completed'
                        )
                        
                        db.session.add(unified_record)
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"迁移批次 {record.LOT_ID} 失败: {e}")
                        error_count += 1
                
                # 提交当前批次
                try:
                    db.session.commit()
                    self.logger.info(f"已迁移 {success_count} 条ET_WAIT_LOT记录")
                except Exception as e:
                    self.logger.error(f"提交批次数据失败: {e}")
                    db.session.rollback()
                    error_count += len(records)
                
                offset += batch_size
                
        except Exception as e:
            self.logger.error(f"ET_WAIT_LOT迁移过程出错: {e}")
            db.session.rollback()
        
        self.logger.info(f"ET_WAIT_LOT迁移完成: 成功 {success_count}, 失败 {error_count}")
        return success_count, error_count
    
    def migrate_from_lot_wip(self, batch_size: int = 1000) -> Tuple[int, int]:
        """从LOT_WIP迁移数据"""
        self.logger.info("开始从LOT_WIP迁移数据...")
        
        _, LOT_WIP, _, _ = get_traditional_models()
        if not LOT_WIP:
            self.logger.error("无法导入LOT_WIP模型")
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            offset = 0
            while True:
                records = LOT_WIP.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 查找或创建统一记录
                        unified_record = UnifiedLotModel.query.filter_by(LOT_ID=record.LOT_ID).first()
                        if not unified_record:
                            # 创建新记录
                            unified_record = UnifiedLotModel(
                                LOT_ID=record.LOT_ID,
                                source_table='LOT_WIP',
                                migration_status='completed'
                            )
                            db.session.add(unified_record)
                        
                        # 更新设备分配信息
                        unified_record.HANDLER_ID = getattr(record, 'HANDLER_ID', None)
                        unified_record.TESTER_ID = getattr(record, 'TESTER_ID', None)
                        unified_record.DEVICE = getattr(record, 'DEVICE', unified_record.DEVICE)
                        unified_record.CHIP_ID = getattr(record, 'CHIP_ID', unified_record.CHIP_ID)
                        unified_record.PKG_PN = getattr(record, 'PKG_PN', unified_record.PKG_PN)
                        unified_record.STAGE = getattr(record, 'STAGE', unified_record.STAGE)
                        
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"迁移LOT_WIP批次 {record.LOT_ID} 失败: {e}")
                        error_count += 1
                
                # 提交当前批次
                try:
                    db.session.commit()
                    self.logger.info(f"已迁移 {success_count} 条LOT_WIP记录")
                except Exception as e:
                    self.logger.error(f"提交LOT_WIP批次数据失败: {e}")
                    db.session.rollback()
                    error_count += len(records)
                
                offset += batch_size
                
        except Exception as e:
            self.logger.error(f"LOT_WIP迁移过程出错: {e}")
            db.session.rollback()
        
        self.logger.info(f"LOT_WIP迁移完成: 成功 {success_count}, 失败 {error_count}")
        return success_count, error_count
    
    def migrate_from_wip_lot(self, batch_size: int = 1000) -> Tuple[int, int]:
        """从WIP_LOT迁移数据"""
        self.logger.info("开始从WIP_LOT迁移数据...")
        
        _, _, WIP_LOT, _ = get_traditional_models()
        if not WIP_LOT:
            self.logger.error("无法导入WIP_LOT模型")
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            offset = 0
            while True:
                records = WIP_LOT.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 查找或创建统一记录
                        unified_record = UnifiedLotModel.query.filter_by(LOT_ID=record.LOT_ID).first()
                        if not unified_record:
                            # 创建新记录
                            unified_record = UnifiedLotModel(
                                LOT_ID=record.LOT_ID,
                                source_table='v_wip_lot_unified',
                                migration_status='completed'
                            )
                            db.session.add(unified_record)
                        
                        # 更新核心字段
                        unified_record.DEVICE = getattr(record, 'DEVICE', unified_record.DEVICE)
                        unified_record.CHIP_ID = getattr(record, 'CHIP_ID', unified_record.CHIP_ID)
                        unified_record.PKG_PN = getattr(record, 'PKG_PN', unified_record.PKG_PN)
                        unified_record.STAGE = getattr(record, 'STAGE', unified_record.STAGE)
                        unified_record.LOT_QTY = getattr(record, 'LOT_QTY', unified_record.LOT_QTY)
                        unified_record.WIP_STATE = getattr(record, 'WIP_STATE', unified_record.WIP_STATE)
                        unified_record.PROC_STATE = getattr(record, 'PROC_STATE', unified_record.PROC_STATE)
                        unified_record.HOLD_STATE = getattr(record, 'HOLD_STATE', unified_record.HOLD_STATE)
                        
                        # 将额外字段存储到扩展数据中
                        extended_data = {}
                        for column in record.__table__.columns:
                            if column.name not in ['LOT_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'STAGE', 
                                                 'LOT_QTY', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE']:
                                value = getattr(record, column.name, None)
                                if value is not None:
                                    extended_data[column.name] = str(value)
                        
                        if extended_data:
                            unified_record.extended_data = json.dumps(extended_data, ensure_ascii=False)
                        
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"迁移WIP_LOT批次 {record.LOT_ID} 失败: {e}")
                        error_count += 1
                
                # 提交当前批次
                try:
                    db.session.commit()
                    self.logger.info(f"已迁移 {success_count} 条WIP_LOT记录")
                except Exception as e:
                    self.logger.error(f"提交WIP_LOT批次数据失败: {e}")
                    db.session.rollback()
                    error_count += len(records)
                
                offset += batch_size
                
        except Exception as e:
            self.logger.error(f"WIP_LOT迁移过程出错: {e}")
            db.session.rollback()
        
        self.logger.info(f"WIP_LOT迁移完成: 成功 {success_count}, 失败 {error_count}")
        return success_count, error_count

class TestSpecMigrationService:
    """测试规范迁移服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def migrate_from_test_spec(self, batch_size: int = 1000) -> Tuple[int, int]:
        """从Test_Spec迁移数据"""
        self.logger.info("开始从Test_Spec迁移数据...")
        
        _, _, _, Test_Spec = get_traditional_models()
        if not Test_Spec:
            self.logger.error("无法导入Test_Spec模型")
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            offset = 0
            while True:
                records = Test_Spec.query.offset(offset).limit(batch_size).all()
                if not records:
                    break
                
                for record in records:
                    try:
                        # 检查是否已存在
                        existing = UnifiedTestSpec.query.filter_by(
                            TEST_SPEC_ID=getattr(record, 'TEST_SPEC_ID', ''),
                            TEST_SPEC_VER=getattr(record, 'TEST_SPEC_VER', '')
                        ).first()
                        if existing:
                            self.logger.debug(f"测试规范 {record.TEST_SPEC_ID} 已存在，跳过")
                            continue
                        
                        # 创建统一记录
                        unified_record = UnifiedTestSpec(
                            TEST_SPEC_ID=getattr(record, 'TEST_SPEC_ID', ''),
                            TEST_SPEC_NAME=getattr(record, 'TEST_SPEC_NAME', ''),
                            TEST_SPEC_VER=getattr(record, 'TEST_SPEC_VER', ''),
                            DEVICE=getattr(record, 'DEVICE', None),
                            CHIP_ID=getattr(record, 'CHIP_ID', None),
                            PKG_PN=getattr(record, 'PKG_PN', None),
                            STAGE=getattr(record, 'STAGE', None),
                            status='active',
                            APPROVAL_STATE=getattr(record, 'APPROVAL_STATE', None)
                        )
                        
                        # 将其他字段存储到扩展数据中
                        extended_data = {}
                        for column in record.__table__.columns:
                            if column.name not in ['TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER',
                                                 'DEVICE', 'CHIP_ID', 'PKG_PN', 'STAGE', 'APPROVAL_STATE']:
                                value = getattr(record, column.name, None)
                                if value is not None:
                                    extended_data[column.name] = str(value)
                        
                        if extended_data:
                            unified_record.extended_data = json.dumps(extended_data, ensure_ascii=False)
                        
                        db.session.add(unified_record)
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"迁移测试规范 {getattr(record, 'TEST_SPEC_ID', 'Unknown')} 失败: {e}")
                        error_count += 1
                
                # 提交当前批次
                try:
                    db.session.commit()
                    self.logger.info(f"已迁移 {success_count} 条Test_Spec记录")
                except Exception as e:
                    self.logger.error(f"提交Test_Spec批次数据失败: {e}")
                    db.session.rollback()
                    error_count += len(records)
                
                offset += batch_size
                
        except Exception as e:
            self.logger.error(f"Test_Spec迁移过程出错: {e}")
            db.session.rollback()
        
        self.logger.info(f"Test_Spec迁移完成: 成功 {success_count}, 失败 {error_count}")
        return success_count, error_count

class DataMigrationManager:
    """数据迁移管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.lot_service = LotDataMigrationService()
        self.test_spec_service = TestSpecMigrationService()
    
    def run_full_migration(self) -> Dict[str, Tuple[int, int]]:
        """运行完整的数据迁移"""
        self.logger.info("开始完整数据迁移...")
        
        results = {}
        
        # 1. 迁移ET_WAIT_LOT数据（优先级最高）
        results['v_et_wait_lot_unified'] = self.lot_service.migrate_from_et_wait_lot()
        
        # 2. 迁移LOT_WIP数据（设备分配信息）
        results['LOT_WIP'] = self.lot_service.migrate_from_lot_wip()
        
        # 3. 迁移WIP_LOT数据（完整信息）
        results['v_wip_lot_unified'] = self.lot_service.migrate_from_wip_lot()
        
        # 4. 迁移测试规范数据
        results['Test_Spec'] = self.test_spec_service.migrate_from_test_spec()
        
        # 输出迁移结果
        self.logger.info("数据迁移完成，结果汇总:")
        total_success = 0
        total_error = 0
        
        for table, (success, error) in results.items():
            self.logger.info(f"  {table}: 成功 {success}, 失败 {error}")
            total_success += success
            total_error += error
        
        self.logger.info(f"总计: 成功 {total_success}, 失败 {total_error}")
        
        return results
    
    def verify_migration(self) -> Dict[str, int]:
        """验证迁移结果"""
        self.logger.info("开始验证迁移结果...")
        
        verification = {}
        
        try:
            # 统计统一模型中的数据
            verification['unified_lot_total'] = UnifiedLotModel.query.count()
            verification['unified_lot_et_wait'] = UnifiedLotModel.query.filter_by(source_table='v_et_wait_lot_unified').count()
            verification['unified_lot_lot_wip'] = UnifiedLotModel.query.filter_by(source_table='LOT_WIP').count()
            verification['unified_lot_wip_lot'] = UnifiedLotModel.query.filter_by(source_table='v_wip_lot_unified').count()
            verification['unified_test_spec'] = UnifiedTestSpec.query.count()
            
            self.logger.info("迁移验证结果:")
            for key, count in verification.items():
                self.logger.info(f"  {key}: {count}")
                
        except Exception as e:
            self.logger.error(f"验证迁移结果失败: {e}")
        
        return verification 