from flask import Blueprint
from app.utils.api_config import get_api_route

# 创建系统管理API蓝图 - 使用配置化的URL前缀
system_bp = Blueprint('system_v2', __name__, url_prefix=get_api_route('system'))

# 导入子模块API
from .database_config import db_config_bp
system_bp.register_blueprint(db_config_bp, url_prefix='/database-config')

# 导入路由
from . import routes

# 注册dashboard子蓝图
from .dashboard import dashboard_bp
system_bp.register_blueprint(dashboard_bp, url_prefix='/dashboard')

# 注册监控子蓝图
from .monitoring import monitoring_bp
system_bp.register_blueprint(monitoring_bp) 
# 注册缺失的监控API
from .missing_monitoring import missing_monitoring_bp
system_bp.register_blueprint(missing_monitoring_bp, url_prefix='/monitoring')

# 注册定时任务API
from .scheduled_tasks_api import scheduled_tasks_api
system_bp.register_blueprint(scheduled_tasks_api)

# 注册AI设置API（向后兼容）
from .ai_settings import ai_settings_bp
system_bp.register_blueprint(ai_settings_bp)

# 注册Dify设置API（新的统一接口）
from .dify_settings import dify_settings_bp
system_bp.register_blueprint(dify_settings_bp)

# 注册数据库初始化API
# 临时禁用database_initialization（语法错误待修复）
# from .database_initialization import db_init_bp
# system_bp.register_blueprint(db_init_bp, url_prefix='/database-initialization')
