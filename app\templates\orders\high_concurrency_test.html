<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高并发邮件处理测试控制台 - APS车规芯片终测智能调度平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .progress-container {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .log-container {
            background-color: #212529;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            padding: 15px;
            border-radius: 5px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
        }
        .config-panel {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-result {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        .test-result.failed {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-microchip"></i> APS车规芯片终测智能调度平台
                </a>
                <span class="navbar-text">
                    <i class="fas fa-tachometer-alt"></i> 高并发邮件处理测试控制台
                </span>
            </div>
        </nav>

        <!-- 系统状态概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">数据库状态</h5>
                        <span id="db-status" class="badge bg-secondary">检查中...</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-2x text-success mb-2"></i>
                        <h5 class="card-title">邮箱配置</h5>
                        <span id="email-status" class="badge bg-secondary">检查中...</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-file-excel fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">测试数据</h5>
                        <span id="test-data-status" class="badge bg-secondary">扫描中...</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                        <h5 class="card-title">处理器状态</h5>
                        <span id="processor-status" class="badge bg-secondary">检查中...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试配置面板 -->
        <div class="config-panel">
            <h4><i class="fas fa-sliders-h"></i> 高并发测试配置</h4>
            <div class="row">
                <div class="col-md-4">
                    <label for="max-workers" class="form-label">最大并发数</label>
                    <input type="number" class="form-control" id="max-workers" value="500" min="1" max="1000">
                    <div class="form-text">建议值: 500 (根据系统性能调整)</div>
                </div>
                <div class="col-md-4">
                    <label for="progress-interval" class="form-label">进度更新间隔 (秒)</label>
                    <input type="number" class="form-control" id="progress-interval" value="10" min="1" max="60">
                    <div class="form-text">日志和进度更新频率</div>
                </div>
                <div class="col-md-4">
                    <label for="email-configs" class="form-label">选择邮箱配置</label>
                    <select class="form-select" id="email-configs" multiple aria-label="邮箱配置选择">
                        <!-- 动态加载 -->
                    </select>
                    <div class="form-text">留空表示使用所有启用的配置</div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button id="start-test-btn" class="btn btn-success btn-lg me-2">
                        <i class="fas fa-play"></i> 启动高并发测试
                    </button>
                    <button id="stop-test-btn" class="btn btn-danger btn-lg me-2" disabled>
                        <i class="fas fa-stop"></i> 停止测试
                    </button>
                    <button id="refresh-status-btn" class="btn btn-info btn-lg">
                        <i class="fas fa-sync"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试进度 -->
        <div class="progress-container" id="progress-container" style="display: none;">
            <h4><i class="fas fa-chart-line"></i> 测试进度</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="progress mb-3">
                        <div id="overall-progress" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <div id="progress-info">
                        <p><strong>状态:</strong> <span id="test-status">准备中</span></p>
                        <p><strong>已处理:</strong> <span id="processed-count">0</span> / <span id="total-count">0</span></p>
                        <p><strong>成功:</strong> <span id="success-count">0</span> | <strong>失败:</strong> <span id="failed-count">0</span></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="metric-card">
                        <h6><i class="fas fa-clock"></i> 运行时间</h6>
                        <h3 id="elapsed-time">00:00:00</h3>
                    </div>
                    <div class="metric-card">
                        <h6><i class="fas fa-tachometer-alt"></i> 处理速度</h6>
                        <h3 id="processing-speed">0 邮件/分钟</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能监控 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-area"></i> 系统性能监控</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="metrics-container">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>CPU 使用率</h6>
                                    <div class="progress">
                                        <div id="cpu-progress" class="progress-bar bg-info" style="width: 0%">0%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>内存使用率</h6>
                                    <div class="progress">
                                        <div id="memory-progress" class="progress-bar bg-warning" style="width: 0%">0%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>数据库连接</h6>
                                    <span id="db-connections" class="badge bg-primary">0 / 0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>活跃线程</h6>
                                    <span id="active-threads" class="badge bg-success">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-terminal"></i> 实时日志</h5>
                <button id="clear-log-btn" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
            <div class="card-body">
                <div id="log-container" class="log-container">
                    <div>系统就绪，等待测试开始...</div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="card" id="results-container" style="display: none;">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 测试结果统计</h5>
            </div>
            <div class="card-body">
                <div id="test-results">
                    <!-- 动态加载测试结果 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class HighConcurrencyTestConsole {
            constructor() {
                this.isRunning = false;
                this.startTime = null;
                this.statusInterval = null;
                this.metricsInterval = null;
                this.elapsedInterval = null;
                
                this.initializeEvents();
                this.loadInitialData();
            }

            initializeEvents() {
                document.getElementById('start-test-btn').addEventListener('click', () => this.startTest());
                document.getElementById('stop-test-btn').addEventListener('click', () => this.stopTest());
                document.getElementById('refresh-status-btn').addEventListener('click', () => this.refreshStatus());
                document.getElementById('clear-log-btn').addEventListener('click', () => this.clearLog());
            }

            async loadInitialData() {
                await this.refreshStatus();
                await this.loadEmailConfigs();
                await this.scanTestData();
                this.startMetricsMonitoring();
            }

            async refreshStatus() {
                try {
                    // 检查处理器状态
                    const response = await fetch('/api/v2/high-concurrency/email-processing/status');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.updateProcessorStatus(data.status);
                    }
                } catch (error) {
                    this.addLog('错误: 无法获取状态 - ' + error.message, 'error');
                }
            }

            async loadEmailConfigs() {
                try {
                    const response = await fetch('/api/v2/high-concurrency/config');
                    const data = await response.json();
                    
                    if (data.success) {
                        const select = document.getElementById('email-configs');
                        select.innerHTML = '';
                        
                        data.config.available_email_configs.forEach(config => {
                            const option = document.createElement('option');
                            option.value = config.id;
                            option.textContent = `${config.name} (${config.email})`;
                            select.appendChild(option);
                        });
                        
                        // 更新邮箱状态
                        const emailStatus = document.getElementById('email-status');
                        if (data.config.available_email_configs.length > 0) {
                            emailStatus.className = 'badge bg-success';
                            emailStatus.textContent = `${data.config.available_email_configs.length} 个配置`;
                        } else {
                            emailStatus.className = 'badge bg-danger';
                            emailStatus.textContent = '无配置';
                        }
                    }
                } catch (error) {
                    this.addLog('错误: 无法加载邮箱配置 - ' + error.message, 'error');
                }
            }

            async scanTestData() {
                try {
                    const response = await fetch('/api/v2/high-concurrency/test-data/scan');
                    const data = await response.json();
                    
                    if (data.success) {
                        const testDataStatus = document.getElementById('test-data-status');
                        if (data.test_data.excel_files_count > 0) {
                            testDataStatus.className = 'badge bg-success';
                            testDataStatus.textContent = `${data.test_data.excel_files_count} 个文件`;
                        } else {
                            testDataStatus.className = 'badge bg-warning';
                            testDataStatus.textContent = '无测试数据';
                        }
                        
                        this.addLog(`扫描到 ${data.test_data.excel_files_count} 个Excel测试文件`);
                    }
                } catch (error) {
                    this.addLog('错误: 无法扫描测试数据 - ' + error.message, 'error');
                }
            }

            async startTest() {
                if (this.isRunning) return;

                const maxWorkers = parseInt(document.getElementById('max-workers').value);
                const progressInterval = parseInt(document.getElementById('progress-interval').value);
                const selectedConfigs = Array.from(document.getElementById('email-configs').selectedOptions)
                    .map(option => parseInt(option.value));

                try {
                    const response = await fetch('/api/v2/high-concurrency/email-processing/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            max_workers: maxWorkers,
                            progress_interval: progressInterval,
                            config_ids: selectedConfigs.length > 0 ? selectedConfigs : null
                        })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.isRunning = true;
                        this.startTime = new Date();
                        
                        // 更新UI
                        document.getElementById('start-test-btn').disabled = true;
                        document.getElementById('stop-test-btn').disabled = false;
                        document.getElementById('progress-container').style.display = 'block';
                        
                        this.addLog(`高并发测试已启动: ${maxWorkers} 并发, ${progressInterval}s 间隔`);
                        
                        // 开始状态监控
                        this.startStatusMonitoring();
                        this.startElapsedTimer();
                        
                    } else {
                        this.addLog('启动失败: ' + data.message, 'error');
                    }
                } catch (error) {
                    this.addLog('启动错误: ' + error.message, 'error');
                }
            }

            async stopTest() {
                if (!this.isRunning) return;

                try {
                    const response = await fetch('/api/v2/high-concurrency/email-processing/stop', {
                        method: 'POST'
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.addLog('停止请求已发送，等待当前任务完成...');
                    } else {
                        this.addLog('停止失败: ' + data.message, 'error');
                    }
                } catch (error) {
                    this.addLog('停止错误: ' + error.message, 'error');
                }
            }

            startStatusMonitoring() {
                this.statusInterval = setInterval(async () => {
                    await this.updateStatus();
                }, 5000); // 每5秒更新一次状态
            }

            startMetricsMonitoring() {
                this.metricsInterval = setInterval(async () => {
                    await this.updateMetrics();
                }, 10000); // 每10秒更新一次性能指标
            }

            startElapsedTimer() {
                this.elapsedInterval = setInterval(() => {
                    if (this.startTime) {
                        const elapsed = new Date() - this.startTime;
                        const hours = Math.floor(elapsed / 3600000);
                        const minutes = Math.floor((elapsed % 3600000) / 60000);
                        const seconds = Math.floor((elapsed % 60000) / 1000);
                        
                        document.getElementById('elapsed-time').textContent = 
                            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    }
                }, 1000);
            }

            async updateStatus() {
                try {
                    const response = await fetch('/api/v2/high-concurrency/email-processing/status');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.updateProcessorStatus(data.status);
                        
                        // 如果测试完成，停止监控
                        if (!data.status.is_running && this.isRunning) {
                            this.testCompleted();
                        }
                    }
                } catch (error) {
                    this.addLog('状态更新错误: ' + error.message, 'error');
                }
            }

            async updateMetrics() {
                try {
                    const response = await fetch('/api/v2/high-concurrency/performance/metrics');
                    const data = await response.json();
                    
                    if (data.success) {
                        const metrics = data.metrics;
                        
                        // 更新CPU使用率
                        const cpuProgress = document.getElementById('cpu-progress');
                        cpuProgress.style.width = `${metrics.system.cpu_percent}%`;
                        cpuProgress.textContent = `${metrics.system.cpu_percent.toFixed(1)}%`;
                        
                        // 更新内存使用率
                        const memoryProgress = document.getElementById('memory-progress');
                        memoryProgress.style.width = `${metrics.system.memory_percent}%`;
                        memoryProgress.textContent = `${metrics.system.memory_percent.toFixed(1)}%`;
                        
                        // 更新数据库连接
                        document.getElementById('db-connections').textContent = 
                            `${metrics.database.checked_out} / ${metrics.database.pool_size}`;
                        
                        // 更新活跃线程
                        document.getElementById('active-threads').textContent = metrics.process.num_threads;
                        
                        // 更新数据库状态
                        const dbStatus = document.getElementById('db-status');
                        dbStatus.className = 'badge bg-success';
                        dbStatus.textContent = '正常';
                    }
                } catch (error) {
                    const dbStatus = document.getElementById('db-status');
                    dbStatus.className = 'badge bg-danger';
                    dbStatus.textContent = '异常';
                }
            }

            updateProcessorStatus(status) {
                const processorStatus = document.getElementById('processor-status');
                
                if (status.is_running) {
                    processorStatus.className = 'badge bg-success';
                    processorStatus.textContent = '运行中';
                    
                    // 更新进度信息
                    if (status.stats) {
                        const stats = status.stats;
                        const progress = stats.total_emails > 0 ? 
                            (stats.processed_emails / stats.total_emails * 100) : 0;
                        
                        document.getElementById('overall-progress').style.width = `${progress}%`;
                        document.getElementById('overall-progress').textContent = `${progress.toFixed(1)}%`;
                        
                        document.getElementById('processed-count').textContent = stats.processed_emails;
                        document.getElementById('total-count').textContent = stats.total_emails;
                        document.getElementById('success-count').textContent = stats.successful_emails;
                        document.getElementById('failed-count').textContent = stats.failed_emails;
                        
                        // 计算处理速度
                        if (this.startTime) {
                            const elapsed = (new Date() - this.startTime) / 1000 / 60; // 分钟
                            const speed = elapsed > 0 ? (stats.processed_emails / elapsed).toFixed(1) : 0;
                            document.getElementById('processing-speed').textContent = `${speed} 邮件/分钟`;
                        }
                    }
                } else {
                    processorStatus.className = 'badge bg-secondary';
                    processorStatus.textContent = '空闲';
                }
            }

            testCompleted() {
                this.isRunning = false;
                
                // 更新UI
                document.getElementById('start-test-btn').disabled = false;
                document.getElementById('stop-test-btn').disabled = true;
                document.getElementById('test-status').textContent = '已完成';
                
                // 停止监控
                if (this.statusInterval) {
                    clearInterval(this.statusInterval);
                    this.statusInterval = null;
                }
                
                if (this.elapsedInterval) {
                    clearInterval(this.elapsedInterval);
                    this.elapsedInterval = null;
                }
                
                this.addLog('高并发测试已完成！');
                
                // 显示结果
                document.getElementById('results-container').style.display = 'block';
            }

            addLog(message, type = 'info') {
                const logContainer = document.getElementById('log-container');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                
                let color = '#00ff00';
                if (type === 'error') color = '#ff4444';
                else if (type === 'warning') color = '#ffaa00';
                
                logEntry.innerHTML = `<span style="color: #888">[${timestamp}]</span> <span style="color: ${color}">${message}</span>`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            clearLog() {
                document.getElementById('log-container').innerHTML = '<div>日志已清空...</div>';
            }
        }

        // 初始化控制台
        document.addEventListener('DOMContentLoaded', () => {
            new HighConcurrencyTestConsole();
        });
    </script>
</body>
</html>