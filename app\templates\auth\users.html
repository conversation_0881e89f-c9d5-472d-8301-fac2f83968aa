{% extends "base.html" %}

{% block title %}车规芯片终测智能调度平台 - 用户管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 权限不足提示 -->
    <div id="accessDenied" class="row mb-4" style="display: none;">
        <div class="col">
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>权限不足</h4>
                <p>抱歉，只有管理员才能访问用户管理功能。</p>
                <hr>
                <p class="mb-0">如需访问此功能，请联系系统管理员。</p>
            </div>
        </div>
    </div>
    
    <!-- 用户管理内容 -->
    <div id="userManagementContent" class="row mb-4" style="display: none;">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">用户列表</h5>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i>添加用户
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>创建时间</th>
                                    <th>权限设置</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <!-- 用户列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" autocomplete="new-password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required aria-label="用户角色选择">
                            <option value="admin">管理员</option>
                            <option value="boss">boss</option>
                            <option value="Operator">Operator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" name="username">
                    <div class="mb-3">
                        <label class="form-label">新密码（留空表示不修改）</label>
                        <input type="password" class="form-control" id="passwordEdit" name="password" autocomplete="new-password">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required aria-label="用户角色选择">
                            <option value="admin">管理员</option>
                            <option value="boss">boss</option>
                            <option value="Operator">Operator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 权限设置模态框 -->
<div class="modal fade" id="permissionModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>设置用户权限
                    <span id="permissionUserName" class="badge bg-primary ms-2"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="permissionForm">
                    <input type="hidden" name="username">
                    <div id="menuPermissions" class="mb-3">
                        <!-- 菜单权限将通过JavaScript动态加载 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">
                    <i class="fas fa-save me-1"></i>保存权限
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 加载用户列表
function loadUsers() {
    fetch('/api/auth/users')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(users => {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.username}</td>
                    <td>${getRoleName(user.role)}</td>
                    <td>${new Date(user.created_at).toLocaleString()}</td>
                    <td>
                        ${user.role !== 'admin' ? `<button class="btn btn-info btn-sm" onclick="showPermissions('${user.username}')">
                            <i class="fas fa-key me-1"></i>权限设置
                        </button>` : '<span class="text-muted">管理员无需设置</span>'}
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm me-1" onclick="showEditUser('${user.username}', '${user.role}')">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.username}')" ${user.username === 'admin' ? 'disabled' : ''}>
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
            document.getElementById('userTableBody').innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-danger">
                        加载用户列表失败: ${error.message}
                    </td>
                </tr>
            `;
        });
}

// 获取角色名称
function getRoleName(role) {
    const roleNames = {
        'admin': '管理员',
        'boss': 'boss',
        'Operator': 'Operator'
    };
    return roleNames[role] || role;
}

// 添加用户
function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/api/auth/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert(result.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            form.reset();
            loadUsers();
        }
    });
}

// 显示编辑用户模态框
function showEditUser(username, role) {
    const form = document.getElementById('editUserForm');
    form.username.value = username;
    form.role.value = role;
    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

// 更新用户
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    if (!data.password) delete data.password;
    
    fetch(`/api/auth/users/${data.username}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert(result.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            form.reset();
            loadUsers();
        }
    });
}

// 删除用户
function deleteUser(username) {
    if (!confirm('确定要删除此用户吗？')) return;
    
    fetch(`/api/auth/users/${username}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (response.ok) {
            alert('用户删除成功');
            loadUsers();
        } else {
            return response.json().then(data => {
                throw new Error(data.error || '删除失败');
            });
        }
    })
    .catch(error => {
        alert('删除失败: ' + error.message);
        console.error('删除用户错误:', error);
    });
}

// 显示权限设置模态框
function showPermissions(username) {
    console.log('开始设置用户权限:', username);
    
    const form = document.getElementById('permissionForm');
    if (!form) {
        console.error('找不到权限表单元素');
        alert('权限设置界面初始化失败，请刷新页面重试');
        return;
    }
    
    form.username.value = username;
    
    // 显示用户名
    const userNameElement = document.getElementById('permissionUserName');
    if (userNameElement) {
        userNameElement.textContent = username;
    }
    
    // 显示加载状态
    const container = document.getElementById('menuPermissions');
    if (container) {
        container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在加载权限设置...</div>';
    }
    
    // 🔧 修复：正确的权限加载流程 - 先加载菜单配置，再加载用户权限
    Promise.all([
        fetch('/api/v2/auth/menu-settings').then(response => {
            if (!response.ok) {
                throw new Error(`菜单配置加载失败: ${response.status} ${response.statusText}`);
            }
            return response.json();
        }),
        fetch(`/api/v2/auth/users/${username}/permissions`).then(response => {
            if (!response.ok) {
                throw new Error(`用户权限加载失败: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
    ])
        .then(([menus, permissions]) => {
            console.log('菜单配置加载成功:', menus.length, '个菜单项');
            console.log('用户权限加载成功:', permissions.length, '个权限');
            
            if (container) {
                // 使用正确的菜单数据构建权限树
                container.innerHTML = buildPermissionTree(menus, permissions);
                
                // 设置动态菜单映射
                setupDynamicMenuMapping(menus);
            }
            
            // 显示模态框
            const modal = document.getElementById('permissionModal');
            if (modal) {
                new bootstrap.Modal(modal).show();
            } else {
                console.error('找不到权限模态框元素');
                alert('权限设置模态框初始化失败');
            }
        })
        .catch(error => {
            console.error('权限设置加载失败:', error);
            if (container) {
                container.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    权限设置加载失败: ${error.message}
                    <br><small>请检查网络连接或联系管理员</small>
                    <button class="btn btn-sm btn-outline-danger mt-2" onclick="showPermissions('${username}')">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                </div>`;
            }
            alert(`权限设置加载失败: ${error.message}`);
        });
}

// 构建权限树HTML
function buildPermissionTree(menus, permissions) {
    // 🔧 修复：使用动态菜单配置而不是硬编码
    if (!menus || menus.length === 0) {
        return `<div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            菜单配置数据为空，无法显示权限设置
            <br><small>请检查菜单配置是否正确或联系管理员</small>
        </div>`;
    }
    
    try {
    // 构建菜单树结构
    const menuTree = buildMenuTreeStructure(menus);
    
    // 生成权限设置界面HTML
    return buildPermissionHTML(menuTree, permissions);
    } catch (error) {
        console.error('构建权限树失败:', error);
        return `<div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            权限界面构建失败: ${error.message}
            <br><small>请检查数据格式或联系管理员</small>
        </div>`;
    }
}

function buildMenuTreeStructure(menus) {
    // 将扁平的菜单列表转换为树形结构
    const menuMap = {};
    const rootMenus = [];
    
    // 首先创建所有菜单项的映射
    menus.forEach(menu => {
        menuMap[menu.id] = {
            ...menu,
            children: []
        };
    });
    
    // 构建父子关系
    menus.forEach(menu => {
        if (menu.parent_id && menuMap[menu.parent_id]) {
            menuMap[menu.parent_id].children.push(menuMap[menu.id]);
        } else {
            rootMenus.push(menuMap[menu.id]);
        }
    });
    
    // 按order排序
    rootMenus.sort((a, b) => (a.order || 0) - (b.order || 0));
    rootMenus.forEach(menu => {
        menu.children.sort((a, b) => (a.order || 0) - (b.order || 0));
    });
    
    return rootMenus;
}

function buildPermissionHTML(menuTree, permissions) {
    // 🔧 修复：动态生成权限设置界面
    let html = '<div class="accordion" id="permissionAccordion">';
    
    menuTree.forEach((menu, index) => {
        const accordionId = `collapse${index + 1}`;
        const headingId = `heading${index + 1}`;
        const isExpanded = index === 0 ? 'true' : 'false';
        const collapseClass = index === 0 ? '' : 'collapsed';
        const showClass = index === 0 ? 'show' : '';
        
        html += `
        <div class="accordion-item">
            <h2 class="accordion-header" id="${headingId}">
                <button class="accordion-button ${collapseClass}" type="button" 
                        data-bs-toggle="collapse" data-bs-target="#${accordionId}" 
                        aria-expanded="${isExpanded}" aria-controls="${accordionId}">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="menu_${menu.id}" 
                               name="permissions[]" value="${menu.id}"
                               ${permissions.includes(menu.id) ? 'checked' : ''}>
                        <label class="form-check-label" for="menu_${menu.id}">
                            <i class="${menu.icon || 'fas fa-folder'} me-2"></i>${menu.name}
                        </label>
                    </div>
                </button>
            </h2>
            <div id="${accordionId}" class="accordion-collapse collapse ${showClass}" 
                 aria-labelledby="${headingId}" data-bs-parent="#permissionAccordion">
                <div class="accordion-body py-2">
                    <div class="row">`;
        
        // 生成子菜单
        if (menu.children && menu.children.length > 0) {
            const midPoint = Math.ceil(menu.children.length / 2);
            const leftChildren = menu.children.slice(0, midPoint);
            const rightChildren = menu.children.slice(midPoint);
            
            // 左列
            html += '<div class="col-md-6">';
            leftChildren.forEach(child => {
                html += generateChildMenuHTML(child, permissions, 0);
            });
            html += '</div>';
            
            // 右列
            html += '<div class="col-md-6">';
            rightChildren.forEach(child => {
                html += generateChildMenuHTML(child, permissions, 0);
            });
            html += '</div>';
        }
        
        html += `
                    </div>
                </div>
            </div>
        </div>`;
    });
    
    html += `
    </div>

    <div class="mt-3">
        <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="selectAllPermissions()">全选</button>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllPermissions()">取消全选</button>
    </div>`;
    
    return html;
}

function generateChildMenuHTML(menu, permissions, depth) {
    const indent = depth > 0 ? 'ms-3' : '';
    const prefix = depth > 0 ? '├─ ' : '';
    const isBold = menu.children && menu.children.length > 0 ? 'strong' : '';
    
    let html = `
    <div class="form-check mb-2 ${indent}">
        <input type="checkbox" class="form-check-input" id="menu_${menu.id}" 
               name="permissions[]" value="${menu.id}"
               ${permissions.includes(menu.id) ? 'checked' : ''}>
        <label class="form-check-label" for="menu_${menu.id}">
            ${isBold ? '<strong>' : ''}${prefix}${menu.name}${isBold ? '</strong>' : ''}
        </label>
    </div>`;
    
    // 递归生成子菜单
    if (menu.children && menu.children.length > 0) {
        html += '<div class="ms-3">';
        menu.children.forEach((child, index) => {
            const childPrefix = index === menu.children.length - 1 ? '└─ ' : '├─ ';
            html += `
            <div class="form-check mb-2">
                <input type="checkbox" class="form-check-input" id="menu_${child.id}" 
                       name="permissions[]" value="${child.id}"
                       ${permissions.includes(child.id) ? 'checked' : ''}>
                <label class="form-check-label" for="menu_${child.id}">
                    ${childPrefix}${child.name}
                </label>
            </div>`;
        });
        html += '</div>';
    }
    
    return html;
}

// 🔧 修复：更新loadMenuPermissions函数使用动态菜单
function loadMenuPermissions(user) {
    // 先加载菜单配置
    fetch('/api/v2/auth/menu-settings')
        .then(response => response.json())
        .then(menus => {
            // 然后获取用户权限
            return Promise.all([
                Promise.resolve(menus),
                fetch(`/api/v2/auth/users/${user.username}/permissions`).then(res => res.json())
            ]);
        })
        .then(([menus, permissions]) => {
            console.log('菜单配置:', menus);
            console.log('用户权限:', permissions);
            
            document.getElementById('permissionUserName').textContent = user.username;
            document.querySelector('#permissionForm input[name="username"]').value = user.username;
            
            // 使用动态菜单生成权限设置界面
            const permissionHTML = buildPermissionTree(menus, permissions);
            document.getElementById('menuPermissions').innerHTML = permissionHTML;
            
            // 设置动态菜单映射
            setupDynamicMenuMapping(menus);
        })
        .catch(error => {
            console.error('加载菜单权限失败:', error);
            document.getElementById('menuPermissions').innerHTML = 
                '<div class="alert alert-danger">加载菜单配置失败，请刷新页面重试</div>';
        });
}

function setupDynamicMenuMapping(menus) {
    // 🔧 修复：动态构建菜单映射关系
    const menuMap = {};
    
    // 构建父子关系映射
    menus.forEach(menu => {
        if (menu.parent_id) {
            if (!menuMap[menu.parent_id]) {
                menuMap[menu.parent_id] = [];
            }
            menuMap[menu.parent_id].push(menu.id);
        }
    });
    
    // 更新事件监听器
    document.addEventListener('change', function(e) {
        if (e.target.matches('input[type="checkbox"][name="permissions[]"]')) {
            const menuId = parseInt(e.target.value);
            const checked = e.target.checked;
            
            // 如果是父菜单，选择/取消选择所有子菜单
            if (menuMap[menuId]) {
                menuMap[menuId].forEach(childId => {
                    const childCheckbox = document.getElementById(`menu_${childId}`);
                    if (childCheckbox) {
                        childCheckbox.checked = checked;
                    }
                });
            }
        }
    });
}

// 🔧 修复完成：删除所有硬编码菜单内容，使用动态菜单生成

// 全选功能
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('#permissionForm input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

// 取消全选功能
function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('#permissionForm input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

// 保存权限设置
function savePermissions() {
    const form = document.getElementById('permissionForm');
    const username = form.username.value;
    const permissions = Array.from(form.querySelectorAll('input[name="permissions[]"]:checked'))
        .map(input => parseInt(input.value));
    
    // 确保有选中的权限
    if (permissions.length === 0) {
        if (!confirm('您没有选择任何权限，用户将无法访问任何菜单。是否继续？')) {
            return;
        }
    }
    
    // 简化请求数据格式 - 直接使用权限ID数组
    console.log('发送权限数据:', permissions);
    
    fetch(`/api/v2/auth/users/${username}/permissions`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        // 直接发送权限ID数组，包装在两种API支持的格式中
        body: JSON.stringify({
            "permissions": permissions
        })
    })
    .then(response => {
        console.log('服务器响应状态:', response.status);
        // 即使状态码不是200，也尝试解析响应内容，以获取更多错误信息
        return response.json().then(data => {
            if (!response.ok) {
                console.error('服务器返回错误:', data);
                throw new Error(`HTTP error! Status: ${response.status}, 详情: ${JSON.stringify(data)}`);
            }
            return data;
        });
    })
    .then(result => {
        console.log('保存权限成功:', result);
        if (result.error) {
            alert('保存权限失败: ' + result.error);
        } else {
            alert('权限保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            loadUsers();
        }
    })
    .catch(error => {
        console.error('保存权限时出错:', error);
        alert('保存权限时发生错误: ' + error.message);
    });
}

// 检查用户权限
function checkUserPermission() {
    fetch('/api/v2/auth/user/info')
        .then(response => response.json())
        .then(userInfo => {
            if (userInfo.role === 'admin') {
                // 管理员用户，显示用户管理内容
                document.getElementById('userManagementContent').style.display = 'block';
                loadUsers();
            } else {
                // 非管理员用户，显示权限不足提示
                document.getElementById('accessDenied').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('检查用户权限失败:', error);
            // 发生错误时，显示权限不足提示
            document.getElementById('accessDenied').style.display = 'block';
        });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    checkUserPermission();
});

// 🔧 修复：已移除硬编码菜单映射，使用动态菜单映射
// 动态菜单映射在 setupDynamicMenuMapping 函数中处理
</script>
{% endblock %} 