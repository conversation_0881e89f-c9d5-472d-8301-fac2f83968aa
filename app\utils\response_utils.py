# -*- coding: utf-8 -*-
"""
响应工具模块

提供统一的API响应格式，确保所有接口返回一致的数据结构。

Author: AI Assistant
Date: 2025-01-14
"""

from flask import jsonify
from typing import Any, Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)


def success_response(data: Any = None, message: str = "操作成功", 
                    status_code: int = 200, **kwargs) -> tuple:
    """
    成功响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        status_code: HTTP状态码
        **kwargs: 额外的响应字段
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    response_data = {
        "success": True,
        "message": message,
        "data": data,
        "status_code": status_code
    }
    
    # 添加额外字段
    response_data.update(kwargs)
    
    return jsonify(response_data), status_code


def error_response(message: str = "操作失败", status_code: int = 400, 
                  error_code: str = None, details: Any = None, **kwargs) -> tuple:
    """
    错误响应格式
    
    Args:
        message: 错误消息
        status_code: HTTP状态码
        error_code: 错误代码
        details: 错误详情
        **kwargs: 额外的响应字段
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    response_data = {
        "success": False,
        "message": message,
        "status_code": status_code
    }
    
    if error_code:
        response_data["error_code"] = error_code
    
    if details:
        response_data["details"] = details
    
    # 添加额外字段
    response_data.update(kwargs)
    
    # 记录错误日志
    logger.error(f"API错误响应: {message} (状态码: {status_code})")
    
    return jsonify(response_data), status_code


def paginated_response(data: list, page: int, per_page: int, total: int,
                      message: str = "获取数据成功", **kwargs) -> tuple:
    """
    分页响应格式
    
    Args:
        data: 分页数据
        page: 当前页码
        per_page: 每页数量
        total: 总记录数
        message: 响应消息
        **kwargs: 额外的响应字段
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    total_pages = (total + per_page - 1) // per_page  # 向上取整
    
    pagination_info = {
        "page": page,
        "per_page": per_page,
        "total": total,
        "total_pages": total_pages,
        "has_prev": page > 1,
        "has_next": page < total_pages
    }
    
    response_data = {
        "success": True,
        "message": message,
        "data": data,
        "pagination": pagination_info,
        "status_code": 200
    }
    
    # 添加额外字段
    response_data.update(kwargs)
    
    return jsonify(response_data), 200


def validation_error_response(errors: Dict[str, list], 
                            message: str = "数据验证失败") -> tuple:
    """
    数据验证错误响应格式
    
    Args:
        errors: 验证错误字典 {字段名: [错误消息列表]}
        message: 响应消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        status_code=422,
        error_code="VALIDATION_ERROR",
        details=errors
    )


def not_found_response(resource: str = "资源") -> tuple:
    """
    资源未找到响应格式
    
    Args:
        resource: 资源名称
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=f"{resource}不存在",
        status_code=404,
        error_code="NOT_FOUND"
    )


def unauthorized_response(message: str = "未授权访问") -> tuple:
    """
    未授权响应格式
    
    Args:
        message: 响应消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        status_code=401,
        error_code="UNAUTHORIZED"
    )


def forbidden_response(message: str = "禁止访问") -> tuple:
    """
    禁止访问响应格式
    
    Args:
        message: 响应消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        status_code=403,
        error_code="FORBIDDEN"
    )


def internal_error_response(message: str = "内部服务器错误") -> tuple:
    """
    内部服务器错误响应格式
    
    Args:
        message: 响应消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        status_code=500,
        error_code="INTERNAL_ERROR"
    )


def rate_limit_response(message: str = "请求过于频繁") -> tuple:
    """
    请求频率限制响应格式
    
    Args:
        message: 响应消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        status_code=429,
        error_code="RATE_LIMIT_EXCEEDED"
    )


def custom_response(success: bool, message: str, data: Any = None,
                   status_code: int = 200, **kwargs) -> tuple:
    """
    自定义响应格式
    
    Args:
        success: 是否成功
        message: 响应消息
        data: 响应数据
        status_code: HTTP状态码
        **kwargs: 额外的响应字段
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    response_data = {
        "success": success,
        "message": message,
        "data": data,
        "status_code": status_code
    }
    
    # 添加额外字段
    response_data.update(kwargs)
    
    return jsonify(response_data), status_code


# 响应装饰器
def api_response(func):
    """
    API响应装饰器，自动处理异常并返回统一格式
    """
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 如果返回的是tuple，直接返回
            if isinstance(result, tuple):
                return result
            
            # 如果返回的是字典，包装成成功响应
            if isinstance(result, dict):
                return success_response(data=result)
            
            # 其他情况，包装成成功响应
            return success_response(data=result)
            
        except ValueError as e:
            return validation_error_response({"error": [str(e)]})
        except PermissionError as e:
            return forbidden_response(str(e))
        except FileNotFoundError as e:
            return not_found_response(str(e))
        except Exception as e:
            logger.exception(f"API异常: {e}")
            return internal_error_response(f"操作失败: {str(e)}")
    
    return wrapper


# 常用响应常量
class ResponseMessages:
    """响应消息常量"""
    
    # 成功消息
    SUCCESS = "操作成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    RETRIEVED = "获取成功"
    
    # 错误消息
    ERROR = "操作失败"
    NOT_FOUND = "资源不存在"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "禁止访问"
    VALIDATION_ERROR = "数据验证失败"
    INTERNAL_ERROR = "内部服务器错误"
    RATE_LIMIT = "请求过于频繁"
    
    # 业务消息
    LOGIN_SUCCESS = "登录成功"
    LOGIN_FAILED = "登录失败"
    LOGOUT_SUCCESS = "退出成功"
    PASSWORD_CHANGED = "密码修改成功"
    DATA_IMPORTED = "数据导入成功"
    DATA_EXPORTED = "数据导出成功"
    SCHEDULE_GENERATED = "排产计划生成成功"
    PRIORITY_UPDATED = "优先级更新成功"


class ResponseCodes:
    """响应代码常量"""
    
    # 成功代码
    SUCCESS = "SUCCESS"
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    DELETED = "DELETED"
    
    # 错误代码
    VALIDATION_ERROR = "VALIDATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    
    # 业务代码
    LOGIN_FAILED = "LOGIN_FAILED"
    PASSWORD_INVALID = "PASSWORD_INVALID"
    DATA_CONFLICT = "DATA_CONFLICT"
    RESOURCE_LOCKED = "RESOURCE_LOCKED" 