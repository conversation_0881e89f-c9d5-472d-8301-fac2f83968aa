"""
标准化连接池配置 - 确保所有服务器连接行为一致
"""

import os
import logging

logger = logging.getLogger(__name__)

class StandardizedPoolConfig:
    """标准化连接池配置"""
    
    # 🔧 硬件无关的固定配置
    STANDARD_CONFIG = {
        # 连接池大小 - 固定配置避免硬件差异影响
        'MIN_POOL_SIZE': 2,      # 最小连接数
        'INIT_POOL_SIZE': 4,     # 初始连接数  
        'MAX_POOL_SIZE': 8,      # 最大连接数（避免高配置服务器创建过多连接）
        
        # 超时配置 - 统一时间避免差异
        'CONNECTION_TIMEOUT': 10,  # 连接超时（秒）
        'POOL_TIMEOUT': 5,        # 获取连接超时（秒）
        'QUERY_TIMEOUT': 30,      # 查询超时（秒）
        
        # 重试配置
        'RETRY_TIMES': 3,         # 重试次数
        'RETRY_DELAY': 1,         # 重试间隔（秒）
        
        # MySQL配置标准化
        'MYSQL_CONFIG': {
            'charset': 'utf8mb4',
            'autocommit': True,
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
            'time_zone': '+08:00',  # 固定时区
            'max_allowed_packet': 64 * 1024 * 1024,  # 64MB
        }
    }
    
    @classmethod
    def get_standardized_config(cls) -> dict:
        """获取标准化配置"""
        config = cls.STANDARD_CONFIG.copy()
        
        # 允许环境变量覆盖（用于调试）
        if os.environ.get('APS_POOL_DEBUG', 'false').lower() == 'true':
            config['MIN_POOL_SIZE'] = 1
            config['INIT_POOL_SIZE'] = 2
            config['MAX_POOL_SIZE'] = 4
            logger.info("🔧 使用调试模式连接池配置")
        
        return config
    
    @classmethod
    def validate_environment(cls) -> bool:
        """验证环境配置一致性"""
        required_vars = ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"缺少必要环境变量: {missing_vars}")
            return False
        
        logger.info("✅ 环境配置验证通过")
        return True

def get_standard_pool_config() -> dict:
    """获取标准连接池配置（便捷函数）"""
    return StandardizedPoolConfig.get_standardized_config()

def validate_pool_environment() -> bool:
    """验证连接池环境（便捷函数）"""
    return StandardizedPoolConfig.validate_environment()
