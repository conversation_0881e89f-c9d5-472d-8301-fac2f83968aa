﻿{% extends "base.html" %}

{% block title %}AEC-FT Intelligent Commander Platform - AI Assistant{% endblock %}

{% block page_title %}AI Assistant{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- AI Assistant Selection Cards - Compressed -->
        <div class="col-12 mb-2">
            <div class="card">
                <div class="card-header bg-danger text-white py-2">
                    <h6 class="mb-0"><i class="fas fa-robot me-2"></i>AI Assistant</h6>
                </div>
                <div class="card-body py-2">
                    <div class="row">
                        <!-- Training AI Assistant -->
                        <div class="col-md-6">
                            <div class="card h-100 ai-assistant-card compact-card">
                                <div class="card-body text-center py-2">
                                    <div class="ai-icon mb-2">
                                        <i class="fas fa-graduation-cap fa-2x text-primary"></i>
                                    </div>
                                    <h6 class="card-title mb-1">Training AI Assistant</h6>
                                    <p class="card-text text-muted small mb-2">
                                        Training guidance and platform knowledge Q&A
                                    </p>
                                    <div class="ai-status mb-2">
                                        <span class="badge bg-secondary small" id="training-status">Checking...</span>
                                    </div>
                                    <button class="btn btn-primary btn-sm ai-select-btn" onclick="showTrainingAI()">
                                        <i class="fas fa-comments me-1"></i>Start Chat
                                    </button>
                                </div>
                            </div>
                        </div>
                    
                        <!-- Progress Query AI Assistant -->
                        <div class="col-md-6">
                            <div class="card h-100 ai-assistant-card compact-card">
                                <div class="card-body text-center py-2">
                                    <div class="ai-icon mb-2">
                                        <i class="fas fa-chart-line fa-2x text-success"></i>
                                    </div>
                                    <h6 class="card-title mb-1">Progress Query AI</h6>
                                    <p class="card-text text-muted small mb-2">
                                        Production progress queries and equipment monitoring
                                    </p>
                                    <div class="ai-status mb-2">
                                        <span class="badge bg-secondary small" id="progress-status">Checking...</span>
                                    </div>
                                    <button class="btn btn-success btn-sm ai-select-btn" onclick="showProgressAI()">
                                        <i class="fas fa-search me-1"></i>Start Query
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    
        <!-- AI Chat Window -->
        <div class="col-12" id="ai-chat-container" style="display: none;">
            <div class="card">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0" id="current-ai-title">AI Assistant Chat</h5>
                        <small id="current-ai-description">Select an AI assistant to start chatting</small>
                            </div>
                    <div>
                        <button class="btn btn-sm btn-light me-2" onclick="refreshChat()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-light" onclick="closeChat()">
                            <i class="fas fa-times me-1"></i>Close
                        </button>
                    </div>
                    </div>
                <div class="card-body p-0">
                    <!-- Dify Chat Window -->
                    <div id="dify-iframe-container" style="width: 100%; height: 800px; position: relative;">
                        <!-- iframe will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* AI Assistant Card Styles */
.ai-assistant-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.ai-assistant-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #dee2e6;
}

.ai-assistant-card .ai-icon {
    transition: transform 0.3s ease;
}

.ai-assistant-card:hover .ai-icon {
    transform: scale(1.05);
}

.ai-select-btn {
    min-width: 120px;
}

.ai-select-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Compact Card Styles */
.compact-card {
    max-height: 180px;
}

.compact-card .card-body {
    padding: 0.75rem 1rem;
}

.compact-card .card-title {
    font-size: 1rem;
    font-weight: 600;
}

.compact-card .card-text {
    font-size: 0.85rem;
    line-height: 1.3;
}

.compact-card .ai-icon i {
    font-size: 1.8rem !important;
}

.compact-card .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Status Badge Styles */
.ai-status .badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-assistant-card {
        margin-bottom: 0.5rem;
    }
    
    .compact-card {
        max-height: 160px;
    }
    
    #dify-iframe-container {
        height: 700px !important;
    }
}

/* iframe Container Styles */
#dify-iframe-container iframe {
        width: 100%;
    height: 100%;
    border: none;
        background-color: #f8f9fa;
    }
</style>

<script>
// Global variable to store AI configurations
let aiConfigs = {};

// Load AI configurations when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAIConfigs();
});

async function loadAIConfigs() {
    try {
        const response = await fetch('/api/v2/system/ai-settings');
        const data = await response.json();
        
                if (data.training_ai || data.progress_ai) {
            aiConfigs = data;
            updateAIStatus();
        } else {
            console.error('Failed to load AI config:', data.error || 'Invalid data format');
            showAllAsNotConfigured();
        }
    } catch (error) {
        console.error('Failed to load AI status:', error);
        showAllAsNotConfigured();
    }
}

function updateAIStatus() {
    // Update Training AI status
    const trainingConfig = aiConfigs.training_ai || {};
    const trainingEnabled = trainingConfig.enabled && 
                           trainingConfig.server_url && 
                           trainingConfig.api_key && 
                           trainingConfig.app_id;
    
    updateAICard('training', trainingEnabled);

    // Update Progress AI status
    const progressConfig = aiConfigs.progress_ai || {};
    const progressEnabled = progressConfig.enabled && 
                           progressConfig.server_url && 
                           progressConfig.api_key && 
                           progressConfig.app_id;
    
    updateAICard('progress', progressEnabled);
}

function updateAICard(type, enabled) {
    const statusElement = document.getElementById(`${type}-status`);
    const buttonElement = document.querySelector(`button[onclick="show${type === 'training' ? 'Training' : 'Progress'}AI()"]`);
    
    if (enabled) {
        statusElement.textContent = 'Configured';
        statusElement.className = 'badge bg-success';
        buttonElement.disabled = false;
            } else {
        statusElement.textContent = 'Not Configured';
        statusElement.className = 'badge bg-secondary';
        buttonElement.disabled = true;
    }
}

function showAllAsNotConfigured() {
    ['training', 'progress'].forEach(type => {
        updateAICard(type, false);
    });
}

function showTrainingAI() {
    const config = aiConfigs.training_ai;
    
    if (!config || !config.enabled) {
        showConfigModal();
        return;
    }

    showChatInterface('training', config);
}

function showProgressAI() {
    const config = aiConfigs.progress_ai;
    
    if (!config || !config.enabled) {
        showConfigModal();
        return;
    }

    showChatInterface('progress', config);
}

function showChatInterface(type, config) {
    const container = document.getElementById('ai-chat-container');
    const titleElement = document.getElementById('current-ai-title');
    const descElement = document.getElementById('current-ai-description');
    const iframeContainer = document.getElementById('dify-iframe-container');

    // Set title and description
    if (type === 'training') {
        titleElement.textContent = 'Training AI Assistant';
        descElement.textContent = 'Professional training guidance and knowledge Q&A';
                } else {
        titleElement.textContent = 'Progress Query AI Assistant';
        descElement.textContent = 'Real-time production data query and analysis';
    }

    // Get chatbot URL
    const chatbotUrl = getChatbotUrl(config, type);
    
    // Clear container and show loading state
    iframeContainer.innerHTML = `
        <div class="d-flex justify-content-center align-items-center" style="height: 800px;">
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Loading AI Assistant...</div>
            </div>
        </div>
    `;
            
    // Show chat interface
    container.style.display = 'block';
    container.scrollIntoView({ behavior: 'smooth' });

    // Delayed iframe loading
    setTimeout(() => {
        iframeContainer.innerHTML = `
            <iframe
                src="${chatbotUrl}"
                style="width: 100%; height: 100%; min-height: 800px"
                frameborder="0"
                allow="microphone">
            </iframe>
        `;
    }, 500);
}

function getChatbotUrl(config, type) {
    // Use configured chatbot URL first
    if (config && config.chatbot_url) {
        return config.chatbot_url;
    }
    
    // Use default URL if not configured
    if (type === 'training') {
        return 'https://udify.app/chatbot/uluSs0xDCF8cY734';
                    } else {
        return 'https://udify.app/chatbot/uluSs0xDCF8cY734';
    }
}

function refreshChat() {
    const iframe = document.querySelector('#dify-iframe-container iframe');
    if (iframe) {
        iframe.src = iframe.src;
    }
}

function closeChat() {
    const container = document.getElementById('ai-chat-container');
    container.style.display = 'none';
    
    // Clear iframe container
    document.getElementById('dify-iframe-container').innerHTML = '';
}

function showConfigModal() {
    alert('This AI assistant is not configured or not enabled. Please go to system settings to configure it.\\n\\nClick OK to open system settings page.');
    window.open('/system/settings', '_blank');
}
</script>
{% endblock %} 