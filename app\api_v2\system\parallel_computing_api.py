#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Task 2.3 并行计算API接口 - Phase 2 智能排产系统优化

API功能：
1. 并行引擎状态监控
2. 并行计算性能统计
3. 并行任务管理
4. 计算资源管理
5. 并行计算基准测试
"""

from flask import Blueprint, jsonify, request
import logging
import time
from datetime import datetime
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

# 创建蓝图
parallel_computing_bp = Blueprint('parallel_computing', __name__)

# 全局并行引擎实例
_parallel_engine = None

def get_parallel_engine():
    """获取并行引擎实例"""
    global _parallel_engine
    if _parallel_engine is None:
        try:
            from app.services.parallel_scheduling_engine import ParallelSchedulingEngine
            
            # 尝试集成多级缓存
            try:
                from app.services.multilevel_cache_manager import MultilevelCacheManager
                cache_manager = MultilevelCacheManager()
                _parallel_engine = ParallelSchedulingEngine(multilevel_cache=cache_manager)
                logger.info("✅ 并行引擎已集成多级缓存")
            except Exception as e:
                logger.warning(f"多级缓存集成失败: {e}")
                _parallel_engine = ParallelSchedulingEngine()
                
        except Exception as e:
            logger.error(f"并行引擎初始化失败: {e}")
            return None
    
    return _parallel_engine

@parallel_computing_bp.route('/status', methods=['GET'])
def get_parallel_engine_status():
    """获取并行引擎状态"""
    try:
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎初始化失败',
                'data': None
            }), 500
        
        # 获取引擎状态
        status_data = {
            'engine_running': engine._running,
            'cpu_count': engine.cpu_count,
            'max_thread_workers': engine.max_thread_workers,
            'max_process_workers': engine.max_process_workers,
            'execution_mode': engine.execution_mode,
            'thread_pool_active': engine.thread_pool is not None,
            'process_pool_active': engine.process_pool is not None,
            'active_tasks': len(engine._active_tasks),
            'queued_tasks': engine._task_queue.qsize(),
            'cache_integration': engine.multilevel_cache is not None,
            'last_check': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'message': '并行引擎状态获取成功',
            'data': status_data
        })
        
    except Exception as e:
        logger.error(f"获取并行引擎状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/performance', methods=['GET'])
def get_parallel_performance_stats():
    """获取并行计算性能统计"""
    try:
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎未初始化',
                'data': None
            }), 500
        
        # 获取性能统计
        performance_stats = engine.get_performance_stats()
        
        return jsonify({
            'success': True,
            'message': '性能统计获取成功',
            'data': performance_stats
        })
        
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取性能统计失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/start', methods=['POST'])
def start_parallel_engine():
    """启动并行引擎"""
    try:
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎初始化失败',
                'data': None
            }), 500
        
        if engine._running:
            return jsonify({
                'success': True,
                'message': '并行引擎已在运行中',
                'data': {'status': 'running'}
            })
        
        # 启动引擎
        engine.start()
        
        return jsonify({
            'success': True,
            'message': '并行引擎启动成功',
            'data': {
                'status': 'started',
                'thread_workers': engine.max_thread_workers,
                'process_workers': engine.max_process_workers
            }
        })
        
    except Exception as e:
        logger.error(f"启动并行引擎失败: {e}")
        return jsonify({
            'success': False,
            'message': f'启动失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/stop', methods=['POST'])
def stop_parallel_engine():
    """停止并行引擎"""
    try:
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎未初始化',
                'data': None
            }), 500
        
        if not engine._running:
            return jsonify({
                'success': True,
                'message': '并行引擎已停止',
                'data': {'status': 'stopped'}
            })
        
        # 停止引擎
        engine.stop()
        
        return jsonify({
            'success': True,
            'message': '并行引擎停止成功',
            'data': {'status': 'stopped'}
        })
        
    except Exception as e:
        logger.error(f"停止并行引擎失败: {e}")
        return jsonify({
            'success': False,
            'message': f'停止失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/benchmark', methods=['POST'])
def run_parallel_benchmark():
    """运行并行计算基准测试"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        batch_count = data.get('batch_count', 100)
        equipment_count = data.get('equipment_count', 20)
        algorithm_type = data.get('algorithm_type', 'heuristic')
        
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎未初始化',
                'data': None
            }), 500
        
        # 确保引擎运行
        if not engine._running:
            engine.start()
        
        # 生成测试数据
        test_lots = _generate_test_lots(batch_count)
        test_equipment = _generate_test_equipment(equipment_count)
        
        logger.info(f"🚀 开始并行计算基准测试: {batch_count}批次 x {equipment_count}设备")
        
        # 并行计算基准测试
        start_time = time.time()
        
        # 执行并行设备评分
        scoring_results = engine.parallel_equipment_scoring(
            lots=test_lots,
            equipment=test_equipment,
            algorithm_type=algorithm_type
        )
        
        parallel_time = time.time() - start_time
        
        # 串行计算对比测试
        start_time = time.time()
        serial_results = engine._serial_equipment_scoring(
            lots=test_lots,
            equipment=test_equipment,
            algorithm_type=algorithm_type
        )
        serial_time = time.time() - start_time
        
        # 计算性能指标
        speedup_ratio = serial_time / parallel_time if parallel_time > 0 else 0
        efficiency = speedup_ratio / engine.max_thread_workers if engine.max_thread_workers > 0 else 0
        
        benchmark_results = {
            'test_parameters': {
                'batch_count': batch_count,
                'equipment_count': equipment_count,
                'algorithm_type': algorithm_type,
                'total_calculations': len(test_lots) * len(test_equipment)
            },
            'parallel_performance': {
                'execution_time': parallel_time,
                'results_count': len(scoring_results),
                'calculations_per_second': len(scoring_results) / parallel_time if parallel_time > 0 else 0,
                'thread_workers': engine.max_thread_workers
            },
            'serial_performance': {
                'execution_time': serial_time,
                'results_count': len(serial_results),
                'calculations_per_second': len(serial_results) / serial_time if serial_time > 0 else 0
            },
            'performance_comparison': {
                'speedup_ratio': speedup_ratio,
                'efficiency_percentage': efficiency * 100,
                'time_saved_percentage': ((serial_time - parallel_time) / serial_time * 100) if serial_time > 0 else 0
            },
            'test_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ 基准测试完成: 加速比{speedup_ratio:.2f}x, 效率{efficiency*100:.1f}%")
        
        return jsonify({
            'success': True,
            'message': '并行计算基准测试完成',
            'data': benchmark_results
        })
        
    except Exception as e:
        logger.error(f"并行计算基准测试失败: {e}")
        return jsonify({
            'success': False,
            'message': f'基准测试失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/equipment-scoring', methods=['POST'])
def execute_parallel_equipment_scoring():
    """执行并行设备匹配评分"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        lots = data.get('lots', [])
        equipment = data.get('equipment', [])
        algorithm_type = data.get('algorithm_type', 'heuristic')
        
        if not lots or not equipment:
            return jsonify({
                'success': False,
                'message': '批次列表或设备列表不能为空',
                'data': None
            }), 400
        
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎未初始化',
                'data': None
            }), 500
        
        # 确保引擎运行
        if not engine._running:
            engine.start()
        
        logger.info(f"🚀 执行并行设备匹配评分: {len(lots)}批次 x {len(equipment)}设备")
        
        # 执行并行计算
        start_time = time.time()
        scoring_results = engine.parallel_equipment_scoring(
            lots=lots,
            equipment=equipment,
            algorithm_type=algorithm_type
        )
        execution_time = time.time() - start_time
        
        # 获取性能统计
        performance_stats = engine.get_performance_stats()
        
        result_data = {
            'scoring_results': scoring_results,
            'execution_summary': {
                'total_time': execution_time,
                'results_count': len(scoring_results),
                'calculations_per_second': len(scoring_results) / execution_time if execution_time > 0 else 0,
                'algorithm_type': algorithm_type
            },
            'performance_stats': performance_stats
        }
        
        logger.info(f"✅ 并行设备评分完成: {len(scoring_results)}个结果, 耗时{execution_time:.3f}s")
        
        return jsonify({
            'success': True,
            'message': '并行设备匹配评分完成',
            'data': result_data
        })
        
    except Exception as e:
        logger.error(f"并行设备匹配评分失败: {e}")
        return jsonify({
            'success': False,
            'message': f'并行计算失败: {str(e)}',
            'data': None
        }), 500

@parallel_computing_bp.route('/lot-optimization', methods=['POST'])
def execute_parallel_lot_optimization():
    """执行并行批次优化"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        lots = data.get('lots', [])
        constraints = data.get('constraints', {})
        algorithm_type = data.get('algorithm_type', 'heuristic')
        
        if not lots:
            return jsonify({
                'success': False,
                'message': '批次列表不能为空',
                'data': None
            }), 400
        
        engine = get_parallel_engine()
        if not engine:
            return jsonify({
                'success': False,
                'message': '并行引擎未初始化',
                'data': None
            }), 500
        
        # 确保引擎运行
        if not engine._running:
            engine.start()
        
        logger.info(f"🚀 执行并行批次优化: {len(lots)}个批次")
        
        # 执行并行优化
        start_time = time.time()
        optimized_lots = engine.parallel_lot_optimization(
            lots=lots,
            constraints=constraints,
            algorithm_type=algorithm_type
        )
        execution_time = time.time() - start_time
        
        result_data = {
            'optimized_lots': optimized_lots,
            'execution_summary': {
                'total_time': execution_time,
                'original_count': len(lots),
                'optimized_count': len(optimized_lots),
                'optimization_rate': len(optimized_lots) / len(lots) if lots else 0,
                'algorithm_type': algorithm_type
            }
        }
        
        logger.info(f"✅ 并行批次优化完成: {len(optimized_lots)}个优化结果, 耗时{execution_time:.3f}s")
        
        return jsonify({
            'success': True,
            'message': '并行批次优化完成',
            'data': result_data
        })
        
    except Exception as e:
        logger.error(f"并行批次优化失败: {e}")
        return jsonify({
            'success': False,
            'message': f'批次优化失败: {str(e)}',
            'data': None
        }), 500

def _generate_test_lots(count: int) -> List[Dict]:
    """生成测试批次数据"""
    test_lots = []
    for i in range(count):
        lot = {
            'LOT_ID': f'TEST_LOT_{i:04d}',
            'DEVICE': f'TEST_DEVICE_{i % 10}',
            'PRIORITY': 50 + (i % 50),
            'QTY': 1000 + (i % 500),
            'RECIPE': f'RECIPE_{i % 20}',
            'created_at': datetime.now().isoformat()
        }
        test_lots.append(lot)
    return test_lots

def _generate_test_equipment(count: int) -> List[Dict]:
    """生成测试设备数据"""
    test_equipment = []
    for i in range(count):
        equipment = {
            'EQP_ID': f'EQP_{i:03d}',
            'EQP_NAME': f'TEST_DEVICE_{i % 10}_EQP_{i}',
            'EQP_STATUS': 'IDLE',
            'CAPABILITY': f'TEST_DEVICE_{i % 10}',
            'UPH': 100 + (i % 200),
            'created_at': datetime.now().isoformat()
        }
        test_equipment.append(equipment)
    return test_equipment 