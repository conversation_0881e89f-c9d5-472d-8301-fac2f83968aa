#!/usr/bin/env python3
"""
检查测试规范数据的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.db_connection_pool import get_connection

def check_test_specs():
    """检查测试规范数据"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # 检查et_ft_test_spec表的数据
        cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec")
        total_specs = cursor.fetchone()[0]
        print(f"📊 et_ft_test_spec表总记录数: {total_specs}")
        
        if total_specs > 0:
            # 检查DEVICE分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                GROUP BY DEVICE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            devices = cursor.fetchall()
            print("\n📋 DEVICE分布（前10）:")
            for device in devices:
                print(f"  - {device[0]}: {device[1]} 条记录")
            
            # 检查STAGE分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                GROUP BY STAGE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            stages = cursor.fetchall()
            print("\n📋 STAGE分布（前10）:")
            for stage in stages:
                print(f"  - {stage[0]}: {stage[1]} 条记录")
            
            # 检查APPROVAL状态
            cursor.execute("""
                SELECT APPROVAL, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                GROUP BY APPROVAL 
                ORDER BY cnt DESC
            """)
            approvals = cursor.fetchall()
            print("\n📋 APPROVAL状态分布:")
            for approval in approvals:
                print(f"  - {approval[0]}: {approval[1]} 条记录")
        
        # 检查et_wait_lot表中的DEVICE/STAGE
        cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
        total_lots = cursor.fetchone()[0]
        print(f"\n📦 et_wait_lot表总记录数: {total_lots}")
        
        if total_lots > 0:
            # 检查批次的DEVICE分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as cnt 
                FROM et_wait_lot 
                GROUP BY DEVICE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            lot_devices = cursor.fetchall()
            print("\n📋 批次DEVICE分布（前10）:")
            for device in lot_devices:
                print(f"  - {device[0]}: {device[1]} 个批次")
            
            # 检查批次的STAGE分布
            cursor.execute("""
                SELECT STAGE, COUNT(*) as cnt 
                FROM et_wait_lot 
                GROUP BY STAGE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            lot_stages = cursor.fetchall()
            print("\n📋 批次STAGE分布（前10）:")
            for stage in lot_stages:
                print(f"  - {stage[0]}: {stage[1]} 个批次")
        
        # 检查匹配情况
        cursor.execute("""
            SELECT COUNT(DISTINCT l.DEVICE, l.STAGE) as lot_combinations,
                   COUNT(DISTINCT s.DEVICE, s.STAGE) as spec_combinations,
                   COUNT(DISTINCT l.DEVICE, l.STAGE, s.DEVICE, s.STAGE) as matched_combinations
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE AND UPPER(l.STAGE) = UPPER(s.STAGE)
        """)
        match_result = cursor.fetchone()
        print(f"\n🔍 匹配分析:")
        print(f"  - 批次DEVICE+STAGE组合数: {match_result[0]}")
        print(f"  - 测试规范DEVICE+STAGE组合数: {match_result[1]}")
        print(f"  - 匹配的组合数: {match_result[2]}")
        
        # 检查具体的不匹配案例
        cursor.execute("""
            SELECT l.DEVICE, l.STAGE, COUNT(*) as lot_count
            FROM et_wait_lot l
            LEFT JOIN et_ft_test_spec s ON l.DEVICE = s.DEVICE AND UPPER(l.STAGE) = UPPER(s.STAGE)
            WHERE s.DEVICE IS NULL
            GROUP BY l.DEVICE, l.STAGE
            ORDER BY lot_count DESC
            LIMIT 5
        """)
        unmatched = cursor.fetchall()
        if unmatched:
            print(f"\n❌ 无匹配测试规范的批次（前5）:")
            for case in unmatched:
                print(f"  - {case[0]}/{case[1]}: {case[2]} 个批次")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_test_specs()
