#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表配置管理 - 为11个子菜单页面提供统一的配置管理
支持字段显示规则、业务逻辑、特殊样式等个性化配置
"""

# 11个表的完整配置映射
TABLE_CONFIGS = {
    'eqp_status': {
        'title': '设备状态管理',
        'icon': 'fas fa-microchip',
        'description': '世界领先的半导体设备状态管理系统，支持实时监控、智能调度和预测性维护。',
        'features': ['实时监控', '智能调度', '预测性维护', '状态分析'],
        'display_fields': ['LOT_ID', 'HANDLER_ID', 'STATUS', 'EQP_CLASS', 'EVENT_TIME'],
        'hidden_fields': ['id', 'created_at', 'updated_at'],
        'readonly_fields': ['id', 'created_at', 'updated_at'],
        'required_fields': ['LOT_ID', 'HANDLER_ID', 'STATUS'],
        'searchable_fields': ['LOT_ID', 'HANDLER_ID', 'EQP_CLASS', 'STATUS'],
        'sortable_fields': ['LOT_ID', 'HANDLER_ID', 'STATUS', 'EVENT_TIME'],
        'field_types': {
            'STATUS': 'status_badge',
            'EVENT_TIME': 'datetime'
        },
        'status_mapping': {
            'ONLINE': {'class': 'success', 'text': '在线'},
            'OFFLINE': {'class': 'danger', 'text': '离线'},
            'MAINTENANCE': {'class': 'warning', 'text': '维护中'},
            'ERROR': {'class': 'danger', 'text': '故障'}
        }
    },
    
    'et_uph_eqp': {
        'title': 'UPH设备管理',
        'icon': 'fas fa-tachometer-alt',
        'description': '专业的UPH设备管理系统，支持产能分析和性能优化。',
        'features': ['产能分析', '性能优化', 'UPH监控', '效率统计'],
        'business_key': 'EQP_ID',
        'display_fields': ['EQP_ID', 'PROD_ID', 'UPH', 'EFFICIENCY', 'UPDATE_TIME'],
        'hidden_fields': ['id', 'created_at'],
        'readonly_fields': ['id', 'created_at'],
        'required_fields': ['EQP_ID', 'PROD_ID', 'UPH'],
        'searchable_fields': ['EQP_ID', 'PROD_ID'],
        'sortable_fields': ['EQP_ID', 'PROD_ID', 'UPH', 'EFFICIENCY'],
        'field_types': {
            'UPH': 'number',
            'EFFICIENCY': 'percentage',
            'UPDATE_TIME': 'datetime',
            'EQP_ID': 'business_key'
        }
    },
    
    'et_ft_test_spec': {
        'title': '测试规范管理',
        'icon': 'fas fa-file-alt',
        'description': '现代化测试规范管理系统，支持规格版本控制和审批流程。',
        'features': ['版本控制', '审批流程', '规格管理', '测试配置'],
        'business_key': 'SPEC_ID',
        'display_fields': ['SPEC_ID', 'SPEC_NAME', 'VERSION', 'STATUS', 'CREATE_TIME'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id', 'CREATE_TIME'],
        'required_fields': ['SPEC_ID', 'SPEC_NAME', 'VERSION'],
        'searchable_fields': ['SPEC_ID', 'SPEC_NAME', 'VERSION'],
        'sortable_fields': ['SPEC_ID', 'SPEC_NAME', 'VERSION', 'CREATE_TIME'],
        'field_types': {
            'STATUS': 'status_badge',
            'CREATE_TIME': 'datetime',
            'VERSION': 'version',
            'SPEC_ID': 'business_key'
        },
        'status_mapping': {
            'ACTIVE': {'class': 'success', 'text': '激活'},
            'DRAFT': {'class': 'warning', 'text': '草稿'},
            'ARCHIVED': {'class': 'secondary', 'text': '归档'}
        }
    },
    
    'ct': {
        'title': '产品周期管理',
        'icon': 'fas fa-clock',
        'description': '智能产品生产周期跟踪系统，支持周期时间和良率分析。',
        'features': ['周期跟踪', '良率分析', '时间统计', '性能监控'],
        'business_key': 'PROD_ID',
        'display_fields': ['PROD_ID', 'CYCLE_TIME', 'YIELD_RATE', 'STATUS', 'UPDATE_TIME'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id', 'UPDATE_TIME'],
        'required_fields': ['PROD_ID', 'CYCLE_TIME'],
        'searchable_fields': ['PROD_ID'],
        'sortable_fields': ['PROD_ID', 'CYCLE_TIME', 'YIELD_RATE', 'UPDATE_TIME'],
        'field_types': {
            'CYCLE_TIME': 'duration',
            'YIELD_RATE': 'percentage',
            'UPDATE_TIME': 'datetime',
            'STATUS': 'status_badge',
            'PROD_ID': 'business_key'
        }
    },
    
    'tcc_inv': {
        'title': 'TCC硬件资源管理',
        'icon': 'fas fa-microscope',
        'description': '专业的测试硬件资源管理系统，基于TCC表提供库存跟踪和资源调度。',
        'features': ['硬件库存', '资源调度', '生命周期管理', '位置跟踪'],
        'business_key': 'Num',
        'display_fields': ['Num', 'Type', 'Quantity', 'State', 'BinCode', 'WhsCode', 'OwnerName', 'CurrentCycleLife', 'UpdateTime'],
        'hidden_fields': [],
        'readonly_fields': ['CreateTime', 'UpdateTime'],
        'required_fields': ['Num', 'Type', 'Quantity'],
        'searchable_fields': ['Num', 'Type', 'BinCode', 'WhsCode', 'OwnerName'],
        'sortable_fields': ['Num', 'Type', 'Quantity', 'State', 'UpdateTime'],
        'field_types': {
            'Quantity': 'number',
            'State': 'status_badge',
            'Num': 'business_key',
            'CurrentCycleLife': 'number',
            'CreateTime': 'datetime',
            'UpdateTime': 'datetime',
            'Type': 'category',
            'BinCode': 'location',
            'WhsCode': 'warehouse'
        },
        'status_mapping': {
            '可用': {'class': 'success', 'text': '可用'},
            '使用中': {'class': 'primary', 'text': '使用中'},
            '维护': {'class': 'warning', 'text': '维护中'},
            '报废': {'class': 'danger', 'text': '报废'},
            '': {'class': 'secondary', 'text': '未知'}
        }
    },
    
    'wip_lot': {
        'title': 'WIP批次管理',
        'icon': 'fas fa-boxes',
        'description': '在制品批次管理系统，支持批次跟踪和状态监控。',
        'features': ['批次跟踪', '状态监控', 'WIP管理', '进度分析'],
        'business_key': 'LOT_ID',
        'display_fields': ['LOT_ID', 'PROD_ID', 'QUANTITY', 'STATUS', 'LOCATION'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id'],
        'required_fields': ['LOT_ID', 'PROD_ID', 'QUANTITY'],
        'searchable_fields': ['LOT_ID', 'PROD_ID', 'LOCATION'],
        'sortable_fields': ['LOT_ID', 'PROD_ID', 'QUANTITY'],
        'field_types': {
            'QUANTITY': 'number',
            'STATUS': 'status_badge',
            'LOT_ID': 'business_key'
        }
    },
    
    'et_wait_lot': {
        'title': '待排产批次管理',
        'icon': 'fas fa-clock',
        'description': '待排产批次管理系统，支持优先级设置和排产调度。',
        'features': ['优先级管理', '排产调度', '批次分析', '等待队列'],
        'business_key': 'LOT_ID',
        'display_fields': ['LOT_ID', 'PROD_ID', 'PRIORITY', 'WAIT_TIME', 'STATUS'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id', 'WAIT_TIME'],
        'required_fields': ['LOT_ID', 'PROD_ID', 'PRIORITY'],
        'searchable_fields': ['LOT_ID', 'PROD_ID'],
        'sortable_fields': ['LOT_ID', 'PRIORITY', 'WAIT_TIME'],
        'field_types': {
            'PRIORITY': 'priority_badge',
            'WAIT_TIME': 'duration',
            'STATUS': 'status_badge',
            'LOT_ID': 'business_key'
        },
        'priority_mapping': {
            'HIGH': {'class': 'danger', 'text': '高'},
            'MEDIUM': {'class': 'warning', 'text': '中'},
            'LOW': {'class': 'success', 'text': '低'}
        }
    },
    
    'et_recipe_file': {
        'title': '设备配方管理',
        'icon': 'fas fa-file-code',
        'description': '设备配方文件管理系统，支持配方版本控制和设备配置。',
        'features': ['配方管理', '版本控制', '设备配置', '文件管理'],
        'business_key': 'RECIPE_ID',
        'display_fields': ['RECIPE_ID', 'RECIPE_NAME', 'EQP_ID', 'VERSION', 'STATUS'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id'],
        'required_fields': ['RECIPE_ID', 'RECIPE_NAME', 'EQP_ID'],
        'searchable_fields': ['RECIPE_ID', 'RECIPE_NAME', 'EQP_ID'],
        'sortable_fields': ['RECIPE_ID', 'RECIPE_NAME', 'VERSION'],
        'field_types': {
            'VERSION': 'version',
            'STATUS': 'status_badge',
            'RECIPE_ID': 'business_key'
        }
    },
    
    'devicepriorityconfig': {
        'title': '产品优先级配置',
        'icon': 'fas fa-sort-amount-up',
        'description': '产品优先级配置管理，支持动态优先级调整和规则设置。',
        'features': ['优先级配置', '规则设置', '动态调整', '策略管理'],
        'business_key': 'DEVICE_ID',
        'display_fields': ['DEVICE_ID', 'PRIORITY', 'RULE_TYPE', 'STATUS', 'UPDATE_TIME'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id', 'UPDATE_TIME'],
        'required_fields': ['DEVICE_ID', 'PRIORITY'],
        'searchable_fields': ['DEVICE_ID'],
        'sortable_fields': ['DEVICE_ID', 'PRIORITY', 'UPDATE_TIME'],
        'field_types': {
            'PRIORITY': 'priority_badge',
            'UPDATE_TIME': 'datetime',
            'STATUS': 'status_badge',
            'DEVICE_ID': 'business_key'
        }
    },
    
    'lotpriorityconfig': {
        'title': '批次优先级配置',
        'icon': 'fas fa-layer-group',
        'description': '批次优先级配置管理，支持批次级别的优先级策略设置。',
        'features': ['批次优先级', '策略配置', '规则管理', '动态调整'],
        'business_key': 'LOT_ID',
        'display_fields': ['LOT_ID', 'PRIORITY', 'RULE_TYPE', 'WEIGHT', 'STATUS'],
        'hidden_fields': ['id'],
        'readonly_fields': ['id'],
        'required_fields': ['LOT_ID', 'PRIORITY'],
        'searchable_fields': ['LOT_ID'],
        'sortable_fields': ['LOT_ID', 'PRIORITY', 'WEIGHT'],
        'field_types': {
            'PRIORITY': 'priority_badge',
            'WEIGHT': 'number',
            'STATUS': 'status_badge',
            'LOT_ID': 'business_key'
        }
    },
    
    'lotprioritydone': {
        'title': '已排产批次管理',
        'icon': 'fas fa-check-circle',
        'description': '已排产批次管理系统，支持排产结果跟踪和执行监控。',
        'features': ['排产跟踪', '执行监控', '结果分析', '历史记录'],
        'business_key': 'LOT_ID',
        'display_fields': [
            'LOT_ID', 'PROD_ID', 'PRIORITY', 'HANDLER_ID', 'LOT_TYPE', 
            'GOOD_QTY', 'CHIP_ID', 'PO_ID', 'STAGE', 'STEP', 'WIP_STATE', 
            'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME',
            'FAC_ID', 'CREATE_TIME', 'SCHDULED_TIME'
        ],
        'hidden_fields': ['id', 'created_at', 'updated_at'],
        'readonly_fields': ['id', 'CREATE_TIME'],
        'required_fields': ['LOT_ID', 'PROD_ID'],
        'searchable_fields': ['LOT_ID', 'PROD_ID', 'HANDLER_ID', 'STEP'],
        'sortable_fields': ['LOT_ID', 'PRIORITY', 'SCHDULED_TIME'],
        'field_types': {
            'PRIORITY': 'priority_badge',
            'SCHDULED_TIME': 'datetime',
            'CREATE_TIME': 'datetime',
            'RELEASE_TIME': 'datetime',
            'LOT_ID': 'business_key',
            'STEP': 'string'
        },
        'priority_mapping': {
            'HIGH': {'class': 'danger', 'text': '高'},
            'MEDIUM': {'class': 'warning', 'text': '中'},
            'LOW': {'class': 'success', 'text': '低'}
        }
    }
}

# 通用字段类型配置
FIELD_TYPE_CONFIGS = {
    'business_key': {
        'css_class': 'business-key-col fw-bold',
        'formatter': 'text'
    },
    'status_badge': {
        'css_class': 'status-col',
        'formatter': 'status_badge'
    },
    'priority_badge': {
        'css_class': 'priority-col',
        'formatter': 'priority_badge'
    },
    'datetime': {
        'css_class': 'datetime-col',
        'formatter': 'datetime'
    },
    'date': {
        'css_class': 'date-col',
        'formatter': 'date'
    },
    'number': {
        'css_class': 'number-col text-end',
        'formatter': 'number'
    },
    'percentage': {
        'css_class': 'percentage-col text-end',
        'formatter': 'percentage'
    },
    'duration': {
        'css_class': 'duration-col',
        'formatter': 'duration'
    },
    'version': {
        'css_class': 'version-col',
        'formatter': 'version'
    }
}

def get_table_config(table_name):
    """获取表配置"""
    return TABLE_CONFIGS.get(table_name, {
        'title': table_name.upper(),
        'icon': 'fas fa-table',
        'description': f'{table_name} 数据管理',
        'features': ['数据管理'],
        'business_key': 'id',
        'display_fields': [],
        'hidden_fields': ['id'],
        'readonly_fields': ['id'],
        'required_fields': [],
        'searchable_fields': [],
        'sortable_fields': [],
        'field_types': {}
    })

def get_field_type_config(field_type):
    """获取字段类型配置"""
    return FIELD_TYPE_CONFIGS.get(field_type, {
        'css_class': '',
        'formatter': 'text'
    })
