#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控服务
实时监控系统性能指标并触发告警
"""

import time
import json
import logging
import psutil
import threading
from datetime import datetime, timedelta
from collections import deque, defaultdict

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config_path='performance_monitoring_config.json'):
        # 首先初始化logger
        self.logger = logging.getLogger('PerformanceMonitor')
        self.config = self._load_config(config_path)
        self.metrics_history = defaultdict(deque)  # 保持最近100个数据点
        self.alert_history = deque(maxlen=1000)  # 保持最近1000个告警
        self.last_alert_times = {}  # 记录最后告警时间，用于冷却
        self.running = False
        
    def _load_config(self, config_path):
        """加载监控配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载监控配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            "performance_thresholds": {
                "memory_usage": {"warning": 1024, "critical": 2048},
                "cpu_usage": {"warning": 70, "critical": 90}
            },
            "monitoring_intervals": {"system_metrics": 60},
            "alert_settings": {"enabled": True, "alert_cooldown": 300}
        }
    
    def collect_system_metrics(self):
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_mb = memory.used / 1024 / 1024
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 网络统计
            net_io = psutil.net_io_counters()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_mb': memory_mb,
                'memory_percent': memory.percent,
                'disk_percent': disk_percent,
                'network_bytes_sent': net_io.bytes_sent,
                'network_bytes_recv': net_io.bytes_recv
            }
            
            # 存储历史数据
            self.metrics_history['system'].append(metrics)
            if len(self.metrics_history['system']) > 100:
                self.metrics_history['system'].popleft()
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def check_thresholds(self, metrics):
        """检查性能阈值并触发告警"""
        if not self.config.get('alert_settings', {}).get('enabled', True):
            return
        
        thresholds = self.config.get('performance_thresholds', {})
        
        # 检查CPU使用率
        if 'cpu_percent' in metrics:
            cpu_threshold = thresholds.get('cpu_usage', {})
            cpu_percent = metrics['cpu_percent']
            
            if cpu_percent >= cpu_threshold.get('critical', 90):
                self._trigger_alert('CRITICAL', f'CPU使用率过高: {cpu_percent:.1f}%')
            elif cpu_percent >= cpu_threshold.get('warning', 70):
                self._trigger_alert('WARNING', f'CPU使用率警告: {cpu_percent:.1f}%')
        
        # 检查内存使用
        if 'memory_mb' in metrics:
            memory_threshold = thresholds.get('memory_usage', {})
            memory_mb = metrics['memory_mb']
            
            if memory_mb >= memory_threshold.get('critical', 2048):
                self._trigger_alert('CRITICAL', f'内存使用过高: {memory_mb:.1f}MB')
            elif memory_mb >= memory_threshold.get('warning', 1024):
                self._trigger_alert('WARNING', f'内存使用警告: {memory_mb:.1f}MB')
    
    def _trigger_alert(self, level, message):
        """触发告警"""
        alert_key = f"{level}_{hash(message)}"
        now = datetime.now()
        
        # 检查冷却时间
        cooldown = self.config.get('alert_settings', {}).get('alert_cooldown', 300)
        if alert_key in self.last_alert_times:
            time_diff = (now - self.last_alert_times[alert_key]).total_seconds()
            if time_diff < cooldown:
                return  # 还在冷却期内
        
        # 记录告警
        alert = {
            'timestamp': now.isoformat(),
            'level': level,
            'message': message
        }
        
        self.alert_history.append(alert)
        self.last_alert_times[alert_key] = now
        
        # 输出告警
        if level == 'CRITICAL':
            self.logger.critical(f"🚨 {message}")
        else:
            self.logger.warning(f"⚠️ {message}")
    
    def get_performance_summary(self):
        """获取性能摘要"""
        if not self.metrics_history['system']:
            return {}
        
        recent_metrics = list(self.metrics_history['system'])[-10:]  # 最近10个数据点
        
        if not recent_metrics:
            return {}
        
        # 计算平均值
        avg_cpu = sum(m['cpu_percent'] for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m['memory_mb'] for m in recent_metrics) / len(recent_metrics)
        
        return {
            'avg_cpu_percent': avg_cpu,
            'avg_memory_mb': avg_memory,
            'recent_alerts': list(self.alert_history)[-5:],  # 最近5个告警
            'total_alerts': len(self.alert_history)
        }
    
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        self.logger.info("🚀 性能监控服务启动")
        
        def monitor_loop():
            while self.running:
                try:
                    # 收集系统指标
                    metrics = self.collect_system_metrics()
                    if metrics:
                        self.check_thresholds(metrics)
                    
                    # 等待下一次检查
                    interval = self.config.get('monitoring_intervals', {}).get('system_metrics', 60)
                    time.sleep(interval)
                    
                except Exception as e:
                    self.logger.error(f"监控循环异常: {e}")
                    time.sleep(10)  # 出错后等待10秒再重试
        
        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        
        return monitor_thread
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        self.logger.info("🛑 性能监控服务停止")

# 全局监控实例
performance_monitor = None

def init_performance_monitor(app=None):
    """初始化性能监控"""
    global performance_monitor
    
    if performance_monitor is None:
        performance_monitor = PerformanceMonitor()
        performance_monitor.start_monitoring()
        
        if app:
            app.logger.info("✅ 性能监控系统已启动")
    
    return performance_monitor

def get_performance_metrics():
    """获取性能指标"""
    if performance_monitor:
        return performance_monitor.get_performance_summary()
    return {}
