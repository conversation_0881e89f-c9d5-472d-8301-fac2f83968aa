#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API层缓存统一适配器
将所有API调用标准化为使用DataSourceManager

核心功能：
1. 统一API数据获取入口
2. 标准化分页和过滤
3. 自动缓存管理
4. 性能监控和日志
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class APIDataCacheAdapter:
    """API数据缓存适配器 - 统一API层缓存管理"""
    
    def __init__(self):
        from app.services.data_source_manager import DataSourceManager
        self.data_manager = DataSourceManager()
        self._stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_response_time': 0,
            'avg_response_time': 0
        }
        
        logger.info("🚀 API缓存适配器初始化完成")
    
    def get_table_data(self, table_name: str, **kwargs) -> dict:
        """
        标准化表数据获取接口
        
        Args:
            table_name: 表名
            **kwargs: 额外参数（page, per_page, filters等）
            
        Returns:
            dict: 标准化的响应格式
        """
        start_time = time.time()
        self._stats['total_requests'] += 1
        
        try:
            logger.debug(f"📊 API缓存适配器请求: {table_name}")
            
            # 使用DataSourceManager的统一接口
            result = self.data_manager.get_table_data(table_name, **kwargs)
            
            # 记录性能统计
            response_time = time.time() - start_time
            self._stats['total_response_time'] += response_time
            self._stats['avg_response_time'] = self._stats['total_response_time'] / self._stats['total_requests']
            
            if result.get('success'):
                self._stats['cache_hits'] += 1
                logger.debug(f"✅ API缓存适配器成功: {table_name}, 耗时: {response_time:.3f}s")
            else:
                self._stats['cache_misses'] += 1
                logger.warning(f"⚠️ API缓存适配器失败: {table_name}")
            
            return result
            
        except Exception as e:
            self._stats['cache_misses'] += 1
            logger.error(f"❌ API缓存适配器异常: {table_name}, 错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total': 0
            }
    
    def get_paginated_data(self, table_name: str, page: int = 1, 
                          per_page: int = 50, filters: List[Dict] = None,
                          search: str = None, sort_by: str = None, 
                          sort_order: str = 'ASC') -> dict:
        """
        分页数据获取 - 标准化分页接口
        
        Args:
            table_name: 表名
            page: 页码
            per_page: 每页数量
            filters: 过滤条件列表
            search: 搜索关键字
            sort_by: 排序字段
            sort_order: 排序方向（ASC/DESC）
            
        Returns:
            dict: 标准化的分页响应
        """
        try:
            # 构建查询参数
            query_params = {
                'page': page,
                'per_page': per_page
            }
            
            if filters:
                query_params['filters'] = filters
            if search:
                query_params['search'] = search
            if sort_by:
                query_params['sort_by'] = sort_by
                query_params['sort_order'] = sort_order
            
            # 调用统一数据获取接口
            result = self.get_table_data(table_name, **query_params)
            
            if result.get('success'):
                # 标准化分页响应格式
                data = result.get('data', [])
                total = result.get('total', len(data))
                pages = (total + per_page - 1) // per_page if per_page > 0 else 1
                
                return {
                    'success': True,
                    'data': data,
                    'total': total,
                    'pages': pages,
                    'current_page': page,
                    'per_page': per_page,
                    'has_next': page < pages,
                    'has_prev': page > 1
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"❌ 分页数据获取失败: {table_name}, 错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total': 0,
                'pages': 0,
                'current_page': page,
                'per_page': per_page
            }
    
    def get_historical_equipment_data(self, device: str, stage: str, 
                                    days_limit: int = 180) -> List[Dict]:
        """
        获取历史设备推荐数据 - 专门的缓存方法
        
        Args:
            device: 产品名称
            stage: 工序
            days_limit: 历史数据天数限制
            
        Returns:
            List[Dict]: 历史设备数据
        """
        cache_key = f"historical_equipment_{device}_{stage}_{days_limit}"
        
        def fetch_historical_data():
            """获取历史设备数据的具体实现"""
            try:
                # 使用DataSourceManager获取CT数据
                ct_result = self.data_manager.get_table_data('ct', filters=[
                    {'field': 'DEVICE', 'operator': 'equals', 'value': device},
                    {'field': 'STAGE', 'operator': 'equals', 'value': stage},
                    {'field': 'CREATE_TIME', 'operator': 'days_ago', 'value': days_limit}
                ])
                
                if ct_result.get('success'):
                    return ct_result.get('data', [])
                else:
                    return []
                    
            except Exception as e:
                logger.error(f"获取历史设备数据失败: {e}")
                return []
        
        # 使用确定性缓存
        try:
            cached_data = self.data_manager._get_cached_data(cache_key, fetch_historical_data)
            return cached_data if cached_data else []
        except Exception as e:
            logger.error(f"历史设备数据缓存操作失败: {e}")
            return fetch_historical_data()
    
    def get_equipment_status_summary(self) -> Dict[str, Any]:
        """
        获取设备状态汇总 - 高频访问数据的专门缓存
        
        Returns:
            Dict: 设备状态汇总数据
        """
        cache_key = "equipment_status_summary"
        
        def fetch_equipment_summary():
            """获取设备状态汇总的具体实现"""
            try:
                equipment_data, _ = self.data_manager.get_equipment_status_data()
                
                # 统计汇总
                summary = {
                    'total_equipment': len(equipment_data),
                    'status_counts': {},
                    'handler_types': {},
                    'available_count': 0,
                    'busy_count': 0,
                    'offline_count': 0
                }
                
                for eq in equipment_data.values():
                    status = eq.get('STATUS', 'Unknown')
                    handler_type = eq.get('HANDLER_TYPE', 'Unknown')
                    
                    # 状态统计
                    summary['status_counts'][status] = summary['status_counts'].get(status, 0) + 1
                    summary['handler_types'][handler_type] = summary['handler_types'].get(handler_type, 0) + 1
                    
                    # 可用性统计
                    if status in ['IDLE', 'READY', 'ONLINE', 'Wait', '']:
                        summary['available_count'] += 1
                    elif status in ['Run', 'SetupRun']:
                        summary['busy_count'] += 1
                    else:
                        summary['offline_count'] += 1
                
                return summary
                
            except Exception as e:
                logger.error(f"获取设备状态汇总失败: {e}")
                return {'total_equipment': 0, 'error': str(e)}
        
        # 使用短期缓存（1分钟），因为设备状态变化频繁
        try:
            return self.data_manager._get_cached_data(cache_key, fetch_equipment_summary)
        except Exception as e:
            logger.error(f"设备状态汇总缓存操作失败: {e}")
            return fetch_equipment_summary()
    
    def invalidate_cache(self, table_name: str = None):
        """
        失效缓存 - 数据更新后调用
        
        Args:
            table_name: 指定表名，None表示清理所有缓存
        """
        try:
            if table_name:
                # 失效指定表的相关缓存
                if hasattr(self.data_manager, 'clear_cache'):
                    self.data_manager.clear_cache(table_name)
                logger.info(f"🧹 已失效缓存: {table_name}")
            else:
                # 清理所有缓存
                if hasattr(self.data_manager, 'clear_cache'):
                    self.data_manager.clear_cache()
                logger.info("🧹 已清理所有缓存")
                
        except Exception as e:
            logger.error(f"缓存失效操作失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict: 缓存和API统计信息
        """
        try:
            # 获取底层缓存状态
            cache_status = {}
            if hasattr(self.data_manager, 'get_cache_status'):
                cache_status = self.data_manager.get_cache_status()
            
            # 合并API适配器统计
            combined_stats = {
                'api_adapter_stats': self._stats,
                'cache_manager_stats': cache_status,
                'cache_hit_rate': (self._stats['cache_hits'] / max(self._stats['total_requests'], 1)) * 100,
                'timestamp': datetime.now().isoformat()
            }
            
            return combined_stats
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查 - 验证适配器和底层缓存系统状态
        
        Returns:
            Dict: 健康状态信息
        """
        try:
            # 测试数据管理器连接
            test_result = self.data_manager.get_table_data('ET_WAIT_LOT', page=1, per_page=1)
            
            health_status = {
                'adapter_status': 'healthy',
                'data_manager_status': 'healthy' if test_result.get('success') else 'unhealthy',
                'total_requests': self._stats['total_requests'],
                'avg_response_time': self._stats['avg_response_time'],
                'cache_hit_rate': (self._stats['cache_hits'] / max(self._stats['total_requests'], 1)) * 100,
                'timestamp': datetime.now().isoformat()
            }
            
            if not test_result.get('success'):
                health_status['data_manager_error'] = test_result.get('error', 'Unknown error')
            
            return health_status
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                'adapter_status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# 全局实例 - 单例模式
_api_cache_adapter = None

def get_api_cache_adapter() -> APIDataCacheAdapter:
    """获取API缓存适配器全局实例"""
    global _api_cache_adapter
    if _api_cache_adapter is None:
        _api_cache_adapter = APIDataCacheAdapter()
    return _api_cache_adapter

# 便捷函数
def get_cached_table_data(table_name: str, **kwargs) -> dict:
    """便捷函数：获取表数据"""
    adapter = get_api_cache_adapter()
    return adapter.get_table_data(table_name, **kwargs)

def get_cached_paginated_data(table_name: str, **kwargs) -> dict:
    """便捷函数：获取分页数据"""
    adapter = get_api_cache_adapter()
    return adapter.get_paginated_data(table_name, **kwargs)

def invalidate_api_cache(table_name: str = None):
    """便捷函数：失效缓存"""
    adapter = get_api_cache_adapter()
    adapter.invalidate_cache(table_name)
