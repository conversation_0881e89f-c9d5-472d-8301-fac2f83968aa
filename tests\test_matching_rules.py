import unittest
import os

# 为了避免执行繁重的 __init__，使用 __new__ 创建实例
from app.services.real_scheduling_service import RealSchedulingService


def make_service():
    svc = RealSchedulingService.__new__(RealSchedulingService)
    return svc


def make_recipe(device, stage, hc, kit):
    return {
        'DEVICE': device,
        'STAGE': stage,
        'HANDLER_CONFIG': hc,
        'KIT_PN': kit,
    }


def make_spec(device, stage, tester, hb, tb, approval='Released'):
    return {
        'DEVICE': device,
        'STAGE': stage,
        'TESTER': tester,
        'HB_PN': hb,
        'TB_PN': tb,
        'APPROVAL_STATE': approval,
    }


class TestMatchingRules(unittest.TestCase):
    def setUp(self):
        self.device = 'DEV1'
        self.stage = 'FT'
        self.eqp_stage_other = 'FT2'
        self.hc = 'HC-001'
        self.kit = 'KIT-001'
        self.tester = 'TST-A'
        self.hb = 'HB-AAA'
        self.tb = 'TB-AAA'

        self.preloaded = {
            'recipe_files': [
                make_recipe(self.device, self.stage, self.hc, self.kit),
                # 另一个组合用于B类判定
                make_recipe(self.device, self.stage, 'HC-002', 'KIT-002'),
            ],
            'test_specs': [
                make_spec(self.device, self.stage, self.tester, self.hb, self.tb, 'Released'),
            ],
        }

        self.lot_req = {
            'DEVICE': self.device,
            'STAGE': self.stage,
        }

    def _call(self, equipment):
        svc = make_service()
        return svc.calculate_equipment_match_score_optimized(self.lot_req, equipment, self.preloaded)

    def test_same_product_continuation(self):
        equipment = {
            'HANDLER_ID': 'EQP-1',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_same_config_is_absorbed_by_continuation(self):
        # 同配置条件在当前实现中会被“同产品续排特例前置”吸收
        equipment = {
            'HANDLER_ID': 'EQP-2',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_minor_changeover_is_absorbed_by_continuation(self):
        # 即便HB/TB不兼容，只要(HC,KIT)为有效组合，仍会先命中“同产品续排”
        equipment = {
            'HANDLER_ID': 'EQP-3',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': self.tester,
            'HB_PN': 'HB-BBB',
            'TB_PN': 'TB-BBB',
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_tester_changeover_is_absorbed_by_continuation(self):
        # TESTER差异也会被“同产品续排特例前置”吸收
        equipment = {
            'HANDLER_ID': 'EQP-4',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': 'TST-X',
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_major_changeover(self):
        # HC一致但KIT不一致 -> 大改机（同工序）
        equipment = {
            'HANDLER_ID': 'EQP-5',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': 'KIT-DIFF',
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '大改机匹配')
        self.assertEqual(t, 120)

    def test_cross_stage_A(self):
        # 跨工序 A：DEVICE或STAGE不一致，且(HC,KIT)在有效组合中
        equipment = {
            'HANDLER_ID': 'EQP-6',
            'DEVICE': 'DEV2',  # 不同器件
            'STAGE': self.eqp_stage_other,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,  # 与有效组合一致
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '跨工序A类匹配')
        self.assertEqual(t, 60)

    def test_cross_stage_B(self):
        # 跨工序 B：HC一致，但(HC,KIT)不在有效组合
        equipment = {
            'HANDLER_ID': 'EQP-7',
            'DEVICE': 'DEV2',
            'STAGE': self.eqp_stage_other,
            'HANDLER_CONFIG': 'HC-002',  # 存在于 req HC 列表
            'KIT_PN': 'KIT-XXX',        # 不在有效组合
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '跨工序B类匹配')
        self.assertEqual(t, 90)

    def test_unable_to_match(self):
        # 完全不匹配 -> 无法上机
        equipment = {
            'HANDLER_ID': 'EQP-8',
            'DEVICE': 'DEVX',
            'STAGE': 'STGY',
            'HANDLER_CONFIG': 'HC-Z',
            'KIT_PN': 'KIT-Z',
            'TESTER': 'TST-Z',
            'HB_PN': 'HB-Z',
            'TB_PN': 'TB-Z',
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '无法上机')
        self.assertEqual(score, 0)


    def test_whitespace_normalization_continuation(self):
        equipment = {
            'HANDLER_ID': 'EQP-WS',
            'DEVICE': f" {self.device} ",
            'STAGE': f" {self.stage} ",
            'HANDLER_CONFIG': f"  {self.hc}  ",
            'KIT_PN': f"  {self.kit}  ",
            'TESTER': f"  {self.tester}  ",
            'HB_PN': f"  {self.hb}  ",
            'TB_PN': f"  {self.tb}  ",
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_hb_tb_empty_equipment_is_compatible_and_absorbed(self):
        equipment = {
            'HANDLER_ID': 'EQP-HB0',
            'DEVICE': self.device,
            'STAGE': self.stage,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': self.tester,
            'HB_PN': '',
            'TB_PN': '',
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '同产品续排')
        self.assertEqual(t, 0)

    def test_tester_set_membership_trim(self):
        # 将需求侧TESTER加入空白，设备应仍然正确识别（用于换测试机逻辑的集合化trim验证）
        self.preloaded['test_specs'][0]['TESTER'] = f"  {self.tester}  "
        equipment = {
            'HANDLER_ID': 'EQP-TST',
            'DEVICE': 'DEV2',
            'STAGE': self.eqp_stage_other,
            'HANDLER_CONFIG': self.hc,
            'KIT_PN': self.kit,
            'TESTER': self.tester,
            'HB_PN': self.hb,
            'TB_PN': self.tb,
        }
        score, mtype, t = self._call(equipment)
        self.assertEqual(mtype, '跨工序A类匹配')
        self.assertEqual(t, 60)

if __name__ == '__main__':
    unittest.main(verbosity=2)

