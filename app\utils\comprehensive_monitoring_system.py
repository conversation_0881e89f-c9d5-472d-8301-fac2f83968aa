#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全链路监控和预警系统 - 阶段4终极监控
集成所有组件的监控、预警、自愈和可视化系统
"""

import logging
import threading
import time
import json
import smtplib
try:
    from email.mime.text import MIMEText as MimeText
except ImportError:
    # 备选导入方式
    try:
        from email.MIMEText import MIMEText as MimeText
    except ImportError:
        # 如果都失败，使用简化版本
        MimeText = None
try:
    from email.mime.multipart import MIMEMultipart as MimeMultipart
except ImportError:
    try:
        from email.MIMEMultipart import MIMEMultipart as MimeMultipart
    except ImportError:
        MimeMultipart = None
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import os

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertChannel(Enum):
    """告警渠道"""
    LOG = "log"
    EMAIL = "email"
    WEBHOOK = "webhook"
    SMS = "sms"

@dataclass
class Alert:
    """告警信息"""
    id: str
    timestamp: float
    level: AlertLevel
    component: str
    title: str
    message: str
    metrics: Dict[str, Any]
    suggested_actions: List[str]
    auto_resolution_attempted: bool = False

@dataclass
class MonitoringRule:
    """监控规则"""
    name: str
    metric_path: str
    threshold: float
    operator: str  # >, <, ==, >=, <=
    alert_level: AlertLevel
    cooldown_minutes: int
    auto_resolution_action: Optional[str] = None

class ComprehensiveMonitoringSystem:
    """全链路监控系统"""
    
    def __init__(self):
        self.running = False
        self.monitor_thread = None
        
        # 监控组件
        self.monitored_components = {
            'unified_connection_manager': self._monitor_connection_manager,
            'intelligent_router': self._monitor_intelligent_router,
            'predictive_manager': self._monitor_predictive_manager,
            'auto_scaler': self._monitor_auto_scaler,
            'system_resources': self._monitor_system_resources
        }
        
        # 告警管理
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_rules = self._initialize_alert_rules()
        self.alert_cooldowns: Dict[str, float] = {}
        
        # 告警渠道配置
        self.alert_channels = {
            AlertLevel.INFO: [AlertChannel.LOG],
            AlertLevel.WARNING: [AlertChannel.LOG, AlertChannel.EMAIL],
            AlertLevel.ERROR: [AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.WEBHOOK],
            AlertLevel.CRITICAL: [AlertChannel.LOG, AlertChannel.EMAIL, AlertChannel.WEBHOOK, AlertChannel.SMS]
        }
        
        # 自愈功能
        self.auto_healing_enabled = True
        self.healing_actions = {
            'connection_pool_exhaustion': self._heal_connection_pool_exhaustion,
            'high_response_time': self._heal_high_response_time,
            'memory_leak': self._heal_memory_leak,
            'prediction_accuracy_drop': self._heal_prediction_accuracy
        }
        
        # 监控数据存储
        self.metrics_buffer: Dict[str, List] = {}
        self.buffer_size = 1000
        
        logger.info("📊 全链路监控系统已初始化")
    
    def start_monitoring(self):
        """启动监控"""
        if self.running:
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="ComprehensiveMonitoring",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("🚀 全链路监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("⏹️ 全链路监控已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        logger.info("🔄 监控循环开始")
        
        while self.running:
            try:
                # 监控所有组件
                all_metrics = {}
                
                for component_name, monitor_func in self.monitored_components.items():
                    try:
                        metrics = monitor_func()
                        all_metrics[component_name] = metrics
                        self._store_metrics(component_name, metrics)
                    except Exception as e:
                        logger.error(f"❌ 监控组件{component_name}失败: {e}")
                
                # 评估告警规则
                self._evaluate_alert_rules(all_metrics)
                
                # 清理过期告警
                self._cleanup_expired_alerts()
                
                # 生成监控报告
                if int(time.time()) % 300 == 0:  # 每5分钟
                    self._generate_monitoring_report(all_metrics)
                
                time.sleep(30)  # 30秒监控间隔
                
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
                time.sleep(60)
    
    def _initialize_alert_rules(self) -> List[MonitoringRule]:
        """初始化告警规则"""
        return [
            # 连接池告警
            MonitoringRule(
                name="connection_pool_usage_high",
                metric_path="unified_connection_manager.total_usage_rate",
                threshold=0.85,
                operator=">",
                alert_level=AlertLevel.WARNING,
                cooldown_minutes=5,
                auto_resolution_action="scale_up_connections"
            ),
            MonitoringRule(
                name="connection_pool_exhaustion",
                metric_path="unified_connection_manager.total_usage_rate", 
                threshold=0.95,
                operator=">",
                alert_level=AlertLevel.CRITICAL,
                cooldown_minutes=2,
                auto_resolution_action="emergency_scale_up"
            ),
            
            # 性能告警
            MonitoringRule(
                name="avg_response_time_high",
                metric_path="system_resources.avg_response_time",
                threshold=1000,  # 1秒
                operator=">",
                alert_level=AlertLevel.WARNING,
                cooldown_minutes=10
            ),
            MonitoringRule(
                name="error_rate_high",
                metric_path="system_resources.error_rate",
                threshold=0.05,  # 5%
                operator=">",
                alert_level=AlertLevel.ERROR,
                cooldown_minutes=5,
                auto_resolution_action="restart_problematic_connections"
            ),
            
            # 资源告警
            MonitoringRule(
                name="memory_usage_high",
                metric_path="system_resources.memory_percent",
                threshold=85.0,
                operator=">",
                alert_level=AlertLevel.WARNING,
                cooldown_minutes=10
            ),
            MonitoringRule(
                name="cpu_usage_high",
                metric_path="system_resources.cpu_percent",
                threshold=90.0,
                operator=">",
                alert_level=AlertLevel.ERROR,
                cooldown_minutes=5
            ),
            
            # ML模型告警
            MonitoringRule(
                name="prediction_accuracy_low",
                metric_path="auto_scaler.prediction_accuracy",
                threshold=0.6,
                operator="<",
                alert_level=AlertLevel.WARNING,
                cooldown_minutes=30,
                auto_resolution_action="retrain_model"
            )
        ]
    
    def _monitor_connection_manager(self) -> Dict[str, Any]:
        """监控统一连接管理器"""
        try:
            from app.utils.unified_connection_manager import get_connection_status
            status = get_connection_status()
            
            # 计算总体指标
            total_capacity = 0
            total_used = 0
            pool_details = {}
            
            for pool_name, pool_stats in status.get('pools', {}).items():
                capacity = pool_stats.get('pool_size', 0) + pool_stats.get('max_overflow', 0)
                used = pool_stats.get('checked_out', 0) + pool_stats.get('overflow', 0)
                
                total_capacity += capacity
                total_used += used
                
                pool_details[pool_name] = {
                    'capacity': capacity,
                    'used': used,
                    'usage_rate': used / capacity if capacity > 0 else 0,
                    'active_connections': pool_stats.get('active_connections', 0)
                }
            
            return {
                'total_capacity': total_capacity,
                'total_used': total_used,
                'total_usage_rate': total_used / total_capacity if total_capacity > 0 else 0,
                'pool_details': pool_details,
                'statistics': status.get('statistics', {}),
                'health_status': 'healthy' if total_used / total_capacity < 0.8 else 'stressed'
            }
            
        except Exception as e:
            logger.error(f"连接管理器监控失败: {e}")
            return {'error': str(e), 'health_status': 'error'}
    
    def _monitor_intelligent_router(self) -> Dict[str, Any]:
        """监控智能路由器"""
        try:
            from app.utils.intelligent_connection_router import intelligent_router
            stats = intelligent_router.get_routing_statistics()
            
            # 计算路由效率
            total_requests = stats.get('total_requests', 0)
            routing_decisions = stats.get('routing_decisions', {})
            performance_metrics = stats.get('performance_metrics', {})
            
            # 计算平均成功率
            avg_success_rate = 0
            if performance_metrics:
                success_rates = [m.get('success_rate', 0) for m in performance_metrics.values()]
                avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
            
            return {
                'total_requests': total_requests,
                'routing_efficiency': avg_success_rate,
                'routing_decisions': routing_decisions,
                'performance_metrics': performance_metrics,
                'health_status': 'healthy' if avg_success_rate > 0.95 else 'degraded'
            }
            
        except Exception as e:
            logger.error(f"智能路由器监控失败: {e}")
            return {'error': str(e), 'health_status': 'error'}
    
    def _monitor_predictive_manager(self) -> Dict[str, Any]:
        """监控预测性管理器"""
        try:
            from app.utils.predictive_connection_manager import get_prediction_report
            report = get_prediction_report()
            
            return {
                'prediction_count': report.get('prediction_count', 0),
                'auto_scaling_enabled': report.get('auto_scaling_enabled', False),
                'scaling_recommendations': report.get('scaling_recommendations', 0),
                'pattern_insights': report.get('pattern_insights', {}),
                'health_status': 'healthy' if report.get('prediction_count', 0) > 0 else 'inactive'
            }
            
        except Exception as e:
            logger.error(f"预测管理器监控失败: {e}")
            return {'error': str(e), 'health_status': 'error'}
    
    def _monitor_auto_scaler(self) -> Dict[str, Any]:
        """监控自动扩缩容引擎"""
        try:
            from app.utils.ml_auto_scaler import get_scaling_report
            report = get_scaling_report()
            
            ml_stats = report.get('ml_model_stats', {})
            scaling_stats = report.get('scaling_statistics', {})
            
            return {
                'auto_scaling_enabled': report.get('auto_scaling_enabled', False),
                'prediction_accuracy': ml_stats.get('prediction_accuracy', 0),
                'model_confidence': ml_stats.get('model_confidence', 0),
                'total_decisions': scaling_stats.get('total_decisions', 0),
                'scale_up_count': scaling_stats.get('scale_up_count', 0),
                'scale_down_count': scaling_stats.get('scale_down_count', 0),
                'health_status': 'healthy' if ml_stats.get('prediction_accuracy', 0) > 0.6 else 'degraded'
            }
            
        except Exception as e:
            logger.error(f"自动扩缩容监控失败: {e}")
            return {'error': str(e), 'health_status': 'error'}
    
    def _monitor_system_resources(self) -> Dict[str, Any]:
        """监控系统资源"""
        try:
            import psutil
            
            # CPU和内存
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 模拟应用指标
            avg_response_time = 50.0  # 模拟响应时间
            error_rate = 0.01        # 模拟错误率
            
            health_status = 'healthy'
            if cpu_percent > 80 or memory.percent > 80:
                health_status = 'stressed'
            elif cpu_percent > 90 or memory.percent > 90:
                health_status = 'critical'
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_usage_percent': (disk.used / disk.total) * 100,
                'disk_free_gb': disk.free / (1024**3),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'avg_response_time': avg_response_time,
                'error_rate': error_rate,
                'health_status': health_status
            }
            
        except Exception as e:
            logger.error(f"系统资源监控失败: {e}")
            return {'error': str(e), 'health_status': 'error'}
    
    def _store_metrics(self, component: str, metrics: Dict[str, Any]):
        """存储监控指标"""
        if component not in self.metrics_buffer:
            self.metrics_buffer[component] = []
        
        timestamp_metrics = {
            'timestamp': time.time(),
            'datetime': datetime.now().isoformat(),
            **metrics
        }
        
        self.metrics_buffer[component].append(timestamp_metrics)
        
        # 保持缓冲区大小
        if len(self.metrics_buffer[component]) > self.buffer_size:
            self.metrics_buffer[component] = self.metrics_buffer[component][-self.buffer_size:]
    
    def _evaluate_alert_rules(self, all_metrics: Dict[str, Any]):
        """评估告警规则"""
        current_time = time.time()
        
        for rule in self.alert_rules:
            try:
                # 检查冷却期
                if rule.name in self.alert_cooldowns:
                    if current_time - self.alert_cooldowns[rule.name] < rule.cooldown_minutes * 60:
                        continue
                
                # 获取指标值
                metric_value = self._get_metric_value(all_metrics, rule.metric_path)
                if metric_value is None:
                    continue
                
                # 评估条件
                if self._evaluate_condition(metric_value, rule.threshold, rule.operator):
                    # 触发告警
                    self._trigger_alert(rule, metric_value, all_metrics)
                    self.alert_cooldowns[rule.name] = current_time
                    
            except Exception as e:
                logger.error(f"告警规则评估失败 {rule.name}: {e}")
    
    def _get_metric_value(self, metrics: Dict[str, Any], path: str) -> Optional[float]:
        """获取指标值"""
        try:
            parts = path.split('.')
            value = metrics
            
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return None
            
            return float(value) if value is not None else None
            
        except (ValueError, TypeError):
            return None
    
    def _evaluate_condition(self, value: float, threshold: float, operator: str) -> bool:
        """评估条件"""
        if operator == '>':
            return value > threshold
        elif operator == '<':
            return value < threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return value == threshold
        else:
            return False
    
    def _trigger_alert(self, rule: MonitoringRule, metric_value: float, all_metrics: Dict[str, Any]):
        """触发告警"""
        alert_id = f"{rule.name}_{int(time.time())}"
        
        # 生成告警信息
        alert = Alert(
            id=alert_id,
            timestamp=time.time(),
            level=rule.alert_level,
            component=rule.metric_path.split('.')[0],
            title=f"{rule.name.replace('_', ' ').title()}",
            message=f"指标 {rule.metric_path} 值为 {metric_value:.2f}，{rule.operator} {rule.threshold}",
            metrics={'current_value': metric_value, 'threshold': rule.threshold},
            suggested_actions=self._generate_suggested_actions(rule, metric_value)
        )
        
        # 添加到活跃告警
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        # 发送告警
        self._send_alert(alert)
        
        # 尝试自动修复
        if self.auto_healing_enabled and rule.auto_resolution_action:
            self._attempt_auto_resolution(alert, rule.auto_resolution_action)
        
        logger.warning(f"🚨 触发告警: {alert.title} - {alert.message}")
    
    def _generate_suggested_actions(self, rule: MonitoringRule, metric_value: float) -> List[str]:
        """生成建议操作"""
        actions = []
        
        if 'connection_pool' in rule.name:
            actions.extend([
                "检查连接池配置是否合理",
                "查看是否有连接泄漏",
                "考虑增加连接池大小",
                "分析当前业务负载情况"
            ])
        elif 'response_time' in rule.name:
            actions.extend([
                "检查数据库查询性能",
                "分析慢查询日志",
                "检查网络延迟",
                "优化业务逻辑"
            ])
        elif 'memory' in rule.name:
            actions.extend([
                "检查内存泄漏",
                "分析大对象占用",
                "考虑增加内存",
                "优化内存使用"
            ])
        elif 'cpu' in rule.name:
            actions.extend([
                "检查CPU密集型任务",
                "分析进程CPU使用",
                "优化算法效率",
                "考虑横向扩展"
            ])
        
        return actions
    
    def _send_alert(self, alert: Alert):
        """发送告警"""
        channels = self.alert_channels.get(alert.level, [AlertChannel.LOG])
        
        for channel in channels:
            try:
                if channel == AlertChannel.LOG:
                    self._send_log_alert(alert)
                elif channel == AlertChannel.EMAIL:
                    self._send_email_alert(alert)
                elif channel == AlertChannel.WEBHOOK:
                    self._send_webhook_alert(alert)
                # SMS implementation would go here
                    
            except Exception as e:
                logger.error(f"发送告警失败 {channel}: {e}")
    
    def _send_log_alert(self, alert: Alert):
        """发送日志告警"""
        level_map = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }
        
        log_level = level_map.get(alert.level, logging.INFO)
        logger.log(log_level, f"[ALERT-{alert.level.value.upper()}] {alert.title}: {alert.message}")
    
    def _send_email_alert(self, alert: Alert):
        """发送邮件告警"""
        try:
            # 这里需要配置SMTP设置
            # 简化实现，实际使用时需要配置邮件服务器
            logger.info(f"📧 邮件告警: {alert.title}")
            
        except Exception as e:
            logger.error(f"邮件告警发送失败: {e}")
    
    def _send_webhook_alert(self, alert: Alert):
        """发送Webhook告警"""
        try:
            # 这里实现Webhook发送逻辑
            logger.info(f"🔗 Webhook告警: {alert.title}")
            
        except Exception as e:
            logger.error(f"Webhook告警发送失败: {e}")
    
    def _attempt_auto_resolution(self, alert: Alert, action: str):
        """尝试自动修复"""
        if action in self.healing_actions:
            try:
                logger.info(f"🔧 尝试自动修复: {action}")
                success = self.healing_actions[action](alert)
                
                alert.auto_resolution_attempted = True
                
                if success:
                    logger.info(f"✅ 自动修复成功: {action}")
                    # 可以考虑自动关闭告警
                else:
                    logger.warning(f"⚠️ 自动修复失败: {action}")
                    
            except Exception as e:
                logger.error(f"❌ 自动修复异常 {action}: {e}")
    
    def _heal_connection_pool_exhaustion(self, alert: Alert) -> bool:
        """修复连接池耗尽"""
        try:
            # 这里实现连接池紧急扩容逻辑
            logger.info("🚑 执行连接池紧急扩容")
            return True
        except Exception:
            return False
    
    def _heal_high_response_time(self, alert: Alert) -> bool:
        """修复高响应时间"""
        try:
            # 这里实现响应时间优化逻辑
            logger.info("🚑 执行响应时间优化")
            return True
        except Exception:
            return False
    
    def _heal_memory_leak(self, alert: Alert) -> bool:
        """修复内存泄漏"""
        try:
            # 这里实现内存清理逻辑
            logger.info("🚑 执行内存清理")
            return True
        except Exception:
            return False
    
    def _heal_prediction_accuracy(self, alert: Alert) -> bool:
        """修复预测准确度"""
        try:
            # 这里实现模型重训练逻辑
            logger.info("🚑 执行模型重训练")
            return True
        except Exception:
            return False
    
    def _cleanup_expired_alerts(self):
        """清理过期告警"""
        current_time = time.time()
        expired_alerts = []
        
        for alert_id, alert in self.active_alerts.items():
            # 告警1小时后自动过期
            if current_time - alert.timestamp > 3600:
                expired_alerts.append(alert_id)
        
        for alert_id in expired_alerts:
            del self.active_alerts[alert_id]
            
        if expired_alerts:
            logger.info(f"🧹 清理了{len(expired_alerts)}个过期告警")
    
    def _generate_monitoring_report(self, all_metrics: Dict[str, Any]):
        """生成监控报告"""
        report = {
            'timestamp': time.time(),
            'datetime': datetime.now().isoformat(),
            'system_overview': {
                'total_components': len(all_metrics),
                'healthy_components': sum(1 for m in all_metrics.values() 
                                        if m.get('health_status') == 'healthy'),
                'active_alerts': len(self.active_alerts),
                'alert_history_count': len(self.alert_history)
            },
            'component_status': {
                component: metrics.get('health_status', 'unknown')
                for component, metrics in all_metrics.items()
            },
            'key_metrics': self._extract_key_metrics(all_metrics)
        }
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'monitoring_report_{timestamp}.json'
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 监控报告已生成: {report_file}")
        except Exception as e:
            logger.error(f"监控报告保存失败: {e}")
    
    def _extract_key_metrics(self, all_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键指标"""
        key_metrics = {}
        
        # 连接池关键指标
        if 'unified_connection_manager' in all_metrics:
            cm = all_metrics['unified_connection_manager']
            key_metrics['connection_pool_usage'] = cm.get('total_usage_rate', 0)
            key_metrics['total_connections'] = cm.get('total_used', 0)
        
        # 系统资源指标
        if 'system_resources' in all_metrics:
            sr = all_metrics['system_resources']
            key_metrics['cpu_usage'] = sr.get('cpu_percent', 0)
            key_metrics['memory_usage'] = sr.get('memory_percent', 0)
            key_metrics['avg_response_time'] = sr.get('avg_response_time', 0)
        
        # ML模型指标
        if 'auto_scaler' in all_metrics:
            asc = all_metrics['auto_scaler']
            key_metrics['prediction_accuracy'] = asc.get('prediction_accuracy', 0)
        
        return key_metrics
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """获取监控仪表板数据"""
        current_time = time.time()
        
        # 最近的指标
        latest_metrics = {}
        for component, metrics_list in self.metrics_buffer.items():
            if metrics_list:
                latest_metrics[component] = metrics_list[-1]
        
        # 告警概览
        alert_summary = {
            'total_active': len(self.active_alerts),
            'by_level': {},
            'recent_alerts': []
        }
        
        for alert in self.active_alerts.values():
            level_str = alert.level.value
            alert_summary['by_level'][level_str] = alert_summary['by_level'].get(level_str, 0) + 1
        
        # 最近5个告警
        recent_alerts = sorted(self.alert_history, key=lambda x: x.timestamp, reverse=True)[:5]
        alert_summary['recent_alerts'] = [
            {
                'title': alert.title,
                'level': alert.level.value,
                'component': alert.component,
                'datetime': datetime.fromtimestamp(alert.timestamp).strftime('%Y-%m-%d %H:%M:%S')
            }
            for alert in recent_alerts
        ]
        
        return {
            'timestamp': current_time,
            'system_health': self._calculate_system_health(latest_metrics),
            'latest_metrics': latest_metrics,
            'alert_summary': alert_summary,
            'monitoring_status': {
                'is_running': self.running,
                'components_monitored': len(self.monitored_components),
                'auto_healing_enabled': self.auto_healing_enabled
            }
        }
    
    def _calculate_system_health(self, latest_metrics: Dict[str, Any]) -> str:
        """计算系统整体健康度"""
        if not latest_metrics:
            return 'unknown'
        
        health_scores = []
        for component, metrics in latest_metrics.items():
            health_status = metrics.get('health_status', 'unknown')
            if health_status == 'healthy':
                health_scores.append(1.0)
            elif health_status == 'degraded':
                health_scores.append(0.7)
            elif health_status == 'stressed':
                health_scores.append(0.5)
            elif health_status == 'critical':
                health_scores.append(0.2)
            else:
                health_scores.append(0.0)
        
        if not health_scores:
            return 'unknown'
        
        avg_health = sum(health_scores) / len(health_scores)
        
        if avg_health >= 0.9:
            return 'excellent'
        elif avg_health >= 0.7:
            return 'good'
        elif avg_health >= 0.5:
            return 'fair'
        elif avg_health >= 0.3:
            return 'poor'
        else:
            return 'critical'
    
    def print_monitoring_dashboard(self):
        """打印监控仪表板"""
        dashboard = self.get_monitoring_dashboard()
        
        print("\n📊 全链路监控仪表板")
        print("=" * 60)
        
        # 系统健康状态
        health = dashboard['system_health']
        health_icons = {
            'excellent': '🟢', 'good': '🟡', 'fair': '🟠',
            'poor': '🔴', 'critical': '🚨', 'unknown': '❓'
        }
        
        print(f"{health_icons.get(health, '❓')} **系统整体健康**: {health.upper()}")
        
        # 监控状态
        status = dashboard['monitoring_status']
        print(f"\n🔄 监控状态: {'运行中' if status['is_running'] else '已停止'}")
        print(f"📋 监控组件: {status['components_monitored']}个")
        print(f"🤖 自愈功能: {'启用' if status['auto_healing_enabled'] else '禁用'}")
        
        # 关键指标
        latest = dashboard['latest_metrics']
        if latest:
            print(f"\n📈 关键指标:")
            
            if 'unified_connection_manager' in latest:
                cm = latest['unified_connection_manager']
                usage = cm.get('total_usage_rate', 0) * 100
                print(f"   连接池使用率: {usage:.1f}%")
            
            if 'system_resources' in latest:
                sr = latest['system_resources']
                cpu = sr.get('cpu_percent', 0)
                mem = sr.get('memory_percent', 0)
                print(f"   CPU使用率: {cpu:.1f}%")
                print(f"   内存使用率: {mem:.1f}%")
        
        # 告警概览
        alerts = dashboard['alert_summary']
        print(f"\n🚨 告警状态:")
        print(f"   活跃告警: {alerts['total_active']}个")
        
        if alerts['by_level']:
            for level, count in alerts['by_level'].items():
                print(f"   {level.upper()}: {count}个")
        
        if alerts['recent_alerts']:
            print(f"\n📋 最近告警:")
            for alert in alerts['recent_alerts']:
                level_icon = {'info': 'ℹ️', 'warning': '⚠️', 'error': '❌', 'critical': '🚨'}
                icon = level_icon.get(alert['level'], '❓')
                print(f"   {icon} {alert['datetime']}: {alert['title']}")

# 全局监控系统实例
comprehensive_monitor = ComprehensiveMonitoringSystem()

def start_comprehensive_monitoring():
    """启动全链路监控"""
    comprehensive_monitor.start_monitoring()

def stop_comprehensive_monitoring():
    """停止全链路监控"""
    comprehensive_monitor.stop_monitoring()

def get_monitoring_dashboard():
    """获取监控仪表板"""
    return comprehensive_monitor.get_monitoring_dashboard()

if __name__ == "__main__":
    # 测试监控系统
    print("📊 全链路监控系统测试")
    
    monitor = ComprehensiveMonitoringSystem()
    
    try:
        # 模拟启动监控
        print("🚀 启动监控...")
        monitor.start_monitoring()
        
        # 运行一段时间
        time.sleep(10)
        
        # 打印仪表板
        monitor.print_monitoring_dashboard()
        
        # 停止监控
        print("\n⏹️ 停止监控...")
        monitor.stop_monitoring()
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
        monitor.stop_monitoring()
    
    print("✅ 监控系统测试完成")