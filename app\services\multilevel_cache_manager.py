#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 多级缓存管理器 - Phase 2 Task 2.2
实现L1内存缓存 + L2Redis缓存 + L3数据库缓存的完整架构

核心功能：
1. L1内存缓存：超高速访问，适用于热点数据
2. L2Redis缓存：跨进程共享，适用于计算结果
3. L3数据库缓存：持久化存储，适用于基础数据
4. 智能降级：缓存失效时自动降级到下一级
5. 预加载机制：根据算法需求智能预加载数据
"""

import logging
import time
import json
import pickle
import hashlib
import threading
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from collections import OrderedDict
from enum import Enum

logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """缓存级别枚举"""
    L1_MEMORY = "l1_memory"     # L1内存缓存
    L2_REDIS = "l2_redis"       # L2Redis缓存  
    L3_DATABASE = "l3_database" # L3数据库缓存

class CacheStrategy(Enum):
    """缓存策略枚举"""
    WRITE_THROUGH = "write_through"   # 写透策略
    WRITE_BACK = "write_back"         # 写回策略
    WRITE_AROUND = "write_around"     # 写绕过策略

class DataType(Enum):
    """数据类型枚举"""
    CONFIG_DATA = "config_data"         # 配置数据（设备优先级、批次优先级）
    BUSINESS_DATA = "business_data"     # 业务数据（待排产批次、设备状态）
    COMPUTATION_RESULT = "computation_result"  # 计算结果（匹配结果、评分）
    ALGORITHM_METADATA = "algorithm_metadata"  # 算法元数据（性能统计、选择历史）

class MultilevelCacheManager:
    """🚀 多级缓存管理器"""
    
    def __init__(self, app=None):
        self.app = app
        
        # L1内存缓存（LRU）
        self._l1_cache = OrderedDict()
        self._l1_max_size = 1000
        self._l1_lock = threading.RLock()
        
        # L2Redis缓存
        self._redis_client = None
        
        # 缓存配置
        self._cache_config = {
            # L1内存缓存配置
            'l1_memory': {
                'max_size': 1000,
                'default_ttl': 300,  # 5分钟
                'hot_data_ttl': 600  # 热点数据10分钟
            },
            # L2Redis缓存配置  
            'l2_redis': {
                'default_ttl': 1800,  # 30分钟
                'computation_result_ttl': 3600,  # 计算结果1小时
                'config_data_ttl': 7200  # 配置数据2小时
            },
            # L3数据库缓存配置
            'l3_database': {
                'query_cache_ttl': 300,  # 查询结果缓存5分钟
                'aggregation_cache_ttl': 900  # 聚合结果缓存15分钟
            }
        }
        
        # 数据类型缓存策略映射
        self._cache_strategies = {
            DataType.CONFIG_DATA: {
                'levels': [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                'strategy': CacheStrategy.WRITE_THROUGH,
                'ttl': {'l1': 600, 'l2': 7200}
            },
            DataType.BUSINESS_DATA: {
                'levels': [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                'strategy': CacheStrategy.WRITE_THROUGH,
                'ttl': {'l1': 60, 'l2': 300}
            },
            DataType.COMPUTATION_RESULT: {
                'levels': [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                'strategy': CacheStrategy.WRITE_BACK,
                'ttl': {'l1': 300, 'l2': 3600}
            },
            DataType.ALGORITHM_METADATA: {
                'levels': [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                'strategy': CacheStrategy.WRITE_THROUGH,
                'ttl': {'l1': 1800, 'l2': 86400}  # 算法元数据缓存较长时间
            }
        }
        
        # 性能统计
        self._stats = {
            'l1_hits': 0,
            'l1_misses': 0,
            'l2_hits': 0,
            'l2_misses': 0,
            'l3_hits': 0,
            'l3_misses': 0,
            'total_requests': 0,
            'preload_count': 0,
            'eviction_count': 0
        }
        
        # 热点数据跟踪
        self._hot_keys = set()
        self._access_frequency = {}
        self._hot_threshold = 10  # 访问10次以上认为是热点数据
        
        if app:
            self.init_app(app)

    def init_app(self, app):
        """初始化缓存管理器"""
        try:
            # 初始化Redis连接
            import redis
            self._redis_client = redis.Redis(
                host=app.config.get('REDIS_HOST', 'localhost'),
                port=app.config.get('REDIS_PORT', 6379),
                db=app.config.get('REDIS_DB', 1),  # 使用db1避免冲突
                decode_responses=False
            )
            self._redis_client.ping()
            logger.info("✅ L2 Redis缓存已连接")
            
        except Exception as e:
            logger.warning(f"⚠️ L2 Redis不可用，使用L1内存缓存: {e}")
            self._redis_client = None
        
        # 启动缓存清理线程
        self._start_cleanup_thread()
        logger.info("🚀 多级缓存管理器初始化完成")

    def get(self, key: str, data_type: DataType = DataType.BUSINESS_DATA) -> Optional[Any]:
        """
        🚀 智能多级缓存获取
        按L1→L2→L3顺序查找，找到后向上填充
        """
        self._stats['total_requests'] += 1
        
        # 更新访问频率
        self._access_frequency[key] = self._access_frequency.get(key, 0) + 1
        if self._access_frequency[key] >= self._hot_threshold:
            self._hot_keys.add(key)
        
        # L1内存缓存查找
        l1_result = self._l1_get(key)
        if l1_result is not None:
            self._stats['l1_hits'] += 1
            logger.debug(f"🎯 L1缓存命中: {key}")
            return l1_result
        
        self._stats['l1_misses'] += 1
        
        # L2Redis缓存查找
        if self._redis_client:
            l2_result = self._l2_get(key)
            if l2_result is not None:
                self._stats['l2_hits'] += 1
                logger.debug(f"🎯 L2缓存命中: {key}")
                
                # 向L1填充
                self._l1_set(key, l2_result, data_type)
                return l2_result
                
            self._stats['l2_misses'] += 1
        
        self._stats['l3_misses'] += 1
        logger.debug(f"❌ 缓存未命中: {key}")
        return None

    def set(self, key: str, value: Any, data_type: DataType = DataType.BUSINESS_DATA, ttl: Optional[int] = None):
        """
        🚀 智能多级缓存设置
        根据数据类型和策略决定缓存级别
        """
        strategy_config = self._cache_strategies.get(data_type, self._cache_strategies[DataType.BUSINESS_DATA])
        levels = strategy_config['levels']
        strategy = strategy_config['strategy']
        
        # L1内存缓存
        if CacheLevel.L1_MEMORY in levels:
            l1_ttl = ttl or strategy_config['ttl']['l1']
            self._l1_set(key, value, data_type, l1_ttl)
            logger.debug(f"📝 L1缓存设置: {key}")
        
        # L2Redis缓存
        if CacheLevel.L2_REDIS in levels and self._redis_client:
            l2_ttl = ttl or strategy_config['ttl']['l2']
            self._l2_set(key, value, l2_ttl)
            logger.debug(f"📝 L2缓存设置: {key}")

    def get_or_compute(self, key: str, compute_func, *args, data_type: DataType = DataType.COMPUTATION_RESULT, **kwargs) -> Any:
        """
        🚀 获取或计算模式
        先尝试从缓存获取，缓存未命中时执行计算函数并缓存结果
        """
        # 尝试从缓存获取
        cached_result = self.get(key, data_type)
        if cached_result is not None:
            return cached_result
        
        # 缓存未命中，执行计算
        start_time = time.time()
        result = compute_func(*args, **kwargs)
        compute_time = time.time() - start_time
        
        # 缓存计算结果
        self.set(key, result, data_type)
        
        logger.debug(f"💻 计算并缓存: {key}, 耗时: {compute_time:.3f}s")
        return result

    def preload_algorithm_data(self, algorithm_type: str, data_requirements: Dict):
        """
        🚀 算法数据预加载
        根据智能算法选择器的需求预加载相关数据
        """
        logger.info(f"🚀 开始预加载算法数据: {algorithm_type}")
        start_time = time.time()
        
        preload_tasks = {
            'heuristic': ['device_priority', 'lot_priority', 'uph_data'],
            'simplified_ortools': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status'],
            'full_ortools': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status', 'uph_data', 'recipe_files'],
            'genetic': ['device_priority', 'lot_priority', 'test_specs'],
            'hybrid': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status', 'uph_data']
        }
        
        required_data = preload_tasks.get(algorithm_type, preload_tasks['hybrid'])
        preloaded_count = 0
        
        for data_key in required_data:
            if data_key in data_requirements:
                cache_key = f"preload_{algorithm_type}_{data_key}"
                if self.get(cache_key) is None:
                    # 预加载数据
                    data = data_requirements[data_key]
                    if data:
                        self.set(cache_key, data, DataType.BUSINESS_DATA, ttl=900)  # 15分钟
                        preloaded_count += 1
        
        preload_time = time.time() - start_time
        self._stats['preload_count'] += preloaded_count
        
        logger.info(f"✅ 算法数据预加载完成: {preloaded_count}项, 耗时: {preload_time:.3f}s")

    def invalidate_by_pattern(self, pattern: str):
        """
        🚀 按模式失效缓存
        支持通配符模式，用于数据更新时批量失效相关缓存
        """
        invalidated_count = 0
        
        # L1内存缓存失效
        with self._l1_lock:
            keys_to_remove = [key for key in self._l1_cache.keys() if self._match_pattern(key, pattern)]
            for key in keys_to_remove:
                del self._l1_cache[key]
                invalidated_count += 1
        
        # L2Redis缓存失效
        if self._redis_client:
            try:
                redis_keys = self._redis_client.keys(f"*{pattern}*")
                if redis_keys:
                    self._redis_client.delete(*redis_keys)
                    invalidated_count += len(redis_keys)
            except Exception as e:
                logger.warning(f"L2缓存失效失败: {e}")
        
        logger.info(f"🧹 按模式失效缓存: {pattern}, 失效数量: {invalidated_count}")

    def _l1_get(self, key: str) -> Optional[Any]:
        """L1内存缓存获取"""
        with self._l1_lock:
            if key in self._l1_cache:
                cached_item = self._l1_cache[key]
                
                # 检查TTL
                if time.time() - cached_item['timestamp'] < cached_item['ttl']:
                    # LRU更新：移到末尾
                    self._l1_cache.pop(key)
                    self._l1_cache[key] = cached_item
                    return cached_item['value']
                else:
                    # 过期，删除
                    del self._l1_cache[key]
        return None

    def _l1_set(self, key: str, value: Any, data_type: DataType, ttl: int = 300):
        """L1内存缓存设置"""
        with self._l1_lock:
            # 检查容量，LRU淘汰
            if len(self._l1_cache) >= self._l1_max_size:
                # 淘汰最久未使用的
                oldest_key = next(iter(self._l1_cache))
                del self._l1_cache[oldest_key]
                self._stats['eviction_count'] += 1
            
            # 为热点数据分配更长TTL
            if key in self._hot_keys:
                ttl = int(ttl * 1.5)
            
            self._l1_cache[key] = {
                'value': value,
                'timestamp': time.time(),
                'ttl': ttl,
                'data_type': data_type.value
            }

    def _l2_get(self, key: str) -> Optional[Any]:
        """L2Redis缓存获取"""
        try:
            data = self._redis_client.get(f"mlcache:{key}")
            if data:
                cached_item = pickle.loads(data)
                # 检查TTL
                if time.time() - cached_item['timestamp'] < cached_item['ttl']:
                    return cached_item['value']
                else:
                    # 过期，删除
                    self._redis_client.delete(f"mlcache:{key}")
        except Exception as e:
            logger.warning(f"L2缓存获取失败: {e}")
        return None

    def _l2_set(self, key: str, value: Any, ttl: int = 1800):
        """L2Redis缓存设置"""
        try:
            cached_item = {
                'value': value,
                'timestamp': time.time(),
                'ttl': ttl
            }
            self._redis_client.setex(
                f"mlcache:{key}",
                ttl,
                pickle.dumps(cached_item)
            )
        except Exception as e:
            logger.warning(f"L2缓存设置失败: {e}")

    def _match_pattern(self, key: str, pattern: str) -> bool:
        """简单的通配符模式匹配"""
        import fnmatch
        return fnmatch.fnmatch(key, pattern)

    def _start_cleanup_thread(self):
        """启动缓存清理线程"""
        def cleanup_loop():
            while True:
                try:
                    time.sleep(60)  # 每分钟清理一次
                    self._cleanup_expired_l1_cache()
                except Exception as e:
                    logger.error(f"缓存清理错误: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()
        logger.info("🧹 缓存清理线程已启动")

    def _cleanup_expired_l1_cache(self):
        """清理过期的L1缓存"""
        current_time = time.time()
        expired_keys = []
        
        with self._l1_lock:
            for key, cached_item in self._l1_cache.items():
                if current_time - cached_item['timestamp'] > cached_item['ttl']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._l1_cache[key]
        
        if expired_keys:
            logger.debug(f"🧹 清理过期L1缓存: {len(expired_keys)}项")

    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        total_requests = self._stats['total_requests']
        l1_hit_rate = (self._stats['l1_hits'] / total_requests * 100) if total_requests > 0 else 0
        l2_hit_rate = (self._stats['l2_hits'] / total_requests * 100) if total_requests > 0 else 0
        overall_hit_rate = ((self._stats['l1_hits'] + self._stats['l2_hits']) / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'l1_cache_size': len(self._l1_cache),
            'l1_hit_rate': round(l1_hit_rate, 2),
            'l2_hit_rate': round(l2_hit_rate, 2),
            'overall_hit_rate': round(overall_hit_rate, 2),
            'hot_keys_count': len(self._hot_keys),
            'preload_count': self._stats['preload_count'],
            'eviction_count': self._stats['eviction_count'],
            'redis_available': self._redis_client is not None
        }

    def clear_all_caches(self):
        """清空所有级别缓存"""
        # 清空L1内存缓存
        with self._l1_lock:
            self._l1_cache.clear()
        
        # 清空L2Redis缓存
        if self._redis_client:
            try:
                # 只清空我们的命名空间
                keys = self._redis_client.keys("mlcache:*")
                if keys:
                    self._redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"清空L2缓存失败: {e}")
        
        # 重置统计
        self._stats = {key: 0 for key in self._stats}
        self._hot_keys.clear()
        self._access_frequency.clear()
        
        logger.info("🧹 所有级别缓存已清空")

# 全局多级缓存实例
multilevel_cache = MultilevelCacheManager() 