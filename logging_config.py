#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE环境日志配置模块 - 优化版
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path

def setup_exe_logging(config_override=None):
    """设置exe环境的日志配置
    
    Args:
        config_override: 可选的配置覆盖参数
            {
                'app_max_bytes': 10*1024*1024,    # 应用日志文件最大大小
                'error_max_bytes': 5*1024*1024,   # 错误日志文件最大大小
                'backup_count': 5,                # 备份文件数量
                'log_level': 'INFO'               # 日志级别
            }
    """
    
    # 默认配置
    default_config = {
        'app_max_bytes': 10*1024*1024,    # 10MB
        'error_max_bytes': 5*1024*1024,   # 5MB  
        'backup_count': 5,
        'log_level': 'INFO'
    }
    
    # 合并用户配置
    config = default_config.copy()
    if config_override:
        config.update(config_override)
    
    # 确定日志目录
    if getattr(sys, 'frozen', False):
        # exe环境
        exe_dir = Path(sys.executable).parent
        logs_dir = exe_dir / 'logs'
    else:
        # 开发环境
        project_dir = Path(__file__).parent
        logs_dir = project_dir / 'logs'
    
    # 创建日志目录
    logs_dir.mkdir(exist_ok=True)
    
    # 配置日志格式 - 增加更详细的时间戳
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 创建文件处理器
    app_log_file = logs_dir / 'app.log'
    error_log_file = logs_dir / 'error.log'
    
    # 应用日志处理器（带轮转）- 使用配置化参数
    app_handler = logging.handlers.RotatingFileHandler(
        app_log_file,
        maxBytes=config['app_max_bytes'],
        backupCount=config['backup_count'],
        encoding='utf-8'
    )
    app_handler.setLevel(getattr(logging, config['log_level']))
    app_handler.setFormatter(formatter)
    
    # 错误日志处理器 - 更小的文件大小，更多备份
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=config['error_max_bytes'],
        backupCount=config['backup_count'] + 5,  # 错误日志保留更多备份
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    
    # 控制台处理器 - 简化格式
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.WARNING)  # 控制台只显示警告以上
    console_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config['log_level']))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加新处理器
    root_logger.addHandler(app_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # 减少第三方库日志噪音
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # 记录配置信息
    logging.info(f"📝 日志系统初始化完成")
    logging.info(f"📁 日志目录: {logs_dir}")
    logging.info(f"📊 应用日志: {config['app_max_bytes']//1024//1024}MB × {config['backup_count']}个")
    logging.info(f"❌ 错误日志: {config['error_max_bytes']//1024//1024}MB × {config['backup_count']+5}个")
    logging.info(f"🔧 日志级别: {config['log_level']}")
    
    return logs_dir

def get_log_info():
    """获取当前日志配置信息"""
    if getattr(sys, 'frozen', False):
        exe_dir = Path(sys.executable).parent
        logs_dir = exe_dir / 'logs'
    else:
        project_dir = Path(__file__).parent
        logs_dir = project_dir / 'logs'
    
    log_files = list(logs_dir.glob('*.log*')) if logs_dir.exists() else []
    
    return {
        'logs_dir': str(logs_dir),
        'log_files': [str(f) for f in log_files],
        'total_size': sum(f.stat().st_size for f in log_files) if log_files else 0
    }

if __name__ == "__main__":
    # 演示不同配置
    print("🧪 测试默认配置")
    setup_exe_logging()
    
    print("\n🧪 测试自定义配置")
    custom_config = {
        'app_max_bytes': 20*1024*1024,  # 20MB
        'error_max_bytes': 10*1024*1024,  # 10MB
        'backup_count': 3,
        'log_level': 'DEBUG'
    }
    setup_exe_logging(custom_config)
    
    # 显示日志信息
    info = get_log_info()
    print(f"\n📊 日志统计:")
    print(f"📁 目录: {info['logs_dir']}")
    print(f"📄 文件数: {len(info['log_files'])}")
    print(f"💾 总大小: {info['total_size']/1024/1024:.1f}MB")
