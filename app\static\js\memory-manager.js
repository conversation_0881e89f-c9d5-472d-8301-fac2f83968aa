/**
 * 内存管理器 - 防止内存泄漏
 */
class MemoryManager {
    constructor() {
        this.timers = new Map();
        this.intervals = new Map();
        this.listeners = new Map();
        this.observers = new Set();
        
        this.init();
    }
    
    init() {
        this.setupCleanupListeners();
        this.startMemoryMonitoring();
        console.log('🧠 内存管理器已启动');
    }
    
    // 定时器管理
    setTimeout(callback, delay, id = null) {
        const timerId = id || `timer_${Date.now()}_${Math.random()}`;
        
        if (this.timers.has(timerId)) {
            clearTimeout(this.timers.get(timerId));
        }
        
        const timer = setTimeout(() => {
            callback();
            this.timers.delete(timerId);
        }, delay);
        
        this.timers.set(timerId, timer);
        return timerId;
    }
    
    setInterval(callback, delay, id = null) {
        const intervalId = id || `interval_${Date.now()}_${Math.random()}`;
        
        if (this.intervals.has(intervalId)) {
            clearInterval(this.intervals.get(intervalId));
        }
        
        const interval = setInterval(callback, delay);
        this.intervals.set(intervalId, interval);
        return intervalId;
    }
    
    clearTimeout(id) {
        if (this.timers.has(id)) {
            clearTimeout(this.timers.get(id));
            this.timers.delete(id);
        }
    }
    
    clearInterval(id) {
        if (this.intervals.has(id)) {
            clearInterval(this.intervals.get(id));
            this.intervals.delete(id);
        }
    }
    
    // 事件监听器管理
    addEventListener(element, event, handler, options = {}) {
        const key = `${element.id || 'anonymous'}_${event}_${Date.now()}`;
        
        element.addEventListener(event, handler, options);
        this.listeners.set(key, { element, event, handler, options });
        
        return key;
    }
    
    removeEventListener(key) {
        const listener = this.listeners.get(key);
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler);
            this.listeners.delete(key);
        }
    }
    
    // Observer管理
    addObserver(observer) {
        this.observers.add(observer);
        return observer;
    }
    
    removeObserver(observer) {
        if (observer && typeof observer.disconnect === 'function') {
            observer.disconnect();
        }
        this.observers.delete(observer);
    }
    
    // 内存监控
    startMemoryMonitoring() {
        if (!performance.memory) {
            console.warn('⚠️ 浏览器不支持内存监控');
            return;
        }
        
        const baseline = performance.memory.usedJSHeapSize;
        
        this.setInterval(() => {
            const current = performance.memory.usedJSHeapSize;
            const increase = current - baseline;
            const increaseMB = increase / 1024 / 1024;
            
            if (increaseMB > 50) { // 50MB增长警告
                console.warn(`⚠️ 内存使用增长: ${increaseMB.toFixed(1)}MB`);
                this.suggestCleanup();
            }
        }, 30000, 'memory_monitor');
    }
    
    suggestCleanup() {
        console.log('🧹 建议执行内存清理...');
        
        // 清理API缓存
        if (window.apiClient && typeof window.apiClient.clearCache === 'function') {
            window.apiClient.clearCache();
        }
        
        // 清理过期的定时器
        this.cleanupExpiredTimers();
    }
    
    cleanupExpiredTimers() {
        // 清理可能泄漏的定时器
        let cleaned = 0;
        
        this.timers.forEach((timer, id) => {
            if (id.includes('temp_') || id.includes('auto_')) {
                clearTimeout(timer);
                this.timers.delete(id);
                cleaned++;
            }
        });
        
        if (cleaned > 0) {
            console.log(`🧹 清理了 ${cleaned} 个临时定时器`);
        }
    }
    
    setupCleanupListeners() {
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // 页面隐藏时部分清理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.partialCleanup();
            }
        });
    }
    
    partialCleanup() {
        // 清理非关键资源
        this.cleanupExpiredTimers();
    }
    
    cleanup() {
        console.log('🧹 执行内存清理...');
        
        // 清理所有定时器
        this.timers.forEach(timer => clearTimeout(timer));
        this.intervals.forEach(interval => clearInterval(interval));
        
        // 清理事件监听器
        this.listeners.forEach(listener => {
            listener.element.removeEventListener(listener.event, listener.handler);
        });
        
        // 清理观察器
        this.observers.forEach(observer => {
            if (observer && typeof observer.disconnect === 'function') {
                observer.disconnect();
            }
        });
        
        // 清空集合
        this.timers.clear();
        this.intervals.clear();
        this.listeners.clear();
        this.observers.clear();
        
        console.log('✅ 内存清理完成');
    }
    
    getStats() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            listeners: this.listeners.size,
            observers: this.observers.size,
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null
        };
    }
}

// 全局内存管理器
window.memoryManager = new MemoryManager();

// 替换原生方法
window.managedSetTimeout = (callback, delay, id) => window.memoryManager.setTimeout(callback, delay, id);
window.managedSetInterval = (callback, delay, id) => window.memoryManager.setInterval(callback, delay, id);
window.managedAddEventListener = (element, event, handler, options) => window.memoryManager.addEventListener(element, event, handler, options);
