#!/usr/bin/env python3
"""
测试排产算法改进前后对比
"""
import sys
sys.path.append('.')
from app.services.real_scheduling_service import RealSchedulingService
from app.utils.db_connection_pool import get_db_connection
import json

def test_scheduling_comparison():
    """测试排产算法改进前后对比"""
    print("=== 排产算法改进对比测试 ===")
    
    service = RealSchedulingService()
    
    # 获取真实的待排产批次数据
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY
                FROM ET_WAIT_LOT
                LIMIT 20
            """)
            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            wait_lots = [dict(zip(columns, row)) for row in rows]
            
        if not wait_lots:
            print("⚠️ 没有找到待排产批次，使用模拟数据")
            wait_lots = [
                {'LOT_ID': f'TEST_LOT_{i:03d}', 'DEVICE': 'HCHC-C-015', 'STAGE': 'FT',
                 'GOOD_QTY': 5000 + i*100}
                for i in range(10)
            ]
        
        print(f"📊 测试数据: {len(wait_lots)} 个批次")
        
        # 执行排产测试
        print("\n=== 执行排产测试 ===")
        results = service.execute_real_scheduling(
            algorithm='intelligent',
            optimization_target='balanced',
            user_id='test_user'
        )
        
        # 统计结果
        if results and results.get('schedule'):
            schedule = results['schedule']
            
            # 统计活跃机台数
            active_handlers = set()
            total_changeover_time = 0
            handler_loads = {}
            
            for lot in schedule:
                handler_id = lot.get('HANDLER_ID')
                if handler_id:
                    active_handlers.add(handler_id)
                    changeover_time = lot.get('changeover_time', 0)
                    total_changeover_time += changeover_time
                    
                    # 统计机台负载
                    if handler_id not in handler_loads:
                        handler_loads[handler_id] = {'lots': 0, 'total_time': 0}
                    handler_loads[handler_id]['lots'] += 1
                    handler_loads[handler_id]['total_time'] += float(lot.get('processing_time', 0) or 0)
            
            print(f"✅ 排产成功: {len(schedule)} 个批次")
            print(f"📈 活跃机台数: {len(active_handlers)}")
            print(f"⏱️ 总改机时间: {total_changeover_time} 分钟")
            print(f"📊 平均改机时间: {total_changeover_time/len(schedule):.1f} 分钟/批次")
            
            # 显示机台负载分布
            print("\n=== 机台负载分布 ===")
            sorted_handlers = sorted(handler_loads.items(), key=lambda x: x[1]['lots'], reverse=True)
            for i, (handler_id, load_info) in enumerate(sorted_handlers[:10]):  # 显示前10台
                print(f"{i+1:2d}. {handler_id}: {load_info['lots']} 批次, {load_info['total_time']:.1f}h")
            
            # 负载集中度分析
            lot_counts = [info['lots'] for info in handler_loads.values()]
            if lot_counts:
                max_lots = max(lot_counts)
                min_lots = min(lot_counts)
                avg_lots = sum(lot_counts) / len(lot_counts)
                print(f"\n📊 负载分布: 最大={max_lots}, 最小={min_lots}, 平均={avg_lots:.1f}")
                print(f"📈 集中度指标: {(max_lots - min_lots) / avg_lots:.2f} (越小越均衡)")
        else:
            print("❌ 排产失败或无结果")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scheduling_comparison()
