/*
 * AEC-FT ICP 主样式表
 * 车规芯片终测智能调度平台
 */

:root {
    --primary-color: #b72424;
    --primary-hover: #9a1f1f;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* 基础样式 */
body {
    line-height: 1.5;
    color: var(--gray-900);
    background-color: var(--gray-100);
}

/* 主题色调整 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* 导航栏样式 */
.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: bold;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

/* 固定顶部导航栏样式 */
.custom-navbar {
    position: fixed !important;
    top: 0 !important;
    left: 200px !important; /* 侧边栏宽度调整 */
    right: 0 !important;
    z-index: 1030 !important;
    padding: 0.5rem 1rem !important; /* 压缩导航栏高度 */
    background-color: var(--primary-color) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* 固定导航栏后，主内容区域需要上边距 */
.main-content {
    padding-top: 75px !important; /* 为固定导航栏留出空间 */
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    padding-bottom: 1.5rem !important;
    min-height: calc(100vh - 75px) !important;
}

/* 侧边栏样式 */
.sidebar {
    background-color: var(--primary-color);
    min-height: 100vh;
    width: 200px !important; /* 压缩侧边栏宽度 */
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1000 !important;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
    /* 内边距、字体大小、间距在全局压缩样式中统一定义 */
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link i {
    text-align: center;
    /* 图标宽度和间距在全局压缩样式中统一定义 */
}

/* 卡片样式 */
.card {
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 0.75rem 1rem !important; /* 增加内边距 */
    background-color: var(--gray-50, #f8f9fa);
    border-bottom: 1px solid var(--gray-200, #e9ecef);
    font-weight: 600;
}

.card-body {
    padding: 1rem !important; /* 显著增加内边距 */
}

.card-footer {
    padding: 0.75rem 1rem !important; /* 增加内边距 */
    background-color: var(--gray-50, #f8f9fa);
    border-top: 1px solid var(--gray-200, #e9ecef);
}

/* 为特定紧凑型卡片提供选项 */
.card-body-compact {
    padding: 0.5rem !important;
}

.table-responsive {
    max-height: 500px; /* 增加表格高度 */
}

/* 调整主内容区域布局 */
.col-md-9.col-lg-10 {
    margin-left: 200px !important; /* 与侧边栏宽度匹配 */
    padding-left: 0 !important;
    width: calc(100% - 200px) !important;
}

/* 响应式设计调整 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -200px; /* 调整为新的侧边栏宽度 */
        width: 200px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.2s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .custom-navbar {
        left: 0 !important;
    }
    
    .col-md-9.col-lg-10 {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .main-content {
        margin-left: 0;
        padding-top: 75px !important;
    }
}

/* 移动端固定导航栏调整 */
@media (max-width: 992px) {
    .custom-navbar {
        left: 0 !important;
    }
    
    .col-md-9.col-lg-10 {
        margin-left: 0 !important;
        width: 100% !important;
    }
}

/* 表格样式 */
.table {
    font-size: 0.875rem !important; /* 统一表格基础字体大小 */
    color: var(--gray-800, #343a40);
    --bs-table-bg: transparent;
    --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
    --bs-table-hover-bg: rgba(183, 36, 36, 0.05);
}

.table thead th {
    font-size: 0.8rem !important; /* 表头字体稍小并加粗 */
    font-weight: 600 !important;
    padding: 0.75rem 1rem !important; /* 增加表头内边距 */
    vertical-align: middle;
    background-color: var(--gray-100, #f8f9fa);
}

.table tbody td {
    padding: 0.65rem 1rem !important; /* 增加单元格内边距 */
    vertical-align: middle;
}

/* 斑马线颜色微调 */
.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.03);
}

/* 鼠标悬停效果 */
.table-hover > tbody > tr:hover {
    background-color: rgba(0,0,0,0.06);
    color: var(--dark-color);
}

/* 按钮样式增强 */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
}

/* 表单样式 */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid var(--gray-400);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(183, 36, 36, 0.25);
}

/* 分页样式 */
.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-link {
    color: var(--primary-color);
}

.page-link:hover {
    color: var(--primary-hover);
    background-color: var(--gray-200);
}

/* 进度条样式 */
.progress-bar {
    background-color: var(--primary-color);
}

/* 徽章样式 */
.badge.bg-primary {
    background-color: var(--primary-color) !important;
}

/* 警告框样式 */
.alert-primary {
    color: #722828;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--danger-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--gray-900);
    color: white;
    border-radius: 0.375rem;
}

/* 加载动画 */
.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

.spinner-border-sm {
    width: 0.75rem;
    height: 0.75rem;
    border-width: 0.1em;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-200);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 最后的微调 - 确保视觉一致性 */

/* 打印样式 */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 0.5rem !important;
    }
    
    .card-body {
        padding: 0.5rem !important;
    }
}

/* 确保小屏幕设备上的可读性 */
@media (max-width: 576px) {
    .btn {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.8rem !important;
    }
    
    .card-body {
        padding: 0.5rem !important;
    }
    
    .table td, .table th {
        padding: 0.25rem !important;
        font-size: 0.75rem !important;
    }
    
    .sidebar .nav-link {
        padding: 0.25rem 0.375rem !important;
        font-size: 0.75rem !important;
    }
}

/* 确保重要信息的可见性 */
.text-important {
    font-weight: 600 !important;
}

.bg-important {
    background-color: var(--warning-color) !important;
    color: #000 !important;
}
