
import uuid
import json
import os
from threading import Thread

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, task_id=None):
        self.task_id = task_id or str(uuid.uuid4())
        self.progress_file = f'instance/parse_progress_{self.task_id}.json'
        self.progress = {
            'task_id': self.task_id,
            'status': 'starting',
            'current_step': '',
            'progress_percent': 0,
            'total_files': 0,
            'processed_files': 0,
            'success_count': 0,
            'error_count': 0,
            'errors': [],
            'start_time': None,
            'end_time': None
        }
        self.update_progress()
    
    def update_progress(self, **kwargs):
        """更新进度"""
        self.progress.update(kwargs)
        
        # 计算百分比
        if self.progress['total_files'] > 0:
            self.progress['progress_percent'] = min(
                100,
                int((self.progress['processed_files'] / self.progress['total_files']) * 100)
            )
        
        # 保存到文件
        try:
            os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存进度失败: {e}")
    
    def complete(self, status='completed'):
        """完成任务"""
        import datetime
        self.update_progress(
            status=status,
            progress_percent=100,
            end_time=datetime.datetime.now().isoformat()
        )
    
    def error(self, error_msg):
        """记录错误"""
        self.progress['errors'].append({
            'timestamp': datetime.datetime.now().isoformat(),
            'message': error_msg
        })
        self.progress['error_count'] += 1
        self.update_progress()
