#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Excel解析器 v2.0
支持复杂Excel结构的智能识别和解析
"""

import os
import json
import logging
import pandas as pd
import datetime
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional, Union
import re
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 导入横向信息提取器
from app.services.horizontal_info_extractor import HorizontalInfoExtractor

logger = logging.getLogger(__name__)

def get_excel_engine(file_path: str) -> str:
    """根据文件扩展名选择合适的Excel读取引擎"""
    _, ext = os.path.splitext(file_path.lower())
    if ext == '.xlsx':
        return 'openpyxl'
    elif ext == '.xls':
        return 'xlrd'
    else:
        # 默认尝试openpyxl
        return 'openpyxl'

@dataclass
class CellPosition:
    """单元格位置"""
    row: int
    col: int
    value: Any = None
    
    def __str__(self):
        return f"({self.row}, {self.col}): {self.value}"

@dataclass
class FieldMapping:
    """字段映射配置"""
    field_name: str          # 目标字段名
    search_keywords: List[str]  # 搜索关键词
    required: bool = True    # 是否必需
    data_type: str = 'str'   # 数据类型
    validation_pattern: str = None  # 验证正则表达式
    
class ExcelStructureAnalyzer:
    """Excel结构分析器"""
    
    def __init__(self):
        # 定义完整的字段搜索配置（根据宜欣生产订单Excel模板）
        self.field_mappings = {
            # 核心必需字段
            'order_no': FieldMapping(
                field_name='订单号',
                search_keywords=['订单号', 'Order', 'order', '工单号', '单号'],
                required=True
            ),
            'lot_type': FieldMapping(
                field_name='Lot Type',
                search_keywords=['Lot Type', 'lot type', 'Lot_Type', 'lottype', '类型', '批次类型', 'LotType'],
                required=True
            ),
            
            # 产品信息字段
            'product_name': FieldMapping(
                field_name='产品名称',
                search_keywords=['产品', 'Product', 'product', '产品名', '产品名称', '标签名称'],
                required=False
            ),
            'circuit_name': FieldMapping(
                field_name='电路名称',
                search_keywords=['电路', 'Circuit', 'circuit', '电路名', '电路名称'],
                required=False
            ),
            'chip_name': FieldMapping(
                field_name='芯片名称',
                search_keywords=['芯片', 'Chip', 'chip', '芯片名', '芯片名称', 'IC名称'],
                required=False
            ),
            
            # 晶圆信息字段
            'wafer_id': FieldMapping(
                field_name='Wafer ID',
                search_keywords=['wafer', 'Wafer', 'WAFER', 'wafer_id', 'wafer id', 'Wafer ID'],
                required=False
            ),
            'wafer_size': FieldMapping(
                field_name='圆片尺寸',
                search_keywords=['圆片尺寸', '尺寸', 'Size', 'size', 'wafer size', '圆片大小'],
                required=False
            ),
            'diffusion_batch': FieldMapping(
                field_name='扩散批号',
                search_keywords=['扩散', '扩散批', '扩散批号', 'Diffusion', 'diffusion', 'Batch'],
                required=False
            ),
            'wafer_number': FieldMapping(
                field_name='片号',
                search_keywords=['片号', 'wafer', '晶圆号', '圆片号', 'Wafer No', 'wafer no'],
                required=False
            ),
            
            # 包装信息字段
            'package_qty': FieldMapping(
                field_name='送包只数',
                search_keywords=['送包', '送包只数', '只数', '包数', 'Package Qty', 'package qty', 'Qty'],
                required=False,
                data_type='int'
            ),
            'package_pieces': FieldMapping(
                field_name='送包片数',
                search_keywords=['送包', '送包片数', '片数', 'Pieces', 'pieces', 'Package Pieces', '数量'],
                required=False,
                data_type='int'
            ),
            'assembly_method': FieldMapping(
                field_name='装片方式',
                search_keywords=['装片', '装片方式', 'Assembly', 'assembly', '装配方式', '装配'],
                required=False
            ),
            'package_form': FieldMapping(
                field_name='封装形式',
                search_keywords=['封装', '封装形式', 'Package', 'package', 'Package Form', '封装类型'],
                required=False
            ),
            
            # 设计信息字段
            'drawing_number': FieldMapping(
                field_name='图号',
                search_keywords=['图号', 'Drawing', 'drawing', '图纸号', 'Drawing No', '设计图号'],
                required=False
            ),
            
            # 交期和编码字段
            'delivery_date': FieldMapping(
                field_name='交期',
                search_keywords=['交期', 'delivery', 'Delivery', '交付日期', '预计交期', '交货期'],
                required=False,
                data_type='date'
            ),
            'item_code': FieldMapping(
                field_name='Item Code',
                search_keywords=['Item Code', 'item code', 'ITEM CODE', 'ItemCode', 'Item', 'Code', '编码'],
                required=False
            ),
            
            # 地址和其他信息
            'shipping_address': FieldMapping(
                field_name='出货地址',
                search_keywords=['出货', '出货地址', 'Address', 'address', 'Ship', 'Shipping', '发货地址'],
                required=False
            ),
            'wafer_lot': FieldMapping(
                field_name='wafer lot',
                search_keywords=['wafer lot', 'Wafer Lot', 'WAFER LOT', 'WaferLot', 'lot', 'Lot'],
                required=False
            ),
            'order_attribute': FieldMapping(
                field_name='订单属性',
                search_keywords=['订单属性', '属性', 'Attribute', 'attribute', 'Order Attribute', '订单类型'],
                required=False
            )
        }
    
    def analyze_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析Excel文件结构"""
        try:
            analysis_result = {
                'header_candidates': [],
                'data_regions': [],
                'field_positions': {},
                'merged_cells': [],
                'data_start_row': None,
                'confidence_score': 0.0
            }
            
            rows, cols = df.shape
            logger.info(f"分析Excel结构: {rows} 行 x {cols} 列")
            
            # 1. 寻找表头候选行
            header_candidates = self._find_header_candidates(df)
            analysis_result['header_candidates'] = header_candidates
            
            # 2. 定位关键字段
            field_positions = self._locate_key_fields(df, header_candidates)
            analysis_result['field_positions'] = field_positions
            
            # 3. 确定数据区域
            data_regions = self._identify_data_regions(df, field_positions)
            analysis_result['data_regions'] = data_regions
            
            # 4. 计算置信度
            confidence = self._calculate_confidence(field_positions, data_regions)
            analysis_result['confidence_score'] = confidence
            
            # 5. 确定最佳数据起始行
            if data_regions:
                analysis_result['data_start_row'] = min(region['start_row'] for region in data_regions)
            
            logger.info(f"结构分析完成，置信度: {confidence:.2f}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Excel结构分析失败: {e}")
            return {'error': str(e)}
    
    def _find_header_candidates(self, df: pd.DataFrame) -> List[Dict]:
        """寻找可能的表头行"""
        candidates = []
        rows, cols = df.shape
        
        # 搜索前30行或总行数的80%，取较大值
        search_limit = min(30, max(rows // 2, int(rows * 0.8)))
        
        for row_idx in range(search_limit):
            row_data = df.iloc[row_idx]
            non_empty_count = sum(1 for val in row_data if pd.notna(val) and str(val).strip() != '')
            
            # 降低门槛，从30%改为15%
            if non_empty_count >= max(3, cols * 0.15):  # 至少3列或15%的列有数据
                # 检查关键字段
                text_content = ' '.join(str(val).lower() for val in row_data if pd.notna(val))
                key_field_count = 0
                
                for mapping in self.field_mappings.values():
                    for keyword in mapping.search_keywords:
                        if keyword.lower() in text_content:
                            key_field_count += 1
                            break
                
                # 特别加分：如果包含"订单号"和"Lot Type"
                special_bonus = 0
                if '订单号' in text_content and 'lot type' in text_content:
                    special_bonus = 2.0
                
                candidate = {
                    'row_index': row_idx,
                    'non_empty_count': non_empty_count,
                    'key_field_count': key_field_count,
                    'score': non_empty_count * 0.3 + key_field_count * 0.6 + special_bonus,
                    'content': [str(val) if pd.notna(val) else '' for val in row_data]
                }
                candidates.append(candidate)
        
        candidates.sort(key=lambda x: x['score'], reverse=True)
        return candidates[:5]
    
    def _locate_key_fields(self, df: pd.DataFrame, header_candidates: List[Dict]) -> Dict[str, CellPosition]:
        """定位关键字段的位置"""
        field_positions = {}
        
        for candidate in header_candidates:
            row_idx = candidate['row_index']
            row_data = df.iloc[row_idx]
            
            # 特殊处理：先找到所有"送包"列
            package_columns = []
            for col_idx, cell_value in enumerate(row_data):
                if pd.notna(cell_value):
                    cell_text = str(cell_value).strip()
                    if cell_text == '送包':
                        package_columns.append(col_idx)
            
            # 如果找到多个"送包"列，分别分配给送包只数和送包片数
            if len(package_columns) >= 2:
                field_positions['package_qty'] = CellPosition(
                    row=row_idx, col=package_columns[0], value='送包'
                )
                field_positions['package_pieces'] = CellPosition(
                    row=row_idx, col=package_columns[1], value='送包'
                )
                logger.debug(f"找到送包字段: 只数列{package_columns[0]}, 片数列{package_columns[1]}")
            elif len(package_columns) == 1:
                # 只有一个送包列，分配给送包只数
                field_positions['package_qty'] = CellPosition(
                    row=row_idx, col=package_columns[0], value='送包'
                )
                logger.debug(f"找到送包字段: 只数列{package_columns[0]}")
            
            # 处理其他字段
            for field_key, mapping in self.field_mappings.items():
                if field_key in field_positions:
                    continue
                
                # 跳过已经特殊处理的送包字段
                if field_key in ['package_qty', 'package_pieces']:
                    continue
                
                for col_idx, cell_value in enumerate(row_data):
                    if pd.notna(cell_value):
                        cell_text = str(cell_value).strip()
                        for keyword in mapping.search_keywords:
                            if keyword.lower() in cell_text.lower():
                                field_positions[field_key] = CellPosition(
                                    row=row_idx, col=col_idx, value=cell_text
                                )
                                logger.debug(f"找到字段 {mapping.field_name}: {field_positions[field_key]}")
                                break
                        if field_key in field_positions:
                            break
        
        return field_positions
    
    def _identify_data_regions(self, df: pd.DataFrame, field_positions: Dict[str, CellPosition]) -> List[Dict]:
        """识别数据区域 - 修复：支持100+行纵向数据扫描"""
        data_regions = []
        
        if not field_positions:
            return data_regions
        
        header_rows = list(set(pos.row for pos in field_positions.values()))
        
        for header_row in header_rows:
            start_row = header_row + 1
            rows, cols = df.shape
            end_row = start_row
            consecutive_empty_rows = 0
            max_empty_tolerance = 10  # 增加空行容忍度，支持稀疏数据
            
            # 扫描到文件末尾，不限制在20-30行
            for row_idx in range(start_row, rows):
                row_data = df.iloc[row_idx]
                # 检查关键列是否有数据（订单号、Lot Type等）
                key_columns_with_data = 0
                for field_key, pos in field_positions.items():
                    if pos.col < len(row_data):
                        cell_value = row_data.iloc[pos.col] if pos.col < len(row_data) else None
                        if pd.notna(cell_value) and str(cell_value).strip():
                            key_columns_with_data += 1
                
                # 如果关键列有数据，认为是有效行
                if key_columns_with_data >= 2:  # 至少2个关键字段有数据
                    end_row = row_idx
                    consecutive_empty_rows = 0
                    logger.debug(f"发现有效数据行 {row_idx + 1}，关键字段数: {key_columns_with_data}")
                else:
                    consecutive_empty_rows += 1
                    # 只有连续空行超过容忍度才停止扫描
                    if consecutive_empty_rows >= max_empty_tolerance:
                        logger.debug(f"连续空行达到{max_empty_tolerance}行，停止扫描")
                        break
            
            if end_row > start_row:
                region = {
                    'header_row': header_row,
                    'start_row': start_row,
                    'end_row': end_row,
                    'total_rows': end_row - start_row + 1
                }
                logger.info(f"识别数据区域: 第{start_row + 1}-{end_row + 1}行，共{region['total_rows']}行数据")
                data_regions.append(region)
        
        return data_regions
    
    def _calculate_confidence(self, field_positions: Dict[str, CellPosition], data_regions: List[Dict]) -> float:
        """计算分析结果的置信度"""
        confidence = 0.0
        
        # 基于关键字段
        required_fields = sum(1 for mapping in self.field_mappings.values() if mapping.required)
        found_required_fields = sum(1 for key, mapping in self.field_mappings.items() 
                                  if mapping.required and key in field_positions)
        
        if required_fields > 0:
            confidence += (found_required_fields / required_fields) * 0.6
        
        # 基于数据区域
        if data_regions:
            avg_data_rows = sum(region['total_rows'] for region in data_regions) / len(data_regions)
            if avg_data_rows >= 5:
                confidence += 0.3
            elif avg_data_rows >= 2:
                confidence += 0.2
            else:
                confidence += 0.1
        
        # 基于字段完整性
        total_fields = len(self.field_mappings)
        found_fields = len(field_positions)
        if total_fields > 0:
            confidence += (found_fields / total_fields) * 0.1
        
        return min(confidence, 1.0)

class EnhancedExcelParser:
    """增强的Excel解析器"""
    
    def __init__(self, downloads_dir: str = "downloads", search_subdirs: bool = True):
        self.downloads_dir = downloads_dir
        self.search_subdirs = search_subdirs
        self.analyzer = ExcelStructureAnalyzer()
        self.classification_rules = {}
        
        # 继承原有的配置
        self.yixin_keywords = ['宜欣', '生产订单', '宜欣生产订单']
        self.engineering_summary_file = os.path.join(downloads_dir, "FT工程订单汇总表.xlsx")
        self.production_summary_file = os.path.join(downloads_dir, "FT量产订单汇总表.xlsx")
        
        # 初始化横向信息提取器
        try:
            self.horizontal_extractor = HorizontalInfoExtractor()
            self.horizontal_extraction_enabled = True
            logger.info("横向信息提取器初始化成功")
        except ImportError as e:
            logger.warning(f"横向信息提取器不可用: {e}")
            self.horizontal_extractor = None
            self.horizontal_extraction_enabled = False
        
        # 默认分类规则
        self.production_keywords = [
            '量产-P', '量产批', '小批量-PE', 'PROD', 'Production', '量產'
        ]
        
        # 回退解析器
        self._init_fallback_parser()
    
    def _init_fallback_parser(self):
        """初始化回退解析器"""
        try:
            from app.services.order_excel_parser import OrderExcelParser
            self.fallback_parser = OrderExcelParser()
        except Exception as e:
            logger.warning(f"初始化回退解析器失败: {e}")
            self.fallback_parser = None
    
    def parse_file_with_structure_analysis(self, file_path: str) -> Dict[str, Any]:
        """使用结构分析解析文件（包含横向和纵向信息）"""
        try:
            logger.info(f"开始解析文件: {file_path}")
            
            # 选择合适的引擎读取Excel文件
            engine = get_excel_engine(file_path)
            df = pd.read_excel(file_path, engine=engine, header=None)
            logger.info(f"文件尺寸: {df.shape[0]} 行 x {df.shape[1]} 列")
            
            # 1. 提取横向信息（前13行的单据信息）
            horizontal_info = {}
            if self.horizontal_extraction_enabled:
                try:
                    horizontal_result = self.horizontal_extractor.extract_horizontal_info(df)
                    if horizontal_result['status'] == 'success':
                        horizontal_info = horizontal_result['horizontal_data']
                        logger.info(f"横向信息提取成功: {len(horizontal_result['found_fields'])} 个字段")
                    else:
                        logger.warning(f"横向信息提取失败: {horizontal_result.get('message', '未知错误')}")
                except Exception as e:
                    logger.warning(f"横向信息提取异常: {e}")
            
            # 2. 分析纵向数据结构
            structure = self.analyzer.analyze_structure(df)
            
            if 'error' in structure:
                return {
                    'status': 'error',
                    'message': f'结构分析失败: {structure["error"]}',
                    'data': [],
                    'horizontal_info': horizontal_info
                }
            
            confidence = structure.get('confidence_score', 0.0)
            if confidence < 0.3:
                return {
                    'status': 'error',
                    'message': f'文件结构置信度过低 ({confidence:.2f})，可能不是标准的宜欣订单文件',
                    'data': [],
                    'structure_analysis': structure,
                    'horizontal_info': horizontal_info
                }
            
            # 3. 提取纵向数据
            parsed_data = self._extract_data_with_structure(df, structure, horizontal_info)
            
            # 4. 为每条纵向记录关联横向信息
            if horizontal_info:
                for record in parsed_data:
                    record.update({
                        '单据编号': horizontal_info.get('单据编号', ''),
                        '单据类型': horizontal_info.get('单据类型', ''),
                        '加工属性': horizontal_info.get('加工属性', ''),
                        '下单日期': horizontal_info.get('下单日期', ''),
                        '加工承揽商': horizontal_info.get('加工承揽商', ''),
                        '加工委托方': horizontal_info.get('加工委托方', '')
                    })
            
            return {
                'status': 'success',
                'message': f'成功解析 {len(parsed_data)} 条数据 (置信度: {confidence:.2f})',
                'data': parsed_data,
                'file_name': os.path.basename(file_path),
                'structure_analysis': structure,
                'confidence_score': confidence,
                'horizontal_info': horizontal_info,
                'horizontal_summary': self.horizontal_extractor.format_horizontal_summary(horizontal_info) if self.horizontal_extraction_enabled else ''
            }
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {str(e)}")
            return {
                'status': 'error',
                'message': f'解析文件失败: {str(e)}',
                'data': [],
                'file_name': os.path.basename(file_path) if file_path else ''
            }
    
    def _extract_data_with_structure(self, df: pd.DataFrame, structure: Dict[str, Any], horizontal_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于结构分析提取数据"""
        parsed_data = []
        field_positions = structure.get('field_positions', {})
        data_regions = structure.get('data_regions', [])
        
        if not field_positions or not data_regions:
            logger.warning("未找到有效的字段位置或数据区域")
            return parsed_data
        
        # 为每个数据区域提取数据
        for region in data_regions:
            start_row = region['start_row']
            end_row = region['end_row']
            
            for row_idx in range(start_row, end_row + 1):
                try:
                    row_data = self._extract_row_data(df, row_idx, field_positions)
                    if row_data and self._is_valid_record(row_data):
                        # 添加元数据
                        row_data['源文件'] = os.path.basename(getattr(df, 'name', ''))
                        row_data['导入时间'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        row_data['数据行号'] = row_idx + 1
                        
                        # 分类处理
                        lot_type = row_data.get('Lot Type', '')
                        row_data['分类结果'] = self._classify_lot_type(lot_type)
                        
                        # 关联横向信息
                        row_data.update(horizontal_info)
                        
                        parsed_data.append(row_data)
                        
                except Exception as e:
                    logger.warning(f"提取第 {row_idx} 行数据失败: {e}")
                    continue
        
        return parsed_data
    
    def _extract_row_data(self, df: pd.DataFrame, row_idx: int, field_positions: Dict[str, CellPosition]) -> Dict[str, Any]:
        """提取单行数据"""
        row_data = {}
        
        for field_key, position in field_positions.items():
            try:
                value = df.iloc[row_idx, position.col]
                cleaned_value = self._clean_cell_value(value)
                
                mapping = self.analyzer.field_mappings.get(field_key)
                if mapping:
                    field_name = mapping.field_name
                    row_data[field_name] = cleaned_value
                else:
                    row_data[field_key] = cleaned_value
                    
            except Exception as e:
                logger.debug(f"提取字段 {field_key} 失败: {e}")
                mapping = self.analyzer.field_mappings.get(field_key)
                field_name = mapping.field_name if mapping else field_key
                row_data[field_name] = ''
        
        return row_data
    
    def _clean_cell_value(self, value: Any) -> str:
        """清理单元格值"""
        if pd.isna(value):
            return ''
        
        if isinstance(value, (int, float)):
            if isinstance(value, float) and value.is_integer():
                return str(int(value))
            return str(value)
        
        return str(value).strip()
    
    def _is_valid_record(self, record: Dict[str, Any]) -> bool:
        """验证记录是否有效"""
        order_no = record.get('订单号', '').strip()
        lot_type = record.get('Lot Type', '').strip()
        return bool(order_no) and bool(lot_type)
    
    def _classify_lot_type(self, lot_type: str) -> str:
        """分类Lot Type"""
        if not lot_type:
            lot_type = '<空值>'
        
        lot_type = lot_type.strip()
        
        # 优先使用自定义分类规则
        if self.classification_rules and lot_type in self.classification_rules:
            return self.classification_rules[lot_type]
        
        # 默认分类逻辑
        lot_type_lower = lot_type.lower()
        
        for keyword in self.production_keywords:
            if keyword.lower() in lot_type_lower:
                return '量产'
        
        return '工程'
    
    def fallback_to_original_parser(self, file_path: str) -> Dict[str, Any]:
        """回退到原解析器"""
        logger.info(f"回退到原始解析方法: {file_path}")
        
        return self._parse_with_original_method(file_path)
    
    def parse_order_file(self, file_path: str, use_fallback: bool = True) -> Dict[str, Any]:
        """解析订单文件（主接口）"""
        # 首先尝试增强解析
        result = self.parse_file_with_structure_analysis(file_path)
        
        # 如果增强解析失败且启用回退，使用原解析器
        if result.get('status') != 'success' and use_fallback:
            logger.info("增强解析失败，回退到原始解析器")
            fallback_result = self._parse_with_original_method(file_path)
            if fallback_result.get('success'):
                # 转换格式为与structure_analysis一致
                result = {
                    'status': 'success',
                    'message': fallback_result['message'],
                    'data': fallback_result['data'],
                    'horizontal_info': fallback_result.get('horizontal_info', {}),
                    'structure_analysis': fallback_result.get('structure_analysis', {}),
                    'confidence_score': fallback_result.get('confidence', 0.5),
                    'parse_method': 'fallback'
                }
            else:
                result = {
                    'status': 'error',
                    'message': fallback_result['message'],
                    'data': [],
                    'parse_method': 'fallback_failed'
                }
        else:
            result['parse_method'] = 'enhanced'
        
        return result
    
    def batch_parse_files(self, file_paths: List[str] = None) -> Dict[str, Any]:
        """批量解析文件"""
        try:
            # 如果没有指定文件，扫描所有宜欣订单文件
            if file_paths is None:
                from app.services.order_excel_parser import OrderExcelParser
                scanner = OrderExcelParser(self.downloads_dir, self.search_subdirs)
                scan_result = scanner.scan_order_files()
                
                if scan_result['status'] != 'success':
                    return scan_result
                
                file_paths = [file_info['path'] for file_info in scan_result['yixin_order_files']]
            
            results = {
                'status': 'success',
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'engineering_records': 0,
                'production_records': 0,
                'unknown_records': 0,
                'file_results': [],
                'engineering_data': [],
                'production_data': [],
                'unknown_data': [],
                'enhanced_success': 0,
                'fallback_success': 0
            }
            
            for file_path in file_paths:
                logger.info(f"处理文件: {os.path.basename(file_path)}")
                parse_result = self.parse_order_file(file_path)
                
                file_result = {
                    'file_name': os.path.basename(file_path),
                    'status': parse_result['status'],
                    'message': parse_result['message'],
                    'records': len(parse_result['data']) if parse_result['status'] == 'success' else 0,
                    'parse_method': parse_result.get('parse_method', 'unknown'),
                    'confidence_score': parse_result.get('confidence_score', 0.0)
                }
                
                if parse_result['status'] == 'success':
                    results['processed_files'] += 1
                    results['total_records'] += len(parse_result['data'])
                    
                    # 统计解析方法
                    if parse_result.get('parse_method') == 'enhanced':
                        results['enhanced_success'] += 1
                    elif parse_result.get('parse_method') == 'fallback':
                        results['fallback_success'] += 1
                    
                    # 按分类结果分组
                    for record in parse_result['data']:
                        classification = record.get('分类结果', '未知')
                        if classification == '工程':
                            results['engineering_data'].append(record)
                            results['engineering_records'] += 1
                        elif classification == '量产':
                            results['production_data'].append(record)
                            results['production_records'] += 1
                        else:
                            results['unknown_data'].append(record)
                            results['unknown_records'] += 1
                else:
                    results['failed_files'] += 1
                
                results['file_results'].append(file_result)
            
            results['message'] = (f"处理完成：成功 {results['processed_files']} 个文件，"
                                f"失败 {results['failed_files']} 个文件，"
                                f"共 {results['total_records']} 条记录 "
                                f"(增强解析: {results['enhanced_success']}, 回退解析: {results['fallback_success']})")
            
            return results
            
        except Exception as e:
            logger.error(f"批量解析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f"批量解析失败: {str(e)}",
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'file_results': []
            }

    def _parse_with_original_method(self, file_path: str) -> Dict[str, Any]:
        """使用原始方法解析文件"""
        try:
            # 选择合适的引擎
            engine = get_excel_engine(file_path)
            logger.info(f"回退到原始解析方法: {file_path}")
            
            # 尝试不同的读取方式
            df = pd.read_excel(file_path, engine=engine, header=None)
            
            # 使用固定的结构假设（基于宜欣订单模板）
            if df.shape[0] >= 15:  # 确保有足够的行数
                # 表头通常在第13行（索引12）
                headers = df.iloc[12].values
                
                # 清理表头
                clean_headers = []
                for i, header in enumerate(headers):
                    if pd.isna(header) or str(header).strip() == '':
                        clean_headers.append(f'Column_{i}')
                    else:
                        clean_headers.append(str(header).strip())
                
                # 数据从第14行开始（索引13）
                data_df = df.iloc[13:].copy()
                data_df.columns = clean_headers[:len(data_df.columns)]
                data_df = data_df.reset_index(drop=True)
                
                # 过滤空行
                data_df = data_df.dropna(how='all')
                
                # 转换为字典列表
                data = []
                for _, row in data_df.iterrows():
                    row_dict = {}
                    for col in data_df.columns:
                        value = row[col]
                        if pd.notna(value):
                            row_dict[col] = str(value).strip()
                        else:
                            row_dict[col] = ""
                    
                    # 只保留非空行
                    if any(v for v in row_dict.values() if v):
                        data.append(row_dict)
                
                return {
                    'success': True,
                    'message': '使用原始方法解析成功',
                    'data': data,
                    'horizontal_info': {},
                    'structure_analysis': {'method': 'fallback'},
                    'confidence': 0.5
                }
            
        except Exception as e:
            logger.error(f"原始方法解析失败: {e}")
        
        return {
            'success': False,
            'message': f'所有解析方法都失败',
            'data': [],
            'horizontal_info': {},
            'structure_analysis': {},
            'confidence': 0.0
        }