#!/usr/bin/env python3
"""
字段卡控功能测试脚本
快速验证数据导入安全机制是否正常工作

Author: AI Assistant
Date: 2025-01-16
Version: 1.0
"""

import os
import sys
import tempfile
import pandas as pd
import json
from datetime import datetime, date
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_excel_files():
    """创建测试用的Excel文件"""
    
    # 创建临时目录
    test_dir = os.path.join(tempfile.gettempdir(), 'field_control_test')
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    logger.info(f"创建测试Excel文件到目录: {test_dir}")
    
    # 1. 创建CT表测试数据（正确格式）
    ct_data = {
        'LOT_ID': ['LOT001', 'LOT002', 'LOT003'],
        'DEVICE': ['DEV_A', 'DEV_B', 'DEV_A'],
        'STAGE': ['STAGE1', 'STAGE2', 'STAGE1'],
        'CHIP_ID': ['CHIP001', 'CHIP002', 'CHIP001'],
        'PKG_PN': ['PKG_A', 'PKG_B', 'PKG_A'],
        'QTY': [100, 150, 200],
        'YIELD_RATE': [95.5, 97.2, 93.8],
        'HANDLER_ID': ['H001', 'H002', 'H001'],
        'TESTER_ID': ['T001', 'T002', 'T001']
    }
    
    ct_df = pd.DataFrame(ct_data)
    ct_file = os.path.join(test_dir, 'CT_test_correct.xlsx')
    ct_df.to_excel(ct_file, sheet_name='CT', index=False)
    logger.info(f"✅ 创建CT测试文件: {ct_file}")
    
    # 2. 创建WIP_LOT表测试数据（正确格式）
    wip_data = {
        'LOT_ID': ['WIP001', 'WIP002', 'WIP003'],
        'DEVICE': ['DEV_A', 'DEV_B', 'DEV_C'],
        'STAGE': ['STAGE1', 'STAGE2', 'STAGE3'],
        'QTY': [50, 75, 100],
        'DUE_DATE': [date(2025, 2, 1), date(2025, 2, 5), date(2025, 2, 10)],
        'PRIORITY': ['high', 'medium', 'low'],
        'PROD_ID': ['PROD001', 'PROD002', 'PROD003'],
        'STATUS': ['waiting', 'processing', 'waiting']
    }
    
    wip_df = pd.DataFrame(wip_data)
    wip_file = os.path.join(test_dir, 'WIP_LOT_test_correct.xlsx')
    wip_df.to_excel(wip_file, sheet_name='wip_lot', index=False)
    logger.info(f"✅ 创建WIP_LOT测试文件: {wip_file}")
    
    # 3. 创建设备优先级配置测试数据（正确格式）
    device_priority_data = {
        'device': ['DEV_A', 'DEV_B', 'DEV_C'],
        'priority': [1, 2, 3],
        'from_time': [datetime(2025, 1, 1, 8, 0), datetime(2025, 1, 1, 9, 0), datetime(2025, 1, 1, 10, 0)],
        'end_time': [datetime(2025, 1, 31, 18, 0), datetime(2025, 1, 31, 18, 0), datetime(2025, 1, 31, 18, 0)],
        'user': ['admin', 'user1', 'user2']
    }
    
    device_df = pd.DataFrame(device_priority_data)
    device_file = os.path.join(test_dir, 'devicepriorityconfig_test.xlsx')
    device_df.to_excel(device_file, sheet_name='Sheet1', index=False)
    logger.info(f"✅ 创建设备优先级配置测试文件: {device_file}")
    
    # 4. 创建错误格式测试文件（缺少必填字段）
    error_data = {
        'DEVICE': ['DEV_A', 'DEV_B'],  # 缺少LOT_ID必填字段
        'STAGE': ['STAGE1', 'STAGE2'],
        'QTY': [100, 150]
    }
    
    error_df = pd.DataFrame(error_data)
    error_file = os.path.join(test_dir, 'CT_test_missing_required.xlsx')
    error_df.to_excel(error_file, sheet_name='CT', index=False)
    logger.info(f"⚠️  创建错误格式测试文件: {error_file}")
    
    # 5. 创建字段映射测试文件（不同的表头名称）
    mapping_data = {
        'Lot_ID': ['MAP001', 'MAP002'],  # 注意大小写和下划线
        'Device_Name': ['DEV_A', 'DEV_B'],
        'Stage_Name': ['STAGE1', 'STAGE2'],
        'Quantity': [100, 150],
        'Priority_Level': ['high', 'medium']
    }
    
    mapping_df = pd.DataFrame(mapping_data)
    mapping_file = os.path.join(test_dir, 'WIP_LOT_test_mapping.xlsx')
    mapping_df.to_excel(mapping_file, sheet_name='wip_lot', index=False)
    logger.info(f"🔄 创建字段映射测试文件: {mapping_file}")
    
    return test_dir

def run_table_structure_validation():
    """运行表结构验证测试"""
    
    logger.info("🔍 开始表结构验证测试...")
    
    try:
        from table_structure_validator import TableStructureValidator
        
        validator = TableStructureValidator()
        success = validator.run_validation()
        
        if success:
            logger.info("✅ 表结构验证测试通过")
            return True
        else:
            logger.error("❌ 表结构验证测试失败")
            return False
            
    except ImportError as e:
        logger.error(f"❌ 无法导入表结构验证器: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 表结构验证测试失败: {e}")
        return False

def run_safe_import_test(test_dir):
    """运行安全导入测试"""
    
    logger.info("🔒 开始安全导入测试...")
    
    try:
        from .import_excel_to_mysql import import_excel_files_safely
        
        # 测试正确格式文件导入
        logger.info("测试正确格式文件导入...")
        success, result = import_excel_files_safely(test_dir)
        
        if success:
            logger.info(f"✅ 安全导入测试通过: {result['message']}")
            logger.info(f"   总记录数: {result['total_records']}")
            logger.info(f"   处理时间: {result['processing_time']} 秒")
            return True
        else:
            logger.warning(f"⚠️  安全导入测试部分失败: {result.get('message', '未知错误')}")
            if 'warnings' in result:
                for warning in result['warnings']:
                    logger.warning(f"   警告: {warning}")
            return False
            
    except ImportError as e:
        logger.error(f"❌ 无法导入安全导入工具: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 安全导入测试失败: {e}")
        return False

def test_field_mapping():
    """测试字段映射功能"""
    
    logger.info("🔄 开始字段映射测试...")
    
    try:
        from .import_excel_to_mysql import BUSINESS_TABLE_SCHEMAS, validate_and_map_fields
        import pandas as pd
        
        # 创建测试数据
        test_data = {
            'Lot_ID': ['TEST001'],  # 应该映射到LOT_ID
            'Device_Name': ['DEV_TEST'],  # 应该无法映射（没有配置）
            'DEVICE': ['DEV_TEST'],  # 应该直接映射
            'STAGE': ['STAGE_TEST']  # 应该直接映射
        }
        
        df = pd.DataFrame(test_data)
        
        # 模拟现有表结构
        existing_structure = {
            'LOT_ID': {'type': 'varchar(50)', 'nullable': False},
            'DEVICE': {'type': 'varchar(50)', 'nullable': False},
            'STAGE': {'type': 'varchar(50)', 'nullable': False}
        }
        
        # 测试映射
        if 'wip_lot' in BUSINESS_TABLE_SCHEMAS:
            mapped_df, mapped_fields = validate_and_map_fields(df, 'wip_lot', existing_structure)
            logger.info(f"✅ 字段映射测试完成，映射字段: {mapped_fields}")
            return True
        else:
            logger.warning("⚠️  wip_lot表未在配置中找到")
            return False
            
    except Exception as e:
        logger.error(f"❌ 字段映射测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'field_control_test_report_{timestamp}.json'
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_results': results,
        'summary': {
            'total_tests': len(results),
            'passed': sum(1 for r in results.values() if r),
            'failed': sum(1 for r in results.values() if not r)
        }
    }
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 测试报告已保存: {report_file}")
        return report_file
    except Exception as e:
        logger.error(f"保存测试报告失败: {e}")
        return None

def cleanup_test_files(test_dir):
    """清理测试文件"""
    
    try:
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            logger.info(f"🧹 清理测试文件: {test_dir}")
    except Exception as e:
        logger.warning(f"清理测试文件失败: {e}")

def main():
    """主测试函数"""
    
    print("🧪 字段卡控功能测试")
    print("=" * 50)
    
    # 存储测试结果
    test_results = {}
    
    # 1. 创建测试文件
    print("\n📁 步骤1: 创建测试Excel文件")
    test_dir = create_test_excel_files()
    
    # 2. 表结构验证测试
    print("\n🔍 步骤2: 表结构验证测试")
    test_results['table_validation'] = run_table_structure_validation()
    
    # 3. 字段映射测试
    print("\n🔄 步骤3: 字段映射测试")
    test_results['field_mapping'] = test_field_mapping()
    
    # 4. 安全导入测试
    print("\n🔒 步骤4: 安全导入测试")
    test_results['safe_import'] = run_safe_import_test(test_dir)
    
    # 5. 生成测试报告
    print("\n📊 步骤5: 生成测试报告")
    report_file = generate_test_report(test_results)
    
    # 6. 清理测试文件
    print("\n🧹 步骤6: 清理测试文件")
    cleanup_test_files(test_dir)
    
    # 输出测试结果
    print("\n" + "="*50)
    print("🎯 测试结果汇总")
    print("="*50)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for r in test_results.values() if r)
    total = len(test_results)
    
    if passed == total:
        print(f"\n🎉 所有测试通过！({passed}/{total})")
        print("✅ 字段卡控功能工作正常，可以安全使用")
        return 0
    else:
        print(f"\n⚠️  部分测试失败 ({passed}/{total})")
        print("❌ 请检查失败的测试项并修复问题")
        return 1

if __name__ == '__main__':
    sys.exit(main()) 