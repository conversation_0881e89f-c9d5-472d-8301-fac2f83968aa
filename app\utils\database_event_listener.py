#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 数据库事件监听器 - 自动失效缓存
监听数据库变更事件，自动触发相关缓存失效，解决数据同步问题
"""

import logging
from typing import Optional
from sqlalchemy import event
from sqlalchemy.orm import Session
from flask import g, current_app

logger = logging.getLogger(__name__)

class DatabaseEventListener:
    """数据库事件监听器"""
    
    def __init__(self):
        self.enabled = True
        logger.info("🎯 数据库事件监听器初始化完成")
    
    def enable(self):
        """启用监听器"""
        self.enabled = True
        logger.info("✅ 数据库事件监听器已启用")
    
    def disable(self):
        """禁用监听器"""
        self.enabled = False
        logger.info("⏸️ 数据库事件监听器已禁用")
    
    def on_bulk_update(self, query_context):
        """批量更新事件处理"""
        if not self.enabled:
            return
            
        try:
            table_name = query_context.mapper.class_.__tablename__
            self._handle_table_change(table_name, 'BULK_UPDATE')
        except Exception as e:
            logger.error(f"❌ 处理批量更新事件失败: {e}")
    
    def on_bulk_delete(self, query_context):
        """批量删除事件处理"""
        if not self.enabled:
            return
            
        try:
            table_name = query_context.mapper.class_.__tablename__
            self._handle_table_change(table_name, 'BULK_DELETE')
        except Exception as e:
            logger.error(f"❌ 处理批量删除事件失败: {e}")
    
    def on_after_insert(self, mapper, connection, target):
        """插入后事件处理"""
        if not self.enabled:
            return
            
        try:
            table_name = mapper.class_.__tablename__
            self._handle_table_change(table_name, 'INSERT')
        except Exception as e:
            logger.error(f"❌ 处理插入事件失败: {e}")
    
    def on_after_update(self, mapper, connection, target):
        """更新后事件处理"""
        if not self.enabled:
            return
            
        try:
            table_name = mapper.class_.__tablename__
            self._handle_table_change(table_name, 'UPDATE')
        except Exception as e:
            logger.error(f"❌ 处理更新事件失败: {e}")
    
    def on_after_delete(self, mapper, connection, target):
        """删除后事件处理"""
        if not self.enabled:
            return
            
        try:
            table_name = mapper.class_.__tablename__
            self._handle_table_change(table_name, 'DELETE')
        except Exception as e:
            logger.error(f"❌ 处理删除事件失败: {e}")
    
    def _handle_table_change(self, table_name: str, operation: str):
        """处理表变更事件"""
        try:
            # 只处理关键业务表
            if table_name.lower() not in ['et_wait_lot', 'eqp_status', 'lotprioritydone']:
                return
                
            logger.info(f"📢 检测到关键表变更: {table_name} - {operation}")
            
            # 尝试获取数据变更监听器
            try:
                from app.services.data_source_manager import DataSourceManager
                dm = DataSourceManager()
                dm.on_data_modified(table_name, operation)
            except Exception as listener_error:
                logger.warning(f"⚠️ 调用数据变更监听器失败: {listener_error}")
                
        except Exception as e:
            logger.error(f"❌ 处理表变更事件失败: {e}")

# 全局监听器实例
_db_event_listener = None

def get_database_event_listener() -> DatabaseEventListener:
    """获取全局数据库事件监听器实例"""
    global _db_event_listener
    if _db_event_listener is None:
        _db_event_listener = DatabaseEventListener()
    return _db_event_listener

def register_database_events(db_session_class):
    """
    注册数据库事件监听器到SQLAlchemy
    
    Args:
        db_session_class: SQLAlchemy的Session类
    """
    try:
        listener = get_database_event_listener()
        
        # 注册会话级别事件
        event.listen(db_session_class, 'after_bulk_update', listener.on_bulk_update)
        event.listen(db_session_class, 'after_bulk_delete', listener.on_bulk_delete)
        
        logger.info("✅ 数据库事件监听器注册成功 - Session级别")
        return True
        
    except Exception as e:
        logger.error(f"❌ 注册数据库事件监听器失败: {e}")
        return False

def register_model_events():
    """
    注册模型级别的数据库事件监听器
    应该在应用启动时调用
    """
    try:
        from app.models import db
        from app.models.production.wait_lots import ET_WAIT_LOT_Model
        from app.models.system.equipment_status import EqpStatusModel
        
        listener = get_database_event_listener()
        
        # 注册关键模型的事件
        models_to_monitor = [ET_WAIT_LOT_Model, EqpStatusModel]
        
        for model_class in models_to_monitor:
            event.listen(model_class, 'after_insert', listener.on_after_insert)
            event.listen(model_class, 'after_update', listener.on_after_update)
            event.listen(model_class, 'after_delete', listener.on_after_delete)
            
            logger.info(f"✅ 已注册模型事件监听: {model_class.__name__}")
        
        logger.info("✅ 模型级别数据库事件监听器注册完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 注册模型事件监听器失败: {e}")
        return False

def initialize_database_listeners(app):
    """
    初始化数据库监听器 - 在Flask应用启动时调用
    
    Args:
        app: Flask应用实例
    """
    try:
        with app.app_context():
            # 注册会话级别事件
            from app import db
            register_database_events(db.session.__class__)
            
            # 注册模型级别事件
            register_model_events()
            
            logger.info("🎯 数据库事件监听器初始化完成")
            return True
            
    except Exception as e:
        logger.error(f"❌ 初始化数据库监听器失败: {e}")
        return False