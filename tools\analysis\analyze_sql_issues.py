#!/usr/bin/env python3
"""
分析SQL检查中的具体问题
"""
import pymysql

def analyze_sql_issues():
    """分析SQL检查中的具体问题"""
    print("🔍 分析SQL检查中的具体问题...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # A类问题：同配置应命中
            print(f"\n🔍 分析A类问题：同配置应命中")
            cursor.execute("""
                SELECT l.LOT_ID, l.HANDLER_ID, l.DEVICE, l.STAGE, l.match_type, l.changeover_time,
                       e.HANDLER_CONFIG as eqp_handler_config, e.KIT_PN as eqp_kit_pn, e.TESTER as eqp_tester
                FROM lotprioritydone l
                JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
                WHERE UPPER(e.STAGE)=UPPER(l.STAGE)
                  AND EXISTS (
                    SELECT 1 FROM et_recipe_file r
                    WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
                      AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
                  )
                  AND EXISTS (
                    SELECT 1 FROM et_ft_test_spec s
                    WHERE s.DEVICE=l.DEVICE AND UPPER(s.STAGE)=UPPER(l.STAGE)
                      AND s.TESTER=e.TESTER AND s.APPROVAL_STATE='Released'
                  )
                LIMIT 10
            """)
            
            a_issues = cursor.fetchall()
            print(f"📋 A类问题样本 ({len(a_issues)} 个):")
            for i, issue in enumerate(a_issues, 1):
                print(f"  {i}. {issue['LOT_ID']}: {issue['DEVICE']}+{issue['STAGE']} -> {issue['HANDLER_ID']}")
                print(f"     当前分类: {issue['match_type']} ({issue['changeover_time']}分钟)")
                print(f"     设备配置: HC={issue['eqp_handler_config']}, KIT={issue['eqp_kit_pn']}, TESTER={issue['eqp_tester']}")
                
                # 检查是否真的应该是同配置匹配
                cursor.execute("""
                    SELECT COUNT(*) as recipe_count
                    FROM et_recipe_file r
                    WHERE r.DEVICE=%s AND UPPER(r.STAGE)=UPPER(%s)
                      AND r.HANDLER_CONFIG=%s AND r.KIT_PN=%s
                """, (issue['DEVICE'], issue['STAGE'], issue['eqp_handler_config'], issue['eqp_kit_pn']))
                
                recipe_match = cursor.fetchone()
                
                cursor.execute("""
                    SELECT COUNT(*) as spec_count
                    FROM et_ft_test_spec s
                    WHERE s.DEVICE=%s AND UPPER(s.STAGE)=UPPER(%s)
                      AND s.TESTER=%s AND s.APPROVAL_STATE='Released'
                """, (issue['DEVICE'], issue['STAGE'], issue['eqp_tester']))
                
                spec_match = cursor.fetchone()
                
                print(f"     匹配证据: 配方={recipe_match['recipe_count']}条, 测试规范={spec_match['spec_count']}条")
                
                # 判断是否应该是同配置匹配
                if recipe_match['recipe_count'] > 0 and spec_match['spec_count'] > 0:
                    if issue['match_type'] != '同配置匹配' and issue['changeover_time'] != 0:
                        print(f"     ❌ 问题确认: 应该是同配置匹配(0分钟)，但被分类为{issue['match_type']}({issue['changeover_time']}分钟)")
                    else:
                        print(f"     ✅ 分类正确: 已正确识别为同配置匹配")
                else:
                    print(f"     ❓ 数据问题: 缺少匹配的配方或测试规范")
                print()
            
            # B类问题：小改机应命中
            print(f"\n🔍 分析B类问题：小改机应命中")
            cursor.execute("""
                SELECT l.LOT_ID, l.HANDLER_ID, l.DEVICE, l.STAGE, l.match_type, l.changeover_time,
                       e.HANDLER_CONFIG as eqp_handler_config, e.KIT_PN as eqp_kit_pn, e.TESTER as eqp_tester
                FROM lotprioritydone l
                JOIN eqp_status e ON e.HANDLER_ID=l.HANDLER_ID
                WHERE EXISTS (
                    SELECT 1 FROM et_recipe_file r
                    WHERE r.DEVICE=l.DEVICE AND UPPER(r.STAGE)=UPPER(l.STAGE)
                      AND r.HANDLER_CONFIG=e.HANDLER_CONFIG AND r.KIT_PN=e.KIT_PN
                  )
                  AND EXISTS (
                    SELECT 1 FROM et_ft_test_spec s
                    WHERE s.DEVICE=l.DEVICE AND UPPER(s.STAGE)=UPPER(l.STAGE)
                      AND s.TESTER=e.TESTER AND s.APPROVAL_STATE='Released'
                  )
                  AND NOT (UPPER(e.STAGE)=UPPER(l.STAGE))
                LIMIT 5
            """)
            
            b_issues = cursor.fetchall()
            print(f"📋 B类问题样本 ({len(b_issues)} 个):")
            for i, issue in enumerate(b_issues, 1):
                print(f"  {i}. {issue['LOT_ID']}: {issue['DEVICE']}+{issue['STAGE']} -> {issue['HANDLER_ID']}")
                print(f"     当前分类: {issue['match_type']} ({issue['changeover_time']}分钟)")
                print(f"     设备配置: HC={issue['eqp_handler_config']}, KIT={issue['eqp_kit_pn']}, TESTER={issue['eqp_tester']}")
                
                if issue['match_type'] != '小改机匹配' and issue['changeover_time'] != 45:
                    print(f"     ❌ 问题确认: 应该是小改机匹配(45分钟)，但被分类为{issue['match_type']}({issue['changeover_time']}分钟)")
                else:
                    print(f"     ✅ 分类正确: 已正确识别为小改机匹配")
                print()
            
            # 统计各类匹配类型的分布
            print(f"\n📊 当前匹配类型分布:")
            cursor.execute("""
                SELECT match_type, changeover_time, COUNT(*) as count
                FROM lotprioritydone
                GROUP BY match_type, changeover_time
                ORDER BY count DESC
            """)
            
            match_types = cursor.fetchall()
            for match_type in match_types:
                print(f"  - {match_type['match_type']} ({match_type['changeover_time']}分钟): {match_type['count']} 个批次")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_sql_issues()
