<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AEC-FT ICP{% endblock %}</title>
    
    <!-- 预加载关键CSS -->
    <link rel="preload" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" as="style">
    <link rel="preload" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}" as="style">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/compatibility.css') }}">
    
    <!-- Socket.IO for real-time communication -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <style>
        /* 使用统一的theme.css变量，移除重复定义 */
        
        body {
            font-family: var(--bs-body-font-family);
            font-display: swap; /* 优化字体加载 */
            background-color: #FFFFFF;
            color: #333333;
            overflow-x: hidden;
        }
        
        .sidebar {
            background-color: var(--aps-primary);
            color: white;
            height: 100vh;
            padding: 0.75rem; /* 压缩内边距 */
            position: fixed;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.3) transparent;
            will-change: scroll-position; /* 优化滚动性能 */
            z-index: 1000; /* 确保侧边栏在最上层 */
            width: 200px !important; /* 压缩宽度 */
        }
        
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .submenu {
            margin-left: 1.5rem;
            overflow: hidden;
            transition: max-height 0.2s ease;
            max-height: 0;
            position: relative;
            z-index: 1;
        }

        .sidebar .submenu.show {
            max-height: 500px; /* 增加最大高度以容纳更多子菜单 */
        }
        
        /* 嵌套子菜单样式 */
        .sidebar .submenu .submenu {
            margin-left: 1rem; /* 嵌套子菜单缩进更少 */
            border-left: 2px solid rgba(255, 255, 255, 0.2);
            padding-left: 0.5rem;
        }
        
        /* 子菜单项样式 */
        .sidebar .submenu .nav-link {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
            margin: 0.1rem 0;
        }
        
        /* 三级子菜单项样式 */
        .sidebar .submenu .submenu .nav-link {
            font-size: 0.85rem;
            padding: 0.3rem 0.6rem;
            opacity: 0.9;
        }

        .sidebar .nav-link .arrow {
            transition: transform 0.2s ease;
        }

        .sidebar .nav-link.expanded .arrow {
            transform: rotate(90deg);
        }
        
        .main-content {
            padding: 1.5rem;
            background-color: #F5F5F5;
            min-height: calc(100vh - 75px);
            margin-left: 0;
        }
        
        /* 响应式布局调整 */
        @media (max-width: 768px) {
            .sidebar {
                left: -200px !important;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0 !important;
            }
            
            .custom-navbar {
                left: 0 !important;
            }
            
            .ms-sm-auto {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }
        
        @media (max-width: 992px) {
            .custom-navbar {
                left: 0 !important;
            }
            
            .ms-sm-auto {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }
             
        .btn-primary {
            background-color: var(--aps-primary);
            border-color: var(--aps-primary);
        }
        
        .btn-primary:hover {
            background-color: var(--aps-primary-dark);
            border-color: var(--aps-primary-dark);
        }
        
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .custom-navbar {
            background-color: var(--aps-primary) !important;
            color: white !important;
            padding: 0.5rem 1rem; /* 压缩导航栏高度 */
            position: fixed !important;
            top: 0 !important;
            left: 200px !important;
            right: 0 !important;
            z-index: 1030 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .custom-navbar .navbar-brand,
        .custom-navbar .nav-link,
        .custom-navbar .btn-link {
            color: white !important;
        }
        
        .version-info {
            padding: 10px 0;
            margin-top: 20px;
            text-align: center;
            opacity: 0.8;
            font-size: 0.8rem;
        }

        .menu-loading {
            text-align: center;
            padding: 20px;
            color: rgba(255,255,255,0.6);
        }
        
        /* 防止页面刷新闪烁的样式控制 */
        html {
            visibility: visible;
            opacity: 1;
        }
        
        /* 只在页面完全刷新时防止闪烁 */
        html.loading body {
            visibility: hidden;
        }
        
        html.loading body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #F5F5F5;
            z-index: 9999;
        }
    </style>
    {% block extra_css %}{% endblock %}

    <!-- 防闪烁检测脚本 -->
    <script>
        // 检测是否为真正的页面刷新（F5或浏览器刷新按钮）
        (function() {
            // 检查是否为页面刷新
            if (performance.navigation.type === performance.navigation.TYPE_RELOAD) {
                document.documentElement.classList.add('loading');
            }
        })();
    </script>
    
    <!-- 优化的性能脚本 -->
    <script src="{{ url_for('static', filename='js/smart-refresh.js') }}" defer></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="px-0 sidebar" style="width: 200px;">
                <div class="text-center py-4">
                    <h4>AEC-FT ICP</h4>
                    <small class="d-block text-light" style="opacity: 0.8; font-size: 0.75rem; line-height: 1.2;">车规芯片终测智能调度平台</small>
                </div>
                <nav class="nav flex-column" id="sidebar-menu">
                    {{ user_menu_html|safe }}
                </nav>
                <div class="version-info">
                    <span>版本：{{ app_version }}</span>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="ms-sm-auto px-0" style="margin-left: 200px; width: calc(100% - 200px);">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-dark custom-navbar">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="{{ url_for('main.index') }}">
                            <i class="fas fa-microchip me-2"></i>AEC-FT Intelligent Commander Platform
                        </a>
                        <div class="d-flex justify-content-between w-100">
                            <h5 class="mb-0">{% block page_title %}{% endblock %}</h5>
                            <div class="dropdown">
                                <button class="btn btn-link text-white dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> {{ current_user.username }}
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    {% if current_user.role == 'admin' %}
                                    <li><a class="dropdown-item" href="/users">用户管理</a></li>
                                    {% endif %}
                                    {% if current_user.is_authenticated and current_user.has_permission(26) %}
                                    <li><a class="dropdown-item" href="/system/settings">系统设置</a></li>
                                    {% endif %}
                                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Content -->
                <main class="main-content">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>
    
    <!-- 只加载必要的JavaScript -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 统一的Toast通知管理器 -->
    <script src="{{ url_for('static', filename='js/base/toast-manager.js') }}"></script>
    
      
    <script>
        // 简化的菜单交互处理（服务端渲染版本）
        (function() {
            'use strict';
            
            let topLevelMenus = [];
            
            // DOM加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                initializeMenuInteractions();
                setActiveMenu();
                
                // 移除加载状态，只在真正的页面刷新时使用
                document.documentElement.classList.remove('loading');
            });
            
            function initializeMenuInteractions() {
                const sidebarMenu = document.getElementById('sidebar-menu');
                if (!sidebarMenu) return;
                
                // 收集顶级菜单项
                topLevelMenus = Array.from(sidebarMenu.querySelectorAll('.nav-link[href="#"]'));
                
                // 使用事件委托处理菜单点击
                sidebarMenu.addEventListener('click', handleMenuClick, { passive: false });
            }
            
            function handleMenuClick(e) {
                const target = e.target.closest('.nav-link');
                if (!target) return;
                
                const href = target.getAttribute('href');
                const submenu = target.nextElementSibling;
                
                if (href === '#' && submenu?.classList.contains('submenu')) {
                    e.preventDefault();
                    
                    // 如果是顶级菜单，关闭其他顶级菜单
                    if (topLevelMenus.includes(target)) {
                        topLevelMenus.forEach(menu => {
                            if (menu !== target && menu.classList.contains('expanded')) {
                                menu.classList.remove('expanded');
                                const menuSubmenu = menu.nextElementSibling;
                                if (menuSubmenu?.classList.contains('submenu')) {
                                    menuSubmenu.classList.remove('show');
                                }
                            }
                        });
                    }
                    
                    // 切换当前菜单
                    target.classList.toggle('expanded');
                    submenu.classList.toggle('show');
                }
            }
            
            function setActiveMenu() {
                const currentPath = window.location.pathname;
                const allLinks = document.querySelectorAll('#sidebar-menu .nav-link');
                
                let bestMatch = null;
                let bestMatchLength = 0;
                
                allLinks.forEach(link => {
                    link.classList.remove('active');
                    const href = link.getAttribute('href');
                    if (href && href !== '#' && currentPath.startsWith(href) && href.length > bestMatchLength) {
                        bestMatch = link;
                        bestMatchLength = href.length;
                    }
                });
                
                if (bestMatch) {
                    bestMatch.classList.add('active');
                    
                    // 展开父菜单
                    let parentSubmenu = bestMatch.closest('.submenu');
                    while (parentSubmenu) {
                        parentSubmenu.classList.add('show');
                        const parentLink = parentSubmenu.previousElementSibling;
                        if (parentLink?.classList.contains('nav-link')) {
                            parentLink.classList.add('expanded');
                            if (parentLink.dataset.isTopLevel === 'true') break;
                        }
                        parentSubmenu = parentSubmenu.parentElement?.closest('.submenu');
                    }
                }
            }
            
            // 暴露刷新菜单函数
            window.refreshMenu = function() {
                location.reload();
            };
            
            // 暴露toggleSubmenu函数供菜单渲染器使用
            window.toggleSubmenu = function(element, event) {
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                const submenu = element.nextElementSibling;
                if (!submenu?.classList.contains('submenu')) return;
                
                // 如果是顶级菜单，关闭其他顶级菜单
                if (element.dataset.isTopLevel === 'true') {
                    topLevelMenus.forEach(menu => {
                        if (menu !== element && menu.classList.contains('expanded')) {
                            menu.classList.remove('expanded');
                            const menuSubmenu = menu.nextElementSibling;
                            if (menuSubmenu?.classList.contains('submenu')) {
                                menuSubmenu.classList.remove('show');
                            }
                        }
                    });
                }
                
                // 切换当前菜单
                element.classList.toggle('expanded');
                submenu.classList.toggle('show');
            };
        })();
    </script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- 懒加载聊天机器人 -->
    <script>
        // 延迟加载聊天机器人，避免影响初始页面性能
        setTimeout(() => {
            const savedSettings = localStorage.getItem('chatbotSettings');
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);
                    if (settings.enabled) {
                        loadChatbot(settings);
                    }
                } catch (e) {
                    console.warn('聊天机器人配置解析失败:', e);
                }
            }
        }, 2000); // 2秒后加载
        
        function loadChatbot(settings) {
            if (settings.integrationType === 'script' && settings.scriptCode) {
                const script = document.createElement('script');
                script.text = settings.scriptCode;
                document.head.appendChild(script);
            } else if (settings.integrationType === 'iframe' && settings.iframeUrl) {
                const iframe = document.createElement('iframe');
                iframe.src = settings.iframeUrl;
                iframe.style.cssText = `
                    position: fixed; bottom: 20px; right: 20px; width: 350px; height: 500px;
                    border: none; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    z-index: 1000; background: white;
                `;
                document.body.appendChild(iframe);
            }
        }
    </script>
</body>
</html> 