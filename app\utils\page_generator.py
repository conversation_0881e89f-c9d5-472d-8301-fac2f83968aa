#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量页面生成工具 - 为11个表自动生成和更新页面配置
支持自动发现字段、生成配置、更新路由等功能
"""

import os
import json
import logging
from typing import Dict, List, Optional
from app.services.dynamic_field_manager import get_field_manager
from app.config.table_configs import TABLE_CONFIGS, get_table_config

logger = logging.getLogger(__name__)

class PageGenerator:
    """页面生成器 - 批量生成表页面配置"""
    
    def __init__(self):
        self.field_manager = get_field_manager()
        self.target_tables = [
            'eqp_status', 'et_uph_eqp', 'et_ft_test_spec', 'ct', 'tcc_inv',
            'wip_lot', 'et_wait_lot', 'et_recipe_file', 'devicepriorityconfig',
            'lotpriorityconfig', 'lotprioritydone'
        ]
    
    def generate_all_pages(self) -> Dict:
        """批量生成所有11个表的页面配置"""
        results = {
            'success': True,
            'generated': [],
            'errors': [],
            'summary': {}
        }
        
        logger.info("🚀 开始批量生成页面配置...")
        
        for table_name in self.target_tables:
            try:
                result = self.generate_single_page(table_name)
                if result['success']:
                    results['generated'].append(table_name)
                    logger.info(f"✅ {table_name} 页面配置生成成功")
                else:
                    results['errors'].append({
                        'table': table_name,
                        'error': result['error']
                    })
                    logger.error(f"❌ {table_name} 页面配置生成失败: {result['error']}")
            except Exception as e:
                results['errors'].append({
                    'table': table_name,
                    'error': str(e)
                })
                logger.error(f"❌ {table_name} 页面配置生成异常: {e}")
        
        results['summary'] = {
            'total': len(self.target_tables),
            'success': len(results['generated']),
            'failed': len(results['errors'])
        }
        
        if results['errors']:
            results['success'] = False
        
        logger.info(f"📊 批量生成完成: {results['summary']}")
        return results
    
    def generate_single_page(self, table_name: str) -> Dict:
        """生成单个表的页面配置"""
        try:
            # 获取表信息
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {
                    'success': False,
                    'error': f'无法获取表信息: {table_name}'
                }
            
            # 获取现有配置
            existing_config = get_table_config(table_name)
            
            # 生成增强配置
            enhanced_config = self._generate_enhanced_config(table_name, table_info, existing_config)
            
            # 验证配置
            validation_result = self._validate_config(table_name, enhanced_config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f'配置验证失败: {validation_result["errors"]}'
                }
            
            return {
                'success': True,
                'table_name': table_name,
                'config': enhanced_config,
                'fields_count': len(table_info.get('fields', [])),
                'auto_discovered': table_info.get('auto_discovered', False)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_enhanced_config(self, table_name: str, table_info: Dict, existing_config: Dict) -> Dict:
        """生成增强的表配置"""
        fields = table_info.get('fields', [])
        
        # 基础配置
        config = {
            'title': existing_config.get('title', table_name.upper()),
            'icon': existing_config.get('icon', 'fas fa-table'),
            'description': existing_config.get('description', f'{table_name} 数据管理'),
            'features': existing_config.get('features', ['数据管理']),
            'business_key': self._detect_business_key(fields),
            'display_fields': self._select_display_fields(fields),
            'hidden_fields': self._detect_hidden_fields(fields),
            'readonly_fields': self._detect_readonly_fields(fields),
            'required_fields': self._detect_required_fields(fields),
            'searchable_fields': self._select_searchable_fields(fields),
            'sortable_fields': self._select_sortable_fields(fields),
            'field_types': self._detect_field_types(fields),
        }
        
        # 添加状态映射（如果有状态字段）
        if any('status' in f.lower() for f in fields):
            config['status_mapping'] = existing_config.get('status_mapping', {
                'ACTIVE': {'class': 'success', 'text': '激活'},
                'INACTIVE': {'class': 'secondary', 'text': '未激活'},
                'PENDING': {'class': 'warning', 'text': '待处理'},
                'ERROR': {'class': 'danger', 'text': '错误'}
            })
        
        # 添加优先级映射（如果有优先级字段）
        if any('priority' in f.lower() for f in fields):
            config['priority_mapping'] = existing_config.get('priority_mapping', {
                'HIGH': {'class': 'danger', 'text': '高'},
                'MEDIUM': {'class': 'warning', 'text': '中'},
                'LOW': {'class': 'success', 'text': '低'}
            })
        
        return config
    
    def _detect_business_key(self, fields: List[str]) -> str:
        """检测业务主键"""
        # 优先级顺序
        priority_patterns = [
            '_id', 'id', '_key', 'key', '_code', 'code', '_no', 'no'
        ]
        
        for pattern in priority_patterns:
            for field in fields:
                if field.lower().endswith(pattern) and field.lower() != 'id':
                    return field
        
        # 如果没找到，返回第一个字段或id
        return fields[0] if fields else 'id'
    
    def _select_display_fields(self, fields: List[str]) -> List[str]:
        """选择显示字段（前5个重要字段）"""
        # 排除系统字段
        system_fields = ['id', 'created_at', 'updated_at', 'deleted_at']
        display_fields = [f for f in fields if f.lower() not in system_fields]
        
        # 返回前5个字段
        return display_fields[:5]
    
    def _detect_hidden_fields(self, fields: List[str]) -> List[str]:
        """检测隐藏字段"""
        hidden_patterns = ['id', 'hash', 'sync', 'override', 'deleted_at']
        return [f for f in fields if any(pattern in f.lower() for pattern in hidden_patterns)]
    
    def _detect_readonly_fields(self, fields: List[str]) -> List[str]:
        """检测只读字段"""
        readonly_patterns = ['id', 'created_at', 'updated_at', 'create_time', 'update_time']
        return [f for f in fields if f.lower() in readonly_patterns]
    
    def _detect_required_fields(self, fields: List[str]) -> List[str]:
        """检测必填字段"""
        # 业务主键和名称字段通常是必填的
        required_patterns = ['_id', '_name', '_code', '_no']
        required_fields = []
        
        for field in fields:
            field_lower = field.lower()
            if any(pattern in field_lower for pattern in required_patterns):
                required_fields.append(field)
        
        return required_fields[:3]  # 最多3个必填字段
    
    def _select_searchable_fields(self, fields: List[str]) -> List[str]:
        """选择可搜索字段"""
        searchable_patterns = ['_id', '_name', '_code', '_no', 'title', 'description']
        return [f for f in fields if any(pattern in f.lower() for pattern in searchable_patterns)]
    
    def _select_sortable_fields(self, fields: List[str]) -> List[str]:
        """选择可排序字段"""
        # 大部分字段都可以排序，排除一些特殊字段
        non_sortable_patterns = ['description', 'comment', 'remark', 'note']
        return [f for f in fields if not any(pattern in f.lower() for pattern in non_sortable_patterns)]
    
    def _detect_field_types(self, fields: List[str]) -> Dict[str, str]:
        """检测字段类型"""
        field_types = {}
        
        for field in fields:
            field_lower = field.lower()
            
            # 业务主键
            if field_lower.endswith('_id') and field_lower != 'id':
                field_types[field] = 'business_key'
            # 状态字段
            elif 'status' in field_lower or 'state' in field_lower:
                field_types[field] = 'status_badge'
            # 优先级字段
            elif 'priority' in field_lower:
                field_types[field] = 'priority_badge'
            # 时间字段
            elif 'time' in field_lower or 'date' in field_lower:
                if 'time' in field_lower:
                    field_types[field] = 'datetime'
                else:
                    field_types[field] = 'date'
            # 数值字段
            elif any(pattern in field_lower for pattern in ['qty', 'quantity', 'count', 'num', 'amount']):
                field_types[field] = 'number'
            # UPH字段
            elif 'uph' in field_lower:
                field_types[field] = 'number'
            # 效率字段
            elif 'efficiency' in field_lower or 'rate' in field_lower:
                field_types[field] = 'percentage'
            # 版本字段
            elif 'version' in field_lower:
                field_types[field] = 'version'
        
        return field_types
    
    def _validate_config(self, table_name: str, config: Dict) -> Dict:
        """验证配置"""
        errors = []
        
        # 检查必需字段
        required_keys = ['title', 'icon', 'description', 'business_key']
        for key in required_keys:
            if key not in config:
                errors.append(f'缺少必需字段: {key}')
        
        # 检查字段列表
        if not config.get('display_fields'):
            errors.append('display_fields 不能为空')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def generate_route_config(self) -> str:
        """生成路由配置代码"""
        route_template = """
# 自动生成的路由配置
GENERATED_ROUTES = {
"""
        
        for table_name in self.target_tables:
            config = get_table_config(table_name)
            route_template += f"""    '{table_name}': {{
        'route': '/api/v3/universal/{table_name}',
        'title': '{config["title"]}',
        'icon': '{config["icon"]}',
        'description': '{config["description"]}'
    }},
"""
        
        route_template += "}\n"
        return route_template
    
    def export_configs_to_json(self, output_path: str = None) -> str:
        """导出所有配置到JSON文件"""
        if not output_path:
            output_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'generated_table_configs.json')
        
        all_configs = {}
        for table_name in self.target_tables:
            all_configs[table_name] = get_table_config(table_name)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(all_configs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 配置已导出到: {output_path}")
        return output_path

# 便捷函数
def generate_all_pages():
    """生成所有页面配置的便捷函数"""
    generator = PageGenerator()
    return generator.generate_all_pages()

def generate_single_page(table_name: str):
    """生成单个页面配置的便捷函数"""
    generator = PageGenerator()
    return generator.generate_single_page(table_name)

if __name__ == "__main__":
    # 命令行执行
    import sys
    
    if len(sys.argv) > 1:
        table_name = sys.argv[1]
        result = generate_single_page(table_name)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        result = generate_all_pages()
        print(json.dumps(result, ensure_ascii=False, indent=2))
