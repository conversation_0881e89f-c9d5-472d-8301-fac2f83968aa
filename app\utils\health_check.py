"""
健康检查工具 - 用于验证重构过程中系统功能正常
"""

import requests
import logging
from typing import List, Dict, Any
import json
from datetime import datetime
import os
import sys
import time
from flask import current_app

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

logger = logging.getLogger(__name__)

class HealthChecker:
    """系统健康检查器"""
    
    def __init__(self, base_url: str = 'http://localhost:5000'):
        self.base_url = base_url
        self.results = []
        
    def check_all_endpoints(self) -> Dict[str, Any]:
        """检查所有关键API端点"""
        endpoints = [
            # 认证相关
            {'path': '/auth/login', 'method': 'GET', 'name': '登录页面', 'auth_required': False},
            
            # 主要功能页面
            {'path': '/dashboard', 'method': 'GET', 'name': '仪表板', 'auth_required': True},
            {'path': '/production/manual', 'method': 'GET', 'name': '手动排产页面', 'auth_required': True},
            {'path': '/resources/hardware', 'method': 'GET', 'name': '设备资源页面', 'auth_required': True},
            
            # API v2 端点 (更新后的资源管理API)
            {'path': '/api/v2/resources/data/eqp_status', 'method': 'GET', 'name': '设备状态API', 'auth_required': True},
            {'path': '/api/v2/resources/data/ET_UPH_EQP', 'method': 'GET', 'name': 'UPH数据API', 'auth_required': True},
            {'path': '/api/v2/resources/data/ET_WAIT_LOT', 'method': 'GET', 'name': '待排产批次API', 'auth_required': True},
            
            # 生产管理API
            {'path': '/api/production/file-data/test', 'method': 'GET', 'name': '文件数据API', 'auth_required': True},
            {'path': '/api/production/imported-files', 'method': 'GET', 'name': '导入文件列表API', 'auth_required': True},
            
            # 系统状态API
            {'path': '/api/database-status', 'method': 'GET', 'name': '数据库状态API', 'auth_required': True},
        ]
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_checks': len(endpoints),
            'passed': 0,
            'failed': 0,
            'details': []
        }
        
        for endpoint in endpoints:
            result = self._check_endpoint(endpoint)
            results['details'].append(result)
            
            if result['status'] == 'success':
                results['passed'] += 1
            else:
                results['failed'] += 1
                
        results['success_rate'] = (results['passed'] / results['total_checks']) * 100
        
        return results
    
    def _check_endpoint(self, endpoint: Dict[str, Any]) -> Dict[str, Any]:
        """检查单个端点"""
        result = {
            'name': endpoint['name'],
            'path': endpoint['path'],
            'method': endpoint['method'],
            'status': 'unknown',
            'response_code': None,
            'response_time': None,
            'error': None
        }
        
        try:
            start_time = datetime.now()
            
            # 构建完整URL
            url = f"{self.base_url}{endpoint['path']}"
            
            # 发送请求
            if endpoint['method'].upper() == 'GET':
                response = requests.get(url, timeout=10)
            elif endpoint['method'].upper() == 'POST':
                response = requests.post(url, timeout=10)
            else:
                response = requests.request(endpoint['method'], url, timeout=10)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            result['response_code'] = response.status_code
            result['response_time'] = response_time
            
            # 判断成功条件
            if response.status_code in [200, 302]:  # 200 OK 或 302 重定向（未登录）
                result['status'] = 'success'
            elif response.status_code == 401 and endpoint.get('auth_required'):
                # 需要认证的端点返回401是正常的
                result['status'] = 'success'
                result['note'] = '需要认证（正常）'
            else:
                result['status'] = 'failed'
                result['error'] = f'HTTP {response.status_code}'
                
        except requests.exceptions.ConnectionError:
            result['status'] = 'failed'
            result['error'] = '连接失败 - 服务器可能未启动'
        except requests.exceptions.Timeout:
            result['status'] = 'failed'
            result['error'] = '请求超时'
        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            
        return result
    
    def check_database_connections(self) -> Dict[str, Any]:
        """检查数据库连接"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'databases': []
        }
        
        try:
            # 尝试导入数据库相关模块
            from app import db
            from app.models import User, ProductPriorityConfig
            
            # 检查主数据库连接
            try:
                user_count = User.query.count()
                results['databases'].append({
                    'name': '主数据库 (aps)',
                    'status': 'success',
                    'test_result': f'用户表记录数: {user_count}'
                })
            except Exception as e:
                results['databases'].append({
                    'name': '主数据库 (aps)',
                    'status': 'failed',
                    'error': str(e)
                })
            
            # 检查系统数据库连接
            try:
                config_count = ProductPriorityConfig.query.count()
                results['databases'].append({
                    'name': '系统数据库 (aps_system)',
                    'status': 'success',
                    'test_result': f'产品优先级配置记录数: {config_count}'
                })
            except Exception as e:
                results['databases'].append({
                    'name': '系统数据库 (aps_system)',
                    'status': 'failed',
                    'error': str(e)
                })
                
        except ImportError as e:
            results['databases'].append({
                'name': '数据库模块',
                'status': 'failed',
                'error': f'无法导入数据库模块: {str(e)}'
            })
            
        return results
    
    def check_import_dependencies(self) -> Dict[str, Any]:
        """检查关键模块导入"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'imports': []
        }
        
        # 关键导入测试
        import_tests = [
            {'module': 'app', 'description': '主应用模块'},
            {'module': 'app.models', 'description': '数据模型'},
            {'module': 'app.api.routes', 'description': '主API路由'},
            {'module': 'app.api.production_api', 'description': '生产API'},
            {'module': 'app.config.menu_config', 'description': '菜单配置'},
            {'module': 'flask', 'description': 'Flask框架'},
            {'module': 'sqlalchemy', 'description': 'SQLAlchemy ORM'},
        ]
        
        for test in import_tests:
            try:
                __import__(test['module'])
                results['imports'].append({
                    'module': test['module'],
                    'description': test['description'],
                    'status': 'success'
                })
            except Exception as e:
                results['imports'].append({
                    'module': test['module'],
                    'description': test['description'],
                    'status': 'failed',
                    'error': str(e)
                })
                
        return results
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成健康检查报告"""
        report = []
        report.append("=" * 60)
        report.append("系统健康检查报告")
        report.append("=" * 60)
        report.append(f"检查时间: {results['timestamp']}")
        report.append("")
        
        if 'total_checks' in results:
            # API端点检查报告
            report.append(f"API端点检查: {results['passed']}/{results['total_checks']} 通过")
            report.append(f"成功率: {results['success_rate']:.1f}%")
            report.append("")
            
            for detail in results['details']:
                status_icon = "✅" if detail['status'] == 'success' else "❌"
                report.append(f"{status_icon} {detail['name']}")
                report.append(f"   路径: {detail['path']}")
                report.append(f"   状态码: {detail['response_code']}")
                if detail.get('response_time'):
                    report.append(f"   响应时间: {detail['response_time']:.3f}s")
                if detail.get('error'):
                    report.append(f"   错误: {detail['error']}")
                if detail.get('note'):
                    report.append(f"   备注: {detail['note']}")
                report.append("")
        
        elif 'databases' in results:
            # 数据库检查报告
            report.append("数据库连接检查:")
            report.append("")
            
            for db_result in results['databases']:
                status_icon = "✅" if db_result['status'] == 'success' else "❌"
                report.append(f"{status_icon} {db_result['name']}")
                if db_result.get('test_result'):
                    report.append(f"   结果: {db_result['test_result']}")
                if db_result.get('error'):
                    report.append(f"   错误: {db_result['error']}")
                report.append("")
        
        elif 'imports' in results:
            # 导入检查报告
            report.append("模块导入检查:")
            report.append("")
            
            for import_result in results['imports']:
                status_icon = "✅" if import_result['status'] == 'success' else "❌"
                report.append(f"{status_icon} {import_result['module']}")
                report.append(f"   描述: {import_result['description']}")
                if import_result.get('error'):
                    report.append(f"   错误: {import_result['error']}")
                report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def save_report(self, results: Dict[str, Any], filename: str = None):
        """保存健康检查报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"health_check_report_{timestamp}.txt"
        
        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)
        filepath = os.path.join('logs', filename)
        
        report = self.generate_report(results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report)
            
        return filepath

def main():
    """主函数 - 执行完整的健康检查"""
    checker = HealthChecker()
    
    print("🔍 开始系统健康检查...")
    print()
    
    # 1. 检查模块导入
    print("1. 检查模块导入...")
    import_results = checker.check_import_dependencies()
    print(checker.generate_report(import_results))
    
    # 2. 检查数据库连接
    print("2. 检查数据库连接...")
    db_results = checker.check_database_connections()
    print(checker.generate_report(db_results))
    
    # 3. 检查API端点（需要服务器运行）
    print("3. 检查API端点...")
    api_results = checker.check_all_endpoints()
    print(checker.generate_report(api_results))
    
    # 保存完整报告
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'import_check': import_results,
        'database_check': db_results,
        'api_check': api_results
    }
    
    report_file = checker.save_report(api_results)  # 保存API检查报告
    print(f"📄 详细报告已保存到: {report_file}")
    
    return all_results

if __name__ == '__main__':
    main() 