"""
功能开关配置 - 控制重构过程中新旧功能的切换

这个文件定义了所有功能开关，允许在重构过程中安全地启用或禁用新功能。
通过修改这些开关，可以实现渐进式迁移，确保系统稳定性。
"""

from datetime import datetime
import os
import logging

logger = logging.getLogger(__name__)

# 主要功能开关
FEATURE_FLAGS = {
    # === 核心架构开关 ===
    'use_new_api_structure': True,       # 是否使用新的API结构 (app/api_v2/)
    'use_new_model_structure': False,    # 是否使用新的模型结构 (app/models/)
    'enable_api_v2': True,              # 是否启用API v2端点
    'enable_new_templates': False,       # 是否使用新的模板结构
    
    # === 数据库相关开关 ===
    'use_unified_database': True,       # 是否使用统一的MySQL数据库
    'enable_model_migration': True,     # 是否启用模型迁移功能
    'enable_unified_models': True,      # 是否启用统一数据模型
    'use_optimized_queries': False,     # 是否使用优化的数据库查询
    
    # === API功能开关 ===
    'enable_production_api_v2': True,   # 生产管理API v2 - 数据源切换需要
    'enable_resources_api_v2': True,    # 资源管理API v2 - 已实现
    'enable_auth_api_v2': True,         # 认证API v2 - 已实现
    'enable_system_api_v2': True,       # 系统管理API v2 - 已实现
    
    # === 前端功能开关 ===
    'use_modular_templates': False,     # 使用模块化模板
    'enable_component_system': False,   # 启用组件系统
    'use_optimized_static': False,      # 使用优化的静态资源
    'enable_unified_resources': True,   # 启用统一资源管理
    
    # === 性能优化开关 ===
    'enable_caching': True,             # 启用缓存
    'enable_lazy_loading': False,       # 启用懒加载
    'enable_compression': False,        # 启用压缩
    
    # === 调试和监控开关 ===
    'enable_debug_mode': False,         # 启用调试模式
    'enable_performance_monitoring': True,  # 启用性能监控
    'enable_detailed_logging': False,   # 启用详细日志
    
    # === 迁移状态开关 ===
    'migration_phase': 'preparation',   # 迁移阶段: preparation, models, apis, templates, cleanup
    'migration_complete': False,        # 迁移是否完成
    'emergency_rollback': False,        # 紧急回滚模式
    
    # === 实验性功能开关 ===
    'enable_experimental_features': False,  # 启用实验性功能
    'enable_beta_ui': False,            # 启用Beta版UI
    'enable_advanced_scheduling': False, # 启用高级排产算法
}

# 功能开关组合配置
FEATURE_PROFILES = {
    # 开发环境配置
    'development': {
        'enable_debug_mode': True,
        'enable_detailed_logging': True,
        'enable_performance_monitoring': True,
        'enable_experimental_features': True,
    },
    
    # 测试环境配置
    'testing': {
        'enable_debug_mode': True,
        'enable_detailed_logging': True,
        'use_new_api_structure': True,
        'use_new_model_structure': True,
    },
    
    # 生产环境配置
    'production': {
        'enable_debug_mode': False,
        'enable_detailed_logging': False,
        'enable_caching': True,
        'enable_compression': True,
        'emergency_rollback': False,
    },
    
    # 迁移阶段1：模型重构
    'migration_phase_1': {
        'migration_phase': 'models',
        'use_new_model_structure': True,
        'enable_model_migration': True,
        'enable_detailed_logging': True,
    },
    
    # 迁移阶段2：API重构
    'migration_phase_2': {
        'migration_phase': 'apis',
        'use_new_model_structure': True,
        'use_new_api_structure': True,
        'enable_api_v2': True,
        'enable_detailed_logging': True,
    },
    
    # 迁移阶段3：前端重构
    'migration_phase_3': {
        'migration_phase': 'templates',
        'use_new_model_structure': True,
        'use_new_api_structure': True,
        'enable_api_v2': True,
        'enable_new_templates': True,
        'use_modular_templates': True,
    },
    
    # 迁移完成
    'migration_complete': {
        'migration_phase': 'complete',
        'migration_complete': True,
        'use_new_model_structure': True,
        'use_new_api_structure': True,
        'enable_api_v2': True,
        'enable_new_templates': True,
        'use_modular_templates': True,
        'enable_component_system': True,
        'use_optimized_static': True,
        'enable_caching': True,
        'enable_compression': True,
    },
    
    # 紧急回滚配置
    'emergency_rollback': {
        'emergency_rollback': True,
        'use_new_api_structure': False,
        'use_new_model_structure': False,
        'enable_api_v2': False,
        'enable_new_templates': False,
        'migration_complete': False,
        'enable_debug_mode': True,
        'enable_detailed_logging': True,
    }
}

class FeatureFlagManager:
    """功能开关管理器"""
    
    def __init__(self):
        self.flags = FEATURE_FLAGS.copy()
        self.load_environment_overrides()
    
    def load_environment_overrides(self):
        """从环境变量加载开关覆盖"""
        for key in self.flags.keys():
            env_key = f'FEATURE_{key.upper()}'
            env_value = os.environ.get(env_key)
            
            if env_value is not None:
                # 转换环境变量值
                if env_value.lower() in ('true', '1', 'yes', 'on'):
                    self.flags[key] = True
                elif env_value.lower() in ('false', '0', 'no', 'off'):
                    self.flags[key] = False
                else:
                    # 对于字符串值，直接使用
                    self.flags[key] = env_value
                
                logger.info(f"功能开关 {key} 被环境变量覆盖为: {self.flags[key]}")
    
    def is_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        return self.flags.get(feature_name, False)
    
    def enable_feature(self, feature_name: str):
        """启用功能"""
        if feature_name in self.flags:
            self.flags[feature_name] = True
            logger.info(f"功能 {feature_name} 已启用")
        else:
            logger.warning(f"未知功能开关: {feature_name}")
    
    def disable_feature(self, feature_name: str):
        """禁用功能"""
        if feature_name in self.flags:
            self.flags[feature_name] = False
            logger.info(f"功能 {feature_name} 已禁用")
        else:
            logger.warning(f"未知功能开关: {feature_name}")
    
    def apply_profile(self, profile_name: str):
        """应用功能配置文件"""
        if profile_name in FEATURE_PROFILES:
            profile = FEATURE_PROFILES[profile_name]
            for key, value in profile.items():
                if key in self.flags:
                    self.flags[key] = value
            
            logger.info(f"已应用功能配置文件: {profile_name}")
            return True
        else:
            logger.error(f"未知功能配置文件: {profile_name}")
            return False
    
    def get_all_flags(self) -> dict:
        """获取所有功能开关"""
        return self.flags.copy()
    
    def get_migration_phase(self) -> str:
        """获取当前迁移阶段"""
        return self.flags.get('migration_phase', 'preparation')
    
    def is_migration_complete(self) -> bool:
        """检查迁移是否完成"""
        return self.flags.get('migration_complete', False)
    
    def is_emergency_rollback(self) -> bool:
        """检查是否处于紧急回滚模式"""
        return self.flags.get('emergency_rollback', False)
    
    def export_config(self) -> str:
        """导出当前配置为字符串"""
        config_lines = [
            "# 当前功能开关配置",
            f"# 导出时间: {datetime.now().isoformat()}",
            "",
            "FEATURE_FLAGS = {"
        ]
        
        for key, value in sorted(self.flags.items()):
            if isinstance(value, str):
                config_lines.append(f"    '{key}': '{value}',")
            else:
                config_lines.append(f"    '{key}': {value},")
        
        config_lines.append("}")
        
        return "\n".join(config_lines)

# 创建全局功能开关管理器实例
feature_manager = FeatureFlagManager()

# 便捷函数
def is_feature_enabled(feature_name: str) -> bool:
    """检查功能是否启用"""
    return feature_manager.is_enabled(feature_name)

def enable_feature(feature_name: str):
    """启用功能"""
    feature_manager.enable_feature(feature_name)

def disable_feature(feature_name: str):
    """禁用功能"""
    feature_manager.disable_feature(feature_name)

def apply_profile(profile_name: str) -> bool:
    """应用功能配置文件"""
    return feature_manager.apply_profile(profile_name)

def get_migration_phase() -> str:
    """获取当前迁移阶段"""
    return feature_manager.get_migration_phase()

def is_migration_complete() -> bool:
    """检查迁移是否完成"""
    return feature_manager.is_migration_complete()

def is_emergency_rollback() -> bool:
    """检查是否处于紧急回滚模式"""
    return feature_manager.is_emergency_rollback()

# 自动检测环境并应用相应配置
def auto_configure():
    """根据环境自动配置功能开关"""
    env = os.environ.get('FLASK_ENV', 'production').lower()
    
    if env == 'development':
        apply_profile('development')
    elif env == 'testing':
        apply_profile('testing')
    elif env == 'production':
        apply_profile('production')
    
    # 检查是否有迁移阶段环境变量
    migration_phase = os.environ.get('MIGRATION_PHASE')
    if migration_phase:
        profile_name = f'migration_phase_{migration_phase}'
        if profile_name in FEATURE_PROFILES:
            apply_profile(profile_name)
    
    # 检查紧急回滚模式
    if os.environ.get('EMERGENCY_ROLLBACK', '').lower() in ('true', '1', 'yes'):
        apply_profile('emergency_rollback')

# 在模块加载时自动配置
auto_configure()

# 导出主要接口
__all__ = [
    'FEATURE_FLAGS',
    'FEATURE_PROFILES',
    'FeatureFlagManager',
    'feature_manager',
    'is_feature_enabled',
    'enable_feature',
    'disable_feature',
    'apply_profile',
    'get_migration_phase',
    'is_migration_complete',
    'is_emergency_rollback',
    'auto_configure',
]
 