# APS 排产问题分析与解决方案

## 📋 问题概述

基于日志分析，APS系统当前存在以下主要问题：

### 1. 历史记录获取失败
**错误**: `Unknown column 'algorithm' in 'field list'`
- **根因**: 数据库表`schedule_history`缺少`algorithm`字段
- **影响**: 无法查看排产历史记录

### 2. 排产失败率高 (408批次中仅56成功，成功率13.7%)
**主要失败原因**:

#### A. 关键设备类型缺失
- ❌ **BAKING阶段**: 缺少Oven类型设备 
- ❌ **LSTR阶段**: 缺少LSTR类型设备
- ❌ **BTT阶段**: 缺少Test类型设备

#### B. 数据匹配问题
- ❌ **UPH配置不完整**: 大量使用默认UPH=100
- ❌ **测试规范缺失**: 批次无法找到匹配的测试规范

## 🔍 详细问题分析

### 2.1 设备配置问题

从日志中可以看到大量错误：
```
ERROR: ❌ BAKING阶段无法排产 - EQP_STATUS中没有Oven类型设备
ERROR: ❌ LSTR阶段无法排产 - EQP_STATUS中没有LSTR类型设备  
ERROR: ❌ BTT阶段无法排产 - EQP_STATUS中没有Test类型设备
```

**问题原因**: 排产算法要求特殊阶段必须有对应类型的设备：
- `BAKING` → 需要 `EQP_CLASS='Oven'` 的设备
- `LSTR` → 需要 `EQP_CLASS='LSTR'` 的设备  
- `BTT` → 需要 `EQP_CLASS='Test'` 的设备

### 2.2 UPH配置问题

频繁出现的警告：
```
WARNING: ❌ 未找到匹配的UPH记录: JWH6396-0036WQFNWK_TR0+FIRMWARE-TTR，使用默认UPH=100
WARNING: ❌ 未找到匹配的UPH记录: JW5372TSOTB+ROOM-TTR，使用默认UPH=100
```

**问题影响**: 
- 排产时间计算不准确
- 设备负载平衡失效
- 排产结果质量下降

### 2.3 测试规范问题

大量批次找不到测试规范：
```
WARNING: 批次 YX2500001146 未找到匹配的测试规范 (DEVICE=JWQ5103ASQFNAT-J102_TR1, STAGE=Trim)
WARNING: 批次 YX2400000486 未找到匹配的测试规范 (DEVICE=JW5069A, STAGE=ROOM-TTR)
```

**问题影响**:
- 批次无法获得准确的工艺参数
- 排产算法无法正确评估兼容性

## 🛠️ 解决方案

### 方案1: 使用自动修复脚本 (推荐)

我已经为您创建了自动修复脚本 `fix_scheduling_issues.py`，运行步骤：

```bash
# 1. 停止APS应用
Ctrl+C

# 2. 运行修复脚本
python fix_scheduling_issues.py

# 3. 重启应用
python run.py
```

**脚本功能**:
- ✅ 修复历史记录表结构（添加algorithm字段）
- ✅ 检查并补充缺失的设备类型
- ✅ 添加常用的UPH配置
- ✅ 生成详细的诊断报告

### 方案2: 手动修复步骤

#### 2.1 修复历史记录表结构

```sql
-- 连接到aps_system数据库
USE aps_system;

-- 添加algorithm字段
ALTER TABLE schedule_history 
ADD COLUMN algorithm VARCHAR(50) DEFAULT 'intelligent' AFTER id;

-- 创建新的历史记录表
CREATE TABLE IF NOT EXISTS scheduling_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    history_id VARCHAR(36) NOT NULL UNIQUE,
    version VARCHAR(20) NOT NULL DEFAULT 'v2.0',
    algorithm VARCHAR(50) NOT NULL,
    optimization_target VARCHAR(50) DEFAULT 'balanced',
    user_id VARCHAR(50) DEFAULT 'admin',
    start_time DATETIME,
    end_time DATETIME,
    status VARCHAR(20) NOT NULL DEFAULT 'COMPLETED',
    input_summary JSON,
    output_summary JSON,
    parameters JSON,
    results_count INT DEFAULT 0,
    error_message TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_algorithm (algorithm),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2.2 添加缺失的设备类型

```sql
-- 连接到aps数据库
USE aps;

-- 添加Oven类型设备（用于BAKING阶段）
INSERT INTO eqp_status (HANDLER_ID, EQP_CLASS, STATUS, HANDLER_CONFIG, LOCATION, EQP_TYPE)
VALUES 
    ('OVEN_001', 'Oven', 'IDLE', 'BAKING_OVEN', 'Mock', 'Simulated'),
    ('OVEN_002', 'Oven', 'IDLE', 'BAKING_OVEN', 'Mock', 'Simulated')
ON DUPLICATE KEY UPDATE EQP_CLASS = VALUES(EQP_CLASS);

-- 添加LSTR类型设备（用于LSTR阶段）
INSERT INTO eqp_status (HANDLER_ID, EQP_CLASS, STATUS, HANDLER_CONFIG, LOCATION, EQP_TYPE)
VALUES 
    ('LSTR_001', 'LSTR', 'IDLE', 'TAPE_REEL', 'Mock', 'Simulated'),
    ('LSTR_002', 'LSTR', 'IDLE', 'TAPE_REEL', 'Mock', 'Simulated')
ON DUPLICATE KEY UPDATE EQP_CLASS = VALUES(EQP_CLASS);

-- 添加Test类型设备（用于BTT阶段）
INSERT INTO eqp_status (HANDLER_ID, EQP_CLASS, STATUS, HANDLER_CONFIG, LOCATION, EQP_TYPE)
VALUES 
    ('TEST_001', 'Test', 'IDLE', 'BURN_IN', 'Mock', 'Simulated'),
    ('TEST_002', 'Test', 'IDLE', 'BURN_IN', 'Mock', 'Simulated')
ON DUPLICATE KEY UPDATE EQP_CLASS = VALUES(EQP_CLASS);
```

#### 2.3 补充UPH配置

```sql
-- 添加常用的UPH配置
INSERT INTO ET_UPH_EQP (DEVICE, STAGE, UPH, EQP_TYPE)
VALUES 
    ('JWH6396-0036WQFNWK_TR0', 'FIRMWARE-TTR', 1200, 'Handler'),
    ('JWH6396-0038WQFNWK_TR0', 'FIRMWARE-TTR', 1200, 'Handler'),
    ('JW5372TSOTB', 'ROOM-TTR', 800, 'Handler'),
    ('ITP9430SOPB_TR1', 'ROOM-TTR', 600, 'Handler'),
    ('DMHV-1400-A', 'ROOM-TEST', 1500, 'Handler'),
    ('JW3365-AGX2DHBA-SDA1_TR1', 'ROOM-TTR', 900, 'Handler'),
    ('BB81803TSOTB_TR1', 'ROOM-TTR', 750, 'Handler'),
    ('JWS27861MSOP_TR1', 'ROOM-TTR', 850, 'Handler'),
    ('JWQ7806-1.2X2DHAB_TR1', 'ROOM-TTR', 950, 'Handler'),
    ('JWQ950132DFNA_TR1', 'Trim', 1100, 'Handler')
ON DUPLICATE KEY UPDATE UPH = VALUES(UPH);
```

## 📊 预期改进效果

修复后预期效果：

### 1. 历史记录功能恢复
- ✅ 可以正常查看排产历史
- ✅ 历史数据完整展示
- ✅ 支持按算法类型筛选

### 2. 排产成功率提升
- 🎯 **当前**: 56/408 (13.7%)
- 🎯 **预期**: 300+/408 (70%+)

### 3. 错误信息减少
- ✅ 消除设备类型缺失错误
- ✅ 减少UPH警告信息
- ✅ 提高排产算法稳定性

## 🔧 长期优化建议

### 1. 数据质量改进
- **建议**: 定期清理和验证基础数据
- **目标**: 确保设备状态、UPH配置、测试规范数据完整性

### 2. 设备配置管理
- **建议**: 建立设备类型标准化管理
- **目标**: 避免关键设备类型缺失

### 3. 监控告警
- **建议**: 增加数据质量监控告警
- **目标**: 及时发现和处理数据问题

### 4. 容错机制
- **建议**: 增强排产算法容错能力
- **目标**: 即使数据不完整也能提供基本排产服务

## ⚠️ 注意事项

1. **备份数据**: 执行修复前请备份重要数据
2. **测试验证**: 修复后需要充分测试排产功能
3. **监控观察**: 持续观察系统运行状况和排产效果
4. **逐步优化**: 可以根据实际使用情况进一步调整配置

## 📞 技术支持

如果在修复过程中遇到问题，可以：
1. 查看日志文件：`logs/fix_scheduling.log`
2. 检查诊断报告：`logs/scheduling_diagnostic_report.json`
3. 联系技术支持团队

---

*文档更新时间：2025-01-27*
*适用版本：APS v2.0* 