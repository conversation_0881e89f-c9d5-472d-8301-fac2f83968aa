#!/usr/bin/env python3
"""
检查测试规范数据的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pymysql
from collections import Counter

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='localhost',
        user='root',
        password='WWWwww123!',
        database='aps',
        charset='utf8mb4'
    )

def check_test_spec_data():
    """检查测试规范数据"""
    conn = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        print("🔍 检查et_ft_test_spec表数据...")
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec")
        total = cursor.fetchone()[0]
        print(f"\n📊 总记录数: {total}")
        
        # 检查APPROVAL_STATE分布
        cursor.execute("""
            SELECT APPROVAL_STATE, COUNT(*) as cnt 
            FROM et_ft_test_spec 
            GROUP BY APPROVAL_STATE 
            ORDER BY cnt DESC
        """)
        approvals = cursor.fetchall()
        print("\n📋 APPROVAL_STATE状态分布:")
        for approval in approvals:
            print(f"  - {approval[0]}: {approval[1]} 条记录")
        
        # 检查Released状态的数据
        cursor.execute("SELECT COUNT(*) FROM et_ft_test_spec WHERE APPROVAL_STATE='Released'")
        released_count = cursor.fetchone()[0]
        print(f"\n✅ Released状态记录数: {released_count}")
        
        if released_count > 0:
            # 检查Released状态的DEVICE分布
            cursor.execute("""
                SELECT DEVICE, COUNT(*) as cnt 
                FROM et_ft_test_spec 
                WHERE APPROVAL_STATE='Released'
                GROUP BY DEVICE 
                ORDER BY cnt DESC 
                LIMIT 10
            """)
            devices = cursor.fetchall()
            print("\n📋 Released状态DEVICE分布（前10）:")
            for device in devices:
                print(f"  - {device[0]}: {device[1]} 条记录")
            
            # 检查第一条记录的详细信息
            cursor.execute("""
                SELECT DEVICE, STAGE, TESTER, HB_PN, TB_PN, APPROVAL_STATE
                FROM et_ft_test_spec 
                WHERE APPROVAL_STATE='Released'
                ORDER BY id
                LIMIT 5
            """)
            first_records = cursor.fetchall()
            print("\n📋 前5条Released记录详情:")
            for i, record in enumerate(first_records, 1):
                print(f"  {i}. DEVICE='{record[0]}' STAGE='{record[1]}' TESTER='{record[2]}' HB_PN='{record[3]}' TB_PN='{record[4]}' APPROVAL='{record[5]}'")
        
        # 检查批次需要的DEVICE
        cursor.execute("""
            SELECT DEVICE, COUNT(*) as cnt 
            FROM et_wait_lot 
            WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
            GROUP BY DEVICE 
            ORDER BY cnt DESC 
            LIMIT 10
        """)
        lot_devices = cursor.fetchall()
        print("\n📋 批次需要的DEVICE分布（前10）:")
        for device in lot_devices:
            print(f"  - {device[0]}: {device[1]} 个批次")
        
        # 检查批次DEVICE与测试规范DEVICE的匹配情况
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT w.DEVICE) as lot_devices,
                COUNT(DISTINCT t.DEVICE) as spec_devices,
                COUNT(DISTINCT CASE WHEN t.DEVICE IS NOT NULL THEN w.DEVICE END) as matched_devices
            FROM et_wait_lot w
            LEFT JOIN et_ft_test_spec t ON w.DEVICE = t.DEVICE AND t.APPROVAL_STATE = 'Released'
            WHERE w.GOOD_QTY > 0 AND w.LOT_ID IS NOT NULL AND w.LOT_ID != ''
        """)
        match_stats = cursor.fetchone()
        print(f"\n📊 DEVICE匹配统计:")
        print(f"  - 批次需要的DEVICE数: {match_stats[0]}")
        print(f"  - 测试规范DEVICE数: {match_stats[1]}")
        print(f"  - 匹配的DEVICE数: {match_stats[2]}")
        
        if match_stats[0] > 0:
            match_rate = (match_stats[2] / match_stats[0]) * 100
            print(f"  - 匹配率: {match_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_test_spec_data()
