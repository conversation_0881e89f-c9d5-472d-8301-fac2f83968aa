
# 🚨 紧急修复：降低连接池上限，解决150秒性能问题
# 修复日期: 2025-09-04 17:46:15
# 问题根源: 多重数据库引擎导致连接池竞争和泄露
# 解决方案: 统一使用Flask共享连接池，降低总连接数上限
# =================================================================
# APS开发环境配置文件
# 当前配置: 远程测试服务器
# =================================================================

[DATABASE]
# 数据库主机地址
host = localhost

# 数据库端口
port = 3306

# 数据库用户名
username = root

# 数据库密码
password = WWWwww123!

# 数据库名称
database = aps

# 字符集
charset = utf8mb4

# ========== 统一连接池管理配置 (阶段6优化) ==========
# 🔥 Phase 6: 基于实际使用模式的精确调优
# 主连接池 - Flask-SQLAlchemy基础池 (进一步压缩)
pool_size = 4
max_overflow = 8  
pool_timeout = 45
pool_recycle = 900

# 🔥 Phase 6: 分层连接池配置 - 基于业务优先级的精确分配
# 核心排产连接池 (最高优先级，快速回收)
critical_pool_size = 2
critical_max_overflow = 4
critical_pool_recycle = 1800

# Web交互连接池 (中高优先级，平衡性能和回收)
interactive_pool_size = 4
interactive_max_overflow = 6
interactive_pool_recycle = 900

# 批处理连接池 (中等优先级，更快回收避免长期占用)
batch_pool_size = 2
batch_max_overflow = 4
batch_pool_recycle = 600

# 监控连接池 (低优先级，最快回收)
monitoring_pool_size = 2
monitoring_max_overflow = 3
monitoring_pool_recycle = 300

# 停用自定义连接池 - 统一管理
# custom_pool_min_size = 5     # 已停用
# custom_pool_init_size = 12   # 已停用  
# custom_pool_max_size = 35    # 已停用

[APPLICATION]
# 应用监听地址
host = 0.0.0.0

# 应用端口
port = 5000

# 调试模式
debug = True

# Excel数据路径（开发环境）
excel_path = ./Excel数据2025.7.23

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai

# 日志级别
log_level = DEBUG

# 最大工作线程数
max_workers = 10

[EMAIL]
# 发送邮箱地址（请修改为实际邮箱）
sender_email = <EMAIL>

# 发送邮箱密码（请修改为实际密码）
# 注意：如果使用163/QQ邮箱，需要使用授权码而不是登录密码
sender_password = your_email_password_here

# 接收邮箱（已固定）
recipient = <EMAIL>

# 🔥 Phase 6: 连接超时配置 - 基于统一连接管理器的精确调优
DB_CONNECTION_TIMEOUT=30
DB_POOL_TIMEOUT=45
DB_IDLE_TIMEOUT=900

# Phase 6优化：更积极的连接生命周期管理
CONNECTION_MAX_IDLE=900
SESSION_CONNECTION_TIMEOUT=1800

# 性能优化配置 - Phase 6调优
CACHE_TIMEOUT=180
API_TIMEOUT=30
SESSION_TIMEOUT=3600

# 并发控制 - Phase 6优化
MAX_WORKERS=6
ASYNC_POOL_SIZE=30
