"""
并发执行确定性控制 - 避免不同硬件配置下的调度差异
"""

import os
import threading
import logging
from typing import Any, Callable, List
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class DeterministicExecutor:
    """确定性执行器 - 硬件无关并发控制"""
    
    def __init__(self):
        self._enable_parallel = self._should_enable_parallel()
        self._fixed_thread_count = 4  # 固定线程数，避免硬件核心数差异
        self._execution_order_lock = threading.Lock()
    
    def _should_enable_parallel(self) -> bool:
        """判断是否启用并行计算"""
        # 优先级：环境变量 > 确定性模式
        parallel_env = os.environ.get('APS_ENABLE_PARALLEL', 'false')
        deterministic_env = os.environ.get('APS_DETERMINISTIC_TIME', 'false')
        
        if deterministic_env.lower() == 'true':
            # 确定性模式下禁用并行
            logger.info("🔧 确定性模式：禁用并行执行确保结果一致")
            return False
        
        return parallel_env.lower() == 'true'
    
    def execute_with_determinism(self, tasks: List[Callable], *args, **kwargs) -> List[Any]:
        """确定性执行任务列表"""
        if not self._enable_parallel:
            # 串行执行，确保顺序一致
            logger.debug("🔄 串行执行模式")
            results = []
            for i, task in enumerate(tasks):
                try:
                    result = task(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    logger.error(f"任务{i}执行失败: {e}")
                    results.append(None)
            return results
        
        # 并行执行（固定线程数）
        logger.debug(f"⚡ 并行执行模式（{self._fixed_thread_count}线程）")
        with ThreadPoolExecutor(max_workers=self._fixed_thread_count) as executor:
            futures = []
            for task in tasks:
                future = executor.submit(task, *args, **kwargs)
                futures.append(future)
            
            results = []
            for i, future in enumerate(futures):
                try:
                    result = future.result(timeout=30)  # 固定超时
                    results.append(result)
                except Exception as e:
                    logger.error(f"并行任务{i}执行失败: {e}")
                    results.append(None)
            
            return results
    
    def sort_results_deterministically(self, results: List[Any], sort_key: Callable = None) -> List[Any]:
        """确定性排序结果"""
        if not results:
            return results
        
        if sort_key is None:
            # 默认按字符串表示排序
            sort_key = lambda x: str(x) if x is not None else ""
        
        try:
            return sorted(results, key=sort_key)
        except Exception as e:
            logger.warning(f"结果排序失败，返回原序列: {e}")
            return results

# 全局确定性执行器
deterministic_executor = DeterministicExecutor()

def execute_deterministically(tasks: List[Callable], *args, **kwargs) -> List[Any]:
    """确定性执行任务（便捷函数）"""
    return deterministic_executor.execute_with_determinism(tasks, *args, **kwargs)

def is_parallel_enabled() -> bool:
    """检查是否启用并行执行"""
    return deterministic_executor._enable_parallel
