---
alwaysApply: true
---
## 1.ET_FT_TEST_SPEC 测试规范表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|--------|----------|----------|------|
| TEST_SPEC_ID | 测试规范编号 | varchar(36) | UUID主键 |
| TEST_SPEC_NAME | 测试规范名称 | varchar(100) | NOT NULL |
| TEST_SPEC_VER | 测试规范版本 | varchar(20) | 格式：x.x.x |
| STAGE | 工序 | varchar(20) | NOT NULL |
| TESTER | 测试机编号 | varchar(50) | 设备外键 |
| INV_ID | 订单编号 | varchar(30) | 采购订单号 |
| TEST_SPEC_TYPE | 测试规范类别 | varchar(30) | NOT NULL |
| APPROVAL_STATE | 审批状态 | smallint | 0=未审批,1=已批准,2=驳回 |
| ACTV_YN | 测试规范激活状态 | boolean | true=激活,false=禁用 |
| PROD_ID | 产品编号 | varchar(50) | 产品外键 |
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| CHIP_ID | 芯片名称 | varchar(50) | - |
| PKG_PN | 封装形式 | varchar(30) | - |
| COMPANY_ID | 工厂编号 | char(30) | 固定30位工厂代码 |
| DISABLE_USER | 测试规范禁用用户 | varchar(50) | - |
| DISABLE_REASON | 测试规范禁用原因 | text | - |
| DISABLE_TIME | 测试规范禁用时间 | timestamptz | 带时区的时间戳 |
| NOTE | 备注 | text | - |
| APPROVE_USER | 批准用户 | varchar(50) | - |
| APPROVE_TIME | 批准时间 | timestamptz | 带时区的时间戳 |
| ORT_QTY | ORT设置数量 | integer | 默认0 |
| REMAIN_QTY | 提醒数量 | integer | 默认0 |
| STANDARD_YIELD | 标准良率 | decimal(5,2) | 百分比(99.99) |
| LOW_YIELD | 修机良率 | decimal(5,2) | 百分比 |
| DOWN_YIELD | 停机良率 | decimal(5,2) | 百分比 |
| TEST_AREA | 测试机类别 | varchar(30) | - |
| HANDLER | 分选机类型 | varchar(30) | - |
| TEMPERATURE | 温度要求 | varchar(20) | 如"25±3℃" |
| FT_PROGRAM | FT程序 | varchar(100) | - |
| QA_PROGRAM | QA程序 | varchar(100) | - |
| GU_PROGRAM | GU程序 | varchar(100) | - |
| TB_PN | TB_PN | varchar(50) | - |
| HB_PN | HB_PN | varchar(50) | - |
| TIB | TIB | varchar(50) | - |
| TEST_TIME | 测试时间 | integer | 单位：秒 |
| UPH | 每小时产出量 | integer | 单位：件/小时 |
| SUFFIX_CODE | SUFFIX_CODE | varchar(20) | - |
| TESTER_CONFIG | 测试机配置 | jsonb | JSON格式存储配置 |
| GU_COMPARE_PARAM | GU对比参数 | text | - |
| STA_COMPARE_PARAM | STA对比参数 | text | - |
| DNR | DNR状态 | boolean | true=启用,false=禁用 |
| SITE | 测试站数量 | smallint | 默认1 |
| DPAT | DPAT状态 | boolean | true=启用,false=禁用 |
| BS_NAME | BS名称 | varchar(100) | - |
| GU_NAME | GU名称 | varchar(100) | - |
| C_SPEC | C_SPEC状态 | boolean | true=达标,false=未达标 |
| TEST_ENG | 测试规范用户名 | varchar(50) | - |
| TEST_OPERATION | 测试操作说明 | text | - |
| ORDER_COMMENT | 备注 | text | - |
| HIGH_YIELD | 高良率限 | decimal(5,2) | 百分比阈值 |
| VISION_LOSS_YIELD | 视觉不良和丢失总控制限 | decimal(5,2) | 百分比阈值 |
| VISION_YIELD | 视觉不良控制限 | decimal(5,2) | 百分比阈值 |
| LOSS_YIELD | 丢失控制限 | decimal(5,2) | 百分比阈值 |
| RETEST_YN | 是否复测 | boolean | true=是,false=否 |
| FT_PROGRAM_PATH | FT程序路径 | varchar(255) | 文件存储路径 |
| QA_PROGRAM_PATH | QA程序路径 | varchar(255) | 文件存储路径 |
| GU_PROGRAM_PATH | GU程序路径 | varchar(255) | 文件存储路径 |
| EFFECTIVE_TIME | 生效时间 | timestamptz | 带时区的时间戳 |
| TPL_RULE_TEMP | TPL_RULE_TEMP | text | - |
| TPL_RULE_TEMP_PATH | TPL_RULE_TEMP路径 | varchar(255) | 文件存储路径 |
| ALARM_DATE | 报警时间 | timestamptz | 带时区的时间戳 |
| FAC_ID | 工厂编号 | char(4) | 固定4位工厂代码 |
| EDIT_STATE | 可编辑状态 | boolean | true=可编辑,false=锁定 |
| EDIT_TIME | 编辑时间 | timestamptz | 带时区的时间戳 |
| EDIT_USER | 编辑用户名称 | varchar(50) | - |
| EVENT | 事件分类 | varchar(30) | - |
| EVENT_KEY | 事件密钥 | varchar(100) | - |
| EVENT_TIME | 事件时间 | timestamptz | 带时区的时间戳 |
| EVENT_USER | 事件用户 | varchar(50) | - |
| EVENT_MSG | 事件信息 | text | - |
| CREATE_TIME | 创建时间 | timestamptz | 带时区的时间戳 |
| CREATE_USER | 创建的用户 | varchar(50) | - |

## 2.ET_WAIT_LOT 待测产品表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|---------------|------------------|---------------|------------------------|
| LOT_ID | 内部工单号 | varchar(36) | UUID主键 |
| LOT_TYPE | 工单分类 | varchar(20) | NOT NULL |
| GOOD_QTY | 当站良品数量 | integer | 默认0 |
| PROD_ID | 产品编号 | varchar(50) | 产品外键 |
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| CHIP_ID | 芯片名称 | varchar(50) | - |
| PKG_PN | 封装形式 | varchar(30) | - |
| PO_ID | 产品名称 | varchar(100) | - |
| STAGE | 工序 | varchar(20) | NOT NULL |
| WIP_STATE | WIP状态 | varchar(20) | NOT NULL |
| PROC_STATE | 流程状态 | varchar(20) | NOT NULL |
| HOLD_STATE | 扣留状态 | smallint | 0=未扣,1=扣留中 |
| FLOW_ID | 测试工艺流程编号 | varchar(36) | UUID |
| FLOW_VER | 测试工艺流程版本 | varchar(20) | 格式：x.x.x |
| RELEASE_TIME | 解除扣押的时间 | timestamptz | 带时区的时间戳 |
| FAC_ID | 工厂编号 | char(4) | 固定4位工厂代码 |
| CREATE_TIME | 创建时间 | timestamptz | 带时区的时间戳 |

## 3.ET_UPH_EQP 每小时产出表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|---------------|----------|-------------|------------------------|
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| PKG_PN | 封装形式 | varchar(30) | - |
| STAGE | 工序 | varchar(20) | NOT NULL |
| UPH | 每小时产出量 | integer | 单位：件/小时 |
| HANDLER | 分选机类型 | varchar(50) | NOT NULL |
| FAC_ID | 工厂编号 | char(4) | 固定4位工厂代码 |
| EDIT_STATE | 可编辑状态 | boolean | true=可编辑,false=锁定 |
| EDIT_TIME | 编辑时间 | timestamptz | 带时区的时间戳 |
| EDIT_USER | 编辑用户名称 | varchar(50) | - |
| EVENT | 事件分类 | varchar(30) | - |
| EVENT_KEY | 事件密钥 | varchar(100) | - |
| EVENT_TIME | 事件时间 | timestamptz | 带时区的时间戳 |
| EVENT_USER | 事件用户 | varchar(50) | - |
| EVENT_MSG | 事件信息 | text | - |
| CREATE_TIME | 创建时间 | timestamptz | 带时区的时间戳 |
| CREATE_USER | 创建的用户 | varchar(50) | - |

## 4.ET_RECIPE_FILE 设备规范表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|-------------------------|----------------------|----------------|--------------------|
| RECIPE_FILE_NAME | 分选机文件名称 | varchar(100) | 主键 |
| RECIPE_FILE_PATH | 分选机文件路径 | varchar(255) | NOT NULL |
| PROD_TYPE | PROD_TYPE | varchar(30) | NOT NULL |
| EQP_TYPE | EQP_TYPE | varchar(30) | NOT NULL |
| HANDLER_CONFIG | 设备配置 | varchar(50) | - |
| SIMP_RECIPE_FILE_PATH | SIMP_RECIPE_FILE_PATH | varchar(255) | - |
| RECIPE_VER | 文件版本 | varchar(20) | NOT NULL |
| SUB_FAC | SUB_FAC | varchar(20) | - |
| KIT_PN | 分选机套件编号 | varchar(50) | - |
| SOCKET_PN | SOCKET_PN | varchar(50) | - |
| FAMILY | 产品族 | varchar(50) | NOT NULL |
| COORDINATE_ONE | COORDINATE_ONE | decimal(9,6) | 设备位置X轴 |
| COORDINATE_TWO | COORDINATE_TWO | decimal(9,6) | 设备位置Y轴 |
| COORDINATE_THREE | COORDINATE_THREE | decimal(9,6) | 设备位置Z轴 |

## 5.EQP_STATUS 分选机数量和状态表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|----------------------|----------------------|---------------|------------------------|
| HANDLER_ID | 分选机编号 | varchar(36) | UUID主键 |
| HANDLER_TYPE | 分选机类别 | varchar(30) | NOT NULL |
| TESTER_ID | 测试机编号 | varchar(36) | UUID |
| HANDLER_CONFIG | 分选机配置 | jsonb | JSON格式存储配置 |
| SOCKET_PN | SOCKET_PN | varchar(50) | - |
| KIT_PN | 分选机套件编号 | varchar(50) | - |
| EQP_CLASS | 分选机配型 | varchar(30) | - |
| EQP_TYPE | 机台类型 | varchar(30) | NOT NULL |
| TEMPERATURE_RANGE | 分选机温控范围 | varchar(30) | 如"-40℃~125℃" |
| TEMPERATURE_CAPACITY | 分选机温度能力 | varchar(30) | - |
| LOT_ID | 内部工单号 | varchar(36) | UUID |
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| STATUS | 机台状态 | TEXT | Run,IDLE,Wait,DOWN |
| HB_PN | HB_PN | varchar(50) | - |
| TB_PN | TB_PN | varchar(50) | - |
| TESTER_CONFIG | 测试机配置 | jsonb | JSON格式存储配置 |
| STAGE | 工序 | varchar(20) | NOT NULL |
| EVENT_TIME | 机台信息变动时间 | timestamptz | 带时区的时间戳 |

## 6.TCC_INV 硬件库
| 字段名 | 中文名称 | 数据类型 | 说明 |
|-------------------------|-----------------|---------------|----------------------|
| 硬件编码 | 硬件编码 | varchar(50) | 主键 |
| 关键硬件 | 关键硬件 | boolean | true=关键,false=普通 |
| 图片 | 图片 | varchar(255) | 图片存储路径 |
| 寿命状态 | 寿命状态 | varchar(20) | 新品/在役/报废 |
| 仓库 | 仓库 | varchar(50) | NOT NULL |
| 初始库位 | 初始库位 | varchar(50) | - |
| 当前储位1 | 当前储位1 | varchar(50) | 主存储位置 |
| 当前储位2 | 当前储位2 | varchar(50) | 备用存储位置 |
| 责任人 | 责任人 | varchar(50) | - |
| 周期消耗数 | 周期消耗数 | integer | 默认0 |
| 当前库位 | 当前库位 | varchar(50) | NOT NULL |
| 封装形式 | 封装形式 | varchar(30) | - |
| 状态 | 状态 | smallint | 0=闲置,1=使用中,2=维护 |
| 类别 | 类别 | varchar(30) | NOT NULL |
| 设备机型 | 设备机型 | varchar(50) | NOT NULL |
| 寄放方 | 寄放方 | varchar(50) | - |
| 备注(状态 shipout信息) | 备注(状态 shipout信息) | text | - |
| 类型 | 类型 | varchar(30) | NOT NULL |
| 操作 | 操作 | varchar(50) | - |

## 7.CT 产品生产周期参考表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|--------------------|--------------------|---------------|------------------------|
| LOT_ID | 内部工单号 | varchar(36) | UUID主键 |
| WORK_ORDER_ID | 生产任务令编号 | varchar(50) | NOT NULL |
| PROD_ID | 产品编号 | varchar(50) | 产品外键 |
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| PKG_PN | 封装形式 | varchar(30) | - |
| CHIP_ID | 芯片名称 | varchar(50) | - |
| FLOW_ID | 测试工艺流程编号 | varchar(36) | UUID |
| STAGE | 工序 | varchar(20) | NOT NULL |
| LOT_QTY | 来料数量 | integer | NOT NULL |
| ACT_QTY | 实物数量 | integer | 默认0 |
| GOOD_QTY | 当站良品数量 | integer | 默认0 |
| REJECT_QTY | 不良品数量 | integer | 默认0 |
| LOSS_QTY | 丢失数量 | integer | 默认0 |
| MAIN_EQP_ID | 测试机编号 | varchar(36) | UUID |
| AUXILIARY_EQP_ID | 分选机编号 | varchar(36) | UUID |
| LOT_START_TIME | 工单开始时间 | timestamptz | NOT NULL |
| LOT_END_TIME | 工单结束时间 | timestamptz | NOT NULL |
| SETUP_TIME | 调试时间 | interval | 时间间隔类型 |
| FT_TEST_PROGRAM | FT程序 | varchar(100) | - |
| IS_HALF_LOT_DOWN | 半批下机状态 | boolean | true=是,false=否 |
| FIRST_PASS_YIELD | 初测良率 | decimal(5,2) | 百分比 |
| FINAL_YIELD | 最终良率 | decimal(5,2) | 百分比 |
| VM_QTY | 视觉不良和丢失总数量 | integer | 默认0 |
| ALARM_BIN | ALARM_BIN | smallint | - |
| EVENT | 事件分类 | varchar(30) | - |
| EVENT_KEY | 事件密钥 | varchar(100) | - |
| EVENT_TIME | 事件时间 | timestamptz | 带时区的时间戳 |
| EVENT_USER | 事件用户 | varchar(50) | - |
| EVENT_MSG | 事件信息 | text | - |
| CREATE_TIME | 创建时间 | timestamptz | 带时区的时间戳 |
| CREATE_USER | 创建的用户 | varchar(50) | - |
| FAC_ID | 工厂编号 | char(4) | 固定4位工厂代码 |
| TRACK_CNT | TRACK_CNT | integer | 默认0 |
| COST_TIME | 总生产时间 | interval | 时间间隔类型 |

## 8.WIP_LOT 在制品表
| 字段名 | 中文名称 | 数据类型 | 说明 |
|-------------------------|------------------|----------------|------------------------|
| LOT_ID | 内部工单号 | varchar(36) | UUID主键 |
| LOT_TYPE | 工单分类 | varchar(20) | NOT NULL |
| DET_LOT_TYPE | 详细工单分类 | varchar(30) | NOT NULL |
| LOT_QTY | 工单数量 | integer | NOT NULL |
| SUB_QTY | 子工单数量 | integer | 默认0 |
| UNIT | 产品类别 | varchar(30) | NOT NULL |
| SUB_UNIT | 产品子类别 | varchar(30) | - |
| WIP_STATE | WIP状态 | varchar(20) | NOT NULL |
| PROC_STATE | 流程状态 | varchar(20) | NOT NULL |
| HOLD_STATE | 扣留状态 | smallint | 0=未扣,1=扣留中 |
| RW_STATE | 返工状态 | smallint | 0=未返工,1=返工中 |
| REPAIR_STATE | 维护状态 | smallint | 0=正常,1=维护中 |
| QC_STATE | QC状态 | smallint | 0=未检,1=合格,2=不合格 |
| PROD_ID | 产品编号 | varchar(50) | NOT NULL |
| PROC_RULE_ID | PROC_RULE_ID | varchar(36) | UUID |
| PRP_ID | PRP_ID | varchar(36) | UUID |
| FLOW_ID | 测试工艺流程编号 | varchar(36) | UUID |
| STAGE | 工序 | varchar(20) | NOT NULL |
| PRP_VER | PRP_VER | varchar(20) | 格式：x.x.x |
| FLOW_VER | 测试工艺流程版本 | varchar(20) | 格式：x.x.x |
| OPER_VER | OPER_VER | varchar(20) | 格式：x.x.x |
| CARR_ID | CARR_ID | varchar(36) | UUID |
| USE_SUB_LOT | USE_SUB_LOT | boolean | true=使用,false=不使用 |
| AREA_ID | AREA_ID | varchar(36) | UUID |
| LOC_ID | LOC_ID | varchar(36) | UUID |
| EQP_ID | EQP_ID | varchar(36) | UUID |
| SUB_EQP_ID | SUB_EQP_ID | varchar(36) | UUID |
| PORT_ID | PORT_ID | varchar(36) | UUID |
| RECIPE_ID | RECIPE_ID | varchar(36) | UUID |
| SUB_RECIPE_ID | SUB_RECIPE_ID | varchar(36) | UUID |
| MARK_ID | MARK_ID | varchar(36) | UUID |
| LOT_IN_QTY | LOT_IN_QTY | integer | 默认0 |
| LOT_OUT_QTY | LOT_OUT_QTY | integer | 默认0 |
| GOOD_QTY | 当站良品数量 | integer | 默认0 |
| NG_QTY | NG_QTY | integer | 默认0 |
| PREV_PROD_ID | PREV_PROD_ID | varchar(50) | - |
| PREV_PROC_RULE_ID | PREV_PROC_RULE_ID | varchar(36) | UUID |
| PREV_PRP_ID | PREV_PRP_ID | varchar(36) | UUID |
| PREV_PRP_VER | PREV_PRP_VER | varchar(20) | 格式：x.x.x |
| PREV_FLOW_ID | PREV_FLOW_ID | varchar(36) | UUID |
| PREV_FLOW_VER | PREV_FLOW_VER | varchar(20) | 格式：x.x.x |
| PREV_OPER_ID | PREV_OPER_ID | varchar(36) | UUID |
| PREV_OPER_VER | PREV_OPER_VER | varchar(20) | 格式：x.x.x |
| PREV_EQP_ID | PREV_EQP_ID | varchar(36) | UUID |
| PREV_PORT_ID | PREV_PORT_ID | varchar(36) | UUID |
| PREV_RECIPE_ID | PREV_RECIPE_ID | varchar(36) | UUID |
| PREV_SUB_RECIPE_ID | PREV_SUB_RECIPE_ID | varchar(36) | UUID |
| RTCL_ID | RTCL_ID | varchar(36) | UUID |
| BATCH_ID | BATCH_ID | varchar(36) | UUID |
| LAST_BATCH_ID | LAST_BATCH_ID | varchar(36) | UUID |
| CTM_ID | CTM_ID | varchar(36) | UUID |
| LOT_GRP_ID | LOT_GRP_ID | varchar(36) | UUID |
| RESV_EQP_ID | RESV_EQP_ID | varchar(36) | UUID |
| HOT_TYPE | HOT_TYPE | varchar(20) | - |
| SEND_COMPANY_ID | SEND_COMPANY_ID | char(4) | 固定4位工厂代码 |
| OPER_CHANGE_TIME | OPER_CHANGE_TIME | timestamptz | 带时区的时间戳 |
| JOB_START_TIME | JOB_START_TIME | timestamptz | 带时区的时间戳 |
| JOB_END_TIME | JOB_END_TIME | timestamptz | 带时区的时间戳 |
| PLAN_START_DATE | PLAN_START_DATE | timestamptz | 带时区的时间戳 |
| PLAN_DUE_DATE | PLAN_DUE_DATE | timestamptz | 带时区的时间戳 |
| GRADE | GRADE | varchar(20) | - |
| REASON_GRP | REASON_GRP | varchar(30) | - |
| REASON_CODE | REASON_CODE | varchar(30) | - |
| FR_RW_PROC_RULE_ID | FR_RW_PROC_RULE_ID | varchar(36) | UUID |
| FR_RW_PRP_ID | FR_RW_PRP_ID | varchar(36) | UUID |
| FR_RW_PRP_VER | FR_RW_PRP_VER | varchar(20) | 格式：x.x.x |
| FR_RW_FLOW_ID | FR_RW_FLOW_ID | varchar(36) | UUID |
| FR_RW_FLOW_VER | FR_RW_FLOW_VER | varchar(20) | 格式：x.x.x |
| FR_RW_OPER_ID | FR_RW_OPER_ID | varchar(36) | UUID |
| FR_RW_OPER_VER | FR_RW_OPER_VER | varchar(20) | 格式：x.x.x |
| RW_RT_PROC_RULE_ID | RW_RT_PROC_RULE_ID | varchar(36) | UUID |
| RW_RT_PRP_ID | RW_RT_PRP_ID | varchar(36) | UUID |
| RW_RT_PRP_VER | RW_RT_PRP_VER | varchar(20) | 格式：x.x.x |
| RW_RT_FLOW_ID | RW_RT_FLOW_ID | varchar(36) | UUID |
| RW_RT_FLOW_VER | RW_RT_FLOW_VER | varchar(20) | 格式：x.x.x |
| RW_RT_OPER_ID | RW_RT_OPER_ID | varchar(36) | UUID |
| RW_RT_OPER_VER | RW_RT_OPER_VER | varchar(20) | 格式：x.x.x |
| PILOT_TYPE | PILOT_TYPE | varchar(20) | - |
| MERGE_OPER_ID | MERGE_OPER_ID | varchar(36) | UUID |
| ACT_NM | ACT_NM | varchar(50) | - |
| LOT_JUDGE | LOT_JUDGE | varchar(20) | - |
| FAC_ID | 工厂编号 | char(4) | 固定4位工厂代码 |
| SUB_FAC | SUB_FAC | varchar(20) | - |
| ROOT_LOT_ID | ROOT_LOT_ID | varchar(36) | UUID |
| PARENT_LOT_ID | PARENT_LOT_ID | varchar(36) | UUID |
| CHILD_LOT_ID | CHILD_LOT_ID | varchar(36) | UUID |
| LOT_OBJ_ID | LOT_OBJ_ID | varchar(36) | UUID |
| CUST_LOT_ID | CUST_LOT_ID | varchar(36) | UUID |
| WORK_ORDER_ID | WORK_ORDER_ID | varchar(50) | NOT NULL |
| WORK_ORDER_VER | WORK_ORDER_VER | varchar(20) | 格式：x.x.x |
| BOM_ID | BOM_ID | varchar(36) | UUID |
| BOM_VER | BOM_VER | varchar(20) | 格式：x.x.x |
| PO_ID | 产品名称 | varchar(100) | - |
| LOT_OWNER | LOT_OWNER | varchar(50) | - |
| PKG_PN | 封装形式 | varchar(30) | - |
| RELEASE_TIME | 解除扣押的时间 | timestamptz | 带时区的时间戳 |
| SHIP_TIME | SHIP_TIME | timestamptz | 带时区的时间戳 |
| SHIP_ORDER_ID | SHIP_ORDER_ID | varchar(50) | - |
| SHIP_FAC_ID | SHIP_FAC_ID | char(4) | 固定4位工厂代码 |
| CREATE_LOT_QTY | CREATE_LOT_QTY | integer | 默认0 |
| CREATE_SUB_QTY | CREATE_SUB_QTY | integer | 默认0 |
| ROOT_LOT_QTY | ROOT_LOT_QTY | integer | 默认0 |
| TRACK_CARD_ID | TRACK_CARD_ID | varchar(36) | UUID |
| DBP_ID | DBP_ID | varchar(36) | UUID |
| CJOB_ID | CJOB_ID | varchar(36) | UUID |
| PROC_CNT | PROC_CNT | integer | 默认0 |
| RETEST_YN | 是否复测 | boolean | true=是,false=否 |
| DUT_ID | DUT_ID | varchar(36) | UUID |
| EVENT | 事件分类 | varchar(30) | - |
| EVENT_KEY | 事件密钥 | varchar(100) | - |
| EVENT_TIME | 事件时间 | timestamptz | 带时区的时间戳 |
| EVENT_USER | 事件用户 | varchar(50) | - |
| EVENT_MSG | 事件信息 | text | - |
| CREATE_TIME | 创建时间 | timestamptz | 带时区的时间戳 |
| CREATE_USER | 创建的用户 | varchar(50) | - |
| STR_FLAG | STR_FLAG | boolean | true=启用,false=禁用 |
| MAIN_EQP_ID | MAIN_EQP_ID | varchar(36) | UUID |
| AUXILIARY_EQP_ID | AUXILIARY_EQP_ID | varchar(36) | UUID |
| CONTAINER_ID | CONTAINER_ID | varchar(36) | UUID |
| CHIP_ID | 芯片名称 | varchar(50) | - |
| ACT_QTY | ACT_QTY | integer | 默认0 |
| TEST_SPEC_ID | 测试规范编号 | varchar(36) | UUID |
| TEST_SPEC_NAME | 测试规范名称 | varchar(100) | - |
| TEST_SPEC_VER | 测试规范版本 | varchar(20) | 格式：x.x.x |
| ORT_QTY | ORT设置数量 | integer | 默认0 |
| OA_FLAG | OA_FLAG | boolean | true=启用,false=禁用 |
| DEVICE | 产品名称 | varchar(100) | NOT NULL |
| WAREHOUSE_CONTAINER_ID | WAREHOUSE_CONTAINER_ID | varchar(36) | UUID |
| PROD_THICKNESS | PROD_THICKNESS | decimal(8,3) | 产品厚度 |
| IQC_QTY | IQC_QTY | integer | 默认0 |
| UPH | 每小时产出量 | integer | 单位：件/小时 |
| SEAL_FLAG | SEAL_FLAG | boolean | true=封装,false=未封装 |
| PACK_SPEC_ID | PACK_SPEC_ID | varchar(36) | UUID |
| PACK_SPEC_VER | PACK_SPEC_VER | varchar(20) | 格式：x.x.x |
| FULL_INSP_QC | FULL_INSP_QC | boolean | true=全检,false=抽检 |
| SPLIT_TYPE | SPLIT_TYPE | varchar(20) | - |
| WH_LOCATION_NO | WH_LOCATION_NO | varchar(50) | 仓库位置编号 |
| RELEASE_HOLD_TYPE | RELEASE_HOLD_TYPE | varchar(20) | - |
| DATA_CONFIRM_HOLD_YN | DATA_CONFIRM_HOLD_YN | boolean | true=确认扣留,false=正常 |
| ORT_SAMP_QTY | ORT_SAMP_QTY | integer | 默认0 |
| IQC_SAMP_QTY | IQC_SAMP_QTY | integer | 默认0 |
| LOCATION | LOCATION | varchar(50) | 位置信息 |
| RETEST_FLOW_ID | RETEST_FLOW_ID | varchar(36) | UUID |
| HALF_LOT_HOLD | HALF_LOT_HOLD | boolean | true=半批扣留,false=正常 |
| MERGE_LOT_ID | MERGE_LOT_ID | varchar(36) | UUID |
| STRM_QTY | STRM_QTY | integer | 默认0 |
| STRM_SAMP_QTY | STRM_SAMP_QTY | integer | 默认0 |
