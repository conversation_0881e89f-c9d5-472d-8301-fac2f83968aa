"""
时间确定性补丁
为排产系统提供可重复的时间计算，避免不同运行时间导致的结果差异
"""

from datetime import datetime, timedelta
import os
import logging

logger = logging.getLogger(__name__)

class DeterministicTimeProvider:
    """确定性时间提供者"""
    
    def __init__(self):
        self._reference_time = None
        self._enable_deterministic = os.environ.get('APS_DETERMINISTIC_TIME', 'false').lower() == 'true'
        
    def set_reference_time(self, reference_time_str: str):
        """设置参考时间"""
        try:
            self._reference_time = datetime.strptime(reference_time_str, '%Y-%m-%d %H:%M:%S')
            logger.info(f"🕐 设置确定性时间参考点: {self._reference_time}")
        except ValueError as e:
            logger.error(f"参考时间格式错误: {e}")
            self._reference_time = None
    
    def now(self):
        """获取当前时间（可确定性控制）"""
        if self._enable_deterministic and self._reference_time:
            return self._reference_time
        return datetime.now()
    
    def calculate_waiting_hours(self, create_time_str: str):
        """计算等待时间（确定性）"""
        create_time = datetime.strptime(create_time_str, '%Y-%m-%d %H:%M:%S')
        current_time = self.now()
        return (current_time - create_time).total_seconds() / 3600

# 全局时间提供者实例
deterministic_time = DeterministicTimeProvider()

def get_deterministic_now():
    """获取确定性的当前时间"""
    return deterministic_time.now()

def set_scheduling_reference_time(reference_time: str = None):
    """设置排产参考时间"""
    if reference_time is None:
        # 默认使用固定参考时间确保一致性
        reference_time = "2025-01-15 12:00:00"
    
    deterministic_time.set_reference_time(reference_time)
    return reference_time
