#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 智能算法选择器 - Phase 2 Task 2.1
根据多维度因素智能选择最优排产算法

核心功能：
1. 多维度评估：数据规模、约束复杂度、历史性能
2. 动态选择：根据当前系统状态选择最优算法
3. 性能学习：根据历史执行结果持续优化选择策略
4. 实时监控：跟踪算法性能并调整选择策略
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)

class AlgorithmType(Enum):
    """排产算法类型枚举"""
    HEURISTIC = "heuristic"           # 启发式算法（快速，适用于大规模）
    SIMPLIFIED_ORTOOLS = "simplified_ortools"  # 简化OR-Tools（中等精度和速度）
    FULL_ORTOOLS = "full_ortools"     # 完整OR-Tools（高精度，适用于小规模）
    GENETIC = "genetic"               # 遗传算法（适用于复杂约束）
    HYBRID = "hybrid"                 # 混合算法（自适应）

class ComplexityLevel(Enum):
    """问题复杂度级别"""
    LOW = "low"        # 低复杂度
    MEDIUM = "medium"  # 中等复杂度
    HIGH = "high"      # 高复杂度
    EXTREME = "extreme" # 极高复杂度

class IntelligentAlgorithmSelector:
    """🧠 智能算法选择器"""
    
    def __init__(self):
        self.performance_history = {}  # 算法性能历史
        self.complexity_weights = {
            'lot_count': 0.3,
            'equipment_count': 0.2,
            'constraint_complexity': 0.25,
            'diversity_index': 0.15,
            'deadline_pressure': 0.1
        }
        
        # 算法性能基准（秒）
        self.algorithm_benchmarks = {
            AlgorithmType.HEURISTIC: {
                'max_lots': 1000,
                'avg_time_per_lot': 0.1,
                'accuracy_rate': 0.75,
                'memory_usage': 'low'
            },
            AlgorithmType.SIMPLIFIED_ORTOOLS: {
                'max_lots': 100,
                'avg_time_per_lot': 0.5,
                'accuracy_rate': 0.85,
                'memory_usage': 'medium'
            },
            AlgorithmType.FULL_ORTOOLS: {
                'max_lots': 50,
                'avg_time_per_lot': 2.0,
                'accuracy_rate': 0.95,
                'memory_usage': 'high'
            },
            AlgorithmType.GENETIC: {
                'max_lots': 200,
                'avg_time_per_lot': 1.0,
                'accuracy_rate': 0.80,
                'memory_usage': 'medium'
            },
            AlgorithmType.HYBRID: {
                'max_lots': 500,
                'avg_time_per_lot': 0.8,
                'accuracy_rate': 0.88,
                'memory_usage': 'medium'
            }
        }
        
        # 算法选择决策树配置
        self.decision_rules = {
            'time_critical': {  # 时间紧急场景
                'threshold': 300,  # 5分钟内必须完成
                'preferred_algorithms': [AlgorithmType.HEURISTIC, AlgorithmType.SIMPLIFIED_ORTOOLS]
            },
            'accuracy_critical': {  # 精度要求高场景
                'threshold': 0.9,  # 90%以上精度要求
                'preferred_algorithms': [AlgorithmType.FULL_ORTOOLS, AlgorithmType.HYBRID]
            },
            'resource_limited': {  # 资源受限场景
                'memory_threshold': 1024,  # MB
                'preferred_algorithms': [AlgorithmType.HEURISTIC, AlgorithmType.SIMPLIFIED_ORTOOLS]
            }
        }

    def select_optimal_algorithm(self, 
                               wait_lots: List[Dict], 
                               available_equipment: List[Dict],
                               constraints: Dict = None,
                               requirements: Dict = None) -> Tuple[AlgorithmType, Dict]:
        """
        🧠 智能选择最优算法
        
        Args:
            wait_lots: 待排产批次列表
            available_equipment: 可用设备列表
            constraints: 约束条件配置
            requirements: 性能要求（时间限制、精度要求等）
            
        Returns:
            Tuple[AlgorithmType, Dict]: (选择的算法, 决策详情)
        """
        logger.info(f"🧠 开始智能算法选择 - 批次数: {len(wait_lots)}, 设备数: {len(available_equipment)}")
        
        start_time = time.time()
        
        # 1. 计算问题复杂度
        complexity_analysis = self._analyze_problem_complexity(wait_lots, available_equipment, constraints)
        
        # 2. 评估性能要求
        performance_requirements = self._analyze_performance_requirements(requirements)
        
        # 3. 检查系统资源状态
        resource_status = self._check_system_resources()
        
        # 4. 获取历史性能数据
        historical_performance = self._get_historical_performance(len(wait_lots), len(available_equipment))
        
        # 5. 执行智能决策
        selected_algorithm, decision_details = self._make_intelligent_decision(
            complexity_analysis,
            performance_requirements,
            resource_status,
            historical_performance
        )
        
        selection_time = time.time() - start_time
        
        # 6. 记录决策日志
        decision_log = {
            'timestamp': datetime.now().isoformat(),
            'selected_algorithm': selected_algorithm.value,
            'lot_count': len(wait_lots),
            'equipment_count': len(available_equipment),
            'complexity_level': complexity_analysis['level'].value,
            'selection_time': selection_time,
            'decision_factors': decision_details
        }
        
        logger.info(f"🎯 算法选择完成 - 选择: {selected_algorithm.value}, "
                   f"复杂度: {complexity_analysis['level'].value}, 耗时: {selection_time:.3f}s")
        
        return selected_algorithm, decision_log

    def _analyze_problem_complexity(self, 
                                  wait_lots: List[Dict], 
                                  available_equipment: List[Dict],
                                  constraints: Dict = None) -> Dict:
        """分析问题复杂度"""
        
        # 基础规模指标
        lot_count = len(wait_lots)
        equipment_count = len(available_equipment)
        
        # 计算数据多样性指数
        diversity_index = self._calculate_diversity_index(wait_lots, available_equipment)
        
        # 计算约束复杂度
        constraint_complexity = self._calculate_constraint_complexity(wait_lots, available_equipment, constraints)
        
        # 计算交期压力指数
        deadline_pressure = self._calculate_deadline_pressure(wait_lots)
        
        # 综合复杂度评分
        complexity_score = (
            lot_count * self.complexity_weights['lot_count'] +
            equipment_count * self.complexity_weights['equipment_count'] +
            constraint_complexity * self.complexity_weights['constraint_complexity'] +
            diversity_index * self.complexity_weights['diversity_index'] +
            deadline_pressure * self.complexity_weights['deadline_pressure']
        )
        
        # 确定复杂度级别
        if complexity_score < 20:
            level = ComplexityLevel.LOW
        elif complexity_score < 50:
            level = ComplexityLevel.MEDIUM
        elif complexity_score < 100:
            level = ComplexityLevel.HIGH
        else:
            level = ComplexityLevel.EXTREME
        
        return {
            'level': level,
            'score': complexity_score,
            'factors': {
                'lot_count': lot_count,
                'equipment_count': equipment_count,
                'diversity_index': diversity_index,
                'constraint_complexity': constraint_complexity,
                'deadline_pressure': deadline_pressure
            }
        }

    def _calculate_diversity_index(self, wait_lots: List[Dict], available_equipment: List[Dict]) -> float:
        """计算数据多样性指数"""
        try:
            # 批次产品类型多样性
            lot_devices = set(lot.get('DEVICE', '') for lot in wait_lots)
            lot_stages = set(lot.get('STAGE', '') for lot in wait_lots)
            
            # 设备类型多样性  
            eqp_types = set(eqp.get('EQP_CLASS', '') for eqp in available_equipment)
            eqp_configs = set(eqp.get('HANDLER_CONFIG', '') for eqp in available_equipment)
            
            # 计算多样性指数 (越多样越复杂)
            diversity_score = (
                len(lot_devices) * 2 +
                len(lot_stages) * 1.5 +
                len(eqp_types) * 2 +
                len(eqp_configs) * 1.5
            )
            
            return min(100.0, diversity_score)
            
        except Exception as e:
            logger.warning(f"计算多样性指数失败: {e}")
            return 50.0

    def _calculate_constraint_complexity(self, 
                                       wait_lots: List[Dict], 
                                       available_equipment: List[Dict],
                                       constraints: Dict = None) -> float:
        """计算约束复杂度"""
        try:
            complexity_score = 0.0
            
            # 基础约束复杂度
            complexity_score += len(wait_lots) * 0.1  # 批次数量影响
            complexity_score += len(available_equipment) * 0.2  # 设备数量影响
            
            # 配置匹配复杂度
            unique_configs = set()
            for lot in wait_lots:
                config_key = f"{lot.get('DEVICE', '')}-{lot.get('STAGE', '')}"
                unique_configs.add(config_key)
            
            complexity_score += len(unique_configs) * 1.5
            
            # 设备状态复杂度
            status_diversity = len(set(eqp.get('STATUS', '') for eqp in available_equipment))
            complexity_score += status_diversity * 2.0
            
            # 自定义约束复杂度
            if constraints:
                complexity_score += len(constraints) * 5.0
            
            return min(100.0, complexity_score)
            
        except Exception as e:
            logger.warning(f"计算约束复杂度失败: {e}")
            return 50.0

    def _calculate_deadline_pressure(self, wait_lots: List[Dict]) -> float:
        """计算交期压力指数"""
        try:
            if not wait_lots:
                return 0.0
            
            urgent_count = 0
            normal_count = 0
            
            current_time = datetime.now()
            
            for lot in wait_lots:
                # 简化处理：根据批次创建时间估算紧急程度
                create_time_str = lot.get('CREATE_TIME', '')
                if create_time_str:
                    try:
                        create_time = datetime.fromisoformat(create_time_str.replace('Z', '+00:00'))
                        hours_waiting = (current_time - create_time).total_seconds() / 3600
                        
                        if hours_waiting > 24:  # 等待超过24小时算紧急
                            urgent_count += 1
                        else:
                            normal_count += 1
                    except:
                        normal_count += 1
                else:
                    normal_count += 1
            
            total_lots = len(wait_lots)
            pressure_score = (urgent_count / total_lots) * 100 if total_lots > 0 else 0
            
            return min(100.0, pressure_score)
            
        except Exception as e:
            logger.warning(f"计算交期压力失败: {e}")
            return 30.0

    def _analyze_performance_requirements(self, requirements: Dict = None) -> Dict:
        """分析性能要求"""
        if not requirements:
            requirements = {}
        
        return {
            'time_limit': requirements.get('time_limit_seconds', 600),  # 默认10分钟
            'accuracy_requirement': requirements.get('accuracy_requirement', 0.8),  # 默认80%精度
            'memory_limit': requirements.get('memory_limit_mb', 2048),  # 默认2GB内存
            'is_time_critical': requirements.get('time_limit_seconds', 600) < 300,  # 5分钟内算紧急
            'is_accuracy_critical': requirements.get('accuracy_requirement', 0.8) > 0.9  # 90%以上算高精度要求
        }

    def _check_system_resources(self) -> Dict:
        """检查系统资源状态"""
        try:
            import psutil
            
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_info = psutil.virtual_memory()
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory_info.percent,
                'available_memory_mb': memory_info.available / (1024 * 1024),
                'is_resource_limited': cpu_percent > 80 or memory_info.percent > 80
            }
        except ImportError:
            logger.warning("psutil未安装，无法获取系统资源信息")
            return {
                'cpu_usage': 50.0,
                'memory_usage': 50.0,
                'available_memory_mb': 2048,
                'is_resource_limited': False
            }
        except Exception as e:
            logger.warning(f"获取系统资源失败: {e}")
            return {
                'cpu_usage': 50.0,
                'memory_usage': 50.0,
                'available_memory_mb': 2048,
                'is_resource_limited': False
            }

    def _get_historical_performance(self, lot_count: int, equipment_count: int) -> Dict:
        """获取历史性能数据"""
        # 简化实现：基于问题规模的性能估算
        scale_key = f"{lot_count//10*10}-{equipment_count//5*5}"  # 规模分组
        
        if scale_key in self.performance_history:
            return self.performance_history[scale_key]
        
        # 默认性能估算
        return {
            'heuristic_avg_time': lot_count * 0.1,
            'ortools_avg_time': lot_count * 2.0,
            'success_rates': {
                'heuristic': 0.75,
                'simplified_ortools': 0.85,
                'full_ortools': 0.95
            }
        }

    def _make_intelligent_decision(self,
                                 complexity_analysis: Dict,
                                 performance_requirements: Dict,
                                 resource_status: Dict,
                                 historical_performance: Dict) -> Tuple[AlgorithmType, Dict]:
        """执行智能决策"""
        
        decision_factors = {
            'complexity_level': complexity_analysis['level'].value,
            'complexity_score': complexity_analysis['score'],
            'time_critical': performance_requirements['is_time_critical'],
            'accuracy_critical': performance_requirements['is_accuracy_critical'],
            'resource_limited': resource_status['is_resource_limited'],
            'lot_count': complexity_analysis['factors']['lot_count'],
            'equipment_count': complexity_analysis['factors']['equipment_count']
        }
        
        # 决策逻辑
        if performance_requirements['is_time_critical']:
            # 时间紧急：优先选择快速算法
            lot_count = decision_factors['lot_count']
            if lot_count > 100 or complexity_analysis['level'] in [ComplexityLevel.HIGH, ComplexityLevel.EXTREME]:
                selected = AlgorithmType.HEURISTIC
                reason = f"大规模({lot_count}批次)或高复杂度且时间紧急，选择启发式算法确保快速响应"
            elif lot_count > 20:
                selected = AlgorithmType.SIMPLIFIED_ORTOOLS
                reason = f"中等规模({lot_count}批次)且时间紧急，选择简化OR-Tools"
            else:
                selected = AlgorithmType.SIMPLIFIED_ORTOOLS
                reason = f"小规模({lot_count}批次)但时间紧急，选择简化OR-Tools平衡速度和精度"
                
        elif performance_requirements['is_accuracy_critical']:
            # 精度要求高：优先选择精确算法
            if complexity_analysis['level'] in [ComplexityLevel.LOW, ComplexityLevel.MEDIUM]:
                selected = AlgorithmType.FULL_ORTOOLS
                reason = "低-中等复杂度且精度要求高，选择完整OR-Tools"
            else:
                selected = AlgorithmType.HYBRID
                reason = "高复杂度且精度要求高，选择混合算法"
                
        elif resource_status['is_resource_limited']:
            # 资源受限：选择轻量级算法
            selected = AlgorithmType.HEURISTIC
            reason = "系统资源受限，选择启发式算法"
            
        else:
            # 标准场景：根据复杂度选择
            if complexity_analysis['level'] == ComplexityLevel.LOW:
                selected = AlgorithmType.FULL_ORTOOLS
                reason = "低复杂度，选择完整OR-Tools获得最佳精度"
            elif complexity_analysis['level'] == ComplexityLevel.MEDIUM:
                selected = AlgorithmType.SIMPLIFIED_ORTOOLS
                reason = "中等复杂度，选择简化OR-Tools平衡性能"
            elif complexity_analysis['level'] == ComplexityLevel.HIGH:
                selected = AlgorithmType.HYBRID
                reason = "高复杂度，选择混合算法"
            else:  # EXTREME
                selected = AlgorithmType.HEURISTIC
                reason = "极高复杂度，选择启发式算法确保可执行性"
        
        decision_factors['selection_reason'] = reason
        
        return selected, decision_factors

    def record_algorithm_performance(self, 
                                   algorithm: AlgorithmType, 
                                   lot_count: int,
                                   equipment_count: int,
                                   execution_time: float,
                                   success_rate: float,
                                   accuracy_score: float = None):
        """记录算法性能数据，用于持续学习优化"""
        
        scale_key = f"{lot_count//10*10}-{equipment_count//5*5}"
        
        if scale_key not in self.performance_history:
            self.performance_history[scale_key] = {}
        
        algorithm_key = algorithm.value
        if algorithm_key not in self.performance_history[scale_key]:
            self.performance_history[scale_key][algorithm_key] = {
                'execution_times': [],
                'success_rates': [],
                'accuracy_scores': []
            }
        
        # 记录性能数据
        perf_data = self.performance_history[scale_key][algorithm_key]
        perf_data['execution_times'].append(execution_time)
        perf_data['success_rates'].append(success_rate)
        
        if accuracy_score is not None:
            perf_data['accuracy_scores'].append(accuracy_score)
        
        # 保持最近100次记录
        for metric in ['execution_times', 'success_rates', 'accuracy_scores']:
            if len(perf_data[metric]) > 100:
                perf_data[metric] = perf_data[metric][-100:]
        
        logger.debug(f"📊 记录算法性能 - {algorithm.value}: 规模{scale_key}, "
                    f"耗时{execution_time:.2f}s, 成功率{success_rate:.2f}")

    def get_algorithm_recommendation_report(self, 
                                          wait_lots: List[Dict], 
                                          available_equipment: List[Dict]) -> Dict:
        """生成算法推荐报告"""
        
        # 分析所有算法的适用性
        recommendations = {}
        
        for algorithm in AlgorithmType:
            benchmark = self.algorithm_benchmarks[algorithm]
            
            # 估算执行时间
            estimated_time = len(wait_lots) * benchmark['avg_time_per_lot']
            
            # 评估适用性
            is_suitable = len(wait_lots) <= benchmark['max_lots']
            
            # 计算推荐度评分
            if is_suitable:
                score = benchmark['accuracy_rate'] * 100
                if estimated_time < 300:  # 5分钟内加分
                    score += 10
                if benchmark['memory_usage'] == 'low':
                    score += 5
            else:
                score = 0
            
            recommendations[algorithm.value] = {
                'suitable': is_suitable,
                'estimated_time_seconds': estimated_time,
                'expected_accuracy': benchmark['accuracy_rate'],
                'memory_usage': benchmark['memory_usage'],
                'recommendation_score': score
            }
        
        # 选择最佳推荐
        best_algorithm = max(recommendations.items(), key=lambda x: x[1]['recommendation_score'])
        
        return {
            'best_recommendation': best_algorithm[0],
            'all_algorithms': recommendations,
            'analysis_timestamp': datetime.now().isoformat()
        } 