{% extends "base.html" %}

{% block title %}订单汇总预览{% endblock %}

{% block extra_css %}
<style>
    .classification-badge {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .classification-badge:hover {
        transform: scale(1.05);
    }
    .summary-card {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }
    .summary-card:hover {
        transform: translateY(-2px);
    }
    .lot-type-tag {
        font-size: 0.75rem;
        padding: 2px 6px;
        border-radius: 4px;
        background: #e9ecef;
        color: #495057;
    }
    .preview-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 500;
    }
    .action-buttons {
        position: sticky;
        bottom: 20px;
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h4 mb-1">📋 订单汇总预览</h2>
            <p class="text-muted mb-0">预览和调整订单分类，确认后生成FT工程/量产汇总表</p>
        </div>
        <div>
            <button type="button" class="btn btn-outline-secondary me-2" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>刷新数据
            </button>
            <button type="button" class="btn btn-primary" onclick="showBatchActions()">
                <i class="fas fa-cogs me-1"></i>批量操作
            </button>
        </div>
    </div>

    <!-- 数据统计概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card summary-card border-0 bg-gradient-primary text-white">
                <div class="card-body text-center">
                    <div class="h3 mb-1" id="totalOrdersCount">--</div>
                    <div class="small opacity-75">总订单数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card border-0 bg-gradient-info text-white">
                <div class="card-body text-center">
                    <div class="h3 mb-1" id="engineeringOrdersCount">--</div>
                    <div class="small opacity-75">工程订单</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card border-0 bg-gradient-success text-white">
                <div class="card-body text-center">
                    <div class="h3 mb-1" id="productionOrdersCount">--</div>
                    <div class="small opacity-75">量产订单</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card border-0 bg-gradient-warning text-white">
                <div class="card-body text-center">
                    <div class="h3 mb-1" id="unclassifiedOrdersCount">--</div>
                    <div class="small opacity-75">未分类订单</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lot Type分布统计 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Lot Type分布统计</h6>
                </div>
                <div class="card-body">
                    <div id="lotTypeChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>分类规则</h6>
                </div>
                <div class="card-body">
                    <div id="classificationRules">
                        <div class="mb-3">
                            <h6 class="text-info">🔧 工程类型</h6>
                            <div class="small text-muted">
                                <span class="lot-type-tag me-1">试验-E</span>
                                <span class="lot-type-tag me-1">新品-E</span>
                                <span class="lot-type-tag me-1">工程批</span>
                                <span class="lot-type-tag me-1">工程</span>
                                <span class="lot-type-tag me-1">DOE-Q</span>
                                <span class="lot-type-tag">qual-Q</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-success">🏭 量产类型</h6>
                            <div class="small text-muted">
                                <span class="lot-type-tag me-1">量产-P</span>
                                <span class="lot-type-tag me-1">小批量-PE</span>
                                <span class="lot-type-tag">量产批</span>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="editClassificationRules()">
                                <i class="fas fa-edit me-1"></i>编辑规则
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label">分类筛选</label>
                    <select class="form-select" id="classificationFilter" onchange="applyFilters()" aria-label="分类筛选">
                        <option value="">全部分类</option>
                        <option value="engineering">工程订单</option>
                        <option value="production">量产订单</option>
                        <option value="unknown">未分类订单</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Lot Type筛选</label>
                    <select class="form-select" id="lotTypeFilter" onchange="applyFilters()" aria-label="Lot类型筛选">
                        <option value="">全部类型</option>
                        <!-- 动态填充 -->
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">产品名称搜索</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="输入产品名称..." onkeyup="applyFilters()">
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>清除筛选
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success" onclick="exportFilteredExcel()">
                                <i class="fas fa-file-excel me-1"></i>导出Excel
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="exportFiltered()">
                                <i class="fas fa-file-code me-1"></i>导出JSON
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单列表预览 -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-table me-2"></i>订单列表预览
                    <span class="badge bg-secondary ms-2" id="displayedRecordsCount">0</span>
                </h6>
                <div>
                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAll()">
                        <i class="fas fa-check-square me-1"></i>全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                        <i class="fas fa-square me-1"></i>清除选择
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover preview-table mb-0">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>订单号</th>
                            <th>产品名称</th>
                            <th>电路名称</th>
                            <th>Lot Type</th>
                            <th>当前分类</th>
                            <th>交期</th>
                            <th>承揽商</th>
                            <th>来源文件</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <tr id="loadingRow">
                            <td colspan="10" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在加载订单数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    <div class="d-flex justify-content-between align-items-center mt-3">
        <div class="text-muted">
            显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalRecords">0</span> 条记录
        </div>
        <nav>
            <ul class="pagination pagination-sm mb-0" id="pagination">
                <!-- 动态生成分页 -->
            </ul>
        </nav>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons mt-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    已选择 <span id="selectedCount">0</span> 条订单
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-outline-info me-2" onclick="previewEngineering()">
                    <i class="fas fa-cog me-1"></i>预览工程汇总表
                </button>
                <button type="button" class="btn btn-outline-success me-3" onclick="previewProduction()">
                    <i class="fas fa-industry me-1"></i>预览量产汇总表
                </button>
                <button type="button" class="btn btn-info me-2" onclick="generateEngineeringSummary()">
                    <i class="fas fa-file-alt me-1"></i>生成工程汇总表
                </button>
                <button type="button" class="btn btn-success" onclick="generateProductionSummary()">
                    <i class="fas fa-file-alt me-1"></i>生成量产汇总表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 分类调整模态框 -->
<div class="modal fade" id="classificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">调整订单分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">订单信息</label>
                    <div class="card bg-light">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>订单号：</strong><span id="modalOrderNumber">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>产品：</strong><span id="modalProductName">-</span>
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-md-6">
                                    <strong>Lot Type：</strong><span id="modalLotType">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>当前分类：</strong><span id="modalCurrentClass">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">选择新分类</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="newClassification" id="engineering" value="engineering">
                        <label class="btn btn-outline-info" for="engineering">
                            <i class="fas fa-cog me-1"></i>工程订单
                        </label>
                        <input type="radio" class="btn-check" name="newClassification" id="production" value="production">
                        <label class="btn btn-outline-success" for="production">
                            <i class="fas fa-industry me-1"></i>量产订单
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">调整原因（可选）</label>
                    <textarea class="form-control" id="adjustmentReason" rows="2" placeholder="请输入调整分类的原因..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveClassificationChange()">保存调整</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>批量分类调整</h6>
                        <div class="mb-3">
                            <label class="form-label">目标分类</label>
                            <select class="form-select" id="batchClassification" aria-label="批次分类">
                                <option value="">请选择分类</option>
                                <option value="engineering">工程订单</option>
                                <option value="production">量产订单</option>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" onclick="applyBatchClassification()">
                            <i class="fas fa-tags me-1"></i>应用到选中订单
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>快速筛选操作</h6>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="selectByLotType('试验-E')">
                                选择所有"试验-E"订单
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="selectByLotType('量产-P')">
                                选择所有"量产-P"订单
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectUnclassified()">
                                选择所有未分类订单
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/echarts/echarts.min.js') }}"></script>
<!-- Excel导出库 -->
<script src="{{ url_for('static', filename='js/libs/xlsx.full.min.js') }}"></script>
<script>
// 全局变量
let ordersData = [];
let filteredData = [];
let selectedOrders = new Set();
let currentPage = 1;
let pageSize = 20;
let currentOrderId = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadOrdersData();
});

// 加载订单数据
async function loadOrdersData() {
    try {
        const response = await fetch('/api/orders/summary-data');
        const result = await response.json();
        
        if (result.success) {
            ordersData = result.data;
            filteredData = [...ordersData];
            updateStatistics();
            updateLotTypeChart();
            updateFilters();
            renderOrdersTable();
        } else {
            showAlert('error', '加载数据失败：' + result.message);
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        showAlert('error', '加载数据失败，请刷新页面重试');
    }
}

// 更新统计数据
function updateStatistics() {
    const stats = {
        total: ordersData.length,
        engineering: ordersData.filter(o => o.classification === 'engineering').length,
        production: ordersData.filter(o => o.classification === 'production').length,
        unclassified: ordersData.filter(o => !o.classification || o.classification === 'unknown').length
    };
    
    document.getElementById('totalOrdersCount').textContent = stats.total;
    document.getElementById('engineeringOrdersCount').textContent = stats.engineering;
    document.getElementById('productionOrdersCount').textContent = stats.production;
    document.getElementById('unclassifiedOrdersCount').textContent = stats.unclassified;
}

// 更新Lot Type图表
function updateLotTypeChart() {
    const lotTypeCounts = {};
    ordersData.forEach(order => {
        const lotType = order.lot_type || '未知';
        lotTypeCounts[lotType] = (lotTypeCounts[lotType] || 0) + 1;
    });
    
    const chartData = Object.entries(lotTypeCounts).map(([name, value]) => ({
        name, value
    }));
    
    const chart = echarts.init(document.getElementById('lotTypeChart'));
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [{
            name: 'Lot Type分布',
            type: 'pie',
            radius: '50%',
            data: chartData,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    chart.setOption(option);
}

// 更新筛选器选项
function updateFilters() {
    const lotTypes = [...new Set(ordersData.map(o => o.lot_type).filter(Boolean))];
    const lotTypeFilter = document.getElementById('lotTypeFilter');
    
    lotTypeFilter.innerHTML = '<option value="">全部类型</option>' +
        lotTypes.map(type => `<option value="${type}">${type}</option>`).join('');
}

// 应用筛选
function applyFilters() {
    const classificationFilter = document.getElementById('classificationFilter').value;
    const lotTypeFilter = document.getElementById('lotTypeFilter').value;
    const productSearch = document.getElementById('productSearch').value.toLowerCase();
    
    filteredData = ordersData.filter(order => {
        if (classificationFilter && order.classification !== classificationFilter) return false;
        if (lotTypeFilter && order.lot_type !== lotTypeFilter) return false;
        if (productSearch && !order.product_name?.toLowerCase().includes(productSearch)) return false;
        return true;
    });
    
    currentPage = 1;
    renderOrdersTable();
}

// 清除筛选
function clearFilters() {
    document.getElementById('classificationFilter').value = '';
    document.getElementById('lotTypeFilter').value = '';
    document.getElementById('productSearch').value = '';
    filteredData = [...ordersData];
    currentPage = 1;
    renderOrdersTable();
}

// 渲染订单表格
function renderOrdersTable() {
    const tbody = document.getElementById('ordersTableBody');
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pageData = filteredData.slice(start, end);
    
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-4 text-muted">
                    <i class="fas fa-search fa-2x mb-2 d-block"></i>
                    没有找到符合条件的订单
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = pageData.map(order => `
            <tr>
                <td>
                    <input type="checkbox" class="order-checkbox" value="${order.id}" 
                           ${selectedOrders.has(order.id) ? 'checked' : ''} 
                           onchange="updateSelection(${order.id}, this.checked)">
                </td>
                <td>${order.order_number || '-'}</td>
                <td>${order.product_name || '-'}</td>
                <td>${order.circuit_name || '-'}</td>
                <td><span class="lot-type-tag">${order.lot_type || '-'}</span></td>
                <td>
                    <span class="badge classification-badge ${getClassificationBadgeClass(order.classification)}" 
                          onclick="showClassificationModal(${order.id})">
                        ${getClassificationText(order.classification)}
                    </span>
                </td>
                <td>${order.delivery_date || '-'}</td>
                <td class="small">${order.contractor_name || '-'}</td>
                <td class="small" title="${order.source_file || '-'}">${truncateText(order.source_file, 20)}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary" 
                            onclick="showClassificationModal(${order.id})" title="调整分类">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    updatePagination();
    updateSelectionCount();
}

// 辅助函数
function getClassificationBadgeClass(classification) {
    switch(classification) {
        case 'engineering': return 'bg-info';
        case 'production': return 'bg-success';
        default: return 'bg-warning';
    }
}

function getClassificationText(classification) {
    switch(classification) {
        case 'engineering': return '工程';
        case 'production': return '量产';
        default: return '未分类';
    }
}

function truncateText(text, maxLength) {
    if (!text) return '-';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 选择相关函数
function updateSelection(orderId, checked) {
    if (checked) {
        selectedOrders.add(orderId);
    } else {
        selectedOrders.delete(orderId);
    }
    updateSelectionCount();
}

function updateSelectionCount() {
    document.getElementById('selectedCount').textContent = selectedOrders.size;
    document.getElementById('displayedRecordsCount').textContent = filteredData.length;
}

function selectAll() {
    filteredData.forEach(order => selectedOrders.add(order.id));
    document.querySelectorAll('.order-checkbox').forEach(cb => cb.checked = true);
    updateSelectionCount();
}

function clearSelection() {
    selectedOrders.clear();
    document.querySelectorAll('.order-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelectionCount();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox.checked) {
        selectAll();
    } else {
        clearSelection();
    }
}

// 分类调整相关函数
function showClassificationModal(orderId) {
    const order = ordersData.find(o => o.id === orderId);
    if (!order) return;
    
    currentOrderId = orderId;
    document.getElementById('modalOrderNumber').textContent = order.order_number || '-';
    document.getElementById('modalProductName').textContent = order.product_name || '-';
    document.getElementById('modalLotType').textContent = order.lot_type || '-';
    document.getElementById('modalCurrentClass').textContent = getClassificationText(order.classification);
    
    // 设置当前分类为选中状态
    if (order.classification) {
        document.getElementById(order.classification).checked = true;
    }
    
    new bootstrap.Modal(document.getElementById('classificationModal')).show();
}

async function saveClassificationChange() {
    const newClassification = document.querySelector('input[name="newClassification"]:checked')?.value;
    const reason = document.getElementById('adjustmentReason').value;
    
    if (!newClassification) {
        showAlert('warning', '请选择新的分类');
        return;
    }
    
    try {
        const response = await fetch('/api/orders/update-classification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                order_id: currentOrderId,
                classification: newClassification,
                reason: reason
            })
        });
        
        const result = await response.json();
        if (result.success) {
            // 更新本地数据
            const order = ordersData.find(o => o.id === currentOrderId);
            if (order) {
                order.classification = newClassification;
            }
            
            updateStatistics();
            renderOrdersTable();
            bootstrap.Modal.getInstance(document.getElementById('classificationModal')).hide();
            showAlert('success', '分类调整成功');
        } else {
            showAlert('error', '分类调整失败：' + result.message);
        }
    } catch (error) {
        console.error('保存分类调整失败:', error);
        showAlert('error', '保存失败，请重试');
    }
}

// 批量操作相关函数
function showBatchActions() {
    new bootstrap.Modal(document.getElementById('batchActionsModal')).show();
}

async function applyBatchClassification() {
    const classification = document.getElementById('batchClassification').value;
    if (!classification) {
        showAlert('warning', '请选择目标分类');
        return;
    }
    
    if (selectedOrders.size === 0) {
        showAlert('warning', '请先选择要调整的订单');
        return;
    }
    
    try {
        const response = await fetch('/api/orders/batch-update-classification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                order_ids: Array.from(selectedOrders),
                classification: classification
            })
        });
        
        const result = await response.json();
        if (result.success) {
            // 更新本地数据
            ordersData.forEach(order => {
                if (selectedOrders.has(order.id)) {
                    order.classification = classification;
                }
            });
            
            updateStatistics();
            renderOrdersTable();
            bootstrap.Modal.getInstance(document.getElementById('batchActionsModal')).hide();
            showAlert('success', `成功调整 ${selectedOrders.size} 条订单的分类`);
        } else {
            showAlert('error', '批量调整失败：' + result.message);
        }
    } catch (error) {
        console.error('批量调整失败:', error);
        showAlert('error', '批量调整失败，请重试');
    }
}

function selectByLotType(lotType) {
    clearSelection();
    ordersData.forEach(order => {
        if (order.lot_type === lotType) {
            selectedOrders.add(order.id);
        }
    });
    renderOrdersTable();
    showAlert('info', `已选择所有"${lotType}"类型的订单`);
}

function selectUnclassified() {
    clearSelection();
    ordersData.forEach(order => {
        if (!order.classification || order.classification === 'unknown') {
            selectedOrders.add(order.id);
        }
    });
    renderOrdersTable();
    showAlert('info', '已选择所有未分类的订单');
}

// 预览和生成汇总表
async function previewEngineering() {
    const engineeringOrders = ordersData.filter(o => o.classification === 'engineering');
    if (engineeringOrders.length === 0) {
        showAlert('warning', '没有工程订单可预览');
        return;
    }
    
    // 这里可以打开一个新窗口或模态框显示预览
    window.open('/orders/preview-summary?type=engineering', '_blank');
}

async function previewProduction() {
    const productionOrders = ordersData.filter(o => o.classification === 'production');
    if (productionOrders.length === 0) {
        showAlert('warning', '没有量产订单可预览');
        return;
    }
    
    window.open('/orders/preview-summary?type=production', '_blank');
}

async function generateEngineeringSummary() {
    if (confirm('确定要生成FT工程订单汇总表吗？')) {
        try {
            const response = await fetch('/api/orders/generate-engineering-summary', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', '工程汇总表生成成功');
                if (result.download_url) {
                    window.open(result.download_url, '_blank');
                }
            } else {
                showAlert('error', '生成失败：' + result.message);
            }
        } catch (error) {
            console.error('生成工程汇总表失败:', error);
            showAlert('error', '生成失败，请重试');
        }
    }
}

async function generateProductionSummary() {
    if (confirm('确定要生成FT量产订单汇总表吗？')) {
        try {
            const response = await fetch('/api/orders/generate-production-summary', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', '量产汇总表生成成功');
                if (result.download_url) {
                    window.open(result.download_url, '_blank');
                }
            } else {
                showAlert('error', '生成失败：' + result.message);
            }
        } catch (error) {
            console.error('生成量产汇总表失败:', error);
            showAlert('error', '生成失败，请重试');
        }
    }
}

// 其他功能
function refreshData() {
    showAlert('info', '正在刷新数据...');
    loadOrdersData();
}

function editClassificationRules() {
    // 这里可以打开分类规则编辑界面
    showAlert('info', '分类规则编辑功能开发中...');
}

// Excel导出功能
function exportFilteredExcel() {
    if (filteredData.length === 0) {
        showAlert('warning', '没有数据可导出');
        return;
    }
    
    try {
        // 准备导出数据
        const exportData = filteredData.map((order, index) => ({
            '序号': index + 1,
            '订单号': order.order_number || '',
            '产品名称': order.product_name || '',
            '电路名称': order.circuit_name || '',
            'Lot Type': order.lot_type || '',
            '分类': getClassificationText(order.classification) || '',
            '交期': order.delivery_date || '',
            '承揽商': order.contractor_name || '',
            '来源文件': order.source_file || ''
        }));
        
        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        ws['!cols'] = [
            { width: 8 },   // 序号
            { width: 20 },  // 订单号
            { width: 25 },  // 产品名称
            { width: 25 },  // 电路名称
            { width: 15 },  // Lot Type
            { width: 15 },  // 分类
            { width: 12 },  // 交期
            { width: 20 },  // 承揽商
            { width: 30 }   // 来源文件
        ];
        
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '订单汇总');
        
        // 生成文件名
        const now = new Date();
        const timestamp = now.getFullYear() + 
            String(now.getMonth() + 1).padStart(2, '0') + 
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') + 
            String(now.getMinutes()).padStart(2, '0');
        const filename = `订单汇总预览_${timestamp}.xlsx`;
        
        // 导出文件
        XLSX.writeFile(wb, filename);
        
        showAlert('success', `Excel导出成功！文件名: ${filename}，包含 ${exportData.length} 条记录`);
        
    } catch (error) {
        console.error('❌ Excel导出失败:', error);
        showAlert('error', 'Excel导出失败: ' + error.message);
    }
}

// JSON导出功能（保留原有功能）
function exportFiltered() {
    if (filteredData.length === 0) {
        showAlert('warning', '没有数据可导出');
        return;
    }
    
    // 导出筛选后的数据
    const exportData = filteredData.map(order => ({
        订单号: order.order_number,
        产品名称: order.product_name,
        电路名称: order.circuit_name,
        'Lot Type': order.lot_type,
        分类: getClassificationText(order.classification),
        交期: order.delivery_date,
        承揽商: order.contractor_name,
        来源文件: order.source_file
    }));
    
    downloadJSON(exportData, `订单筛选结果_${new Date().toISOString().slice(0,10)}.json`);
}

// 分页相关函数
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, filteredData.length);
    
    document.getElementById('pageStart').textContent = filteredData.length > 0 ? start : 0;
    document.getElementById('pageEnd').textContent = end;
    document.getElementById('totalRecords').textContent = filteredData.length;
    
    // 生成分页按钮
    const pagination = document.getElementById('pagination');
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderOrdersTable();
    }
}

// 工具函数
function showAlert(type, message) {
    // 这里可以使用 SweetAlert 或其他提示组件
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alertHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHTML);
    
    // 3秒后自动消失
    setTimeout(() => {
        const alert = document.querySelector('.alert:last-of-type');
        if (alert) {
            bootstrap.Alert.getOrCreateInstance(alert).close();
        }
    }, 3000);
}

function downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
</script>
{% endblock %} 