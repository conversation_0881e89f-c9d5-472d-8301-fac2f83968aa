#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测性连接管理器 - 阶段3智能优化
基于历史模式和机器学习预测连接需求，提前15分钟进行资源调度
"""

import logging
import threading
import time
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque
import math

logger = logging.getLogger(__name__)

@dataclass
class ConnectionDemandPoint:
    """连接需求数据点"""
    timestamp: float
    hour_of_day: int
    day_of_week: int
    total_connections: int
    pool_usage: Dict[str, int]
    request_rate: float
    avg_duration: float
    business_activity: str  # 业务活动类型

@dataclass
class PredictionResult:
    """预测结果"""
    predicted_demand: int
    confidence: float
    time_horizon: int  # 预测时间范围(分钟)
    recommendation: str
    risk_level: str

class ConnectionDemandPredictor:
    """连接需求预测器"""
    
    def __init__(self, history_size=1000):
        self.history_size = history_size
        self.demand_history: deque = deque(maxlen=history_size)
        
        # 模式识别参数
        self.hourly_patterns = {}  # 小时模式
        self.weekly_patterns = {}  # 周模式
        self.business_patterns = {}  # 业务模式
        
        # 预测模型参数
        self.trend_window = 10
        self.seasonal_window = 24  # 24小时季节性
        self.learning_rate = 0.1
        
        logger.info("🔮 连接需求预测器已初始化")
    
    def add_demand_point(self, connections: int, pool_usage: Dict[str, int], 
                        request_rate: float = 0, avg_duration: float = 0,
                        business_activity: str = "normal"):
        """添加需求数据点"""
        now = datetime.now()
        point = ConnectionDemandPoint(
            timestamp=time.time(),
            hour_of_day=now.hour,
            day_of_week=now.weekday(),
            total_connections=connections,
            pool_usage=pool_usage.copy(),
            request_rate=request_rate,
            avg_duration=avg_duration,
            business_activity=business_activity
        )
        
        self.demand_history.append(point)
        self._update_patterns(point)
        
        logger.debug(f"📊 添加需求数据点: {connections}连接 @{now.strftime('%H:%M')}")
    
    def _update_patterns(self, point: ConnectionDemandPoint):
        """更新模式识别"""
        # 更新小时模式
        hour_key = point.hour_of_day
        if hour_key not in self.hourly_patterns:
            self.hourly_patterns[hour_key] = []
        self.hourly_patterns[hour_key].append(point.total_connections)
        
        # 只保留最近30天的数据
        if len(self.hourly_patterns[hour_key]) > 30:
            self.hourly_patterns[hour_key] = self.hourly_patterns[hour_key][-30:]
        
        # 更新周模式
        week_key = point.day_of_week
        if week_key not in self.weekly_patterns:
            self.weekly_patterns[week_key] = []
        self.weekly_patterns[week_key].append(point.total_connections)
        
        if len(self.weekly_patterns[week_key]) > 4:  # 保留4周数据
            self.weekly_patterns[week_key] = self.weekly_patterns[week_key][-4:]
        
        # 更新业务模式
        business_key = point.business_activity
        if business_key not in self.business_patterns:
            self.business_patterns[business_key] = []
        self.business_patterns[business_key].append(point.total_connections)
        
        if len(self.business_patterns[business_key]) > 50:
            self.business_patterns[business_key] = self.business_patterns[business_key][-50:]
    
    def predict_demand(self, minutes_ahead: int = 15) -> PredictionResult:
        """预测连接需求"""
        if len(self.demand_history) < 3:
            return PredictionResult(
                predicted_demand=10,
                confidence=0.1,
                time_horizon=minutes_ahead,
                recommendation="数据不足，使用保守估计",
                risk_level="low"
            )
        
        # 计算多种预测方法的结果
        trend_prediction = self._predict_by_trend()
        seasonal_prediction = self._predict_by_seasonal()
        pattern_prediction = self._predict_by_patterns(minutes_ahead)
        
        # 加权组合预测结果
        weights = [0.3, 0.3, 0.4]  # 趋势、季节性、模式
        predictions = [trend_prediction, seasonal_prediction, pattern_prediction]
        
        weighted_prediction = sum(pred * weight for pred, weight in zip(predictions, weights))
        
        # 计算置信度
        variance = sum((pred - weighted_prediction) ** 2 for pred in predictions) / len(predictions)
        confidence = max(0.1, min(0.9, 1.0 / (1.0 + variance / 100)))
        
        # 生成建议和风险评估
        recommendation, risk_level = self._generate_recommendation(weighted_prediction, confidence)
        
        return PredictionResult(
            predicted_demand=int(max(1, weighted_prediction)),
            confidence=confidence,
            time_horizon=minutes_ahead,
            recommendation=recommendation,
            risk_level=risk_level
        )
    
    def _predict_by_trend(self) -> float:
        """基于趋势预测"""
        if len(self.demand_history) < self.trend_window:
            return float(self.demand_history[-1].total_connections)
        
        recent_points = list(self.demand_history)[-self.trend_window:]
        x = np.arange(len(recent_points))
        y = np.array([point.total_connections for point in recent_points])
        
        # 简单线性回归
        if len(x) > 1:
            slope = np.sum((x - np.mean(x)) * (y - np.mean(y))) / np.sum((x - np.mean(x)) ** 2)
            intercept = np.mean(y) - slope * np.mean(x)
            prediction = slope * len(x) + intercept
        else:
            prediction = float(y[0])
        
        return max(0, prediction)
    
    def _predict_by_seasonal(self) -> float:
        """基于季节性预测"""
        now = datetime.now()
        current_hour = now.hour
        
        if current_hour in self.hourly_patterns:
            hourly_avg = np.mean(self.hourly_patterns[current_hour])
            return hourly_avg
        
        # 如果没有当前小时的数据，使用相邻小时
        for offset in [-1, 1, -2, 2]:
            hour = (current_hour + offset) % 24
            if hour in self.hourly_patterns:
                return np.mean(self.hourly_patterns[hour])
        
        return float(self.demand_history[-1].total_connections)
    
    def _predict_by_patterns(self, minutes_ahead: int) -> float:
        """基于模式预测"""
        if not self.demand_history:
            return 10.0
        
        recent_point = self.demand_history[-1]
        base_demand = float(recent_point.total_connections)
        
        # 考虑业务活动模式
        business_activity = recent_point.business_activity
        if business_activity in self.business_patterns:
            business_avg = np.mean(self.business_patterns[business_activity])
            business_factor = business_avg / base_demand if base_demand > 0 else 1.0
        else:
            business_factor = 1.0
        
        # 考虑请求率变化
        if recent_point.request_rate > 0:
            request_factor = min(2.0, recent_point.request_rate / 10.0)  # 归一化
        else:
            request_factor = 1.0
        
        # 考虑时间因子(未来15分钟的变化)
        time_factor = 1.0 + (minutes_ahead / 60.0) * 0.1  # 轻微增长假设
        
        prediction = base_demand * business_factor * request_factor * time_factor
        
        return max(1, prediction)
    
    def _generate_recommendation(self, predicted_demand: float, confidence: float) -> Tuple[str, str]:
        """生成建议和风险评估"""
        current_demand = self.demand_history[-1].total_connections if self.demand_history else 10
        demand_change = (predicted_demand - current_demand) / current_demand if current_demand > 0 else 0
        
        # 风险评估
        if confidence < 0.3:
            risk_level = "high"
        elif confidence < 0.6:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        # 生成建议
        if demand_change > 0.5:  # 需求增长超过50%
            recommendation = f"预计需求激增{demand_change:.0%}，建议提前扩容"
        elif demand_change > 0.2:  # 需求增长超过20%
            recommendation = f"预计需求增长{demand_change:.0%}，建议准备扩容"
        elif demand_change < -0.3:  # 需求下降超过30%
            recommendation = f"预计需求下降{abs(demand_change):.0%}，可考虑缩容"
        else:
            recommendation = "需求稳定，维持当前配置"
        
        return recommendation, risk_level
    
    def get_pattern_insights(self) -> Dict[str, Any]:
        """获取模式洞察"""
        insights = {
            'hourly_peaks': [],
            'weekly_trends': {},
            'business_impacts': {},
            'data_points': len(self.demand_history)
        }
        
        # 小时峰值分析
        if self.hourly_patterns:
            hourly_avgs = {hour: np.mean(demands) 
                          for hour, demands in self.hourly_patterns.items()}
            peak_hours = sorted(hourly_avgs.items(), key=lambda x: x[1], reverse=True)[:3]
            insights['hourly_peaks'] = [(f"{h:02d}:00", round(demand, 1)) 
                                       for h, demand in peak_hours]
        
        # 周趋势分析
        if self.weekly_patterns:
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            for day, demands in self.weekly_patterns.items():
                if day < len(weekday_names):
                    insights['weekly_trends'][weekday_names[day]] = round(np.mean(demands), 1)
        
        # 业务影响分析
        if self.business_patterns:
            for activity, demands in self.business_patterns.items():
                insights['business_impacts'][activity] = {
                    'avg_demand': round(np.mean(demands), 1),
                    'max_demand': int(max(demands)),
                    'samples': len(demands)
                }
        
        return insights

class PredictiveConnectionManager:
    """预测性连接管理器"""
    
    def __init__(self):
        self.predictor = ConnectionDemandPredictor()
        self.auto_scaling_enabled = True
        self.prediction_interval = 300  # 5分钟预测一次
        
        # 自动扩缩容参数
        self.scale_up_threshold = 0.7    # 70%使用率触发扩容
        self.scale_down_threshold = 0.3  # 30%使用率触发缩容
        self.scale_up_factor = 1.5       # 扩容系数
        self.scale_down_factor = 0.8     # 缩容系数
        
        # 预测历史
        self.prediction_history: List[Dict] = []
        self.scaling_history: List[Dict] = []
        
        # 运行状态
        self._running = False
        self._predictor_thread = None
        self._last_prediction_time = 0
        
        logger.info("🤖 预测性连接管理器已初始化")
    
    def start_predictive_management(self):
        """启动预测性管理"""
        if self._running:
            return
        
        self._running = True
        self._predictor_thread = threading.Thread(
            target=self._prediction_loop,
            name="PredictiveConnectionManager",
            daemon=True
        )
        self._predictor_thread.start()
        
        logger.info("🚀 预测性连接管理已启动")
    
    def stop_predictive_management(self):
        """停止预测性管理"""
        self._running = False
        
        if self._predictor_thread and self._predictor_thread.is_alive():
            self._predictor_thread.join(timeout=5)
        
        logger.info("⏹️ 预测性连接管理已停止")
    
    def _prediction_loop(self):
        """预测循环"""
        logger.info("🔄 预测循环开始运行")
        
        while self._running:
            try:
                # 收集当前状态
                self._collect_current_state()
                
                # 执行预测
                prediction = self.predictor.predict_demand(minutes_ahead=15)
                
                # 记录预测历史
                self.prediction_history.append({
                    'timestamp': time.time(),
                    'prediction': asdict(prediction),
                    'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
                # 保持历史记录在合理范围
                if len(self.prediction_history) > 100:
                    self.prediction_history = self.prediction_history[-100:]
                
                # 基于预测执行扩缩容决策
                if self.auto_scaling_enabled:
                    self._execute_scaling_decision(prediction)
                
                # 记录日志
                logger.info(f"🔮 预测结果: {prediction.predicted_demand}连接 "
                          f"(置信度: {prediction.confidence:.1%}, 风险: {prediction.risk_level})")
                logger.info(f"💡 建议: {prediction.recommendation}")
                
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                logger.error(f"❌ 预测循环异常: {e}")
                time.sleep(60)  # 异常时等待1分钟
    
    def _collect_current_state(self):
        """收集当前连接池状态"""
        try:
            from app.utils.unified_connection_manager import get_connection_status
            status = get_connection_status()
            
            # 计算总连接数和池使用情况
            total_connections = status.get('active_connections_total', 0)
            pool_usage = {}
            
            for pool_name, pool_stats in status.get('pools', {}).items():
                pool_usage[pool_name] = pool_stats.get('active_connections', 0)
            
            # 估算请求率和平均持续时间
            request_rate = self._estimate_request_rate(status)
            avg_duration = self._estimate_avg_duration(status)
            
            # 识别当前业务活动
            business_activity = self._identify_business_activity()
            
            # 添加到预测器
            self.predictor.add_demand_point(
                connections=total_connections,
                pool_usage=pool_usage,
                request_rate=request_rate,
                avg_duration=avg_duration,
                business_activity=business_activity
            )
            
        except Exception as e:
            logger.error(f"❌ 状态收集失败: {e}")
    
    def _estimate_request_rate(self, status: Dict) -> float:
        """估算请求率"""
        # 基于统计信息估算当前请求率
        stats = status.get('statistics', {})
        total_created = stats.get('total_connections_created', 0)
        total_closed = stats.get('total_connections_closed', 0)
        
        # 简单的请求率估算
        current_time = time.time()
        if self._last_prediction_time > 0:
            time_diff = current_time - self._last_prediction_time
            connection_diff = total_created - total_closed
            request_rate = connection_diff / max(time_diff, 1) * 60  # 每分钟请求数
        else:
            request_rate = 0
        
        self._last_prediction_time = current_time
        return max(0, request_rate)
    
    def _estimate_avg_duration(self, status: Dict) -> float:
        """估算平均连接持续时间"""
        # 这里可以基于池配置估算
        pools = status.get('pools', {})
        if pools:
            total_recycle_time = sum(pool.get('pool_recycle', 600) for pool in pools.values())
            avg_duration = total_recycle_time / len(pools)
        else:
            avg_duration = 300  # 默认5分钟
        
        return avg_duration
    
    def _identify_business_activity(self) -> str:
        """识别当前业务活动"""
        now = datetime.now()
        hour = now.hour
        
        # 基于时间识别业务活动
        if 2 <= hour <= 6:
            return "maintenance"  # 维护期
        elif 7 <= hour <= 9 or 17 <= hour <= 19:
            return "peak_hours"   # 高峰期
        elif 9 <= hour <= 17:
            return "business_hours"  # 工作时间
        elif 19 <= hour <= 22:
            return "evening"      # 晚间
        else:
            return "off_hours"    # 非工作时间
    
    def _execute_scaling_decision(self, prediction: PredictionResult):
        """执行扩缩容决策"""
        try:
            from app.utils.unified_connection_manager import get_connection_status
            current_status = get_connection_status()
            
            # 计算当前使用率
            total_capacity = 0
            total_used = 0
            
            for pool_stats in current_status.get('pools', {}).values():
                capacity = pool_stats.get('pool_size', 0) + pool_stats.get('max_overflow', 0)
                used = pool_stats.get('checked_out', 0) + pool_stats.get('overflow', 0)
                total_capacity += capacity
                total_used += used
            
            current_usage_rate = total_used / total_capacity if total_capacity > 0 else 0
            predicted_usage_rate = prediction.predicted_demand / total_capacity if total_capacity > 0 else 0
            
            # 扩缩容决策逻辑
            scaling_action = None
            
            if predicted_usage_rate > self.scale_up_threshold and prediction.confidence > 0.5:
                scaling_action = "scale_up"
                recommended_capacity = int(prediction.predicted_demand * self.scale_up_factor)
            elif current_usage_rate < self.scale_down_threshold and prediction.confidence > 0.6:
                scaling_action = "scale_down"
                recommended_capacity = int(total_capacity * self.scale_down_factor)
            
            if scaling_action:
                scaling_record = {
                    'timestamp': time.time(),
                    'action': scaling_action,
                    'current_usage_rate': current_usage_rate,
                    'predicted_usage_rate': predicted_usage_rate,
                    'confidence': prediction.confidence,
                    'current_capacity': total_capacity,
                    'recommended_capacity': recommended_capacity,
                    'reason': prediction.recommendation
                }
                
                self.scaling_history.append(scaling_record)
                
                # 记录扩缩容建议（实际实施需要更多考虑）
                logger.info(f"🎯 扩缩容建议: {scaling_action}")
                logger.info(f"   当前容量: {total_capacity}, 建议容量: {recommended_capacity}")
                logger.info(f"   使用率: {current_usage_rate:.1%} → 预测: {predicted_usage_rate:.1%}")
                
                # 保持历史记录
                if len(self.scaling_history) > 50:
                    self.scaling_history = self.scaling_history[-50:]
                    
        except Exception as e:
            logger.error(f"❌ 扩缩容决策失败: {e}")
    
    def get_prediction_report(self) -> Dict[str, Any]:
        """获取预测报告"""
        if not self.prediction_history:
            return {'message': '暂无预测数据'}
        
        recent_predictions = self.prediction_history[-10:]
        
        report = {
            'latest_prediction': recent_predictions[-1] if recent_predictions else None,
            'prediction_accuracy': self._calculate_prediction_accuracy(),
            'pattern_insights': self.predictor.get_pattern_insights(),
            'scaling_recommendations': len(self.scaling_history),
            'auto_scaling_enabled': self.auto_scaling_enabled,
            'prediction_count': len(self.prediction_history)
        }
        
        return report
    
    def _calculate_prediction_accuracy(self) -> Dict[str, float]:
        """计算预测准确性"""
        if len(self.prediction_history) < 2:
            return {'accuracy': 0, 'samples': 0}
        
        accuracies = []
        
        # 比较前面的预测和实际结果
        for i in range(min(10, len(self.prediction_history) - 1)):
            pred = self.prediction_history[-(i+2)]
            actual = self.prediction_history[-(i+1)]
            
            predicted_demand = pred['prediction']['predicted_demand']
            # 这里需要实际的连接数据，暂时用下一个预测作为近似
            actual_demand = actual['prediction']['predicted_demand']
            
            if actual_demand > 0:
                accuracy = 1.0 - abs(predicted_demand - actual_demand) / actual_demand
                accuracies.append(max(0, min(1, accuracy)))
        
        if accuracies:
            return {
                'accuracy': np.mean(accuracies),
                'samples': len(accuracies)
            }
        else:
            return {'accuracy': 0, 'samples': 0}
    
    def print_prediction_report(self):
        """打印预测报告"""
        report = self.get_prediction_report()
        
        print("\n🔮 预测性连接管理报告")
        print("=" * 50)
        
        if report.get('latest_prediction'):
            latest = report['latest_prediction']['prediction']
            print(f"📊 最新预测:")
            print(f"   预测需求: {latest['predicted_demand']}个连接")
            print(f"   置信度: {latest['confidence']:.1%}")
            print(f"   风险等级: {latest['risk_level']}")
            print(f"   建议: {latest['recommendation']}")
        
        accuracy = report.get('prediction_accuracy', {})
        if accuracy.get('samples', 0) > 0:
            print(f"\n🎯 预测准确性:")
            print(f"   准确率: {accuracy['accuracy']:.1%}")
            print(f"   样本数: {accuracy['samples']}")
        
        insights = report.get('pattern_insights', {})
        if insights.get('hourly_peaks'):
            print(f"\n📈 模式洞察:")
            print(f"   高峰时段: {', '.join([f'{h}({d})' for h, d in insights['hourly_peaks']])}")
        
        if insights.get('data_points', 0) > 0:
            print(f"   数据点数: {insights['data_points']}")
        
        print(f"\n⚙️  配置状态:")
        print(f"   自动扩缩容: {'启用' if report['auto_scaling_enabled'] else '禁用'}")
        print(f"   扩缩容建议: {report['scaling_recommendations']}次")
        print(f"   预测总数: {report['prediction_count']}次")

# 全局预测性管理器实例
predictive_manager = PredictiveConnectionManager()

def start_predictive_management():
    """启动预测性管理"""
    predictive_manager.start_predictive_management()

def stop_predictive_management():
    """停止预测性管理"""
    predictive_manager.stop_predictive_management()

def get_connection_prediction(minutes_ahead: int = 15) -> PredictionResult:
    """获取连接预测"""
    return predictive_manager.predictor.predict_demand(minutes_ahead)

def get_prediction_report():
    """获取预测报告"""
    return predictive_manager.get_prediction_report()

if __name__ == "__main__":
    # 测试预测器
    print("🔮 预测性连接管理器测试")
    
    manager = PredictiveConnectionManager()
    
    # 模拟一些历史数据
    for i in range(20):
        connections = 10 + int(5 * math.sin(i * 0.3)) + np.random.randint(-2, 3)
        pool_usage = {'HIGH': connections//2, 'NORMAL': connections//3, 'LOW': connections//6}
        manager.predictor.add_demand_point(
            connections=max(1, connections),
            pool_usage=pool_usage,
            request_rate=float(np.random.uniform(5, 15)),
            business_activity="business_hours" if i % 3 == 0 else "normal"
        )
        time.sleep(0.1)  # 模拟时间间隔
    
    # 执行预测
    prediction = manager.predictor.predict_demand()
    print(f"\n📊 预测结果:")
    print(f"   预测需求: {prediction.predicted_demand}")
    print(f"   置信度: {prediction.confidence:.1%}")
    print(f"   建议: {prediction.recommendation}")
    
    # 打印洞察
    insights = manager.predictor.get_pattern_insights()
    print(f"\n🧠 模式洞察:")
    print(f"   数据点: {insights['data_points']}")
    if insights['business_impacts']:
        print(f"   业务影响: {insights['business_impacts']}")
    
    print(f"\n✅ 预测器测试完成")