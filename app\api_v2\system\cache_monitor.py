"""
缓存监控API - 监控数据源管理器的缓存状态
"""

import logging
from flask import Blueprint, jsonify, request
from app.services.data_source_manager import DataSourceManager

logger = logging.getLogger(__name__)

cache_monitor_bp = Blueprint('cache_monitor', __name__)

@cache_monitor_bp.route('/cache/status', methods=['GET'])
def get_cache_status():
    """获取缓存状态"""
    try:
        data_manager = DataSourceManager()
        cache_status = data_manager.get_cache_status()
        
        return jsonify({
            'success': True,
            'data': cache_status,
            'message': '缓存状态获取成功'
        })
    except Exception as e:
        logger.error(f"获取缓存状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_monitor_bp.route('/cache/clear', methods=['POST'])
def clear_cache():
    """清理缓存"""
    try:
        data_manager = DataSourceManager()
        data_manager.clear_cache()
        
        return jsonify({
            'success': True,
            'message': '缓存清理成功'
        })
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_monitor_bp.route('/cache/stats', methods=['GET'])
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        data_manager = DataSourceManager()
        cache_status = data_manager.get_cache_status()
        
        # 计算统计信息
        total_items = cache_status['total_cache_items']
        expired_items = sum(1 for item in cache_status['cache_details'] if item['is_expired'])
        active_items = total_items - expired_items
        
        # 按类型分组统计
        type_stats = {}
        for item in cache_status['cache_details']:
            key_type = item['key'].replace('_excel', '')
            if key_type not in type_stats:
                type_stats[key_type] = {
                    'count': 0,
                    'total_data_count': 0,
                    'avg_age': 0,
                    'min_remaining': float('inf'),
                    'max_remaining': float('-inf')
                }
            
            stats = type_stats[key_type]
            stats['count'] += 1
            stats['total_data_count'] += item['data_count']
            stats['avg_age'] += item['age_seconds']
            stats['min_remaining'] = min(stats['min_remaining'], item['remaining_seconds'])
            stats['max_remaining'] = max(stats['max_remaining'], item['remaining_seconds'])
        
        # 计算平均值
        for stats in type_stats.values():
            if stats['count'] > 0:
                stats['avg_age'] = round(stats['avg_age'] / stats['count'], 1)
                if stats['min_remaining'] == float('inf'):
                    stats['min_remaining'] = 0
                if stats['max_remaining'] == float('-inf'):
                    stats['max_remaining'] = 0
        
        return jsonify({
            'success': True,
            'data': {
                'summary': {
                    'total_cache_items': total_items,
                    'active_items': active_items,
                    'expired_items': expired_items,
                    'cache_efficiency': round((active_items / total_items * 100) if total_items > 0 else 0, 1)
                },
                'by_type': type_stats,
                'timestamp': cache_status['timestamp']
            },
            'message': '缓存统计获取成功'
        })
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 