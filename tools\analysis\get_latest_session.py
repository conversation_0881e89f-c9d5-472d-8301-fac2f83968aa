#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取最新排产会话ID
"""

from app import create_app
from app.utils.db_connection_pool import get_db_cursor

def main():
    app, _ = create_app()
    
    with app.app_context():
        try:
            with get_db_cursor('aps') as cur:
                cur.execute('SELECT SESSION_ID FROM lotprioritydone ORDER BY CREATE_TIME DESC LIMIT 1')
                row = cur.fetchone()
                
                if row:
                    session_id = row['SESSION_ID']
                    print(f"最新会话ID: {session_id}")
                    return session_id
                else:
                    print("未找到排产记录")
                    return None
                    
        except Exception as e:
            print(f"获取会话ID失败: {e}")
            return None

if __name__ == '__main__':
    main()
