# -*- coding: utf-8 -*-
"""
智能优先级匹配服务
实现待排产批次与优先级配置的智能匹配

Author: AI Assistant
Date: 2025-06-13
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from difflib import SequenceMatcher
from dataclasses import dataclass

from flask import current_app
from app import db
from app.models import ProductPriorityConfig, ET_WAIT_LOT, ET_UPH_EQP
from sqlalchemy import text, and_, or_

logger = logging.getLogger(__name__)

@dataclass
class MatchResult:
    """匹配结果数据类"""
    batch_id: str
    config_id: Optional[int]
    confidence_score: float
    match_strategy: str
    match_details: Dict[str, Any]
    priority_level: str
    priority_order: int
    suggested_actions: List[str]

class MatchingStrategy:
    """匹配策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.weight_config = {}
    
    def match(self, batch: Dict, priority_configs: List[ProductPriorityConfig]) -> <PERSON><PERSON>[Optional[ProductPriorityConfig], float, Dict]:
        """执行匹配逻辑"""
        raise NotImplementedError
    
    def fuzzy_match(self, str1: str, str2: str) -> float:
        """模糊匹配算法"""
        if not str1 or not str2:
            return 0.0
        
        # 使用SequenceMatcher计算相似度
        similarity = SequenceMatcher(None, str1.upper(), str2.upper()).ratio()
        
        # 考虑包含关系
        if str1.upper() in str2.upper() or str2.upper() in str1.upper():
            similarity = max(similarity, 0.8)
        
        return similarity

class ExactMatchStrategy(MatchingStrategy):
    """精确匹配策略"""
    
    def __init__(self):
        super().__init__("exact")
        self.weight_config = {
            'device_exact': 0.5,
            'stage_exact': 0.3,
            'pkg_pn_exact': 0.1,
            'chip_id_exact': 0.1
        }
    
    def match(self, batch: Dict, priority_configs: List[ProductPriorityConfig]) -> Tuple[Optional[ProductPriorityConfig], float, Dict]:
        """精确匹配逻辑"""
        best_match = None
        best_score = 0.0
        match_details = {}
        
        batch_device = batch.get('DEVICE', '').strip()
        batch_stage = batch.get('STAGE', '').strip()
        batch_pkg_pn = batch.get('PKG_PN', '').strip()
        batch_chip_id = batch.get('CHIP_ID', '').strip()
        
        for config in priority_configs:
            score = 0.0
            details = {}
            
            # 设备名称精确匹配
            if batch_device and config.device and batch_device == config.device:
                score += self.weight_config['device_exact']
                details['device_match'] = True
            
            # 工序精确匹配
            if batch_stage and config.stage and batch_stage == config.stage:
                score += self.weight_config['stage_exact']
                details['stage_match'] = True
            
            # 封装型号精确匹配
            if batch_pkg_pn and config.pkg_pn and batch_pkg_pn == config.pkg_pn:
                score += self.weight_config['pkg_pn_exact']
                details['pkg_pn_match'] = True
            
            # 芯片名称精确匹配
            if batch_chip_id and config.chip_id and batch_chip_id == config.chip_id:
                score += self.weight_config['chip_id_exact']
                details['chip_id_match'] = True
            
            if score > best_score:
                best_score = score
                best_match = config
                match_details = details
        
        return best_match, best_score, match_details

class FuzzyMatchStrategy(MatchingStrategy):
    """模糊匹配策略"""
    
    def __init__(self):
        super().__init__("fuzzy")
        self.weight_config = {
            'device_fuzzy': 0.4,
            'stage_fuzzy': 0.3,
            'pkg_pn_fuzzy': 0.2,
            'chip_id_fuzzy': 0.1
        }
        self.similarity_threshold = 0.7
    
    def match(self, batch: Dict, priority_configs: List[ProductPriorityConfig]) -> Tuple[Optional[ProductPriorityConfig], float, Dict]:
        """模糊匹配逻辑"""
        best_match = None
        best_score = 0.0
        match_details = {}
        
        batch_device = batch.get('DEVICE', '').strip()
        batch_stage = batch.get('STAGE', '').strip()
        batch_pkg_pn = batch.get('PKG_PN', '').strip()
        batch_chip_id = batch.get('CHIP_ID', '').strip()
        
        for config in priority_configs:
            score = 0.0
            details = {}
            
            # 设备名称模糊匹配
            if batch_device and config.device:
                device_similarity = self.fuzzy_match(batch_device, config.device)
                if device_similarity >= self.similarity_threshold:
                    score += device_similarity * self.weight_config['device_fuzzy']
                    details['device_similarity'] = device_similarity
            
            # 工序模糊匹配
            if batch_stage and config.stage:
                stage_similarity = self.fuzzy_match(batch_stage, config.stage)
                if stage_similarity >= self.similarity_threshold:
                    score += stage_similarity * self.weight_config['stage_fuzzy']
                    details['stage_similarity'] = stage_similarity
            
            # 封装型号模糊匹配
            if batch_pkg_pn and config.pkg_pn:
                pkg_similarity = self.fuzzy_match(batch_pkg_pn, config.pkg_pn)
                if pkg_similarity >= self.similarity_threshold:
                    score += pkg_similarity * self.weight_config['pkg_pn_fuzzy']
                    details['pkg_pn_similarity'] = pkg_similarity
            
            # 芯片名称模糊匹配
            if batch_chip_id and config.chip_id:
                chip_similarity = self.fuzzy_match(batch_chip_id, config.chip_id)
                if chip_similarity >= self.similarity_threshold:
                    score += chip_similarity * self.weight_config['chip_id_fuzzy']
                    details['chip_id_similarity'] = chip_similarity
            
            if score > best_score:
                best_score = score
                best_match = config
                match_details = details
        
        return best_match, best_score, match_details

class IntelligentMatchStrategy(MatchingStrategy):
    """智能匹配策略 - 结合精确匹配和模糊匹配"""
    
    def __init__(self):
        super().__init__("intelligent")
        self.weight_config = {
            'device_exact': 0.35,
            'device_fuzzy': 0.25,
            'stage_exact': 0.25,
            'stage_fuzzy': 0.15,
            'pkg_pn_match': 0.15,
            'chip_id_match': 0.10,
            'uph_bonus': 0.05,  # UPH数据存在时的加分
            'history_bonus': 0.05  # 历史匹配成功的加分
        }
        self.similarity_threshold = 0.6
    
    def match(self, batch: Dict, priority_configs: List[ProductPriorityConfig]) -> Tuple[Optional[ProductPriorityConfig], float, Dict]:
        """智能匹配逻辑"""
        best_match = None
        best_score = 0.0
        match_details = {}
        
        batch_device = batch.get('DEVICE', '').strip()
        batch_stage = batch.get('STAGE', '').strip()
        batch_pkg_pn = batch.get('PKG_PN', '').strip()
        batch_chip_id = batch.get('CHIP_ID', '').strip()
        
        for config in priority_configs:
            score = 0.0
            details = {}
            
            # 设备名称匹配（精确优先）
            if batch_device and config.device:
                if batch_device == config.device:
                    score += self.weight_config['device_exact']
                    details['device_match'] = 'exact'
                else:
                    device_similarity = self.fuzzy_match(batch_device, config.device)
                    if device_similarity >= self.similarity_threshold:
                        score += device_similarity * self.weight_config['device_fuzzy']
                        details['device_match'] = f'fuzzy_{device_similarity:.2f}'
            
            # 工序匹配（精确优先）
            if batch_stage and config.stage:
                if batch_stage == config.stage:
                    score += self.weight_config['stage_exact']
                    details['stage_match'] = 'exact'
                else:
                    stage_similarity = self.fuzzy_match(batch_stage, config.stage)
                    if stage_similarity >= self.similarity_threshold:
                        score += stage_similarity * self.weight_config['stage_fuzzy']
                        details['stage_match'] = f'fuzzy_{stage_similarity:.2f}'
            
            # 封装型号匹配
            if batch_pkg_pn and config.pkg_pn:
                pkg_similarity = self.fuzzy_match(batch_pkg_pn, config.pkg_pn)
                if pkg_similarity >= 0.8:  # 封装型号要求更高的相似度
                    score += pkg_similarity * self.weight_config['pkg_pn_match']
                    details['pkg_pn_similarity'] = pkg_similarity
            
            # 芯片名称匹配
            if batch_chip_id and config.chip_id:
                chip_similarity = self.fuzzy_match(batch_chip_id, config.chip_id)
                if chip_similarity >= 0.8:  # 芯片名称要求更高的相似度
                    score += chip_similarity * self.weight_config['chip_id_match']
                    details['chip_id_similarity'] = chip_similarity
            
            # UPH数据存在加分
            if config.uph_override and config.uph_override > 0:
                score += self.weight_config['uph_bonus']
                details['has_uph'] = True
            
            # 记录匹配详情
            details['total_score'] = score
            
            if score > best_score:
                best_score = score
                best_match = config
                match_details = details
        
        return best_match, best_score, match_details

class PriorityMatchingService:
    """智能优先级匹配服务"""
    
    def __init__(self):
        self.matching_strategies = {
            'exact': ExactMatchStrategy(),
            'fuzzy': FuzzyMatchStrategy(),
            'intelligent': IntelligentMatchStrategy()
        }
        self.match_cache = {}  # 简单的内存缓存
        self.cache_timeout = 300  # 5分钟缓存过期
    
    def match_batches_to_priority(self, strategy: str = 'intelligent', 
                                 batch_filter: Dict = None,
                                 confidence_threshold: float = 0.5) -> List[MatchResult]:
        """将待排产批次匹配到优先级配置"""
        try:
            logger.info(f"开始批次匹配，策略: {strategy}, 置信度阈值: {confidence_threshold}")
            
            # 获取待排产批次
            batches = self._get_wait_lot_batches(batch_filter)
            logger.info(f"获取到 {len(batches)} 个待排产批次")
            
            # 获取优先级配置
            priority_configs = self._get_priority_configs()
            logger.info(f"获取到 {len(priority_configs)} 个优先级配置")
            
            if not batches:
                logger.warning("没有找到待排产批次")
                return []
            
            if not priority_configs:
                logger.warning("没有找到优先级配置")
                return []
            
            # 执行匹配
            matching_strategy = self.matching_strategies.get(strategy)
            if not matching_strategy:
                raise ValueError(f"不支持的匹配策略: {strategy}")
            
            match_results = []
            matched_count = 0
            
            for batch in batches:
                try:
                    # 检查缓存
                    cache_key = self._generate_cache_key(batch, strategy)
                    cached_result = self._get_from_cache(cache_key)
                    
                    if cached_result:
                        match_results.append(cached_result)
                        if cached_result.config_id:
                            matched_count += 1
                        continue
                    
                    # 执行匹配
                    best_config, confidence_score, match_details = matching_strategy.match(
                        batch, priority_configs
                    )
                    
                    # 创建匹配结果
                    match_result = self._create_match_result(
                        batch, best_config, confidence_score, strategy, 
                        match_details, confidence_threshold
                    )
                    
                    # 缓存结果
                    self._cache_result(cache_key, match_result)
                    
                    match_results.append(match_result)
                    
                    if match_result.config_id:
                        matched_count += 1
                
                except Exception as e:
                    logger.error(f"匹配批次 {batch.get('LOT_ID', 'Unknown')} 时出错: {e}")
                    # 创建失败的匹配结果
                    match_result = MatchResult(
                        batch_id=batch.get('LOT_ID', 'Unknown'),
                        config_id=None,
                        confidence_score=0.0,
                        match_strategy=strategy,
                        match_details={'error': str(e)},
                        priority_level='low',
                        priority_order=999,
                        suggested_actions=['检查数据完整性', '手动配置优先级']
                    )
                    match_results.append(match_result)
            
            logger.info(f"匹配完成: 总批次 {len(batches)}, 成功匹配 {matched_count}, 匹配率 {matched_count/len(batches)*100:.1f}%")
            
            return match_results
            
        except Exception as e:
            logger.error(f"批次匹配失败: {e}")
            raise
    
    def calculate_dynamic_priority(self, batch_info: Dict, priority_config: ProductPriorityConfig = None) -> Dict[str, Any]:
        """动态计算批次优先级"""
        try:
            # 基础优先级
            base_priority = priority_config.priority_level if priority_config else 'medium'
            base_order = priority_config.priority_order if priority_config else 500
            
            # 动态调整因子
            adjustment_factors = {
                'quantity_factor': 1.0,
                'urgency_factor': 1.0,
                'uph_factor': 1.0,
                'customer_factor': 1.0
            }
            
            # 数量因子（数量越大，优先级可能需要调整）
            quantity = batch_info.get('GOOD_QTY', 0)
            if quantity > 10000:
                adjustment_factors['quantity_factor'] = 0.9  # 大批次降低优先级
            elif quantity < 1000:
                adjustment_factors['urgency_factor'] = 1.1  # 小批次提高优先级
            
            # 紧急程度因子（基于计划交期）
            plan_due_date = batch_info.get('PLAN_DUE_DATE')
            if plan_due_date:
                # 这里需要根据实际的日期格式进行处理
                # 假设是时间戳格式
                try:
                    if isinstance(plan_due_date, (int, float)):
                        due_date = datetime.fromtimestamp(plan_due_date)
                        days_to_due = (due_date - datetime.now()).days
                        
                        if days_to_due <= 1:
                            adjustment_factors['urgency_factor'] = 1.5  # 非常紧急
                        elif days_to_due <= 3:
                            adjustment_factors['urgency_factor'] = 1.3  # 紧急
                        elif days_to_due <= 7:
                            adjustment_factors['urgency_factor'] = 1.1  # 较紧急
                except:
                    pass
            
            # UPH因子
            if priority_config and priority_config.uph_override:
                uph = priority_config.uph_override
                if uph > 2000:
                    adjustment_factors['uph_factor'] = 1.2  # 高产能产品优先
                elif uph < 500:
                    adjustment_factors['uph_factor'] = 0.9  # 低产能产品降低优先级
            
            # 计算最终优先级分数
            final_score = base_order
            for factor_name, factor_value in adjustment_factors.items():
                final_score = final_score / factor_value
            
            # 确定最终优先级等级
            if final_score <= 100:
                final_priority = 'high'
                final_order = int(final_score)
            elif final_score <= 500:
                final_priority = 'medium'
                final_order = int(final_score)
            else:
                final_priority = 'low'
                final_order = int(final_score)
            
            return {
                'original_priority': base_priority,
                'original_order': base_order,
                'final_priority': final_priority,
                'final_order': final_order,
                'adjustment_factors': adjustment_factors,
                'calculation_details': {
                    'quantity': quantity,
                    'base_score': base_order,
                    'final_score': final_score
                }
            }
            
        except Exception as e:
            logger.error(f"动态优先级计算失败: {e}")
            return {
                'original_priority': 'medium',
                'original_order': 500,
                'final_priority': 'medium',
                'final_order': 500,
                'adjustment_factors': {},
                'calculation_details': {'error': str(e)}
            }
    
    def get_matching_report(self, match_results: List[MatchResult]) -> Dict[str, Any]:
        """生成匹配结果报告"""
        if not match_results:
            return {
                'total_batches': 0,
                'matched_batches': 0,
                'unmatched_batches': 0,
                'match_rate': 0.0,
                'confidence_distribution': {},
                'strategy_performance': {},
                'suggestions': []
            }
        
        total_batches = len(match_results)
        matched_batches = len([r for r in match_results if r.config_id])
        unmatched_batches = total_batches - matched_batches
        match_rate = matched_batches / total_batches * 100 if total_batches > 0 else 0
        
        # 置信度分布
        confidence_ranges = {
            'high (>0.8)': 0,
            'medium (0.5-0.8)': 0,
            'low (<0.5)': 0
        }
        
        for result in match_results:
            if result.confidence_score > 0.8:
                confidence_ranges['high (>0.8)'] += 1
            elif result.confidence_score >= 0.5:
                confidence_ranges['medium (0.5-0.8)'] += 1
            else:
                confidence_ranges['low (<0.5)'] += 1
        
        # 策略性能统计
        strategy_stats = {}
        for result in match_results:
            strategy = result.match_strategy
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    'total': 0,
                    'matched': 0,
                    'avg_confidence': 0.0
                }
            
            strategy_stats[strategy]['total'] += 1
            if result.config_id:
                strategy_stats[strategy]['matched'] += 1
            strategy_stats[strategy]['avg_confidence'] += result.confidence_score
        
        # 计算平均置信度
        for strategy, stats in strategy_stats.items():
            if stats['total'] > 0:
                stats['avg_confidence'] = stats['avg_confidence'] / stats['total']
                stats['match_rate'] = stats['matched'] / stats['total'] * 100
        
        # 生成建议
        suggestions = []
        if match_rate < 70:
            suggestions.append("匹配率较低，建议检查优先级配置的完整性")
        if confidence_ranges['low (<0.5)'] > total_batches * 0.3:
            suggestions.append("低置信度匹配较多，建议优化匹配策略或增加配置数据")
        if unmatched_batches > 0:
            suggestions.append(f"有 {unmatched_batches} 个批次未匹配，建议手动配置或调整匹配阈值")
        
        return {
            'total_batches': total_batches,
            'matched_batches': matched_batches,
            'unmatched_batches': unmatched_batches,
            'match_rate': round(match_rate, 2),
            'confidence_distribution': confidence_ranges,
            'strategy_performance': strategy_stats,
            'suggestions': suggestions,
            'generated_at': datetime.now().isoformat()
        }
    
    def _get_wait_lot_batches(self, batch_filter: Dict = None) -> List[Dict]:
        """获取待排产批次数据"""
        try:
            query = db.session.query(ET_WAIT_LOT)
            
            # 应用过滤条件
            if batch_filter:
                if batch_filter.get('device'):
                    query = query.filter(ET_WAIT_LOT.DEVICE.like(f"%{batch_filter['device']}%"))
                if batch_filter.get('stage'):
                    query = query.filter(ET_WAIT_LOT.STAGE.like(f"%{batch_filter['stage']}%"))
                if batch_filter.get('min_qty'):
                    query = query.filter(ET_WAIT_LOT.GOOD_QTY >= batch_filter['min_qty'])
            
            # 只获取有效数据
            query = query.filter(
                and_(
                    ET_WAIT_LOT.DEVICE.isnot(None),
                    ET_WAIT_LOT.DEVICE != '',
                    ET_WAIT_LOT.STAGE.isnot(None),
                    ET_WAIT_LOT.STAGE != ''
                )
            )
            
            batches = query.all()
            
            # 转换为字典格式
            batch_list = []
            for batch in batches:
                batch_dict = {
                    'LOT_ID': batch.LOT_ID,
                    'DEVICE': batch.DEVICE,
                    'STAGE': batch.STAGE,
                    'GOOD_QTY': batch.GOOD_QTY,
                    'PKG_PN': batch.PKG_PN,
                    'CHIP_ID': batch.CHIP_ID,
                    'LOT_TYPE': batch.LOT_TYPE,
                    'WIP_STATE': batch.WIP_STATE,
                    'PROC_STATE': batch.PROC_STATE,
                    'RELEASE_TIME': batch.RELEASE_TIME,
                    'CREATE_TIME': batch.CREATE_TIME
                }
                batch_list.append(batch_dict)
            
            return batch_list
            
        except Exception as e:
            logger.error(f"获取待排产批次失败: {e}")
            return []
    
    def _get_priority_configs(self) -> List[ProductPriorityConfig]:
        """获取优先级配置"""
        try:
            configs = db.session.query(ProductPriorityConfig).filter(
                and_(
                    ProductPriorityConfig.device.isnot(None),
                    ProductPriorityConfig.device != ''
                )
            ).all()
            
            return configs
            
        except Exception as e:
            logger.error(f"获取优先级配置失败: {e}")
            return []
    
    def _create_match_result(self, batch: Dict, config: Optional[ProductPriorityConfig], 
                           confidence_score: float, strategy: str, match_details: Dict,
                           confidence_threshold: float) -> MatchResult:
        """创建匹配结果对象"""
        
        # 生成建议操作
        suggested_actions = []
        
        if not config:
            suggested_actions.extend([
                "未找到匹配的优先级配置",
                "建议手动创建优先级配置",
                "或调整匹配策略和阈值"
            ])
        elif confidence_score < confidence_threshold:
            suggested_actions.extend([
                f"匹配置信度较低 ({confidence_score:.2f})",
                "建议人工确认匹配结果",
                "或优化优先级配置数据"
            ])
        else:
            suggested_actions.append("匹配成功，可直接应用")
        
        return MatchResult(
            batch_id=batch.get('LOT_ID', 'Unknown'),
            config_id=config.id if config else None,
            confidence_score=confidence_score,
            match_strategy=strategy,
            match_details=match_details,
            priority_level=config.priority_level if config else 'low',
            priority_order=config.priority_order if config else 999,
            suggested_actions=suggested_actions
        )
    
    def _generate_cache_key(self, batch: Dict, strategy: str) -> str:
        """生成缓存键"""
        key_parts = [
            batch.get('LOT_ID', ''),
            batch.get('DEVICE', ''),
            batch.get('STAGE', ''),
            strategy
        ]
        return '_'.join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[MatchResult]:
        """从缓存获取结果"""
        if cache_key in self.match_cache:
            cached_data, timestamp = self.match_cache[cache_key]
            if datetime.now().timestamp() - timestamp < self.cache_timeout:
                return cached_data
            else:
                # 缓存过期，删除
                del self.match_cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: MatchResult):
        """缓存匹配结果"""
        self.match_cache[cache_key] = (result, datetime.now().timestamp())
        
        # 简单的缓存清理（保持缓存大小在合理范围内）
        if len(self.match_cache) > 1000:
            # 删除最旧的100个缓存项
            sorted_items = sorted(
                self.match_cache.items(), 
                key=lambda x: x[1][1]
            )
            for key, _ in sorted_items[:100]:
                del self.match_cache[key]
    
    def clear_cache(self):
        """清空缓存"""
        self.match_cache.clear()
        logger.info("匹配缓存已清空")

# 全局服务实例
priority_matching_service = PriorityMatchingService() 