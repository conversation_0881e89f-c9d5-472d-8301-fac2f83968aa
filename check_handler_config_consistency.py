#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HANDLER_CONFIG 一致性检查工具
检查已排产批次的 HANDLER_CONFIG 与 et_recipe_file 表中的配置是否一致
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('handler_config_check.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('HandlerConfigCheck')

def analyze_handler_config_consistency():
    """分析 HANDLER_CONFIG 配置一致性"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        from app.services.data_source_manager import DataSourceManager
        
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 初始化数据管理器
            data_manager = DataSourceManager()
            
            # 1. 获取已排产批次数据
            logger.info("📊 获取已排产批次数据...")
            scheduled_lots_result = data_manager.get_table_data('lotprioritydone')
            scheduled_lots = scheduled_lots_result.get('data', []) if scheduled_lots_result.get('success') else []
            
            # 2. 获取 et_recipe_file 数据
            logger.info("📁 获取 et_recipe_file 配置数据...")
            recipe_result = data_manager.get_table_data('et_recipe_file')
            recipe_data = recipe_result.get('data', []) if recipe_result.get('success') else []
            
            logger.info(f"📊 已排产批次总数: {len(scheduled_lots)}")
            logger.info(f"📁 配方文件记录总数: {len(recipe_data)}")
            
            # 3. 建立 recipe_file 的索引
            recipe_index = defaultdict(list)
            for recipe in recipe_data:
                device = (recipe.get('DEVICE') or '').strip()
                stage = (recipe.get('STAGE') or '').strip()
                pkg_pn = (recipe.get('PKG_PN') or '').strip()
                approval_state = (recipe.get('APPROVAL_STATE') or '').strip()
                
                if device and stage and approval_state == 'Released':
                    key = f"{device}|{stage}"
                    recipe_index[key].append(recipe)
                    
                    # 也建立包含PKG_PN的索引
                    if pkg_pn:
                        key_with_pkg = f"{device}|{stage}|{pkg_pn}"
                        recipe_index[key_with_pkg].append(recipe)
            
            logger.info(f"📁 有效配方索引数量: {len(recipe_index)}")
            
            # 4. 分析一致性
            inconsistencies = []
            checked_lots = 0
            consistent_lots = 0
            
            for lot in scheduled_lots:
                try:
                    lot_id = lot.get('LOT_ID', '')
                    device = (lot.get('DEVICE') or '').strip()
                    stage = (lot.get('STAGE') or '').strip()
                    pkg_pn = (lot.get('PKG_PN') or '').strip()
                    handler_id = lot.get('HANDLER_ID', '')
                    
                    if not device or not stage:
                        continue
                        
                    checked_lots += 1
                    
                    # 查找对应的 recipe 配置
                    # 首先尝试精确匹配（包含PKG_PN）
                    key_with_pkg = f"{device}|{stage}|{pkg_pn}" if pkg_pn else None
                    key_basic = f"{device}|{stage}"
                    
                    matching_recipes = []
                    if key_with_pkg and key_with_pkg in recipe_index:
                        matching_recipes = recipe_index[key_with_pkg]
                    elif key_basic in recipe_index:
                        matching_recipes = recipe_index[key_basic]
                    
                    if not matching_recipes:
                        inconsistencies.append({
                            'lot_id': lot_id,
                            'device': device,
                            'stage': stage,
                            'pkg_pn': pkg_pn,
                            'scheduled_handler_id': handler_id,
                            'recipe_handler_configs': [],
                            'issue': 'NO_RECIPE_FOUND',
                            'description': f'在 et_recipe_file 中未找到 {device}-{stage} 的匹配记录'
                        })
                        continue
                    
                    # 收集所有匹配的 HANDLER_CONFIG
                    recipe_handler_configs = []
                    for recipe in matching_recipes:
                        handler_config = (recipe.get('HANDLER_CONFIG') or '').strip()
                        if handler_config:
                            recipe_handler_configs.append(handler_config)
                    
                    recipe_handler_configs = list(set(recipe_handler_configs))  # 去重
                    
                    # 检查一致性
                    is_consistent = False
                    if recipe_handler_configs:
                        # 这里需要分析 HANDLER_ID 和 HANDLER_CONFIG 的关系
                        # 因为可能是通过设备ID推断的，或者有特定的映射规则
                        
                        # 简单检查：HANDLER_ID是否包含在HANDLER_CONFIG中，或者完全匹配
                        handler_id_str = str(handler_id).strip().upper()
                        for config in recipe_handler_configs:
                            config_upper = config.upper()
                            if (handler_id_str == config_upper or 
                                handler_id_str in config_upper or 
                                config_upper in handler_id_str):
                                is_consistent = True
                                break
                    
                    if is_consistent:
                        consistent_lots += 1
                    else:
                        inconsistencies.append({
                            'lot_id': lot_id,
                            'device': device,
                            'stage': stage,
                            'pkg_pn': pkg_pn,
                            'scheduled_handler_id': handler_id,
                            'recipe_handler_configs': recipe_handler_configs,
                            'issue': 'CONFIG_MISMATCH',
                            'description': f'HANDLER_ID "{handler_id}" 与 recipe 中的 HANDLER_CONFIG {recipe_handler_configs} 不匹配'
                        })
                
                except Exception as e:
                    logger.error(f"分析批次 {lot.get('LOT_ID', 'Unknown')} 时出错: {e}")
                    continue
            
            # 5. 生成报告
            logger.info("=" * 60)
            logger.info("🔍 HANDLER_CONFIG 一致性检查报告")
            logger.info("=" * 60)
            logger.info(f"📊 总检查批次数: {checked_lots}")
            logger.info(f"✅ 配置一致批次: {consistent_lots}")
            logger.info(f"❌ 配置不一致批次: {len(inconsistencies)}")
            
            if inconsistencies:
                logger.info("\n🚨 发现的配置不一致问题:")
                
                # 按问题类型分组
                by_issue_type = defaultdict(list)
                for issue in inconsistencies:
                    by_issue_type[issue['issue']].append(issue)
                
                for issue_type, issues in by_issue_type.items():
                    logger.info(f"\n📋 {issue_type} ({len(issues)} 个批次):")
                    for i, issue in enumerate(issues[:10]):  # 只显示前10个
                        logger.info(f"  {i+1}. LOT_ID: {issue['lot_id']}")
                        logger.info(f"     设备-工序: {issue['device']}-{issue['stage']}")
                        logger.info(f"     已排产 HANDLER_ID: {issue['scheduled_handler_id']}")
                        logger.info(f"     Recipe HANDLER_CONFIG: {issue['recipe_handler_configs']}")
                        logger.info(f"     问题描述: {issue['description']}")
                        logger.info("")
                    
                    if len(issues) > 10:
                        logger.info(f"  ... 还有 {len(issues) - 10} 个类似问题")
            
            # 6. 分析可能的原因
            logger.info("\n🔍 可能的问题原因分析:")
            logger.info("1. 排产时未严格按照 et_recipe_file 表的 HANDLER_CONFIG 进行匹配")
            logger.info("2. 设备上机匹配逻辑中存在兜底或推断机制，未使用正确的配置")
            logger.info("3. et_recipe_file 表中的数据不完整或者 APPROVAL_STATE 状态问题")
            logger.info("4. HANDLER_ID 和 HANDLER_CONFIG 之间的映射关系不明确")
            
            # 7. 返回结果
            return {
                'total_checked': checked_lots,
                'consistent_count': consistent_lots,
                'inconsistent_count': len(inconsistencies),
                'inconsistencies': inconsistencies,
                'success': True
            }
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {'success': False, 'error': str(e)}

def main():
    """入口函数"""
    logger.info("🚀 开始 HANDLER_CONFIG 一致性检查...")
    result = analyze_handler_config_consistency()
    
    if result.get('success'):
        logger.info("🎉 检查完成")
        
        # 输出简要统计
        print("\n" + "="*50)
        print("📊 检查结果统计")
        print("="*50)
        print(f"总检查批次: {result['total_checked']}")
        print(f"配置一致: {result['consistent_count']}")
        print(f"配置不一致: {result['inconsistent_count']}")
        
        if result['inconsistent_count'] > 0:
            consistency_rate = (result['consistent_count'] / result['total_checked']) * 100
            print(f"一致性比例: {consistency_rate:.1f}%")
            print("\n⚠️  发现配置不一致问题，详细信息请查看日志文件")
        else:
            print("✅ 所有批次的配置都是一致的")
    else:
        print("❌ 检查失败")

if __name__ == "__main__":
    main()