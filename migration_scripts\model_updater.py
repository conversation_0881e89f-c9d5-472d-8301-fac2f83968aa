#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 模型更新器
负责更新模型的数据库绑定配置
"""

import logging
import re
from pathlib import Path

class ModelUpdater:
    """模型更新器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.models_file = Path('app/models.py')
        
        # 需要移除__bind_key__的模型列表
        self.models_to_update = {
            'Settings': 'Settings',
            'DatabaseInfo': 'DatabaseInfo', 
            'SchedulingTasks': 'SchedulingTasks',
            'UserFilterPresets': 'UserFilterPresets',
            'SystemSetting': 'SystemSetting',
            'AISettings': 'AISettings',
            'AlgorithmWeights': 'AlgorithmWeights',
            'SchedulingConfig': 'SchedulingConfig',
            'MigrationLog': 'MigrationLog',
            'SchedulingHistory': 'SchedulingHistory',
            'User': 'User',
            'UserPermission': 'UserPermission',
            'UserActionLog': 'UserActionLog',
            'MenuSetting': 'MenuSetting',
            'EmailConfig': 'EmailConfig',
            'ExcelMapping': 'ExcelMapping',
            'EmailAttachment': 'EmailAttachment',
            'OrderData': 'OrderData',
            'LotTypeClassificationRule': 'LotTypeClassificationRule',
            'SchedulerJob': 'SchedulerJob',
            'SchedulerJobLog': 'SchedulerJobLog',
            'SchedulerConfig': 'SchedulerConfig',
            'DatabaseConfig': 'DatabaseConfig',
            'DatabaseMapping': 'DatabaseMapping'
        }
    
    def update_model_bindings(self, model_names):
        """更新指定模型的绑定配置"""
        try:
            self.logger.info(f"🔧 开始更新模型绑定: {model_names}")
            
            if not self.models_file.exists():
                self.logger.error(f"模型文件不存在: {self.models_file}")
                return False
            
            # 读取模型文件内容
            with open(self.models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            updated_count = 0
            
            # 为每个模型移除__bind_key__
            for model_name in model_names:
                if model_name in self.models_to_update:
                    content, was_updated = self.remove_bind_key_from_model(content, model_name)
                    if was_updated:
                        updated_count += 1
                        self.logger.info(f"✅ 模型 {model_name} 绑定已更新")
                    else:
                        self.logger.info(f"ℹ️ 模型 {model_name} 无需更新")
                else:
                    self.logger.warning(f"未知模型: {model_name}")
            
            # 如果有更新，写回文件
            if content != original_content:
                # 创建备份
                backup_file = self.models_file.with_suffix('.py.backup')
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                self.logger.info(f"📦 已创建模型文件备份: {backup_file}")
                
                # 写入更新后的内容
                with open(self.models_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.logger.info(f"✅ 模型文件已更新，共更新 {updated_count} 个模型")
                return True
            else:
                self.logger.info("ℹ️ 模型文件无需更新")
                return True
                
        except Exception as e:
            self.logger.error(f"更新模型绑定时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def remove_bind_key_from_model(self, content, model_name):
        """从指定模型中移除__bind_key__配置"""
        try:
            # 查找模型类定义
            class_pattern = rf'class\s+{model_name}\s*\([^)]*\):\s*\n'
            class_match = re.search(class_pattern, content)
            
            if not class_match:
                self.logger.warning(f"未找到模型类: {model_name}")
                return content, False
            
            class_start = class_match.end()
            
            # 查找类的结束位置（下一个类定义或文件结尾）
            next_class_pattern = r'\nclass\s+\w+\s*\([^)]*\):\s*\n'
            next_class_match = re.search(next_class_pattern, content[class_start:])
            
            if next_class_match:
                class_end = class_start + next_class_match.start()
            else:
                class_end = len(content)
            
            class_content = content[class_start:class_end]
            
            # 查找并移除__bind_key__行
            bind_key_patterns = [
                r'\s*__bind_key__\s*=\s*[\'"]system[\'"]\s*#[^\n]*\n',  # 带注释
                r'\s*__bind_key__\s*=\s*[\'"]system[\'"]\s*\n',        # 不带注释
                r'\s*__bind_key__\s*=\s*[\'"]system[\'"]\s*',          # 行末无换行
            ]
            
            updated_class_content = class_content
            was_updated = False
            
            for pattern in bind_key_patterns:
                if re.search(pattern, updated_class_content):
                    updated_class_content = re.sub(pattern, '', updated_class_content)
                    was_updated = True
                    break
            
            if was_updated:
                # 重新组装内容
                new_content = content[:class_start] + updated_class_content + content[class_end:]
                return new_content, True
            else:
                return content, False
                
        except Exception as e:
            self.logger.error(f"处理模型 {model_name} 时发生错误: {e}")
            return content, False
    
    def update_all_models(self):
        """更新所有需要更新的模型"""
        try:
            self.logger.info("🔧 开始更新所有模型绑定")
            
            all_model_names = list(self.models_to_update.keys())
            return self.update_model_bindings(all_model_names)
            
        except Exception as e:
            self.logger.error(f"更新所有模型时发生错误: {e}")
            return False
    
    def verify_model_updates(self, model_names):
        """验证模型更新结果"""
        try:
            self.logger.info(f"🔍 验证模型更新结果: {model_names}")
            
            if not self.models_file.exists():
                self.logger.error(f"模型文件不存在: {self.models_file}")
                return False
            
            with open(self.models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            verification_passed = True
            
            for model_name in model_names:
                if model_name in self.models_to_update:
                    has_bind_key = self.check_model_has_bind_key(content, model_name)
                    if has_bind_key:
                        self.logger.error(f"❌ 模型 {model_name} 仍有__bind_key__配置")
                        verification_passed = False
                    else:
                        self.logger.info(f"✅ 模型 {model_name} 验证通过")
            
            if verification_passed:
                self.logger.info("✅ 所有模型验证通过")
            else:
                self.logger.error("❌ 部分模型验证失败")
            
            return verification_passed
            
        except Exception as e:
            self.logger.error(f"验证模型更新时发生错误: {e}")
            return False
    
    def check_model_has_bind_key(self, content, model_name):
        """检查模型是否仍有__bind_key__配置"""
        try:
            # 查找模型类定义
            class_pattern = rf'class\s+{model_name}\s*\([^)]*\):\s*\n'
            class_match = re.search(class_pattern, content)
            
            if not class_match:
                return False
            
            class_start = class_match.end()
            
            # 查找类的结束位置
            next_class_pattern = r'\nclass\s+\w+\s*\([^)]*\):\s*\n'
            next_class_match = re.search(next_class_pattern, content[class_start:])
            
            if next_class_match:
                class_end = class_start + next_class_match.start()
            else:
                class_end = len(content)
            
            class_content = content[class_start:class_end]
            
            # 检查是否包含__bind_key__
            bind_key_pattern = r'__bind_key__\s*=\s*[\'"]system[\'"]'
            return bool(re.search(bind_key_pattern, class_content))
            
        except Exception as e:
            self.logger.error(f"检查模型 {model_name} 时发生错误: {e}")
            return False
    
    def restore_models_backup(self):
        """恢复模型文件备份"""
        try:
            backup_file = self.models_file.with_suffix('.py.backup')
            
            if not backup_file.exists():
                self.logger.error("备份文件不存在，无法恢复")
                return False
            
            # 读取备份内容
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_content = f.read()
            
            # 恢复到原文件
            with open(self.models_file, 'w', encoding='utf-8') as f:
                f.write(backup_content)
            
            self.logger.info(f"✅ 模型文件已从备份恢复: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复模型文件备份时发生错误: {e}")
            return False
    
    def get_models_status(self):
        """获取所有模型的当前状态"""
        try:
            if not self.models_file.exists():
                return {}
            
            with open(self.models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            status = {}
            for model_name in self.models_to_update.keys():
                has_bind_key = self.check_model_has_bind_key(content, model_name)
                status[model_name] = {
                    'has_bind_key': has_bind_key,
                    'status': 'needs_update' if has_bind_key else 'updated'
                }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取模型状态时发生错误: {e}")
            return {} 