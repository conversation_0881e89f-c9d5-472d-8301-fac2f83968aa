{% extends "base.html" %}

{% set page_title = "订单处理中心" %}

{% block title %}{{ page_title }} - APS智能调度平台{% endblock %}

{% block extra_css %}
<style>
/* 自动订单处理页面样式 */
.auto-processing-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    min-height: 100vh;
    padding: 1rem;
}

.processing-dashboard {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 1.5rem;
}

.dashboard-header {
    background: linear-gradient(135deg, var(--theme-color), #d73027);
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.dashboard-body {
    padding: 1.5rem;
}

.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--theme-color);
}

.status-card-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.status-card-label {
    color: var(--gray-600);
    font-size: 0.9rem;
}

.auto-controls {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.control-section {
    margin-bottom: 2rem;
}

.control-section:last-child {
    margin-bottom: 0;
}

.control-section h5 {
    color: var(--theme-color);
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--theme-color);
    padding-bottom: 0.5rem;
}

.processing-log {
    background: #1a1a1a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    padding: 1rem;
    border-radius: 0.375rem;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

.log-entry {
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.log-timestamp {
    color: #ffff00;
}

.log-info {
    color: #00ff00;
}

.log-warning {
    color: #ff8800;
}

.log-error {
    color: #ff0000;
}

.btn-auto-start {
    background: linear-gradient(135deg, var(--success-color), #28a745);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-auto-start:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-auto-stop {
    background: linear-gradient(135deg, var(--danger-color), #dc3545);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-auto-stop:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.schedule-config {
    background: #f8f9fa;
    border: 1px solid var(--gray-200);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.form-control:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
}
</style>
{% endblock %}

{% block content %}
<div class="auto-processing-container">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0" style="color: var(--theme-color); font-weight: 600;">
                <i class="fas fa-rocket me-2"></i>{{ page_title }}
            </h1>
            <p class="text-muted mb-0 mt-1">智能化订单自动处理与监控中心</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="exportReport()">
                <i class="fas fa-download me-1"></i>导出报告
            </button>
        </div>
    </div>

    <!-- 状态仪表板 -->
    <div class="processing-dashboard">
        <div class="dashboard-header">
            <h4 class="mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>处理状态监控
            </h4>
            <small class="opacity-75">实时监控自动订单处理状态</small>
        </div>
        <div class="dashboard-body">
            <div class="status-cards">
                <div class="status-card">
                    <div class="status-card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="status-card-value" id="pending-orders">0</div>
                    <div class="status-card-label">待处理订单</div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="status-card-value" id="processing-orders">0</div>
                    <div class="status-card-label">处理中订单</div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-card-value" id="completed-orders">0</div>
                    <div class="status-card-label">已完成订单</div>
                </div>
                <div class="status-card">
                    <div class="status-card-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="status-card-value" id="error-orders">0</div>
                    <div class="status-card-label">错误订单</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自动处理控制 -->
    <div class="row">
        <div class="col-md-6">
            <div class="auto-controls">
                <div class="control-section">
                    <h5><i class="fas fa-play-circle me-2"></i>自动处理控制</h5>
                    <div class="d-flex gap-3 align-items-center">
                        <button class="btn-auto-start" id="start-auto-processing">
                            <i class="fas fa-play me-2"></i>启动自动处理
                        </button>
                        <button class="btn-auto-stop" id="stop-auto-processing" style="display: none;">
                            <i class="fas fa-stop me-2"></i>停止处理
                        </button>
                        <div class="ms-auto">
                            <span class="badge bg-secondary" id="processing-status">已停止</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h5><i class="fas fa-calendar-alt me-2"></i>调度配置</h5>
                    <div class="schedule-config">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">处理间隔（分钟）</label>
                                <input type="number" class="form-control" id="processing-interval" value="5" min="1" max="60">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">批处理大小</label>
                                <input type="number" class="form-control" id="batch-size" value="10" min="1" max="100">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">开始时间</label>
                                <input type="time" class="form-control" id="start-time" value="08:00">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">结束时间</label>
                                <input type="time" class="form-control" id="end-time" value="18:00">
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="saveScheduleConfig()">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="auto-controls">
                <div class="control-section">
                    <h5><i class="fas fa-list-alt me-2"></i>处理日志</h5>
                    <div class="processing-log" id="processing-log">
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-12-22 21:45:00]</span>
                            <span class="log-info">系统初始化完成</span>
                        </div>
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-12-22 21:45:01]</span>
                            <span class="log-info">等待自动处理启动...</span>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearLog()">
                            <i class="fas fa-trash me-1"></i>清空日志
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="downloadLog()">
                            <i class="fas fa-download me-1"></i>下载日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 统一导出系统依赖 -->
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified-export.js') }}"></script>

<script>
$(document).ready(function() {
    // 初始化页面
    initializeAutoProcessing();
    
    // 定时刷新状态
    setInterval(updateStatus, 5000);
});

let isProcessing = false;
let processingInterval;

function initializeAutoProcessing() {
    // 加载配置
    loadScheduleConfig();
    
    // 绑定事件
    $('#start-auto-processing').click(startAutoProcessing);
    $('#stop-auto-processing').click(stopAutoProcessing);
    
    // 初始化状态
    updateStatus();
}

function startAutoProcessing() {
    if (isProcessing) return;
    
    isProcessing = true;
    $('#start-auto-processing').hide();
    $('#stop-auto-processing').show();
    $('#processing-status').removeClass('bg-secondary').addClass('bg-success').text('运行中');
    
    addLogEntry('启动自动订单处理', 'info');
    
    // 开始处理循环
    const interval = parseInt($('#processing-interval').val()) * 60000; // 转换为毫秒
    processingInterval = setInterval(processOrders, interval);
    
    // 立即执行一次
    processOrders();
}

function stopAutoProcessing() {
    if (!isProcessing) return;
    
    isProcessing = false;
    $('#start-auto-processing').show();
    $('#stop-auto-processing').hide();
    $('#processing-status').removeClass('bg-success').addClass('bg-secondary').text('已停止');
    
    if (processingInterval) {
        clearInterval(processingInterval);
    }
    
    addLogEntry('停止自动订单处理', 'warning');
}

function processOrders() {
    addLogEntry('开始处理订单批次...', 'info');
    
    // 模拟订单处理
    $.ajax({
        url: '/api/orders/auto-process',
        method: 'POST',
        data: {
            batch_size: $('#batch-size').val()
        },
        success: function(response) {
            if (response.success) {
                addLogEntry(`成功处理 ${response.processed_count} 个订单`, 'info');
                updateStatus();
            } else {
                addLogEntry(`处理失败: ${response.message}`, 'error');
            }
        },
        error: function(xhr, status, error) {
            addLogEntry(`处理错误: ${error}`, 'error');
        }
    });
}

function updateStatus() {
    // 获取处理状态
    $.ajax({
        url: '/api/orders/processing-status',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                $('#pending-orders').text(response.data.pending || 0);
                $('#processing-orders').text(response.data.processing || 0);
                $('#completed-orders').text(response.data.completed || 0);
                $('#error-orders').text(response.data.errors || 0);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取状态失败:', error);
        }
    });
}

function addLogEntry(message, type = 'info') {
    const timestamp = new Date().toLocaleString('zh-CN');
    const logClass = `log-${type}`;
    const logEntry = `
        <div class="log-entry">
            <span class="log-timestamp">[${timestamp}]</span>
            <span class="${logClass}">${message}</span>
        </div>
    `;
    
    const logContainer = $('#processing-log');
    logContainer.append(logEntry);
    
    // 自动滚动到底部
    logContainer.scrollTop(logContainer[0].scrollHeight);
    
    // 限制日志条数
    const entries = logContainer.find('.log-entry');
    if (entries.length > 100) {
        entries.first().remove();
    }
}

function saveScheduleConfig() {
    const config = {
        processing_interval: $('#processing-interval').val(),
        batch_size: $('#batch-size').val(),
        start_time: $('#start-time').val(),
        end_time: $('#end-time').val()
    };
    
    $.ajax({
        url: '/api/orders/save-schedule-config',
        method: 'POST',
        data: config,
        success: function(response) {
            if (response.success) {
                showAlert('配置保存成功', 'success');
                addLogEntry('调度配置已更新', 'info');
            } else {
                showAlert('配置保存失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('配置保存失败: ' + error, 'danger');
        }
    });
}

function loadScheduleConfig() {
    $.ajax({
        url: '/api/orders/get-schedule-config',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                $('#processing-interval').val(response.data.processing_interval || 5);
                $('#batch-size').val(response.data.batch_size || 10);
                $('#start-time').val(response.data.start_time || '08:00');
                $('#end-time').val(response.data.end_time || '18:00');
            }
        },
        error: function(xhr, status, error) {
            console.error('加载配置失败:', error);
        }
    });
}

function refreshDashboard() {
    updateStatus();
    addLogEntry('仪表板已刷新', 'info');
    showAlert('仪表板已刷新', 'success');
}

// 高效前端导出 - 替代慢速的服务端导出
function exportReport() {
    addLogEntry('开始导出处理报告', 'info');
    
    // 从API获取数据并使用前端导出
    unifiedExportFromAPI(
        '/api/orders/processing-data', // 假设有这个API提供JSON数据
        EXPORT_CONFIGS.orders.columns,
        '订单处理报告',
        '处理报告'
    );
}

// 备用：如果没有JSON API，回退到原方式
function exportReportLegacy() {
    window.open('/api/orders/export-processing-report', '_blank');
    addLogEntry('导出处理报告（传统方式）', 'info');
}

function clearLog() {
    $('#processing-log').empty();
    addLogEntry('日志已清空', 'info');
}

function downloadLog() {
    const logContent = $('#processing-log').text();
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `订单处理日志_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示提示
    const alertContainer = $('.auto-processing-container').first();
    alertContainer.prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %} 