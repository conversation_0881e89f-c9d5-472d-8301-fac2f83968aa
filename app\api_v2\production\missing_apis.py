#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缺失的生产API端点
补充日志中发现的404端点
"""

from flask import Blueprint, jsonify, request
from app.services.data_source_manager import DataSourceManager
import time
import json

missing_production_api = Blueprint('missing_production_api', __name__)

@missing_production_api.route('/api/production/history-times', methods=['GET'])
def get_history_times():
    """获取历史时间数据"""
    try:
        # 返回模拟数据
        return jsonify({
            'status': 'success',
            'data': {
                'times': ['08:00', '12:00', '16:00', '20:00'],
                'message': '历史时间数据'
            }
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@missing_production_api.route('/api/production/table-data', methods=['GET'])
def get_table_data():
    """获取表格数据"""
    try:
        table = request.args.get('table', '')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        
        # 表名映射
        table_mapping = {
            'Lot_WIP': 'et_wait_lot',
            'HANDLER_WIP': 'eqp_status',
            'EQP_STATUS': 'eqp_status'
        }
        
        actual_table = table_mapping.get(table, table.lower())
        
        # 使用数据源管理器获取数据
        manager = DataSourceManager()
        data = manager.get_table_data(actual_table, page=page, per_page=size)
        
        return jsonify({
            'status': 'success',
            'data': data.get('data', []),
            'total': data.get('total', 0),
            'page': page,
            'size': size
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@missing_production_api.route('/api/production/chart-data', methods=['GET'])
def get_chart_data():
    """获取图表数据"""
    try:
        table = request.args.get('table', '')
        
        # 表名映射
        table_mapping = {
            'Lot_WIP': 'et_wait_lot',
            'HANDLER_WIP': 'eqp_status',
            'EQP_STATUS': 'eqp_status'
        }
        
        actual_table = table_mapping.get(table, table.lower())
        
        # 使用数据源管理器获取数据
        manager = DataSourceManager()
        data = manager.get_table_data(actual_table, page=1, per_page=100)
        
        # 生成图表数据
        chart_data = {
            'labels': [],
            'datasets': [{
                'label': f'{table} 数据',
                'data': [],
                'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                'borderColor': 'rgba(54, 162, 235, 1)',
                'borderWidth': 1
            }]
        }
        
        # 根据表类型生成不同的图表数据
        if 'wip' in actual_table.lower():
            # WIP数据按状态统计
            status_count = {}
            for item in data.get('data', []):
                status = item.get('status', 'Unknown')
                status_count[status] = status_count.get(status, 0) + 1
            
            chart_data['labels'] = list(status_count.keys())
            chart_data['datasets'][0]['data'] = list(status_count.values())
            
        elif 'eqp' in actual_table.lower():
            # 设备数据按状态统计
            status_count = {}
            for item in data.get('data', []):
                status = item.get('eqp_status', 'Unknown')
                status_count[status] = status_count.get(status, 0) + 1
            
            chart_data['labels'] = list(status_count.keys())
            chart_data['datasets'][0]['data'] = list(status_count.values())
        
        return jsonify({
            'status': 'success',
            'data': chart_data
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@missing_production_api.route('/api/user-filter-presets', methods=['GET'])
def get_user_filter_presets():
    """获取用户过滤器预设"""
    try:
        page_type = request.args.get('page_type', '')
        
        # 返回空的预设数据
        return jsonify({
            'status': 'success',
            'data': [],
            'message': f'暂无 {page_type} 页面的过滤器预设'
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@missing_production_api.route('/api/user-filter-presets', methods=['POST'])
def save_user_filter_preset():
    """保存用户过滤器预设"""
    try:
        data = request.get_json()
        page_type = data.get('page_type', '')
        preset_name = data.get('preset_name', '')
        filters = data.get('filters', {})
        
        # 模拟保存成功
        return jsonify({
            'status': 'success',
            'message': f'过滤器预设 "{preset_name}" 已保存',
            'data': {
                'id': 1,
                'page_type': page_type,
                'preset_name': preset_name,
                'filters': filters
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def update_user_filter_preset(preset_id):
    """更新用户过滤器预设"""
    try:
        data = request.get_json()
        preset_name = data.get('preset_name', '')
        filters = data.get('filters', {})
        
        # 模拟更新成功
        return jsonify({
            'status': 'success',
            'message': f'过滤器预设 ID:{preset_id} 已更新',
            'data': {
                'id': preset_id,
                'preset_name': preset_name,
                'filters': filters
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def delete_user_filter_preset(preset_id):
    """删除用户过滤器预设"""
    try:
        # 模拟删除成功
        return jsonify({
            'status': 'success',
            'message': f'过滤器预设 ID:{preset_id} 已删除'
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def start_auto_schedule():
    """启动自动排产"""
    try:
        # 模拟启动自动排产
        schedule_id = f'AUTO_{int(time.time())}'
        
        return jsonify({
            'status': 'success',
            'message': '自动排产已启动',
            'data': {
                'schedule_id': schedule_id,
                'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'running',
                'estimated_duration': '5-10分钟'
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def save_schedule_history():
    """保存排产历史记录"""
    try:
        data = request.get_json()
        schedule_id = data.get('schedule_id', f'HIST_{int(time.time())}')
        
        # 模拟保存历史记录
        return jsonify({
            'status': 'success',
            'message': '排产历史记录已保存',
            'data': {
                'history_id': schedule_id,
                'saved_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@missing_production_api.route('/api/production/schedule-history', methods=['GET'])
def get_schedule_history():
    """获取排产历史记录"""
    try:
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        
        # 返回模拟的排产历史数据
        history_data = []
        for i in range(size):
            history_data.append({
                'id': i + 1,
                'schedule_date': f'2025-06-{20 + i % 10:02d}',
                'lot_count': 50 + i * 5,
                'equipment_count': 10 + i % 5,
                'efficiency': 85 + i % 15,
                'status': 'completed' if i % 3 == 0 else 'running',
                'created_at': f'2025-06-{20 + i % 10:02d} 08:00:00'
            })
        
        return jsonify({
            'status': 'success',
            'data': history_data,
            'total': 100,
            'page': page,
            'size': size
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def export_schedule():
    """导出排产结果"""
    try:
        data = request.get_json()
        schedule_id = data.get('schedule_id', '')
        export_format = data.get('format', 'excel')
        
        # 模拟导出成功
        export_filename = f'schedule_{schedule_id}_{int(time.time())}.{export_format}'
        
        return jsonify({
            'status': 'success',
            'message': '排产结果导出成功',
            'data': {
                'filename': export_filename,
                'download_url': f'/exports/{export_filename}',
                'file_size': '2.5MB',
                'export_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500 