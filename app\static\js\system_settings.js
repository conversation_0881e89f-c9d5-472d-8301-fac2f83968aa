/**
 * 系统设置页面JavaScript
 */

class SystemSettings {
    constructor() {
        this.apiClient = new APIClient();
        this.initializeUI();
        this.loadSettings();
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        // 绑定保存按钮
        const saveTrainingBtn = document.getElementById('save-training-ai-btn');
        const saveProgressBtn = document.getElementById('save-progress-ai-btn');

        if (saveTrainingBtn) {
            saveTrainingBtn.addEventListener('click', () => this.saveSettings());
        }

        if (saveProgressBtn) {
            saveProgressBtn.addEventListener('click', () => this.saveSettings());
        }

        // 绑定测试连接按钮
        const testTrainingBtn = document.getElementById('test-training-connection');
        const testProgressBtn = document.getElementById('test-progress-connection');

        if (testTrainingBtn) {
            testTrainingBtn.addEventListener('click', () => this.testConnection('training_ai'));
        }

        if (testProgressBtn) {
            testProgressBtn.addEventListener('click', () => this.testConnection('progress_ai'));
        }

        // 绑定启用/禁用开关
        const trainingSwitch = document.getElementById('training-ai-enabled');
        const progressSwitch = document.getElementById('progress-ai-enabled');

        if (trainingSwitch) {
            trainingSwitch.addEventListener('change', (e) => this.toggleFields('training', e.target.checked));
        }

        if (progressSwitch) {
            progressSwitch.addEventListener('change', (e) => this.toggleFields('progress', e.target.checked));
        }
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const response = await this.apiClient.get('/api/v2/system/ai-settings');
            
            if (response.success) {
                this.populateForm(response.data);
            } else {
                this.showToast('加载设置失败: ' + response.error, 'error');
            }
        } catch (error) {
            console.error('加载设置失败:', error);
            this.showToast('加载设置失败', 'error');
        }
    }

    /**
     * 填充表单
     */
    populateForm(data) {
        // 培训AI设置
        const trainingConfig = data.training_ai || {};
        this.setFieldValue('training-ai-enabled', trainingConfig.enabled || false);
        this.setFieldValue('training-ai-server', trainingConfig.server_url || '');
        this.setFieldValue('training-ai-key', trainingConfig.api_key || '');
        this.setFieldValue('training-ai-model', trainingConfig.app_id || '');

        // 进度查询AI设置
        const progressConfig = data.progress_ai || {};
        this.setFieldValue('progress-ai-enabled', progressConfig.enabled || false);
        this.setFieldValue('progress-ai-server', progressConfig.server_url || '');
        this.setFieldValue('progress-ai-key', progressConfig.api_key || '');
        this.setFieldValue('progress-ai-model', progressConfig.app_id || '');

        // 切换字段状态
        this.toggleFields('training', trainingConfig.enabled || false);
        this.toggleFields('progress', progressConfig.enabled || false);
    }

    /**
     * 设置字段值
     */
    setFieldValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = value;
            } else {
                element.value = value;
            }
        }
    }

    /**
     * 获取字段值
     */
    getFieldValue(id) {
        const element = document.getElementById(id);
        if (element) {
            return element.type === 'checkbox' ? element.checked : element.value.trim();
        }
        return '';
    }

    /**
     * 切换字段启用状态
     */
    toggleFields(type, enabled) {
        const fields = [
            `${type}-ai-server`,
            `${type}-ai-key`,
            `${type}-ai-model`
        ];

        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.disabled = !enabled;
                element.parentElement.style.opacity = enabled ? '1' : '0.5';
            }
        });

        // 测试按钮
        const testBtn = document.getElementById(`test-${type}-connection`);
        if (testBtn) {
            testBtn.disabled = !enabled;
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            // 收集表单数据
            const settings = {
                training_ai: {
                    enabled: this.getFieldValue('training-ai-enabled'),
                    server_url: this.getFieldValue('training-ai-server'),
                    api_key: this.getFieldValue('training-ai-key'),
                    app_id: this.getFieldValue('training-ai-model')
                },
                progress_ai: {
                    enabled: this.getFieldValue('progress-ai-enabled'),
                    server_url: this.getFieldValue('progress-ai-server'),
                    api_key: this.getFieldValue('progress-ai-key'),
                    app_id: this.getFieldValue('progress-ai-model')
                }
            };

            // 发送保存请求
            const response = await this.apiClient.post('/api/v2/system/ai-settings', settings);

            if (response.success) {
                this.showToast('设置保存成功', 'success');
            } else {
                this.showToast('保存设置失败: ' + response.error, 'error');
            }

        } catch (error) {
            console.error('保存设置失败:', error);
            this.showToast('保存设置失败', 'error');
        }
    }

    /**
     * 测试连接
     */
    async testConnection(type) {
        const serverField = `${type.replace('_ai', '')}-ai-server`;
        const keyField = `${type.replace('_ai', '')}-ai-key`;
        const modelField = `${type.replace('_ai', '')}-ai-model`;

        const server_url = this.getFieldValue(serverField);
        const api_key = this.getFieldValue(keyField);
        const app_id = this.getFieldValue(modelField);

        if (!server_url || !api_key || !app_id) {
            this.showToast('请填写完整的连接信息', 'warning');
            return;
        }

        // 显示测试状态
        const testBtn = document.getElementById(`test-${type.replace('_ai', '')}-connection`);
        const originalText = testBtn.textContent;
        testBtn.textContent = '测试中...';
        testBtn.disabled = true;

        try {
            const response = await this.apiClient.post('/api/v2/system/test-ai', {
                type: type,
                server_url: server_url,
                api_key: api_key,
                app_id: app_id
            });

            if (response.success) {
                this.showToast('连接测试成功', 'success');
                this.updateConnectionStatus(type, true, response.server_url);
            } else {
                this.showToast('连接测试失败: ' + response.error, 'error');
                this.updateConnectionStatus(type, false, response.details || '');
            }

        } catch (error) {
            console.error('测试连接失败:', error);
            this.showToast('测试连接失败', 'error');
            this.updateConnectionStatus(type, false, '');
        } finally {
            // 恢复按钮状态
            testBtn.textContent = originalText;
            testBtn.disabled = false;
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(type, success, details) {
        const statusId = `${type.replace('_ai', '')}-connection-status`;
        const statusElement = document.getElementById(statusId);

        if (statusElement) {
            statusElement.innerHTML = '';

            const statusDiv = document.createElement('div');
            statusDiv.className = `alert alert-${success ? 'success' : 'danger'} mt-2`;
            
            if (success) {
                statusDiv.innerHTML = `
                    <i class="fas fa-check-circle"></i> 连接测试成功！
                    ${details ? `<br><small>服务器地址: ${details}</small>` : ''}
                `;
            } else {
                statusDiv.innerHTML = `
                    <i class="fas fa-times-circle"></i> 连接测试失败
                    ${details ? `<br><small>${details}</small>` : ''}
                `;
            }

            statusElement.appendChild(statusDiv);

            // 5秒后自动隐藏
            setTimeout(() => {
                statusElement.innerHTML = '';
            }, 5000);
        }
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// API客户端类
class APIClient {
    constructor() {
        this.baseURL = '';
    }

    async request(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    async get(url) {
        return this.request(url, { method: 'GET' });
    }

    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.systemSettings = new SystemSettings();
}); 