# 数据源管理API路由
from flask import Blueprint, request, jsonify, render_template
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

data_source_bp = Blueprint('data_source', __name__, url_prefix='/admin/data-source')

@data_source_bp.route('/')
def index():
    """数据源管理主页"""
    return render_template('admin/data_source_management.html')

@data_source_bp.route('/api/status')
def get_data_source_status():
    """获取数据源状态"""
    try:
        from app.services.dual_data_source_manager import DualDataSourceManager
        manager = DualDataSourceManager()
        report = manager.generate_data_source_report()
        return jsonify(report)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@data_source_bp.route('/api/switch', methods=['POST'])
def switch_data_source():
    """切换数据源"""
    try:
        data = request.get_json()
        table_name = data.get('table_name')
        target_source = data.get('target_source')  # 'mysql' or 'excel'
        
        if not table_name or not target_source:
            return jsonify({'success': False, 'message': '参数不完整'}), 400
        
        from app.services.dual_data_source_manager import DualDataSourceManager
        manager = DualDataSourceManager()
        success, message = manager.force_switch_data_source(table_name, target_source)
        
        return jsonify({
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@data_source_bp.route('/api/health-check')
def health_check():
    """执行健康检查"""
    try:
        from app.services.dual_data_source_manager import DualDataSourceManager
        manager = DualDataSourceManager()
        results = {}
        
        for table in manager.table_mapping.keys():
            mysql_healthy, mysql_status = manager.check_mysql_data_health(table)
            excel_healthy, excel_status = manager.check_excel_data_health(table)
            
            results[table] = {
                'mysql': {'healthy': mysql_healthy, 'status': mysql_status},
                'excel': {'healthy': excel_healthy, 'status': excel_status}
            }
        
        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
