#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优雅降级管理器 - 在系统故障时提供降级服务

当系统组件发生故障时，自动降级到基础功能，确保核心业务不中断
"""

import logging
import time
import threading
from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Callable, Any
from functools import wraps

logger = logging.getLogger(__name__)

class ServiceLevel(Enum):
    """服务级别枚举"""
    FULL = "full"           # 完整服务
    DEGRADED = "degraded"   # 降级服务
    BASIC = "basic"         # 基础服务
    EMERGENCY = "emergency" # 应急服务

class ComponentStatus(Enum):
    """组件状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    RECOVERING = "recovering"

@dataclass
class ComponentHealth:
    """组件健康状态"""
    name: str
    status: ComponentStatus
    last_check: float
    failure_count: int = 0
    recovery_attempts: int = 0
    error_message: str = ""
    fallback_active: bool = False

@dataclass
class DegradationRule:
    """降级规则"""
    component: str
    required_level: ServiceLevel
    fallback_function: Optional[Callable] = None
    timeout_ms: int = 5000
    retry_attempts: int = 3
    auto_recovery: bool = True

class GracefulDegradationManager:
    """优雅降级管理器"""
    
    def __init__(self):
        self.components: Dict[str, ComponentHealth] = {}
        self.degradation_rules: Dict[str, DegradationRule] = {}
        self.current_service_level = ServiceLevel.FULL
        self.degradation_callbacks: List[Callable] = []
        self.recovery_callbacks: List[Callable] = []
        self.lock = threading.RLock()
        
        logger.info("🛡️ 优雅降级管理器已初始化")
    
    def register_component(self, name: str, rule: DegradationRule):
        """注册组件和降级规则"""
        with self.lock:
            self.components[name] = ComponentHealth(
                name=name,
                status=ComponentStatus.HEALTHY,
                last_check=time.time()
            )
            self.degradation_rules[name] = rule
            logger.info(f"✅ 注册组件: {name} (降级级别: {rule.required_level.value})")
    
    def health_check(self, component_name: str, check_function: Callable) -> bool:
        """执行组件健康检查"""
        if component_name not in self.components:
            return True  # 未注册的组件默认健康
        
        with self.lock:
            component = self.components[component_name]
            rule = self.degradation_rules[component_name]
            
            try:
                # 执行健康检查
                start_time = time.time()
                result = check_function()
                check_duration = (time.time() - start_time) * 1000
                
                if result and check_duration < rule.timeout_ms:
                    # 健康检查通过
                    if component.status != ComponentStatus.HEALTHY:
                        logger.info(f"🔄 组件恢复健康: {component_name}")
                        component.status = ComponentStatus.HEALTHY
                        component.failure_count = 0
                        component.error_message = ""
                        self._trigger_recovery_callbacks(component_name)
                    
                    component.last_check = time.time()
                    return True
                else:
                    # 健康检查失败
                    component.failure_count += 1
                    component.error_message = f"检查超时 ({check_duration:.1f}ms)" if check_duration >= rule.timeout_ms else "功能检查失败"
                    
                    if component.failure_count >= rule.retry_attempts:
                        self._handle_component_failure(component_name)
                    
                    return False
                    
            except Exception as e:
                # 健康检查异常
                component.failure_count += 1
                component.error_message = str(e)
                
                if component.failure_count >= rule.retry_attempts:
                    self._handle_component_failure(component_name)
                
                logger.warning(f"⚠️ 组件健康检查异常 {component_name}: {e}")
                return False
    
    def _handle_component_failure(self, component_name: str):
        """处理组件故障"""
        component = self.components[component_name]
        rule = self.degradation_rules[component_name]
        
        logger.warning(f"🚨 组件故障: {component_name} ({component.error_message})")
        
        # 更新组件状态
        component.status = ComponentStatus.FAILED
        component.fallback_active = True
        
        # 评估是否需要降级服务级别
        self._evaluate_service_level()
        
        # 触发降级回调
        self._trigger_degradation_callbacks(component_name)
        
        # 安排自动恢复
        if rule.auto_recovery:
            threading.Timer(30.0, self._attempt_recovery, args=[component_name]).start()
    
    def _evaluate_service_level(self):
        """评估当前服务级别"""
        failed_components = []
        degraded_components = []
        
        for name, component in self.components.items():
            if component.status == ComponentStatus.FAILED:
                failed_components.append(name)
            elif component.status == ComponentStatus.DEGRADED:
                degraded_components.append(name)
        
        # 根据故障组件数量和重要性决定服务级别
        critical_components = ['ml_auto_scaler', 'connection_manager']
        
        if any(comp in failed_components for comp in critical_components):
            new_level = ServiceLevel.BASIC
        elif len(failed_components) > 2:
            new_level = ServiceLevel.DEGRADED
        elif len(failed_components) > 0 or len(degraded_components) > 1:
            new_level = ServiceLevel.DEGRADED
        else:
            new_level = ServiceLevel.FULL
        
        if new_level != self.current_service_level:
            logger.warning(f"📊 服务级别变更: {self.current_service_level.value} → {new_level.value}")
            self.current_service_level = new_level
    
    def _attempt_recovery(self, component_name: str):
        """尝试组件恢复"""
        if component_name not in self.components:
            return
        
        component = self.components[component_name]
        component.recovery_attempts += 1
        component.status = ComponentStatus.RECOVERING
        
        logger.info(f"🔄 尝试恢复组件: {component_name} (第{component.recovery_attempts}次)")
        
        # 恢复逻辑将由具体的健康检查触发
        # 这里只是标记状态和记录
    
    def get_fallback_result(self, component_name: str, default_result: Any = None) -> Any:
        """获取组件的降级结果"""
        if component_name not in self.components:
            return default_result
        
        component = self.components[component_name]
        rule = self.degradation_rules[component_name]
        
        if component.status == ComponentStatus.FAILED and rule.fallback_function:
            try:
                logger.info(f"🔧 使用降级功能: {component_name}")
                return rule.fallback_function()
            except Exception as e:
                logger.error(f"❌ 降级功能失败 {component_name}: {e}")
                return default_result
        
        return default_result
    
    def is_component_healthy(self, component_name: str) -> bool:
        """检查组件是否健康"""
        if component_name not in self.components:
            return True
        
        return self.components[component_name].status == ComponentStatus.HEALTHY
    
    def get_service_level(self) -> ServiceLevel:
        """获取当前服务级别"""
        return self.current_service_level
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康状态报告"""
        with self.lock:
            return {
                'service_level': self.current_service_level.value,
                'components': {
                    name: {
                        'status': comp.status.value,
                        'failure_count': comp.failure_count,
                        'last_check': comp.last_check,
                        'error_message': comp.error_message,
                        'fallback_active': comp.fallback_active
                    }
                    for name, comp in self.components.items()
                },
                'health_summary': {
                    'total_components': len(self.components),
                    'healthy_components': sum(1 for c in self.components.values() if c.status == ComponentStatus.HEALTHY),
                    'failed_components': sum(1 for c in self.components.values() if c.status == ComponentStatus.FAILED),
                    'degraded_components': sum(1 for c in self.components.values() if c.status == ComponentStatus.DEGRADED)
                }
            }
    
    def add_degradation_callback(self, callback: Callable[[str], None]):
        """添加降级回调函数"""
        self.degradation_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[str], None]):
        """添加恢复回调函数"""
        self.recovery_callbacks.append(callback)
    
    def _trigger_degradation_callbacks(self, component_name: str):
        """触发降级回调"""
        for callback in self.degradation_callbacks:
            try:
                callback(component_name)
            except Exception as e:
                logger.error(f"❌ 降级回调异常: {e}")
    
    def _trigger_recovery_callbacks(self, component_name: str):
        """触发恢复回调"""
        for callback in self.recovery_callbacks:
            try:
                callback(component_name)
            except Exception as e:
                logger.error(f"❌ 恢复回调异常: {e}")

def with_graceful_degradation(component_name: str, fallback_result: Any = None):
    """优雅降级装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = degradation_manager
            
            if not manager.is_component_healthy(component_name):
                logger.warning(f"🔧 组件 {component_name} 不健康，使用降级结果")
                return manager.get_fallback_result(component_name, fallback_result)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"❌ 组件 {component_name} 执行异常: {e}")
                # 触发健康检查更新
                manager.health_check(component_name, lambda: False)
                return manager.get_fallback_result(component_name, fallback_result)
        
        return wrapper
    return decorator

# 全局实例
degradation_manager = GracefulDegradationManager()

# 降级回调函数示例
def on_component_degraded(component_name: str):
    """组件降级时的回调"""
    logger.warning(f"📉 组件已降级: {component_name}")

def on_component_recovered(component_name: str):
    """组件恢复时的回调"""
    logger.info(f"📈 组件已恢复: {component_name}")

# 注册回调
degradation_manager.add_degradation_callback(on_component_degraded)
degradation_manager.add_recovery_callback(on_component_recovered)