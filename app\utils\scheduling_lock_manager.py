from sqlalchemy import text
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产全局锁管理器 - 防止并发排产导致重复记录

功能：
1. 全局排产执行锁，确保同时只有一个排产任务运行
2. 基于Redis的分布式锁（如果可用），否则使用跨平台锁机制
3. 排产历史记录幂等性检查
4. 事务统一管理
"""

import os
import time
import logging
import hashlib
import platform
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from contextlib import contextmanager
from dataclasses import dataclass

# 跨平台文件锁导入
try:
    if platform.system() == "Windows":
        import msvcrt
    else:
        import fcntl
except ImportError:
    pass

logger = logging.getLogger(__name__)

# 全局内存锁字典 - 用于Windows环境或文件锁失败时的备用方案
_memory_locks = {}
_memory_locks_lock = threading.Lock()

@dataclass
class SchedulingContext:
    """排产执行上下文"""
    lock_id: str
    algorithm: str
    user_id: str
    optimization_target: str
    start_time: datetime
    parameters: Dict[str, Any]
    
class SchedulingLockManager:
    """排产锁管理器"""
    
    def __init__(self, redis_client=None):
        """
        初始化锁管理器
        
        Args:
            redis_client: Redis客户端（如果可用）
        """
        self.redis_client = redis_client
        self.lock_timeout = 300  # 5分钟锁超时
        self.use_memory_lock = platform.system() == "Windows"  # Windows默认使用内存锁
        
        # 跨平台锁文件路径
        if platform.system() == "Windows":
            # Windows: 使用temp目录
            import tempfile
            self.lock_file_path = os.path.join(tempfile.gettempdir(), 'aps_scheduling.lock')
        else:
            # Unix/Linux: 使用/tmp
            self.lock_file_path = '/tmp/aps_scheduling.lock'
        
        # 确保锁文件目录存在
        os.makedirs(os.path.dirname(self.lock_file_path), exist_ok=True)
        
    @contextmanager
    def acquire_scheduling_lock(self, algorithm: str, user_id: str, 
                               optimization_target: str = 'balanced',
                               parameters: Dict[str, Any] = None):
        """
        获取排产执行锁
        
        Args:
            algorithm: 排产算法
            user_id: 用户ID
            optimization_target: 优化目标
            parameters: 排产参数
            
        Yields:
            SchedulingContext: 排产上下文
        """
        lock_id = self._generate_lock_id(algorithm, user_id, optimization_target)
        context = SchedulingContext(
            lock_id=lock_id,
            algorithm=algorithm,
            user_id=user_id,
            optimization_target=optimization_target,
            start_time=datetime.now(),
            parameters=parameters or {}
        )
        
        acquired = False
        try:
            # 尝试获取锁
            acquired = self._acquire_lock(lock_id)
            if not acquired:
                raise Exception("系统正在执行其他排产任务，请稍后重试")
            
            logger.info(f"🔒 排产锁已获取: {lock_id}")
            yield context
            
        finally:
            if acquired:
                self._release_lock(lock_id)
                logger.info(f"🔓 排产锁已释放: {lock_id}")
    
    def _generate_lock_id(self, algorithm: str, user_id: str, optimization_target: str) -> str:
        """生成锁ID"""
        content = f"scheduling_{algorithm}_{user_id}_{optimization_target}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _acquire_lock(self, lock_id: str) -> bool:
        """获取锁"""
        if self.redis_client:
            return self._acquire_redis_lock(lock_id)
        elif self.use_memory_lock:
            return self._acquire_memory_lock(lock_id)
        else:
            # 尝试文件锁，失败时回退到内存锁
            success = self._acquire_file_lock(lock_id)
            if not success:
                logger.warning("文件锁获取失败，回退到内存锁")
                self.use_memory_lock = True
                return self._acquire_memory_lock(lock_id)
            return success
    
    def _release_lock(self, lock_id: str):
        """释放锁"""
        if self.redis_client:
            self._release_redis_lock(lock_id)
        elif self.use_memory_lock:
            self._release_memory_lock(lock_id)
        else:
            self._release_file_lock(lock_id)
    
    def _acquire_redis_lock(self, lock_id: str) -> bool:
        """使用Redis获取分布式锁"""
        try:
            key = f"aps:scheduling:lock:{lock_id}"
            result = self.redis_client.set(
                key, 
                f"{os.getpid()}_{time.time()}", 
                nx=True, 
                ex=self.lock_timeout
            )
            return bool(result)
        except Exception as e:
            logger.warning(f"Redis锁获取失败，回退到内存锁: {e}")
            self.use_memory_lock = True
            return self._acquire_memory_lock(lock_id)
    
    def _release_redis_lock(self, lock_id: str):
        """释放Redis锁"""
        try:
            key = f"aps:scheduling:lock:{lock_id}"
            self.redis_client.delete(key)
        except Exception as e:
            logger.warning(f"Redis锁释放失败: {e}")
    
    def _acquire_memory_lock(self, lock_id: str) -> bool:
        """使用内存锁（线程安全）"""
        global _memory_locks, _memory_locks_lock
        
        with _memory_locks_lock:
            if lock_id not in _memory_locks:
                _memory_locks[lock_id] = threading.Lock()
            
            lock = _memory_locks[lock_id]
        
        # 尝试非阻塞获取锁
        acquired = lock.acquire(blocking=False)
        if acquired:
            # 存储锁信息，用于释放时验证
            if not hasattr(self, '_acquired_locks'):
                self._acquired_locks = set()
            self._acquired_locks.add(lock_id)
            logger.debug(f"内存锁获取成功: {lock_id}")
            return True
        else:
            logger.debug(f"内存锁获取失败: {lock_id}")
            return False
    
    def _release_memory_lock(self, lock_id: str):
        """释放内存锁"""
        global _memory_locks, _memory_locks_lock
        
        try:
            if hasattr(self, '_acquired_locks') and lock_id in self._acquired_locks:
                with _memory_locks_lock:
                    if lock_id in _memory_locks:
                        _memory_locks[lock_id].release()
                        self._acquired_locks.remove(lock_id)
                        logger.debug(f"内存锁释放成功: {lock_id}")
        except Exception as e:
            logger.warning(f"内存锁释放失败: {e}")
    
    def _acquire_file_lock(self, lock_id: str) -> bool:
        """跨平台文件锁实现"""
        try:
            self.lock_file = open(self.lock_file_path, 'w')
            
            if platform.system() == "Windows":
                # Windows: 使用msvcrt
                try:
                    msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                except OSError:
                    self.lock_file.close()
                    return False
            else:
                # Unix/Linux: 使用fcntl
                try:
                    fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                except (IOError, OSError):
                    self.lock_file.close()
                    return False
            
            # 写入锁信息
            self.lock_file.write(f"{lock_id}_{os.getpid()}_{time.time()}")
            self.lock_file.flush()
            return True
            
        except Exception as e:
            logger.warning(f"文件锁获取失败: {e}")
            if hasattr(self, 'lock_file'):
                try:
                    self.lock_file.close()
                except:
                    pass
            return False
    
    def _release_file_lock(self, lock_id: str):
        """跨平台文件锁释放"""
        try:
            if hasattr(self, 'lock_file'):
                if platform.system() == "Windows":
                    # Windows: 使用msvcrt解锁
                    try:
                        msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_UNLCK, 1)
                    except:
                        pass
                else:
                    # Unix/Linux: 使用fcntl解锁
                    try:
                        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
                    except:
                        pass
                
                self.lock_file.close()
                
        except Exception as e:
            logger.warning(f"文件锁释放失败: {e}")

class SchedulingHistoryManager:
    """排产历史记录管理器 - 防重复保存"""
    
    def __init__(self):
        self.recent_executions = {}  # 内存缓存最近的执行记录
    
    def check_duplicate_execution(self, algorithm: str, user_id: str, 
                                 optimization_target: str, tolerance_seconds: int = 60) -> bool:
        """
        检查是否为重复执行
        
        Args:
            algorithm: 算法名称
            user_id: 用户ID
            optimization_target: 优化目标
            tolerance_seconds: 容忍的时间间隔（秒）
            
        Returns:
            bool: True表示重复执行，False表示正常执行
        """
        key = f"{algorithm}_{user_id}_{optimization_target}"
        now = datetime.now()
        
        # 检查内存缓存
        if key in self.recent_executions:
            last_time = self.recent_executions[key]
            if (now - last_time).total_seconds() < tolerance_seconds:
                logger.warning(f"检测到重复排产执行: {key}, 间隔: {(now - last_time).total_seconds()}秒")
                return True
        
        # 检查数据库中的最近记录
        try:
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT COUNT(*) FROM scheduling_history 
                    WHERE algorithm = %s 
                      AND user_id = %s 
                      AND optimization_target = %s 
                      AND created_at > %s
                """, (
                    algorithm, 
                    user_id, 
                    optimization_target, 
                    now - timedelta(seconds=tolerance_seconds)
                ))
                
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.warning(f"数据库中发现重复排产记录: {key}")
                    return True
                
        except Exception as e:
            logger.error(f"检查重复执行失败: {e}")
        
        # 记录本次执行
        self.recent_executions[key] = now
        return False
    
    def create_unique_history_id(self, algorithm: str, user_id: str, 
                                optimization_target: str, timestamp: datetime) -> str:
        """创建唯一的历史记录ID"""
        content = f"{algorithm}_{user_id}_{optimization_target}_{timestamp.isoformat()}"
        return hashlib.sha256(content.encode()).hexdigest()

# 全局实例
_lock_manager = None
_history_manager = None

def get_scheduling_lock_manager(redis_client=None) -> SchedulingLockManager:
    """获取排产锁管理器单例"""
    global _lock_manager
    if _lock_manager is None:
        _lock_manager = SchedulingLockManager(redis_client)
    return _lock_manager

def get_scheduling_history_manager() -> SchedulingHistoryManager:
    """获取排产历史记录管理器单例"""
    global _history_manager
    if _history_manager is None:
        _history_manager = SchedulingHistoryManager()
    return _history_manager 