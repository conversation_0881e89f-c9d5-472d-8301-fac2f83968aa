#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 scheduling_failed_lots 中的失败批次是否真的无法上机。
- 逐条读取最近的失败批次
- 根据当前六类匹配逻辑，重新评估是否存在可排产的设备（只要求 DEVICE+STAGE 完全一致）
- 即便 HB_PN/TB_PN 不匹配或为空，也按当前规则判断小改机/换测试机/大改机等
- 输出 3-5 个典型案例的详细分析与结论
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.utils.db_connection_pool import get_db_connection
from app.services.real_scheduling_service import RealSchedulingService

MAX_FAILED_SAMPLES = 30
SHOW_EXAMPLES = 5


def fetch_recent_failed_lots(limit=MAX_FAILED_SAMPLES):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        """
        SELECT id, lot_id, device, stage, good_qty, failure_reason, failure_details, timestamp
        FROM scheduling_failed_lots
        ORDER BY timestamp DESC
        LIMIT %s
        """,
        (limit,)
    )
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows


def get_specs_and_recipes(cursor, device, stage):
    cursor.execute("SELECT * FROM et_ft_test_spec WHERE DEVICE=%s AND UPPER(STAGE)=UPPER(%s)", (device, stage))
    specs = cursor.fetchall()
    cursor.execute("SELECT * FROM et_recipe_file WHERE DEVICE=%s AND UPPER(STAGE)=UPPER(%s)", (device, stage))
    recipes = cursor.fetchall()
    return specs, recipes


def get_equipments(cursor, device, stage):
    cursor.execute(
        """
        SELECT * FROM eqp_status
        WHERE DEVICE=%s AND UPPER(STAGE)=UPPER(%s)
    """,
        (device, stage),
    )
    eqps = cursor.fetchall()
    return eqps


def analyze_failed_lot(service: RealSchedulingService, cursor, lot_row):
    lot_id = lot_row.get('lot_id')
    device = lot_row.get('device')
    stage = lot_row.get('stage')
    failure_reason = lot_row.get('failure_reason')
    failure_details = lot_row.get('failure_details') or ''

    # 构建预加载数据（只取当前 DEVICE+STAGE 的数据）
    specs, recipes = get_specs_and_recipes(cursor, device, stage)
    eqps = get_equipments(cursor, device, stage)

    preloaded_data = {
        'test_specs': specs,
        'recipe_files': recipes,
        'equipment_status': eqps,
        'wait_lots': []
    }

    lot_requirements = {
        'DEVICE': device,
        'STAGE': stage
    }

    # 如果无设备，直接判定为确实无法上机
    if not eqps:
        return {
            'lot_id': lot_id,
            'device': device,
            'stage': stage,
            'failure_reason': failure_reason,
            'failure_details': failure_details,
            'verdict': '确实无法上机（无DEVICE+STAGE匹配设备）',
            'possible': False,
            'candidates': []
        }

    # 逐台设备评估
    candidates = []
    for eqp in eqps:
        score, match_type, changeover_time = service.calculate_equipment_match_score_optimized(
            lot_requirements, eqp, preloaded_data
        )
        candidates.append({
            'equipment': eqp,
            'score': score,
            'match_type': match_type,
            'changeover_time': changeover_time
        })

    # 过滤掉无法上机
    feasible = [c for c in candidates if c['match_type'] != '无法上机']
    feasible_sorted = sorted(feasible, key=lambda x: (-x['score'], x['changeover_time']))

    if feasible_sorted:
        best = feasible_sorted[0]
        eqp = best['equipment']
        return {
            'lot_id': lot_id,
            'device': device,
            'stage': stage,
            'failure_reason': failure_reason,
            'failure_details': failure_details,
            'verdict': '可排产（当前逻辑评估有可行设备）',
            'possible': True,
            'best_handler': eqp.get('HANDLER_ID', 'UNKNOWN'),
            'best_match_type': best['match_type'],
            'best_score': best['score'],
            'best_changeover_time': best['changeover_time']
        }
    else:
        return {
            'lot_id': lot_id,
            'device': device,
            'stage': stage,
            'failure_reason': failure_reason,
            'failure_details': failure_details,
            'verdict': '确实无法上机（所有设备均判定为无法上机）',
            'possible': False,
            'candidates': candidates[:3]
        }


def main():
    print("🔍 失败批次深度分析开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    failed_rows = fetch_recent_failed_lots()
    if not failed_rows:
        print("✅ scheduling_failed_lots 表为空，无需分析")
        return

    print(f"读取最近失败批次数: {len(failed_rows)}")

    service = RealSchedulingService()

    # 复用单一数据库连接，避免连接配额耗尽
    conn = get_db_connection()
    cursor = conn.cursor()

    analyses = []
    for row in failed_rows:
        analysis = analyze_failed_lot(service, cursor, row)
        analyses.append(analysis)

    cursor.close()
    conn.close()

    # 统计
    reclassified = [a for a in analyses if a['possible']]
    truly_failed = [a for a in analyses if not a['possible']]

    print("\n=== 分析统计 ===")
    print(f"可重排/应可排产的批次: {len(reclassified)}/{len(analyses)}")
    print(f"确实无法上机的批次: {len(truly_failed)}/{len(analyses)}")

    # 输出若干典型案例
    print("\n=== 典型案例（最多显示5个） ===")
    for i, a in enumerate(analyses[:SHOW_EXAMPLES]):
        print(f"\n案例 {i+1}:")
        print(f"  LOT: {a['lot_id']}  DEVICE: {a['device']}  STAGE: {a['stage']}")
        print(f"  原失败原因: {a['failure_reason']}")
        try:
            if a.get('failure_details'):
                details_obj = json.loads(a['failure_details']) if isinstance(a['failure_details'], str) else a['failure_details']
                print(f"  原失败详情: {json.dumps(details_obj, ensure_ascii=False)}")
        except Exception:
            print(f"  原失败详情: {a.get('failure_details')}")
        print(f"  重新评估结论: {a['verdict']}")
        if a['possible']:
            print(f"  ▶ 建议设备: {a['best_handler']}  匹配类型: {a['best_match_type']}  评分: {a['best_score']}  改机: {a['best_changeover_time']}分钟")

    print("\n=== 建议 ===")
    if reclassified:
        print("- 发现部分失败批次在当前严格规则下其实可以排产，建议：")
        print("  1) 复核六类匹配实现是否在主调度链路中正确调用（本脚本已调用相同逻辑）")
        print("  2) 排查失败批次的DEVICE/STAGE是否在调度时被错误过滤")
        print("  3) 对可排产的失败批次进行二次调度或自动重试")
    else:
        print("- 当前采样的失败批次确实无法上机，建议：")
        print("  1) 扩充设备覆盖的DEVICE/STAGE组合")
        print("  2) 补齐et_ft_test_spec或et_recipe_file缺失的数据")
        print("  3) 逐条对接产线确认不可上机原因并修正数据")

    print("\n" + "=" * 60)
    print("🎉 分析结束")


if __name__ == "__main__":
    main()

