"""
API v2 生产调度服务模块
集成Excel数据源备用机制和智能排产功能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from flask import current_app
from app.services.data_source_manager import DataSourceManager
# from app.services.intelligent_scheduling_service import IntelligentSchedulingService  # DISABLED - 统一使用RealSchedulingService
from app.services.real_scheduling_service import RealSchedulingService

logger = logging.getLogger(__name__)

class ProductionSchedulingServiceV2:
    """API v2 生产调度服务 - 集成数据源管理和智能排产"""
    
    def __init__(self):
        self.data_manager = DataSourceManager()
        # self.scheduling_service = IntelligentSchedulingService()  # DISABLED - 统一使用RealSchedulingService
        self.scheduling_service = RealSchedulingService()
        
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        try:
            return self.data_manager.get_data_source_status()
        except Exception as e:
            logger.error(f"获取数据源状态失败: {e}")
            return {
                'mysql_available': False,
                'excel_available': False,
                'current_source': 'unknown',
                'error': str(e)
            }
    
    def switch_data_source(self, source: str) -> Dict[str, Any]:
        """切换数据源"""
        try:
            if source not in ['mysql', 'excel', 'auto']:
                return {
                    'success': False,
                    'message': '无效的数据源类型，支持: mysql, excel, auto'
                }
            
            if source == 'auto':
                # 自动选择最佳数据源
                status = self.data_manager.get_data_source_status()
                if status['mysql_available']:
                    source = 'mysql'
                elif status['excel_available']:
                    source = 'excel'
                else:
                    return {
                        'success': False,
                        'message': '没有可用的数据源'
                    }
            
            # 这里可以添加数据源切换逻辑
            # 目前DataSourceManager会自动处理切换
            
            return {
                'success': True,
                'message': f'数据源已切换到: {source}',
                'current_source': source
            }
            
        except Exception as e:
            logger.error(f"切换数据源失败: {e}")
            return {
                'success': False,
                'message': f'切换数据源失败: {str(e)}'
            }
    
    def execute_intelligent_scheduling(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能排产"""
        try:
            logger.info(f"🚀 API v2 开始执行智能排产: {params}")
            
            # 使用智能排产服务执行排产
            result = self.scheduling_service.execute_intelligent_scheduling(
                algorithm=params.get('algorithm', 'intelligent'),
                optimization_target=params.get('optimization_target', 'balanced')
            )
            
            # 添加API v2标识
            result['api_version'] = 'v2'
            result['service'] = 'ProductionSchedulingServiceV2'
            
            return result
            
        except Exception as e:
            logger.error(f"API v2 智能排产执行失败: {e}")
            return {
                'success': False,
                'message': f'智能排产执行失败: {str(e)}',
                'schedule': [],
                'metrics': {},
                'api_version': 'v2',
                'error': str(e)
            }
    
    def get_wait_lot_data(self, source: Optional[str] = None) -> Tuple[List[Dict], str]:
        """获取待排产批次数据"""
        try:
            return self.data_manager.get_wait_lot_data(source)
        except Exception as e:
            logger.error(f"获取待排产批次数据失败: {e}")
            return [], 'error'
    
    def get_uph_equipment_data(self, source: Optional[str] = None) -> Tuple[List[Dict], str]:
        """获取设备UPH数据"""
        try:
            return self.data_manager.get_uph_equipment_data(source)
        except Exception as e:
            logger.error(f"获取设备UPH数据失败: {e}")
            return [], 'error'
    
    def get_test_spec_data(self, source: Optional[str] = None) -> Tuple[List[Dict], str]:
        """获取测试规范数据"""
        try:
            return self.data_manager.get_test_spec_data(source)
        except Exception as e:
            logger.error(f"获取测试规范数据失败: {e}")
            return [], 'error'
    
    def get_equipment_status_data(self, source: Optional[str] = None) -> Tuple[List[Dict], str]:
        """获取设备状态数据"""
        try:
            return self.data_manager.get_equipment_status_data(source)
        except Exception as e:
            logger.error(f"获取设备状态数据失败: {e}")
            return [], 'error'
    
    def get_recipe_file_data(self, source: Optional[str] = None) -> Tuple[List[Dict], str]:
        """获取工艺配方数据"""
        try:
            return self.data_manager.get_recipe_file_data(source)
        except Exception as e:
            logger.error(f"获取工艺配方数据失败: {e}")
            return [], 'error'
    
    def validate_data_consistency(self) -> Dict[str, Any]:
        """验证数据一致性"""
        try:
            # 获取MySQL和Excel数据进行对比
            mysql_wait_lots, _ = self.get_wait_lot_data('mysql')
            excel_wait_lots, _ = self.get_wait_lot_data('excel')
            
            mysql_count = len(mysql_wait_lots)
            excel_count = len(excel_wait_lots)
            
            consistency_score = 0
            if mysql_count > 0 and excel_count > 0:
                consistency_score = min(mysql_count, excel_count) / max(mysql_count, excel_count)
            
            return {
                'mysql_records': mysql_count,
                'excel_records': excel_count,
                'consistency_score': consistency_score,
                'is_consistent': consistency_score > 0.8,
                'recommendation': 'mysql' if mysql_count >= excel_count else 'excel'
            }
            
        except Exception as e:
            logger.error(f"验证数据一致性失败: {e}")
            return {
                'mysql_records': 0,
                'excel_records': 0,
                'consistency_score': 0,
                'is_consistent': False,
                'error': str(e)
            }
    
    def get_scheduling_metrics(self) -> Dict[str, Any]:
        """获取排产指标统计"""
        try:
            # 获取基础数据统计
            wait_lots, source1 = self.get_wait_lot_data()
            uph_data, source2 = self.get_uph_equipment_data()
            equipment_status, source3 = self.get_equipment_status_data()
            
            # 计算可用设备数量
            available_equipment = [eq for eq in equipment_status if eq.get('STATUS') == 'RUN']
            
            return {
                'total_wait_lots': len(wait_lots),
                'total_equipment': len(equipment_status),
                'available_equipment': len(available_equipment),
                'uph_records': len(uph_data),
                'data_sources': {
                    'wait_lots': source1,
                    'uph_data': source2,
                    'equipment_status': source3
                },
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取排产指标失败: {e}")
            return {
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            } 