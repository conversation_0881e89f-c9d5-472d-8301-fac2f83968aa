#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存失效装饰器
用于自动处理数据更新时的缓存失效

🚀 功能：
- 装饰器自动失效相关缓存
- 支持指定表名或自动检测
- 集成统一缓存系统
"""

import logging
import functools
from typing import List, Optional, Callable, Any

logger = logging.getLogger(__name__)

def invalidate_wait_lot_cache(func: Callable) -> Callable:
    """
    待排产批次缓存失效装饰器
    在数据更新操作后自动失效相关缓存
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 执行原始函数
            result = func(*args, **kwargs)
            
            # 失效相关缓存
            try:
                from app.utils.api_cache_adapter import get_api_cache_adapter
                api_cache = get_api_cache_adapter()
                
                # 失效待排产相关的缓存
                cache_tables = [
                    'et_wait_lot',
                    'wait_lot',
                    'wip_lot',
                    'ET_WAIT_LOT'
                ]
                
                for table in cache_tables:
                    api_cache.invalidate_cache(table)
                
                logger.info(f"✅ 自动失效缓存完成: {cache_tables}")
                
            except Exception as cache_error:
                logger.warning(f"⚠️ 缓存失效失败: {cache_error}")
                # 缓存失效失败不应影响主要功能
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 装饰器执行失败: {e}")
            raise
    
    return wrapper

def invalidate_table_cache(table_names: List[str] = None):
    """
    通用表缓存失效装饰器
    
    Args:
        table_names: 要失效的表名列表，None表示失效所有缓存
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 执行原始函数
                result = func(*args, **kwargs)
                
                # 失效指定缓存
                try:
                    from app.utils.api_cache_adapter import get_api_cache_adapter
                    api_cache = get_api_cache_adapter()
                    
                    if table_names:
                        for table in table_names:
                            api_cache.invalidate_cache(table)
                        logger.info(f"✅ 自动失效指定表缓存: {table_names}")
                    else:
                        api_cache.invalidate_cache()
                        logger.info("✅ 自动失效所有缓存")
                        
                except Exception as cache_error:
                    logger.warning(f"⚠️ 缓存失效失败: {cache_error}")
                
                return result
                
            except Exception as e:
                logger.error(f"❌ 装饰器执行失败: {e}")
                raise
        
        return wrapper
    return decorator

def invalidate_equipment_cache(func: Callable) -> Callable:
    """
    设备状态缓存失效装饰器
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            try:
                from app.utils.api_cache_adapter import get_api_cache_adapter
                api_cache = get_api_cache_adapter()
                
                equipment_tables = [
                    'eqp_status',
                    'EQP_STATUS',
                    'equipment_status_data'
                ]
                
                for table in equipment_tables:
                    api_cache.invalidate_cache(table)
                
                logger.info(f"✅ 自动失效设备缓存完成: {equipment_tables}")
                
            except Exception as cache_error:
                logger.warning(f"⚠️ 设备缓存失效失败: {cache_error}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 设备缓存失效装饰器失败: {e}")
            raise
    
    return wrapper

def invalidate_production_cache(func: Callable) -> Callable:
    """
    生产相关缓存失效装饰器
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            try:
                from app.utils.api_cache_adapter import get_api_cache_adapter
                api_cache = get_api_cache_adapter()
                
                production_tables = [
                    'lotprioritydone',
                    'ct',
                    'et_wait_lot',
                    'wip_lot'
                ]
                
                for table in production_tables:
                    api_cache.invalidate_cache(table)
                
                logger.info(f"✅ 自动失效生产缓存完成: {production_tables}")
                
            except Exception as cache_error:
                logger.warning(f"⚠️ 生产缓存失效失败: {cache_error}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 生产缓存失效装饰器失败: {e}")
            raise
    
    return wrapper

# 便捷的装饰器别名
invalidate_cache = invalidate_table_cache
invalidate_all_cache = invalidate_table_cache()

def invalidate_dynamic_table_cache(func: Callable) -> Callable:
    """
    动态表缓存失效装饰器 - 根据函数参数中的table_name自动失效对应表的缓存
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 从函数参数中提取table_name
            table_name = None
            
            # 尝试从kwargs中获取（Flask路由参数通常在这里）
            if 'table_name' in kwargs:
                table_name = kwargs['table_name']
            # 尝试从函数签名中获取参数
            elif args and len(args) >= 1:
                import inspect
                try:
                    # 获取函数签名
                    sig = inspect.signature(func)
                    param_names = list(sig.parameters.keys())
                    
                    # 查找table_name参数的位置
                    if 'table_name' in param_names:
                        param_index = param_names.index('table_name')
                        if len(args) > param_index:
                            table_name = args[param_index]
                except Exception:
                    # 如果签名检测失败，使用传统方法
                    if len(args) > 0:
                        table_name = args[0]
            
            if table_name:
                try:
                    from app.utils.api_cache_adapter import get_api_cache_adapter
                    api_cache = get_api_cache_adapter()
                    
                    # 失效多个相关的缓存键
                    cache_keys_to_invalidate = [
                        table_name,
                        table_name.lower(),
                        table_name.upper(),
                        f"{table_name}_data",
                        f"{table_name.lower()}_data",
                        f"equipment_status_data" if table_name.lower() in ['eqp_status', 'equipment_status'] else None
                    ]
                    
                    # 过滤掉None值
                    cache_keys_to_invalidate = [key for key in cache_keys_to_invalidate if key]
                    
                    for cache_key in cache_keys_to_invalidate:
                        api_cache.invalidate_cache(cache_key)
                    
                    logger.info(f"✅ 动态失效表 '{table_name}' 相关缓存: {cache_keys_to_invalidate}")
                    
                except Exception as cache_error:
                    logger.warning(f"⚠️ 动态缓存失效失败 (表: {table_name}): {cache_error}")
            else:
                logger.warning("⚠️ 无法从函数参数中提取table_name，跳过缓存失效")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 动态缓存失效装饰器执行失败: {e}")
            raise
    
    return wrapper

logger.info("🔄 缓存失效装饰器模块加载完成")
