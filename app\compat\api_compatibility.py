# -*- coding: utf-8 -*-
"""
API兼容性层 - 自动生成
提供旧版本API到新版本的兼容性重定向
"""

from flask import Blueprint, request, redirect, url_for, jsonify
from functools import wraps
import logging

logger = logging.getLogger(__name__)

# 创建兼容性蓝图
compat_bp = Blueprint('api_compatibility', __name__)

def deprecated_endpoint(message="此端点已废弃，请使用新版本API", new_endpoint=None, removal_date=None, reason=None):
    """标记端点为废弃的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            endpoint_info = f"端点: {request.endpoint}"
            if new_endpoint:
                endpoint_info += f", 新端点: {new_endpoint}"
            if removal_date:
                endpoint_info += f", 移除日期: {removal_date}"
            if reason:
                endpoint_info += f", 原因: {reason}"
            
            logger.warning(f"访问了废弃端点: {endpoint_info}")
            
            # 执行原函数
            response = f(*args, **kwargs)
            
            # 在响应头中添加废弃警告（使用ASCII安全的值）
            if hasattr(response, 'headers'):
                response.headers['X-API-Deprecated'] = 'true'
                # 使用ASCII安全的消息
                response.headers['X-API-Deprecation-Message'] = 'This endpoint is deprecated'
                if new_endpoint:
                    response.headers['X-API-New-Endpoint'] = new_endpoint
                if removal_date:
                    response.headers['X-API-Removal-Date'] = removal_date
            return response
        return decorated_function
    return decorator

def redirect_to_v2(v2_endpoint):
    """重定向到API v2端点的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            logger.info(f"重定向 {request.endpoint} 到 {v2_endpoint}")
            # 保持查询参数和请求方法
            if request.method == 'GET':
                return redirect(url_for(v2_endpoint, **request.args))
            else:
                # 对于非GET请求，返回302重定向指示
                return jsonify({
                    'redirect': True,
                    'new_endpoint': v2_endpoint,
                    'message': '请使用新的API v2端点'
                }), 302
        return decorated_function
    return decorator

# 兼容性端点映射
COMPATIBILITY_MAPPINGS = {
    '/settings': '/settings',
}

# 注册兼容性端点
@compat_bp.route('/api/status')
@deprecated_endpoint("请使用 /api/v2/system/status")
def legacy_status():
    return redirect(url_for('api_v2.system.get_system_status'))

# 自动生成的兼容性端点将在这里添加

class APICompatibilityManager:
    """API兼容性管理器"""
    
    def __init__(self):
        self.compatibility_mappings = COMPATIBILITY_MAPPINGS
    
    def get_compatible_response(self, endpoint, request_data=None):
        """获取兼容性响应"""
        if endpoint in self.compatibility_mappings:
            new_endpoint = self.compatibility_mappings[endpoint]
            return {
                'success': True,
                'redirect': True,
                'new_endpoint': new_endpoint,
                'message': f'端点已重定向到 {new_endpoint}'
            }
        else:
            return {
                'success': True,
                'redirect': False,
                'endpoint': endpoint,
                'message': '端点兼容性正常'
            }
    
    def is_deprecated(self, endpoint):
        """检查端点是否已废弃"""
        return endpoint in self.compatibility_mappings
    
    def get_new_endpoint(self, endpoint):
        """获取新端点"""
        return self.compatibility_mappings.get(endpoint, endpoint)

# 创建全局兼容性管理器实例
api_compatibility_manager = APICompatibilityManager()

def init_api_compatibility(app):
    """初始化API兼容性层"""
    logger.info("API兼容性层初始化完成")
    return True
