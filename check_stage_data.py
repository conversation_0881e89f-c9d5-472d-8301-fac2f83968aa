#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查scheduling_failed_lots表中的stage字段数据
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_stage_data.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('CheckStageData')

def check_stage_data():
    """检查scheduling_failed_lots表中的stage字段数据"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入数据库连接
            from app.utils.db_connection_pool import get_db_connection_context
            
            logger.info("🔍 检查scheduling_failed_lots表中的stage字段数据...")
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = 'scheduling_failed_lots'
                """)
                
                if cursor.fetchone()[0] == 0:
                    logger.error("❌ scheduling_failed_lots表不存在")
                    return False
                
                logger.info("✅ scheduling_failed_lots表存在")
                
                # 查询所有不同的stage值
                cursor.execute("""
                SELECT DISTINCT stage, COUNT(*) as count
                FROM scheduling_failed_lots 
                WHERE stage IS NOT NULL AND stage != ''
                GROUP BY stage
                ORDER BY count DESC, stage
                """)
                
                results = cursor.fetchall()
                logger.info(f"📊 共找到 {len(results)} 个不同的工序值:")
                
                for i, (stage, count) in enumerate(results, 1):
                    logger.info(f'{i:2d}. 工序="{stage}" (出现 {count} 次)')
                    
                    # 特别标记单字符工序
                    if len(str(stage).strip()) == 1:
                        logger.warning(f'    ⚠️ 发现单字符工序: "{stage}"')
                        
                # 专门查询包含'A'的记录
                logger.info("\n🎯 专门查询包含字母'A'的工序记录:")
                cursor.execute("""
                SELECT lot_id, device, stage, failure_reason, timestamp
                FROM scheduling_failed_lots 
                WHERE stage = 'A' OR stage LIKE '%A%'
                ORDER BY timestamp DESC
                LIMIT 10
                """)
                
                a_records = cursor.fetchall()
                if a_records:
                    logger.info(f"找到 {len(a_records)} 条包含'A'的记录:")
                    for i, (lot_id, device, stage, reason, timestamp) in enumerate(a_records, 1):
                        logger.info(f'{i}. LOT_ID={lot_id}, DEVICE={device}, STAGE="{stage}", 原因={reason[:50]}..., 时间={timestamp}')
                else:
                    logger.info("未找到包含'A'的工序记录")
                    
                # 查询最近的几条记录来看数据结构
                logger.info("\n📋 查询最近5条失败记录:")
                cursor.execute("""
                SELECT lot_id, device, stage, failure_reason, timestamp
                FROM scheduling_failed_lots 
                ORDER BY timestamp DESC
                LIMIT 5
                """)
                
                recent_records = cursor.fetchall()
                for i, (lot_id, device, stage, reason, timestamp) in enumerate(recent_records, 1):
                    logger.info(f'{i}. LOT_ID={lot_id}, DEVICE={device}, STAGE="{stage}", 原因={reason[:30]}..., 时间={timestamp}')
                
                return True
            
    except Exception as e:
        logger.error(f"❌ 检查stage数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_stage_data()
    print("🎉 检查: 通过" if success else "❌ 检查: 失败")

if __name__ == "__main__":
    main()