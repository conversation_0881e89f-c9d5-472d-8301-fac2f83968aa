// email_attachment.js
// 处理邮件附件相关的前端JavaScript代码

document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在附件处理页面
    const fetchAttachmentsBtn = document.getElementById('fetchAttachmentsBtn');
    if (!fetchAttachmentsBtn) return;

    // 初始化
    initEmailAttachmentHandlers();
    loadEmailConfigs();
    loadAttachments();

    // 绑定过滤器事件
    const emailConfigSelect = document.getElementById('emailConfigSelect');
    const processedFilter = document.getElementById('processedFilter');
    if (emailConfigSelect) {
        emailConfigSelect.addEventListener('change', loadAttachments);
    }
    if (processedFilter) {
        processedFilter.addEventListener('change', loadAttachments);
    }
});

/**
 * 初始化附件处理相关的事件处理程序
 */
function initEmailAttachmentHandlers() {
    const fetchAttachmentsBtn = document.getElementById('fetchAttachmentsBtn');
    
    // 获取新附件按钮点击事件
    fetchAttachmentsBtn.addEventListener('click', function() {
        startFetchAttachments();
    });
}

/**
 * 加载邮箱配置到下拉框
 */
function loadEmailConfigs() {
    const emailConfigSelect = document.getElementById('emailConfigSelect');
    if (!emailConfigSelect) return;

    // 清空现有选项
    emailConfigSelect.innerHTML = '<option value="">所有邮箱</option>';

    // 从API获取邮箱配置数据
    fetch('/api/email_config')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.configs) {
                data.configs.forEach(config => {
                    const option = document.createElement('option');
                    option.value = config.id;
                    option.textContent = config.name || config.username;
                    emailConfigSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('获取邮箱配置失败:', error);
            showToast('获取邮箱配置失败: ' + error.message, 'danger');
        });
}

/**
 * 加载附件列表
 */
function loadAttachments() {
    const tableBody = document.getElementById('attachmentsTableBody');
    const emailConfigSelect = document.getElementById('emailConfigSelect');
    const processedFilter = document.getElementById('processedFilter');
    if (!tableBody) return;

    // 构建查询参数
    const params = new URLSearchParams();
    if (emailConfigSelect && emailConfigSelect.value) {
        params.append('email_config_id', emailConfigSelect.value);
    }
    if (processedFilter && processedFilter.value !== '') {
        params.append('processed', processedFilter.value);
    }

    // 显示加载指示器
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm text-primary" role="status"></div> 加载中...</td></tr>';

    // 从API获取附件数据
    fetch('/api/email_attachments?' + params.toString())
        .then(response => response.json())
        .then(data => {
            if (data.success && data.attachments) {
                renderAttachmentsTable(data.attachments);
            } else {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">没有找到任何附件</td></tr>';
                showToast(data.message || '加载附件失败', 'danger');
            }
        })
        .catch(error => {
            console.error('加载附件失败:', error);
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
            showToast('加载附件失败: ' + error.message, 'danger');
        });
}

/**
 * 渲染附件表格
 * @param {Array} attachments 附件数据数组
 */
function renderAttachmentsTable(attachments) {
    const tableBody = document.getElementById('attachmentsTableBody');
    const theadRow = document.querySelector('#attachmentsTable thead tr');
    
    if (!tableBody) return;
    
    // 如果表头不存在，则创建表头
    if (!theadRow || theadRow.children.length === 0) {
        const thead = document.querySelector('#attachmentsTable thead');
        if (thead) {
            thead.innerHTML = `
                <tr>
                    <th>文件名</th>
                    <th>发件人</th>
                    <th>接收时间</th>
                    <th>状态</th>
                    <th>处理时间</th>
                    <th>操作</th>
                </tr>
            `;
        }
    }

    // 清空表格
    tableBody.innerHTML = '';

    if (attachments.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">没有找到任何附件</td></tr>';
        return;
    }

    // 设置刷新时间戳，用于标识新增附件
    const refreshTimestamp = new Date().getTime();
    
    // 填充表格
    attachments.forEach(attachment => {
        const row = document.createElement('tr');
        const received = new Date(attachment.received_at);
        const processed = attachment.processed_at ? new Date(attachment.processed_at) : null;
        
        // 如果是新附件（过去2分钟内添加的），添加高亮样式
        const isNew = attachment.created_at && 
                     (new Date(attachment.created_at).getTime() > (refreshTimestamp - 2 * 60 * 1000));
        
        if (isNew) {
            row.classList.add('table-success');
        }
        
        // 状态徽章样式
        let statusBadge = '';
        if (attachment.processed) {
            statusBadge = '<span class="badge bg-success">已处理</span>';
        } else {
            statusBadge = '<span class="badge bg-secondary">未处理</span>';
        }
        
        // 新增徽章
        const newBadge = isNew ? '<span class="badge bg-info ms-1">新增</span>' : '';

        row.innerHTML = `
            <td>${escapeHtml(attachment.filename || '未命名附件')} ${newBadge}</td>
            <td>${escapeHtml(attachment.sender || '未知')}</td>
            <td>${received.toLocaleString()}</td>
            <td>${statusBadge}</td>
            <td>${processed ? processed.toLocaleString() : '-'}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary download-attachment-btn" 
                        data-id="${attachment.id}" title="下载附件">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info process-attachment-btn" 
                        data-id="${attachment.id}" title="处理附件" ${attachment.processed ? 'disabled' : ''}>
                        <i class="fas fa-cogs"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger delete-attachment-btn" 
                        data-id="${attachment.id}" title="删除附件">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        // 绑定按钮事件
        const downloadBtn = row.querySelector('.download-attachment-btn');
        const processBtn = row.querySelector('.process-attachment-btn');
        const deleteBtn = row.querySelector('.delete-attachment-btn');

        downloadBtn.addEventListener('click', function() {
            window.location.href = `/api/email_attachments/${attachment.id}/download`;
        });

        processBtn.addEventListener('click', function() {
            processEmailAttachment(attachment.id);
        });

        deleteBtn.addEventListener('click', function() {
            deleteEmailAttachment(attachment.id);
        });

        tableBody.appendChild(row);
    });
}

/**
 * 开始获取新附件
 */
function startFetchAttachments() {
    const emailConfigId = document.getElementById('emailConfigSelect').value;
    const progressOverlay = document.getElementById('progressOverlay');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressTitle = document.getElementById('progressTitle');
    
    // 如果没有选择邮箱配置，提示用户
    if (!emailConfigId) {
        showToast('请先选择一个邮箱配置', 'warning');
        return;
    }
    
    // 获取选择的邮箱配置对象
    const selectedConfig = emailConfigs.find(config => config.id == emailConfigId);
    if (!selectedConfig) {
        showToast('无法找到选择的邮箱配置', 'warning');
        return;
    }
    
    // 从配置中获取抓取天数，如果未设置则默认10天
    const fetchDays = selectedConfig.fetch_days || 10;
    
    // 重置进度条
    progressBar.style.width = '0%';
    progressBar.setAttribute('aria-valuenow', 0);
    progressText.textContent = '正在连接邮箱...';
    progressTitle.textContent = '获取新邮件附件中...';
    
    // 显示进度条覆盖层
    progressOverlay.style.display = 'flex';
    
    // 调用API开始获取附件
    fetch(`/api/email_configs/${emailConfigId}/fetch`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            days: fetchDays, // 使用配置中的抓取天数
        })
    })
    .then(response => {
        // 检查SSE支持
        if (response.headers.get('Content-Type') === 'text/event-stream') {
            handleSSEProgress(response);
        } else {
            return response.json().then(data => {
                // 非SSE响应处理
                handleFetchComplete(data);
            });
        }
    })
    .catch(error => {
        console.error('获取附件失败:', error);
        progressOverlay.style.display = 'none';
        showToast('获取附件失败: ' + error.message, 'danger');
    });
}

/**
 * 处理SSE进度更新
 * @param {Response} response 服务器响应
 */
function handleSSEProgress(response) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    // 处理事件流
    const processStream = async () => {
        let buffer = '';
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n\n');
                buffer = lines.pop() || '';
                
                for (const line of lines) {
                    if (line.trim() === '') continue;
                    
                    if (line.startsWith('data:')) {
                        const eventData = JSON.parse(line.substring(5).trim());
                        
                        // 更新进度条
                        if (eventData.progress !== undefined) {
                            const progress = Math.round(eventData.progress);
                            progressBar.style.width = `${progress}%`;
                            progressBar.setAttribute('aria-valuenow', progress);
                        }
                        
                        // 更新状态文本
                        if (eventData.message) {
                            progressText.textContent = eventData.message;
                        }
                        
                        // 判断是否是最终结果
                        if (eventData.complete) {
                            handleFetchComplete(eventData.result);
                            return;
                        }
                    }
                }
            }
            
            // 处理最后一部分数据
            if (buffer.trim() !== '') {
                const lastEvent = buffer.trim();
                if (lastEvent.startsWith('data:')) {
                    const eventData = JSON.parse(lastEvent.substring(5).trim());
                    if (eventData.complete) {
                        handleFetchComplete(eventData.result);
                    }
                }
            }
        } catch (error) {
            console.error('处理SSE流失败:', error);
            document.getElementById('progressOverlay').style.display = 'none';
            showToast('处理进度更新失败: ' + error.message, 'danger');
        }
    };
    
    processStream();
}

/**
 * 处理获取附件完成
 * @param {Object} result 获取结果
 */
function handleFetchComplete(result) {
    const progressOverlay = document.getElementById('progressOverlay');
    progressOverlay.style.display = 'none';
    
    // 刷新附件列表
    loadAttachments();
    
    // 显示结果模态框
    showResultModal(result);
}

/**
 * 显示结果模态框
 * @param {Object} result 处理结果
 */
function showResultModal(result) {
    // 更新结果计数
    document.getElementById('totalEmails').textContent = result.data.total_emails || 0;
    document.getElementById('downloadedFiles').textContent = result.data.downloaded || 0;
    document.getElementById('newDownloadedFiles').textContent = result.data.new_downloaded || 0; // 新增附件数
    document.getElementById('skippedFiles').textContent = result.data.skipped || 0;
    document.getElementById('failedFiles').textContent = result.data.failed || 0;
    
    // 更新标签页徽章
    document.getElementById('processedBadge').textContent = result.data.downloaded || 0;
    document.getElementById('newBadge').textContent = result.data.new_downloaded || 0; // 新增附件徽章
    document.getElementById('skippedBadge').textContent = result.data.skipped || 0;
    document.getElementById('failedBadge').textContent = result.data.failed || 0;
    
    // 清空列表
    document.getElementById('processedList').innerHTML = '';
    document.getElementById('newList').innerHTML = ''; // 清空新增附件列表
    document.getElementById('skippedList').innerHTML = '';
    document.getElementById('failedList').innerHTML = '';
    
    // 填充处理成功列表和新增附件列表
    if (result.data.processed_files && result.data.processed_files.length > 0) {
        const processedList = document.getElementById('processedList');
        const newList = document.getElementById('newList');
        
        result.data.processed_files.forEach(file => {
            const tr = document.createElement('tr');
            const received = new Date(file.receive_date);
            
            // 为新增附件添加样式
            if (file.is_new) {
                tr.classList.add('table-success');
            }
            
            tr.innerHTML = `
                <td>${escapeHtml(file.filename || '未命名')}</td>
                <td>${formatFileSize(file.size || 0)}</td>
                <td>${escapeHtml(file.sender || '未知')}</td>
                <td>${received.toLocaleString()}</td>
                <td>${file.is_new ? '<span class="badge bg-success">新增</span>' : '<span class="badge bg-secondary">已有</span>'}</td>
            `;
            processedList.appendChild(tr);
            
            // 如果是新增附件，也添加到新增附件标签页
            if (file.is_new) {
                const newTr = document.createElement('tr');
                newTr.innerHTML = `
                    <td>${escapeHtml(file.filename || '未命名')}</td>
                    <td>${formatFileSize(file.size || 0)}</td>
                    <td>${escapeHtml(file.sender || '未知')}</td>
                    <td>${received.toLocaleString()}</td>
                `;
                newList.appendChild(newTr);
            }
        });
        
        // 如果没有新增附件，显示提示信息
        if (result.data.new_downloaded === 0) {
            const newTr = document.createElement('tr');
            newTr.innerHTML = `
                <td colspan="4" class="text-center text-muted">本次没有新增附件</td>
            `;
            newList.appendChild(newTr);
        }
    }
    
    // 填充已跳过列表
    if (result.data.skipped_files && result.data.skipped_files.length > 0) {
        const skippedList = document.getElementById('skippedList');
        result.data.skipped_files.forEach(file => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${escapeHtml(file.filename || '未命名')}</td>
                <td>${escapeHtml(file.reason || '未知原因')}</td>
                <td>${escapeHtml(file.sender || '未知')}</td>
                <td>${escapeHtml(file.subject || '无主题')}</td>
            `;
            skippedList.appendChild(tr);
        });
    }
    
    // 填充失败列表
    if (result.data.failed_files && result.data.failed_files.length > 0) {
        const failedList = document.getElementById('failedList');
        result.data.failed_files.forEach(file => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${escapeHtml(file.filename || '未命名')}</td>
                <td>${escapeHtml(file.reason || '未知错误')}</td>
                <td>${escapeHtml(file.sender || '未知')}</td>
                <td>${escapeHtml(file.subject || '无主题')}</td>
            `;
            failedList.appendChild(tr);
        });
    }
    
    // 显示模态框
    const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
    resultModal.show();
    
    // 如果有新增附件，自动切换到新增附件标签页
    if (result.data.new_downloaded > 0) {
        const newTab = document.getElementById('newTab');
        const tabInstance = new bootstrap.Tab(newTab);
        tabInstance.show();
    }
}

/**
 * 处理单个邮件附件
 * @param {number} attachmentId 附件ID
 */
function processEmailAttachment(attachmentId) {
    if (!confirm('确定要处理这个附件吗？')) return;
    
    // 显示加载指示器
    showToast('正在处理附件...', 'info');
    
    // 调用API处理附件
    fetch(`/api/email_attachments/${attachmentId}/process`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('附件处理成功', 'success');
            loadAttachments();
        } else {
            showToast(data.message || '附件处理失败', 'danger');
        }
    })
    .catch(error => {
        console.error('处理附件失败:', error);
        showToast('处理附件失败: ' + error.message, 'danger');
    });
}

/**
 * 删除邮件附件
 * @param {number} attachmentId 附件ID
 */
function deleteEmailAttachment(attachmentId) {
    if (!confirm('确定要删除这个附件吗？此操作不可恢复。')) return;
    
    // 调用API删除附件
    fetch(`/api/email_attachments/${attachmentId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('附件删除成功', 'success');
            loadAttachments();
        } else {
            showToast(data.message || '附件删除失败', 'danger');
        }
    })
    .catch(error => {
        console.error('删除附件失败:', error);
        showToast('删除附件失败: ' + error.message, 'danger');
    });
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

/**
 * 转义HTML特殊字符
 * @param {string} text 需要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
    if (!text) return '';
    
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * 显示提示消息
 * @param {string} message 消息内容
 * @param {string} type 消息类型（success, info, warning, danger）
 */
function showToast(message, type = 'info') {
    // 使用统一的Toast管理器
    if (window.ToastManager) {
        // 类型映射
        const typeMap = {
            'danger': 'error',
            'success': 'success',
            'warning': 'warning',
            'info': 'info'
        };
        const mappedType = typeMap[type] || type;
        window.ToastManager.show(message, mappedType);
    } else {
        // 回退方案
        console.log(`Toast [${type}]:`, message);
        alert(`${type.toUpperCase()}: ${message}`);
    }
} 