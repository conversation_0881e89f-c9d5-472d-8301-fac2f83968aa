#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 确定性缓存系统
基于数据内容Hash的确定性缓存，确保相同输入产生相同输出，解决排产结果不一致问题

核心特性：
1. 内容驱动缓存：基于数据内容计算确定性键值
2. 版本链管理：维护数据版本链，支持回滚和对比
3. 一致性校验：排产前进行数据一致性检查
4. 硬件无关：缓存策略不依赖硬件性能差异
"""

import hashlib
import json
import time
import threading
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

@dataclass
class DataVersion:
    """数据版本信息"""
    version_hash: str
    content_hash: str
    timestamp: float
    data: Any
    size: int
    dependencies: Dict[str, str]  # 依赖的其他数据版本
    metadata: Dict[str, Any]

@dataclass
class CacheSnapshot:
    """缓存快照 - 确保数据一致性"""
    snapshot_id: str
    timestamp: float
    data_versions: Dict[str, str]  # data_type -> version_hash
    consistency_score: float
    conflicts: List[str]

class DeterministicCacheManager:
    """
    🎯 确定性缓存管理器
    
    解决问题：
    1. 时间基础缓存导致的数据不一致
    2. 硬件性能差异导致的排产结果差异
    3. 缓存键冲突和竞争条件
    4. 数据版本管理缺失
    """
    
    def __init__(self, max_versions_per_type: int = 10):
        self.max_versions_per_type = max_versions_per_type
        
        # 🔧 核心数据结构
        self._version_chains: Dict[str, OrderedDict[str, DataVersion]] = defaultdict(OrderedDict)
        self._content_index: Dict[str, str] = {}  # content_hash -> version_hash
        self._dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self._snapshots: Dict[str, CacheSnapshot] = {}
        
        # 🔒 线程安全
        self._lock = threading.RLock()
        self._read_locks: Dict[str, threading.RLock] = defaultdict(threading.RLock)
        
        # 📊 性能统计
        self._stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'version_creates': 0,
            'consistency_checks': 0,
            'conflicts_detected': 0,
            'snapshots_created': 0
        }
        
        logger.info("🎯 确定性缓存管理器初始化完成")

    def _calculate_content_hash(self, data: Any, metadata: Dict[str, Any] = None) -> str:
        """
        计算数据内容的确定性Hash
        
        🎯 关键设计：仅基于核心数据内容计算Hash，不包含环境相关的元数据
        这确保了相同数据在不同硬件环境下产生相同的Hash值
        """
        try:
            # 1. 数据标准化处理
            normalized_data = self._normalize_data_for_hash(data)
            
            # 2. 从元数据中提取影响业务逻辑的核心信息，排除环境相关信息
            core_metadata = {}
            if metadata:
                # 只包含影响数据语义的核心元数据，排除硬件配置等环境信息
                core_keys = ['category', 'volatility', 'importance', 'schema_version']
                core_metadata = {k: v for k, v in metadata.items() if k in core_keys}
            
            # 3. 构建Hash输入（仅包含核心数据和核心元数据）
            hash_input = {
                'data': normalized_data,
                'core_metadata': core_metadata,
                'schema_version': '1.0'  # 防止未来数据结构变更影响
            }
            
            # 4. 计算确定性Hash
            json_str = json.dumps(hash_input, sort_keys=True, separators=(',', ':'), ensure_ascii=False)
            content_hash = hashlib.sha256(json_str.encode('utf-8')).hexdigest()
            
            return content_hash
            
        except Exception as e:
            logger.error(f"❌ 计算内容Hash失败: {e}")
            # 降级到简单Hash
            return hashlib.md5(str(data).encode()).hexdigest()

    def _normalize_data_for_hash(self, data: Any) -> Any:
        """标准化数据以确保Hash一致性"""
        if isinstance(data, dict):
            # 字典：排序键，递归标准化值
            return {k: self._normalize_data_for_hash(v) for k, v in sorted(data.items())}
        elif isinstance(data, list):
            # 列表：保持顺序，递归标准化元素
            return [self._normalize_data_for_hash(item) for item in data]
        elif isinstance(data, (int, float, str, bool, type(None))):
            # 基本类型：直接返回
            return data
        else:
            # 其他类型：转换为字符串
            return str(data)

    def _generate_version_hash(self, content_hash: str, dependencies: Dict[str, str] = None) -> str:
        """生成版本Hash"""
        version_input = {
            'content_hash': content_hash,
            'dependencies': sorted((dependencies or {}).items()),
            'timestamp_floor': int(time.time() / 60)  # 按分钟取整，减少版本碎片
        }
        
        version_str = json.dumps(version_input, sort_keys=True)
        return hashlib.sha256(version_str.encode()).hexdigest()[:16]  # 16位短Hash

    @contextmanager
    def _write_lock(self, data_type: str):
        """写锁上下文管理器"""
        with self._lock:
            with self._read_locks[data_type]:
                yield

    def store_data(self, data_type: str, data: Any, metadata: Dict[str, Any] = None, 
                  dependencies: Dict[str, str] = None) -> str:
        """
        存储数据并返回版本Hash
        
        Args:
            data_type: 数据类型标识
            data: 要存储的数据
            metadata: 元数据信息
            dependencies: 依赖的其他数据版本
            
        Returns:
            str: 版本Hash
        """
        with self._write_lock(data_type):
            # 1. 计算内容Hash
            content_hash = self._calculate_content_hash(data, metadata)
            
            # 2. 检查是否已存在相同内容
            if content_hash in self._content_index:
                existing_version = self._content_index[content_hash]
                logger.debug(f"🎯 内容已存在: {data_type} -> {existing_version[:8]}")
                self._stats['cache_hits'] += 1
                return existing_version
            
            # 3. 生成新版本
            version_hash = self._generate_version_hash(content_hash, dependencies)
            timestamp = time.time()
            
            # 4. 创建数据版本
            data_version = DataVersion(
                version_hash=version_hash,
                content_hash=content_hash,
                timestamp=timestamp,
                data=data,
                size=len(str(data)) if data else 0,
                dependencies=dependencies or {},
                metadata=metadata or {}
            )
            
            # 5. 存储到版本链
            if data_type not in self._version_chains:
                self._version_chains[data_type] = OrderedDict()
            
            self._version_chains[data_type][version_hash] = data_version
            self._content_index[content_hash] = version_hash
            
            # 6. 更新依赖图
            if dependencies:
                for dep_data_type, dep_version in dependencies.items():
                    self._dependency_graph[f"{data_type}:{version_hash}"].add(f"{dep_data_type}:{dep_version}")
            
            # 7. 版本链管理 - 保持最大版本数限制
            self._cleanup_old_versions(data_type)
            
            self._stats['version_creates'] += 1
            logger.info(f"📦 新版本创建: {data_type} -> {version_hash[:8]} (内容Hash: {content_hash[:8]})")
            
            return version_hash

    def get_data(self, data_type: str, version_hash: str = None) -> Optional[Any]:
        """
        获取数据
        
        Args:
            data_type: 数据类型
            version_hash: 版本Hash，None表示获取最新版本
            
        Returns:
            数据内容或None
        """
        with self._read_locks[data_type]:
            if data_type not in self._version_chains:
                return None
            
            version_chain = self._version_chains[data_type]
            
            if version_hash is None:
                # 获取最新版本
                if not version_chain:
                    return None
                latest_version = next(reversed(version_chain.values()))
                return latest_version.data
            else:
                # 获取指定版本
                if version_hash in version_chain:
                    return version_chain[version_hash].data
                return None

    def get_version_info(self, data_type: str, version_hash: str = None) -> Optional[DataVersion]:
        """获取版本信息"""
        with self._read_locks[data_type]:
            if data_type not in self._version_chains:
                return None
            
            version_chain = self._version_chains[data_type]
            
            if version_hash is None:
                if not version_chain:
                    return None
                return next(reversed(version_chain.values()))
            else:
                return version_chain.get(version_hash)

    def create_snapshot(self, data_types: List[str] = None, 
                       snapshot_id: str = None) -> CacheSnapshot:
        """
        创建数据快照，确保数据一致性
        
        Args:
            data_types: 要包含的数据类型列表，None表示所有类型
            snapshot_id: 快照ID，None表示自动生成
            
        Returns:
            CacheSnapshot: 快照对象
        """
        with self._lock:
            if snapshot_id is None:
                snapshot_id = f"snapshot_{int(time.time())}_{len(self._snapshots)}"
            
            if data_types is None:
                data_types = list(self._version_chains.keys())
            
            # 1. 收集当前最新版本
            data_versions = {}
            for data_type in data_types:
                if data_type in self._version_chains and self._version_chains[data_type]:
                    latest_version = next(reversed(self._version_chains[data_type]))
                    data_versions[data_type] = latest_version
            
            # 2. 一致性检查
            consistency_score, conflicts = self._check_snapshot_consistency(data_versions)
            
            # 3. 创建快照
            snapshot = CacheSnapshot(
                snapshot_id=snapshot_id,
                timestamp=time.time(),
                data_versions=data_versions,
                consistency_score=consistency_score,
                conflicts=conflicts
            )
            
            self._snapshots[snapshot_id] = snapshot
            self._stats['snapshots_created'] += 1
            
            logger.info(f"📸 快照创建: {snapshot_id}, 一致性评分: {consistency_score:.2f}, 冲突: {len(conflicts)}")
            
            return snapshot

    def _check_snapshot_consistency(self, data_versions: Dict[str, str]) -> Tuple[float, List[str]]:
        """检查快照数据一致性"""
        self._stats['consistency_checks'] += 1
        conflicts = []
        
        # 1. 时间戳一致性检查
        timestamps = []
        for data_type, version_hash in data_versions.items():
            version_info = self.get_version_info(data_type, version_hash)
            if version_info:
                timestamps.append(version_info.timestamp)
        
        if len(timestamps) > 1:
            time_span = max(timestamps) - min(timestamps)
            if time_span > 300:  # 5分钟
                conflicts.append(f"时间跨度过大: {time_span:.1f}秒")
        
        # 2. 依赖关系检查
        for data_type, version_hash in data_versions.items():
            version_info = self.get_version_info(data_type, version_hash)
            if version_info and version_info.dependencies:
                for dep_type, dep_version in version_info.dependencies.items():
                    if dep_type in data_versions:
                        if data_versions[dep_type] != dep_version:
                            conflicts.append(f"依赖版本冲突: {data_type} 需要 {dep_type}:{dep_version[:8]}, 但快照中是 {data_versions[dep_type][:8]}")
        
        # 3. 计算一致性评分
        consistency_score = max(0.0, 1.0 - len(conflicts) * 0.2)
        
        if conflicts:
            self._stats['conflicts_detected'] += len(conflicts)
        
        return consistency_score, conflicts

    def get_snapshot_data(self, snapshot_id: str) -> Dict[str, Any]:
        """获取快照中的所有数据"""
        if snapshot_id not in self._snapshots:
            return {}
        
        snapshot = self._snapshots[snapshot_id]
        result = {}
        
        for data_type, version_hash in snapshot.data_versions.items():
            data = self.get_data(data_type, version_hash)
            if data is not None:
                result[data_type] = data
        
        return result

    def _cleanup_old_versions(self, data_type: str):
        """清理旧版本，保持版本数量限制"""
        if data_type not in self._version_chains:
            return
        
        version_chain = self._version_chains[data_type]
        
        while len(version_chain) > self.max_versions_per_type:
            # 移除最旧的版本
            oldest_version_hash, oldest_version = version_chain.popitem(last=False)
            
            # 从内容索引中移除
            if oldest_version.content_hash in self._content_index:
                del self._content_index[oldest_version.content_hash]
            
            # 从依赖图中移除
            dep_key = f"{data_type}:{oldest_version_hash}"
            if dep_key in self._dependency_graph:
                del self._dependency_graph[dep_key]
            
            logger.debug(f"🧹 清理旧版本: {data_type}:{oldest_version_hash[:8]}")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            stats = self._stats.copy()
            
            # 添加存储统计
            stats.update({
                'total_data_types': len(self._version_chains),
                'total_versions': sum(len(chain) for chain in self._version_chains.values()),
                'total_snapshots': len(self._snapshots),
                'version_chains': {
                    data_type: len(chain) 
                    for data_type, chain in self._version_chains.items()
                }
            })
            
            # 计算命中率
            total_requests = stats['cache_hits'] + stats['cache_misses']
            if total_requests > 0:
                stats['hit_rate'] = stats['cache_hits'] / total_requests
            else:
                stats['hit_rate'] = 0.0
            
            return stats

    def clear_cache(self, data_type: str = None):
        """清理缓存"""
        with self._lock:
            if data_type:
                # 清理特定数据类型
                if data_type in self._version_chains:
                    # 清理版本链
                    for version_hash, version in self._version_chains[data_type].items():
                        if version.content_hash in self._content_index:
                            del self._content_index[version.content_hash]
                    
                    del self._version_chains[data_type]
                    
                    # 清理依赖图中相关项
                    keys_to_remove = [k for k in self._dependency_graph.keys() if k.startswith(f"{data_type}:")]
                    for key in keys_to_remove:
                        del self._dependency_graph[key]
                    
                    logger.info(f"🧹 已清理数据类型: {data_type}")
            else:
                # 清理全部缓存
                self._version_chains.clear()
                self._content_index.clear()
                self._dependency_graph.clear()
                self._snapshots.clear()
                logger.info("🧹 已清理全部缓存")

    def validate_consistency(self, data_types: List[str] = None) -> Dict[str, Any]:
        """验证当前缓存的一致性"""
        if data_types is None:
            data_types = list(self._version_chains.keys())
        
        # 创建临时快照进行一致性检查
        temp_snapshot = self.create_snapshot(data_types, f"temp_validation_{time.time()}")
        
        result = {
            'consistency_score': temp_snapshot.consistency_score,
            'conflicts': temp_snapshot.conflicts,
            'data_types_checked': data_types,
            'validation_time': time.time()
        }
        
        # 删除临时快照
        del self._snapshots[temp_snapshot.snapshot_id]
        
        return result


# 全局实例
deterministic_cache = DeterministicCacheManager()