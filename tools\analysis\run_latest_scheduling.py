#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行最新排产并获取会话ID
"""

from app import create_app
from app.services.real_scheduling_service import RealSchedulingService

def main():
    app, _ = create_app()
    
    with app.app_context():
        print("🚀 开始执行最新排产...")
        rs = RealSchedulingService()
        
        try:
            result = rs.execute_real_scheduling(
                algorithm='balanced',
                user_id='admin',
                optimization_target='balanced'
            )

            # 兼容多返回形态：list[dict] 或 dict
            try:
                size = len(result) if hasattr(result, '__len__') else 0
            except Exception:
                size = 0
            print(f"✅ 排产完成: {size} 批次成功排产")

            # 获取最新会话ID（多种兜底策略）
            session_id = None
            try:
                if isinstance(result, list) and result and isinstance(result[0], dict):
                    session_id = result[0].get('SESSION_ID')
                elif isinstance(result, dict):
                    session_id = result.get('SESSION_ID')
                    if not session_id and 'scheduled_lots' in result and isinstance(result['scheduled_lots'], list) and result['scheduled_lots']:
                        session_id = result['scheduled_lots'][0].get('SESSION_ID')
            except Exception:
                session_id = None

            if not session_id:
                # 兜底：从数据库获取最近一次写入的SESSION_ID
                try:
                    import pymysql
                    conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', database='aps', charset='utf8mb4')
                    cur = conn.cursor()
                    cur.execute("SELECT SESSION_ID FROM lotprioritydone WHERE SESSION_ID IS NOT NULL ORDER BY CREATE_TIME DESC LIMIT 1")
                    row = cur.fetchone()
                    if row and row[0]:
                        session_id = row[0]
                    cur.close(); conn.close()
                except Exception:
                    session_id = None

            if session_id:
                print(f"📋 最新会话ID: {session_id}")
                return session_id
            else:
                print("⚠️ 未能解析会话ID（可能被定时任务覆盖），请在短时间内重新执行或使用--date过滤验收")
                return None
                
        except Exception as e:
            print(f"❌ 排产执行失败: {e}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == '__main__':
    main()
