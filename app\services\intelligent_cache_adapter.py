#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 智能缓存适配器 - Task 2.2扩展组件
根据智能算法选择器的结果动态调整缓存策略

核心功能：
1. 算法感知缓存：根据所选算法调整缓存策略
2. 预测性预加载：基于历史模式预测数据需求
3. 自适应TTL：根据数据访问频率动态调整生存时间
4. 性能反馈：基于算法执行效果优化缓存配置
"""

import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum

from app.services.multilevel_cache_manager import MultilevelCacheManager, DataType
from app.services.algorithm_selector import AlgorithmType

logger = logging.getLogger(__name__)

class CachePattern(Enum):
    """缓存访问模式"""
    SEQUENTIAL = "sequential"       # 顺序访问
    RANDOM = "random"              # 随机访问
    LOCALITY = "locality"          # 局部性访问
    HEAVY_COMPUTATION = "heavy_computation"  # 重计算访问

class IntelligentCacheAdapter:
    """🧠 智能缓存适配器"""
    
    def __init__(self, multilevel_cache: MultilevelCacheManager):
        self.cache_manager = multilevel_cache
        
        # 算法特定的缓存配置
        self._algorithm_cache_configs = {
            AlgorithmType.HEURISTIC: {
                'preload_priority': ['device_priority', 'lot_priority', 'uph_data'],
                'cache_pattern': CachePattern.SEQUENTIAL,
                'ttl_multiplier': 0.8,  # 快速算法，缓存时间稍短
                'memory_ratio': 0.6     # 60%内存缓存
            },
            AlgorithmType.SIMPLIFIED_ORTOOLS: {
                'preload_priority': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status'],
                'cache_pattern': CachePattern.LOCALITY,
                'ttl_multiplier': 1.0,
                'memory_ratio': 0.7
            },
            AlgorithmType.FULL_ORTOOLS: {
                'preload_priority': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status', 'uph_data', 'recipe_files'],
                'cache_pattern': CachePattern.HEAVY_COMPUTATION,
                'ttl_multiplier': 1.5,  # 重计算算法，缓存时间更长
                'memory_ratio': 0.8
            },
            AlgorithmType.GENETIC: {
                'preload_priority': ['device_priority', 'lot_priority', 'test_specs'],
                'cache_pattern': CachePattern.RANDOM,
                'ttl_multiplier': 1.2,
                'memory_ratio': 0.7
            },
            AlgorithmType.HYBRID: {
                'preload_priority': ['device_priority', 'lot_priority', 'test_specs', 'equipment_status', 'uph_data'],
                'cache_pattern': CachePattern.LOCALITY,
                'ttl_multiplier': 1.0,
                'memory_ratio': 0.75
            }
        }
        
        # 访问模式监控
        self._access_patterns = defaultdict(deque)
        self._pattern_window_size = 100
        
        # 性能反馈数据
        self._performance_feedback = {
            'algorithm_execution_times': defaultdict(list),
            'cache_hit_rates': defaultdict(list),
            'memory_usage': deque(maxlen=50),
            'prediction_accuracy': deque(maxlen=20)
        }
        
        # 预测模型状态
        self._prediction_model = {
            'data_access_sequences': defaultdict(list),
            'temporal_patterns': defaultdict(list),
            'correlation_matrix': {}
        }
        
        # 自适应配置
        self._adaptive_config = {
            'base_ttl': 300,
            'min_ttl': 60,
            'max_ttl': 3600,
            'ttl_adjustment_factor': 0.1
        }

    def prepare_for_algorithm(self, algorithm_type: AlgorithmType, 
                            data_context: Dict, 
                            performance_requirements: Dict = None) -> Dict:
        """
        🧠 为特定算法准备缓存环境
        根据算法类型优化缓存策略和预加载数据
        """
        logger.info(f"🧠 为算法 {algorithm_type.value} 准备缓存环境")
        start_time = time.time()
        
        # 获取算法特定配置
        config = self._algorithm_cache_configs.get(algorithm_type, self._algorithm_cache_configs[AlgorithmType.HYBRID])
        
        # 1. 调整缓存策略
        self._adjust_cache_strategy(algorithm_type, config)
        
        # 2. 预测性数据预加载
        preload_result = self._predictive_preload(algorithm_type, data_context, config)
        
        # 3. 优化内存分配
        self._optimize_memory_allocation(config['memory_ratio'])
        
        # 4. 设置访问模式监控
        self._setup_pattern_monitoring(algorithm_type, config['cache_pattern'])
        
        preparation_time = time.time() - start_time
        
        result = {
            'algorithm_type': algorithm_type.value,
            'preparation_time': preparation_time,
            'preloaded_items': preload_result['preloaded_count'],
            'cache_strategy': config['cache_pattern'].value,
            'estimated_hit_rate': self._estimate_hit_rate(algorithm_type),
            'memory_allocation': f"{config['memory_ratio']*100:.0f}%"
        }
        
        logger.info(f"✅ 缓存环境准备完成: {result}")
        return result

    def _adjust_cache_strategy(self, algorithm_type: AlgorithmType, config: Dict):
        """调整缓存策略"""
        ttl_multiplier = config['ttl_multiplier']
        
        # 动态调整不同数据类型的TTL
        if algorithm_type == AlgorithmType.HEURISTIC:
            # 启发式算法：快速缓存，短TTL
            self.cache_manager._cache_strategies[DataType.BUSINESS_DATA]['ttl']['l1'] = int(60 * ttl_multiplier)
            self.cache_manager._cache_strategies[DataType.COMPUTATION_RESULT]['ttl']['l1'] = int(180 * ttl_multiplier)
            
        elif algorithm_type == AlgorithmType.FULL_ORTOOLS:
            # 完整OR-Tools：长时间缓存，避免重复计算
            self.cache_manager._cache_strategies[DataType.COMPUTATION_RESULT]['ttl']['l1'] = int(600 * ttl_multiplier)
            self.cache_manager._cache_strategies[DataType.COMPUTATION_RESULT]['ttl']['l2'] = int(3600 * ttl_multiplier)
            
        logger.debug(f"🔧 已调整 {algorithm_type.value} 的缓存策略，TTL倍数: {ttl_multiplier}")

    def _predictive_preload(self, algorithm_type: AlgorithmType, data_context: Dict, config: Dict) -> Dict:
        """预测性数据预加载"""
        preload_priority = config['preload_priority']
        preloaded_count = 0
        preload_errors = []
        
        # 基于历史访问模式预测需要的数据
        predicted_data = self._predict_required_data(algorithm_type, data_context)
        
        # 合并预设优先级和预测数据
        all_required_data = list(set(preload_priority + predicted_data))
        
        for data_key in all_required_data:
            try:
                cache_key = f"algo_{algorithm_type.value}_{data_key}"
                
                # 检查是否已缓存
                if self.cache_manager.get(cache_key) is None:
                    # 从数据上下文获取数据
                    if data_key in data_context:
                        data = data_context[data_key]
                        if data:
                            # 根据数据类型选择合适的缓存级别
                            data_type = self._determine_data_type(data_key)
                            self.cache_manager.set(cache_key, data, data_type)
                            preloaded_count += 1
                            logger.debug(f"📥 预加载数据: {data_key}")
                        
            except Exception as e:
                preload_errors.append(f"{data_key}: {str(e)}")
                logger.warning(f"⚠️ 预加载失败 {data_key}: {e}")
        
        return {
            'preloaded_count': preloaded_count,
            'predicted_data': predicted_data,
            'errors': preload_errors
        }

    def _predict_required_data(self, algorithm_type: AlgorithmType, data_context: Dict) -> List[str]:
        """基于历史模式预测需要的数据"""
        # 获取历史访问序列
        historical_sequences = self._prediction_model['data_access_sequences'].get(algorithm_type.value, [])
        
        if len(historical_sequences) < 3:
            # 数据不足，返回默认预测
            return ['equipment_status', 'test_specs']
        
        # 分析访问频率
        frequency_analysis = defaultdict(int)
        for sequence in historical_sequences[-10:]:  # 最近10次
            for data_key in sequence:
                frequency_analysis[data_key] += 1
        
        # 返回高频访问的数据
        predicted = [key for key, freq in frequency_analysis.items() if freq >= 3]
        
        logger.debug(f"🔮 预测 {algorithm_type.value} 需要数据: {predicted}")
        return predicted

    def record_algorithm_execution(self, algorithm_type: AlgorithmType, 
                                 execution_time: float,
                                 data_accessed: List[str],
                                 cache_hit_rate: float):
        """记录算法执行信息，用于优化预测"""
        
        # 记录执行时间
        self._performance_feedback['algorithm_execution_times'][algorithm_type.value].append({
            'timestamp': time.time(),
            'execution_time': execution_time,
            'cache_hit_rate': cache_hit_rate
        })
        
        # 记录数据访问序列
        self._prediction_model['data_access_sequences'][algorithm_type.value].append(data_accessed)
        
        # 限制历史数据量
        if len(self._prediction_model['data_access_sequences'][algorithm_type.value]) > 20:
            self._prediction_model['data_access_sequences'][algorithm_type.value].pop(0)
        
        # 记录缓存命中率
        self._performance_feedback['cache_hit_rates'][algorithm_type.value].append(cache_hit_rate)
        
        logger.debug(f"📊 记录算法执行: {algorithm_type.value}, 时间: {execution_time:.3f}s, 命中率: {cache_hit_rate:.2%}")

    def _setup_pattern_monitoring(self, algorithm_type: AlgorithmType, pattern: CachePattern):
        """设置访问模式监控"""
        self._current_monitoring = {
            'algorithm': algorithm_type.value,
            'pattern': pattern.value,
            'start_time': time.time(),
            'access_log': []
        }

    def _optimize_memory_allocation(self, memory_ratio: float):
        """优化内存分配"""
        # 根据内存比例调整L1缓存大小
        optimal_l1_size = int(1000 * memory_ratio)
        self.cache_manager._l1_max_size = max(500, min(2000, optimal_l1_size))
        
        logger.debug(f"🧠 优化L1缓存大小: {self.cache_manager._l1_max_size}")

    def _determine_data_type(self, data_key: str) -> DataType:
        """确定数据类型"""
        if 'priority' in data_key or 'config' in data_key:
            return DataType.CONFIG_DATA
        elif 'result' in data_key or 'score' in data_key or 'match' in data_key:
            return DataType.COMPUTATION_RESULT
        elif 'algorithm' in data_key or 'performance' in data_key:
            return DataType.ALGORITHM_METADATA
        else:
            return DataType.BUSINESS_DATA

    def _estimate_hit_rate(self, algorithm_type: AlgorithmType) -> float:
        """估算缓存命中率"""
        historical_rates = self._performance_feedback['cache_hit_rates'].get(algorithm_type.value, [])
        
        if not historical_rates:
            # 基于算法类型的经验估算
            base_rates = {
                AlgorithmType.HEURISTIC: 0.75,
                AlgorithmType.SIMPLIFIED_ORTOOLS: 0.82,
                AlgorithmType.FULL_ORTOOLS: 0.88,
                AlgorithmType.GENETIC: 0.78,
                AlgorithmType.HYBRID: 0.85
            }
            return base_rates.get(algorithm_type, 0.80)
        
        # 基于历史数据的加权平均
        recent_rates = historical_rates[-5:]  # 最近5次
        return sum(recent_rates) / len(recent_rates)

    def analyze_cache_performance(self) -> Dict:
        """分析缓存性能"""
        cache_stats = self.cache_manager.get_cache_stats()
        
        # 计算各算法的平均性能
        algorithm_performance = {}
        for algo, executions in self._performance_feedback['algorithm_execution_times'].items():
            if executions:
                recent_executions = executions[-10:]  # 最近10次
                avg_time = sum(e['execution_time'] for e in recent_executions) / len(recent_executions)
                avg_hit_rate = sum(e['cache_hit_rate'] for e in recent_executions) / len(recent_executions)
                
                algorithm_performance[algo] = {
                    'avg_execution_time': round(avg_time, 3),
                    'avg_cache_hit_rate': round(avg_hit_rate, 3),
                    'execution_count': len(executions)
                }
        
        # 预测准确性分析
        prediction_accuracy = (
            sum(self._performance_feedback['prediction_accuracy']) / 
            len(self._performance_feedback['prediction_accuracy'])
        ) if self._performance_feedback['prediction_accuracy'] else 0.0
        
        return {
            'overall_cache_stats': cache_stats,
            'algorithm_performance': algorithm_performance,
            'prediction_accuracy': round(prediction_accuracy, 3),
            'total_patterns_learned': len(self._prediction_model['data_access_sequences']),
            'adaptive_adjustments': self._get_adaptive_adjustment_count()
        }

    def _get_adaptive_adjustment_count(self) -> int:
        """获取自适应调整次数"""
        # 简化实现，实际可以跟踪具体的调整记录
        return sum(len(sequences) for sequences in self._prediction_model['data_access_sequences'].values())

    def clear_learning_data(self):
        """清空学习数据"""
        self._prediction_model = {
            'data_access_sequences': defaultdict(list),
            'temporal_patterns': defaultdict(list),
            'correlation_matrix': {}
        }
        
        self._performance_feedback = {
            'algorithm_execution_times': defaultdict(list),
            'cache_hit_rates': defaultdict(list),
            'memory_usage': deque(maxlen=50),
            'prediction_accuracy': deque(maxlen=20)
        }
        
        logger.info("🧹 智能缓存适配器学习数据已清空")

    def get_optimization_recommendations(self) -> List[Dict]:
        """获取缓存优化建议"""
        recommendations = []
        
        # 分析缓存命中率
        overall_stats = self.cache_manager.get_cache_stats()
        if overall_stats['overall_hit_rate'] < 70:
            recommendations.append({
                'type': 'hit_rate_optimization',
                'priority': 'high',
                'description': f"整体缓存命中率偏低({overall_stats['overall_hit_rate']:.1f}%)，建议增加预加载或调整TTL",
                'action': 'increase_preload_scope'
            })
        
        # 分析算法性能差异
        for algo, perf in self.analyze_cache_performance()['algorithm_performance'].items():
            if perf['avg_cache_hit_rate'] < 0.75:
                recommendations.append({
                    'type': 'algorithm_specific_optimization',
                    'priority': 'medium',
                    'description': f"算法{algo}缓存命中率偏低({perf['avg_cache_hit_rate']:.2%})，建议调整预加载策略",
                    'action': 'adjust_algorithm_cache_strategy',
                    'target': algo
                })
        
        # 分析L1缓存使用率
        if overall_stats['l1_cache_size'] >= self.cache_manager._l1_max_size * 0.9:
            recommendations.append({
                'type': 'memory_optimization',
                'priority': 'medium', 
                'description': "L1缓存使用率过高，建议增加缓存容量或优化淘汰策略",
                'action': 'increase_l1_cache_size'
            })
        
        return recommendations 