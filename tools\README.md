# API接口清理工具

本目录包含一系列用于API接口清理和迁移的工具，帮助项目从旧版API结构迁移到新的模块化API v2结构。

## 可用工具

### 1. API分析器 (api_analyzer.py)

此工具用于扫描项目中的所有API端点，并生成报告。

用法:
```bash
python api_analyzer.py
```

输出:
- `docs/api_audit/api_endpoints_report.md`: 所有API端点的详细报告
- `docs/api_audit/api_endpoints.json`: API端点数据(JSON格式)
- `docs/api_audit/api_stats.json`: API统计信息(JSON格式)

### 2. API迁移助手 (api_migration_helper.py)

此工具提供了API迁移的各种功能，包括分析端点、生成模板、标记废弃等。

用法:
```bash
# 扫描API端点
python api_migration_helper.py scan [--module old|new|both]

# 分析单个端点
python api_migration_helper.py analyze /api/production/some-endpoint

# 添加端点映射
python api_migration_helper.py map /api/old-endpoint /api/v2/new-endpoint

# 生成迁移模板
python api_migration_helper.py template /api/old-endpoint /api/v2/new-endpoint

# 标记端点为废弃
python api_migration_helper.py deprecate /api/old-endpoint /api/v2/new-endpoint

# 生成迁移报告
python api_migration_helper.py report
```

### 3. 自动API迁移工具 (auto_migrate_api.py)

此工具可以自动执行批量迁移操作，包括标记废弃、生成模板代码等。

用法:
```bash
# 迁移单个端点
python tools/auto_migrate_api.py migrate /api/production/some-endpoint

# 带参数的单个迁移
python tools/auto_migrate_api.py migrate /api/production/some-endpoint --new-endpoint /api/v2/custom-path --dry-run

# 批量迁移
python tools/auto_migrate_api.py batch --module production --limit 5

# 从JSON文件批量迁移
python tools/auto_migrate_api.py batch --file endpoints.json --dry-run
```

## 迁移流程

1. 使用 `api_analyzer.py` 扫描所有API端点，生成报告
2. 使用 `api_migration_helper.py report` 查看当前迁移进度
3. 使用 `auto_migrate_api.py batch` 批量迁移API
4. 检查和调整生成的模板代码
5. 更新前端代码以使用新的API端点

## 迁移规范

1. 旧API路径: `/api/module/action`
2. 新API路径: `/api/v2/module/action`
3. 旧API文件位置: `app/api/*.py`
4. 新API文件位置: `app/api_v2/module/*.py` 