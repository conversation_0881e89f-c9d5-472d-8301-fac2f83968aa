#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 APS系统统一连接管理器 - 解决双重架构不一致问题
整合Flask-SQLAlchemy与自定义连接池，提供统一的数据库连接接口

核心功能：
- 统一管理Flask-SQLAlchemy和自定义连接池
- 解决DataSourceManager中的双重连接架构问题
- 实现连接复用最大化和及时回收
- 支持批量处理连接优化
- 提供智能连接策略选择
"""

import logging
import threading
import time
import functools
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
import pymysql
from sqlalchemy import create_engine, pool
from sqlalchemy.orm import sessionmaker
from config.aps_config import config

logger = logging.getLogger(__name__)

class ConnectionStrategy(Enum):
    """🔥 连接策略枚举 - 解决双重架构问题"""
    FLASK_SQLALCHEMY = "flask"      # 使用Flask-SQLAlchemy (db.session)
    CUSTOM_POOL = "custom"          # 使用自定义连接池
    BATCH_PROCESSING = "batch"      # 使用批量处理连接
    ADAPTIVE = "adaptive"           # 自适应选择最优连接

class OperationType(Enum):
    """操作类型枚举"""
    READ = "read"                   # 读取操作 (SELECT)
    WRITE = "write"                 # 写入操作 (INSERT/UPDATE/DELETE)
    BATCH = "batch"                 # 批量操作 (大量INSERT/UPDATE)
    TRANSACTION = "transaction"     # 事务操作

class ConnectionPriority(Enum):
    """连接优先级枚举"""
    CRITICAL = 1    # 核心排产计算
    HIGH = 2        # Web交互和API  
    NORMAL = 3      # 批处理任务
    LOW = 4         # 监控诊断

@dataclass
class ConnectionPoolConfig:
    """连接池配置数据类"""
    pool_size: int
    max_overflow: int
    pool_recycle: int
    priority: ConnectionPriority
    timeout: int = 45

class UnifiedConnectionManager:
    """统一连接池管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._initialized = True
        self.config = config
        
        # 连接池字典
        self._pools: Dict[ConnectionPriority, Any] = {}
        self._pool_configs: Dict[ConnectionPriority, ConnectionPoolConfig] = {}
        self._active_connections: Dict[ConnectionPriority, int] = {}
        
        # 连接统计
        self._stats = {
            'total_connections_created': 0,
            'total_connections_closed': 0,
            'connection_leaks_prevented': 0,
            'pool_exhaustion_events': 0
        }
        
        # 连接跟踪
        self._connection_registry: Dict[str, Dict] = {}
        self._registry_lock = threading.Lock()
        
        # 初始化连接池
        self._initialize_pools()
        
        # 🔥 新增：双重架构桥接组件
        self._setup_dual_architecture_bridge()
        
        logger.info("✅ 统一连接池管理器已初始化 - 已集成双重架构桥接")
    
    def _initialize_pools(self):
        """初始化分层连接池"""
        # 定义各层连接池配置
        pool_configs = {
            ConnectionPriority.CRITICAL: ConnectionPoolConfig(
                pool_size=self.config._get_config_value('critical_pool_size', 'DATABASE', 5, int),
                max_overflow=self.config._get_config_value('critical_max_overflow', 'DATABASE', 5, int),
                pool_recycle=self.config._get_config_value('critical_pool_recycle', 'DATABASE', 3600, int),
                priority=ConnectionPriority.CRITICAL
            ),
            ConnectionPriority.HIGH: ConnectionPoolConfig(
                pool_size=self.config._get_config_value('interactive_pool_size', 'DATABASE', 10, int),
                max_overflow=self.config._get_config_value('interactive_max_overflow', 'DATABASE', 15, int), 
                pool_recycle=self.config._get_config_value('interactive_pool_recycle', 'DATABASE', 600, int),
                priority=ConnectionPriority.HIGH
            ),
            ConnectionPriority.NORMAL: ConnectionPoolConfig(
                pool_size=self.config._get_config_value('batch_pool_size', 'DATABASE', 5, int),
                max_overflow=self.config._get_config_value('batch_max_overflow', 'DATABASE', 10, int),
                pool_recycle=self.config._get_config_value('batch_pool_recycle', 'DATABASE', 300, int),
                priority=ConnectionPriority.NORMAL
            ),
            ConnectionPriority.LOW: ConnectionPoolConfig(
                pool_size=self.config._get_config_value('monitoring_pool_size', 'DATABASE', 2, int),
                max_overflow=self.config._get_config_value('monitoring_max_overflow', 'DATABASE', 3, int),
                pool_recycle=self.config._get_config_value('monitoring_pool_recycle', 'DATABASE', 300, int),
                priority=ConnectionPriority.LOW
            )
        }
        
        # 创建各层连接池
        for priority, pool_config in pool_configs.items():
            self._pool_configs[priority] = pool_config
            self._active_connections[priority] = 0
            
            # 创建SQLAlchemy引擎
            engine = create_engine(
                self.config.SQLALCHEMY_DATABASE_URI,
                poolclass=pool.QueuePool,
                pool_size=pool_config.pool_size,
                max_overflow=pool_config.max_overflow,
                pool_recycle=pool_config.pool_recycle,
                pool_timeout=pool_config.timeout,
                pool_pre_ping=True,
                echo=False
            )
            
            self._pools[priority] = {
                'engine': engine,
                'sessionmaker': sessionmaker(bind=engine),
                'config': pool_config
            }
            
            logger.info(f"✅ {priority.name}连接池已创建: {pool_config.pool_size}+{pool_config.max_overflow}, {pool_config.pool_recycle}s")
    
    def _setup_dual_architecture_bridge(self):
        """
        🔥 设置双重架构桥接 - 核心优化功能
        解决DataSourceManager中Flask-SQLAlchemy与自定义连接池不一致问题
        """
        # 延迟导入避免循环依赖
        try:
            from app.utils.db_connection_pool import get_connection_pool
            from app import db
            
            self._custom_pool_manager = get_connection_pool()
            self._flask_db = db
            
            # 连接策略映射：操作类型 -> 推荐连接策略
            self._operation_strategies = {
                OperationType.READ: ConnectionStrategy.FLASK_SQLALCHEMY,      # 读取操作优先使用ORM
                OperationType.WRITE: ConnectionStrategy.CUSTOM_POOL,          # 写入操作优先使用自定义池
                OperationType.BATCH: ConnectionStrategy.BATCH_PROCESSING,     # 批量操作强制使用批处理连接
                OperationType.TRANSACTION: ConnectionStrategy.ADAPTIVE        # 事务操作自适应选择
            }
            
            # 性能统计
            self._dual_arch_stats = {
                'flask_connections': 0,
                'custom_connections': 0, 
                'batch_connections': 0,
                'strategy_switches': 0,
                'performance_comparisons': []
            }
            
            logger.info("🔗 双重架构桥接已配置")
            
        except ImportError as e:
            logger.warning(f"⚠️ 双重架构桥接配置部分失败: {e}")
    
    @contextmanager
    def get_unified_connection(self, operation_type: OperationType, 
                             database: str = 'aps',
                             prefer_strategy: ConnectionStrategy = None,
                             batch_id: str = None):
        """
        🔥 获取统一连接 - 解决双重架构问题的核心方法
        
        根据操作类型智能选择Flask-SQLAlchemy或自定义连接池
        
        Args:
            operation_type: 操作类型(READ/WRITE/BATCH/TRANSACTION)
            database: 数据库名称
            prefer_strategy: 偏好的连接策略(可选)
            batch_id: 批量操作ID(批量操作时使用)
            
        Returns:
            统一连接上下文管理器
            
        Usage:
            # 替代DataSourceManager中的混合连接模式
            with connection_manager.get_unified_connection(OperationType.READ) as conn:
                if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
                    result = conn.execute(text("SELECT * FROM et_wait_lot"))
                else:  # 自定义连接池
                    cursor = conn.cursor()
                    cursor.execute("SELECT * FROM et_wait_lot")
        """
        # 确定连接策略
        strategy = prefer_strategy or self._operation_strategies[operation_type]
        
        # 性能计时
        start_time = time.time()
        connection = None
        
        try:
            if strategy == ConnectionStrategy.FLASK_SQLALCHEMY:
                # 使用Flask-SQLAlchemy连接
                connection = self._flask_db.session.connection()
                self._dual_arch_stats['flask_connections'] += 1
                yield connection
                
            elif strategy == ConnectionStrategy.CUSTOM_POOL:
                # 使用自定义连接池
                with self._custom_pool_manager.get_connection_context(database) as custom_conn:
                    self._dual_arch_stats['custom_connections'] += 1
                    yield custom_conn
                    
            elif strategy == ConnectionStrategy.BATCH_PROCESSING:
                # 使用批量处理连接
                with self._custom_pool_manager.batch_processing_connection(database, batch_id) as batch_conn:
                    self._dual_arch_stats['batch_connections'] += 1
                    yield batch_conn
                    
            elif strategy == ConnectionStrategy.ADAPTIVE:
                # 自适应选择最优连接
                optimal_strategy = self._choose_optimal_strategy(operation_type)
                with self.get_unified_connection(operation_type, database, optimal_strategy, batch_id) as adaptive_conn:
                    self._dual_arch_stats['strategy_switches'] += 1
                    yield adaptive_conn
                    
        except Exception as e:
            # 连接失败时的降级策略
            logger.warning(f"⚠️ 连接策略 {strategy.value} 失败，尝试降级: {e}")
            fallback_strategy = self._get_fallback_strategy(strategy)
            
            if fallback_strategy != strategy:
                self._dual_arch_stats['strategy_switches'] += 1
                with self.get_unified_connection(operation_type, database, fallback_strategy, batch_id) as fallback_conn:
                    yield fallback_conn
            else:
                raise
        
        finally:
            # 记录性能数据
            duration = time.time() - start_time
            self._dual_arch_stats['performance_comparisons'].append({
                'operation_type': operation_type.value,
                'strategy': strategy.value,
                'duration': duration,
                'timestamp': time.time()
            })
            
            # 保持性能历史在合理范围内
            if len(self._dual_arch_stats['performance_comparisons']) > 500:
                self._dual_arch_stats['performance_comparisons'] = \
                    self._dual_arch_stats['performance_comparisons'][-250:]
    
    def _choose_optimal_strategy(self, operation_type: OperationType) -> ConnectionStrategy:
        """
        🎯 智能选择最优连接策略
        基于历史性能数据决定使用Flask-SQLAlchemy还是自定义连接池
        """
        # 如果性能数据不足，使用默认策略
        if len(self._dual_arch_stats['performance_comparisons']) < 20:
            return self._operation_strategies[operation_type]
        
        # 分析最近的性能数据
        recent_data = self._dual_arch_stats['performance_comparisons'][-100:]
        relevant_data = [d for d in recent_data if d['operation_type'] == operation_type.value]
        
        if len(relevant_data) < 10:
            return self._operation_strategies[operation_type]
        
        # 计算不同策略的平均性能
        flask_times = [d['duration'] for d in relevant_data if d['strategy'] == 'flask']
        custom_times = [d['duration'] for d in relevant_data if d['strategy'] == 'custom']
        
        if len(flask_times) > 5 and len(custom_times) > 5:
            avg_flask = sum(flask_times) / len(flask_times)
            avg_custom = sum(custom_times) / len(custom_times)
            
            # 选择性能更好的策略
            if avg_custom < avg_flask * 0.8:  # 自定义连接池显著更快
                return ConnectionStrategy.CUSTOM_POOL
            elif avg_flask < avg_custom * 0.8:  # Flask-SQLAlchemy显著更快
                return ConnectionStrategy.FLASK_SQLALCHEMY
        
        # 默认策略
        return self._operation_strategies[operation_type]
    
    def _get_fallback_strategy(self, failed_strategy: ConnectionStrategy) -> ConnectionStrategy:
        """获取失败策略的降级方案"""
        fallback_map = {
            ConnectionStrategy.FLASK_SQLALCHEMY: ConnectionStrategy.CUSTOM_POOL,
            ConnectionStrategy.CUSTOM_POOL: ConnectionStrategy.FLASK_SQLALCHEMY,
            ConnectionStrategy.BATCH_PROCESSING: ConnectionStrategy.CUSTOM_POOL,
            ConnectionStrategy.ADAPTIVE: ConnectionStrategy.FLASK_SQLALCHEMY
        }
        return fallback_map.get(failed_strategy, ConnectionStrategy.FLASK_SQLALCHEMY)
    
    # 🔥 DataSourceManager替换方法 - 解决双重架构问题
    @contextmanager
    def get_read_connection(self, database: str = 'aps', force_flask: bool = False):
        """获取读取连接 - DataSourceManager替换方法"""
        strategy = ConnectionStrategy.FLASK_SQLALCHEMY if force_flask else None
        with self.get_unified_connection(OperationType.READ, database, strategy) as conn:
            yield conn
    
    @contextmanager 
    def get_write_connection(self, database: str = 'aps', force_custom: bool = True):
        """获取写入连接 - DataSourceManager替换方法"""
        strategy = ConnectionStrategy.CUSTOM_POOL if force_custom else None
        with self.get_unified_connection(OperationType.WRITE, database, strategy) as conn:
            yield conn
    
    @contextmanager
    def get_batch_connection(self, database: str = 'aps', batch_id: str = None):
        """获取批量连接 - DataSourceManager替换方法"""
        with self.get_unified_connection(OperationType.BATCH, database, 
                                      ConnectionStrategy.BATCH_PROCESSING, batch_id) as conn:
            yield conn
    
    def connection_leak_protection(self, timeout=30):
        """连接泄漏保护装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                connection_id = f"{func.__name__}_{threading.current_thread().ident}_{time.time()}"
                
                # 注册连接
                with self._registry_lock:
                    self._connection_registry[connection_id] = {
                        'function': func.__name__,
                        'start_time': time.time(),
                        'timeout': timeout,
                        'thread_id': threading.current_thread().ident
                    }
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    logger.error(f"连接操作失败 {func.__name__}: {e}")
                    raise
                finally:
                    # 清理连接注册
                    with self._registry_lock:
                        if connection_id in self._connection_registry:
                            duration = time.time() - self._connection_registry[connection_id]['start_time']
                            del self._connection_registry[connection_id]
                            
                            if duration > timeout:
                                self._stats['connection_leaks_prevented'] += 1
                                logger.warning(f"⚠️ 连接超时保护触发: {func.__name__} 耗时{duration:.1f}s")
            
            return wrapper
        return decorator
    
    @contextmanager
    def get_connection(self, priority: ConnectionPriority = ConnectionPriority.HIGH, auto_commit=True):
        """获取连接 - 上下文管理器"""
        session = None
        try:
            pool_info = self._pools[priority]
            session = pool_info['sessionmaker']()
            
            # 更新统计
            self._active_connections[priority] += 1
            self._stats['total_connections_created'] += 1
            
            yield session
            
            if auto_commit:
                session.commit()
                
        except Exception as e:
            if session:
                session.rollback()
            logger.error(f"连接操作异常 ({priority.name}): {e}")
            raise
        finally:
            if session:
                session.close()
                self._active_connections[priority] -= 1
                self._stats['total_connections_closed'] += 1
    
    @contextmanager  
    def get_raw_connection(self, priority: ConnectionPriority = ConnectionPriority.HIGH):
        """获取原始数据库连接"""
        connection = None
        try:
            pool_info = self._pools[priority]
            connection = pool_info['engine'].raw_connection()
            
            # 更新统计
            self._active_connections[priority] += 1
            self._stats['total_connections_created'] += 1
            
            yield connection
            
        except Exception as e:
            logger.error(f"原始连接操作异常 ({priority.name}): {e}")
            raise
        finally:
            if connection:
                connection.close()
                self._active_connections[priority] -= 1
                self._stats['total_connections_closed'] += 1
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态 - 包含双重架构统计"""
        status = {
            'pools': {},
            'statistics': self._stats.copy(),
            'active_connections_total': sum(self._active_connections.values()),
            'dual_architecture_stats': self._dual_arch_stats.copy()  # 🔥 新增双重架构统计
        }
        
        for priority, pool_info in self._pools.items():
            engine = pool_info['engine']
            pool_obj = engine.pool
            config = self._pool_configs[priority]
            
            status['pools'][priority.name] = {
                'pool_size': config.pool_size,
                'max_overflow': config.max_overflow,
                'pool_recycle': config.pool_recycle,
                'checked_out': pool_obj.checkedout(),
                'checked_in': pool_obj.checkedin(),
                'overflow': pool_obj.overflow(),
                'active_connections': self._active_connections[priority]
            }
        
        return status
    
    def cleanup_leaked_connections(self):
        """清理泄漏的连接"""
        cleaned_count = 0
        current_time = time.time()
        
        with self._registry_lock:
            expired_connections = []
            
            for conn_id, conn_info in self._connection_registry.items():
                if current_time - conn_info['start_time'] > conn_info['timeout']:
                    expired_connections.append(conn_id)
            
            for conn_id in expired_connections:
                del self._connection_registry[conn_id]
                cleaned_count += 1
                self._stats['connection_leaks_prevented'] += 1
        
        if cleaned_count > 0:
            logger.warning(f"🧹 清理了{cleaned_count}个泄漏连接")
        
        return cleaned_count
    
    def health_check(self) -> Dict[str, Any]:
        """连接池健康检查"""
        health_status = {
            'overall_status': 'healthy',
            'issues': [],
            'recommendations': []
        }
        
        status = self.get_pool_status()
        
        # 检查连接池使用率
        for pool_name, pool_stats in status['pools'].items():
            total_capacity = pool_stats['pool_size'] + pool_stats['max_overflow']
            usage_rate = (pool_stats['checked_out'] + pool_stats['overflow']) / total_capacity
            
            if usage_rate > 0.9:
                health_status['issues'].append(f"{pool_name}连接池使用率过高: {usage_rate:.1%}")
                health_status['overall_status'] = 'warning'
            elif usage_rate > 0.7:
                health_status['recommendations'].append(f"{pool_name}连接池使用率较高: {usage_rate:.1%}, 建议关注")
        
        # 检查连接泄漏
        leaked_count = len(self._connection_registry)
        if leaked_count > 5:
            health_status['issues'].append(f"检测到{leaked_count}个潜在连接泄漏")
            health_status['overall_status'] = 'critical'
        
        return health_status

# 全局统一连接管理器实例
unified_connection_manager = UnifiedConnectionManager()

# 便利函数
def get_db_connection(priority: ConnectionPriority = ConnectionPriority.HIGH, auto_commit=True):
    """获取数据库连接的便利函数"""
    return unified_connection_manager.get_connection(priority, auto_commit)

def get_raw_db_connection(priority: ConnectionPriority = ConnectionPriority.HIGH):
    """获取原始数据库连接的便利函数"""
    return unified_connection_manager.get_raw_connection(priority)

def connection_protected(timeout=30):
    """连接保护装饰器的便利函数"""
    return unified_connection_manager.connection_leak_protection(timeout)

def get_connection_status():
    """获取连接状态的便利函数"""
    return unified_connection_manager.get_pool_status()

# 🔥 新增：双重架构桥接便利方法
def get_unified_read_connection(database: str = 'aps', force_flask: bool = False):
    """便利方法：获取统一读取连接 - 解决DataSourceManager双重架构"""
    return unified_connection_manager.get_read_connection(database, force_flask)

def get_unified_write_connection(database: str = 'aps', force_custom: bool = True):
    """便利方法：获取统一写入连接 - 解决DataSourceManager双重架构"""
    return unified_connection_manager.get_write_connection(database, force_custom)

def get_unified_batch_connection(database: str = 'aps', batch_id: str = None):
    """便利方法：获取统一批量连接 - 核心优化功能"""
    return unified_connection_manager.get_batch_connection(database, batch_id)

def get_dual_architecture_stats():
    """便利方法：获取双重架构使用统计"""
    return unified_connection_manager._dual_arch_stats.copy()

def optimize_connection_strategies():
    """便利方法：触发连接策略优化"""
    flask_count = unified_connection_manager._dual_arch_stats['flask_connections']
    custom_count = unified_connection_manager._dual_arch_stats['custom_connections']
    batch_count = unified_connection_manager._dual_arch_stats['batch_connections']
    
    total = flask_count + custom_count + batch_count
    if total > 0:
        logger.info(f"📊 连接使用统计: Flask-SQLAlchemy: {flask_count} ({flask_count/total:.1%}), "
                   f"自定义连接池: {custom_count} ({custom_count/total:.1%}), "
                   f"批量连接: {batch_count} ({batch_count/total:.1%})")
    
    return {
        'flask_percentage': flask_count / total if total > 0 else 0,
        'custom_percentage': custom_count / total if total > 0 else 0,
        'batch_percentage': batch_count / total if total > 0 else 0,
        'total_connections': total
    }

# 🔥 双重架构桥接使用示例
"""
# 1. 统一读取连接（替代DataSourceManager中的db.session模式）
with get_unified_read_connection() as conn:
    if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
        from sqlalchemy import text
        result = conn.execute(text("SELECT * FROM et_wait_lot"))
    else:  # 自定义连接池
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM et_wait_lot")

# 2. 统一写入连接（替代DataSourceManager中的_get_db_connection_pooled模式）  
with get_unified_write_connection() as conn:
    cursor = conn.cursor()
    cursor.execute("INSERT INTO lotprioritydone (...) VALUES (...)", data)

# 3. 批量处理连接（核心优化：268个批次只用1个连接）
with get_unified_batch_connection('aps', 'scheduling_001') as conn:
    for lot in lots:  # 处理数百个批次
        cursor = conn.cursor() 
        cursor.execute("INSERT INTO lotprioritydone (...) VALUES (...)", lot)
        # 只使用这一个连接处理所有批次

# 4. 原有功能保持兼容
with get_db_connection(ConnectionPriority.HIGH) as session:
    result = session.execute("SELECT * FROM table")

# 5. 连接统计监控
stats = get_dual_architecture_stats()
print(f"Flask连接: {stats['flask_connections']}, 自定义连接: {stats['custom_connections']}")
optimization_report = optimize_connection_strategies()
"""